{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///../demo3/src/js/pages/widgets.js"], "names": [], "mappings": ";QAAA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;;AClFa;;AAEb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,oDAAoD,SAAS;AAC7D;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA,sDAAsD,SAAS;AAC/D;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,IAA6B;AACjC;AACA;;AAEA;AACA;AACA,CAAC", "file": "js/pages/widgets.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"../demo3/src/js/pages/widgets.js\");\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTWidgets = function () {\r\n    // Private properties\r\n\r\n    // General Controls\r\n    var _initDaterangepicker = function () {\r\n        if ($('#kt_dashboard_daterangepicker').length == 0) {\r\n            return;\r\n        }\r\n\r\n        var picker = $('#kt_dashboard_daterangepicker');\r\n        var start = moment();\r\n        var end = moment();\r\n\r\n        function cb(start, end, label) {\r\n            var title = '';\r\n            var range = '';\r\n\r\n            if ((end - start) < 100 || label == 'Today') {\r\n                title = 'Today:';\r\n                range = start.format('MMM D');\r\n            } else if (label == 'Yesterday') {\r\n                title = 'Yesterday:';\r\n                range = start.format('MMM D');\r\n            } else {\r\n                range = start.format('MMM D') + ' - ' + end.format('MMM D');\r\n            }\r\n\r\n            $('#kt_dashboard_daterangepicker_date').html(range);\r\n            $('#kt_dashboard_daterangepicker_title').html(title);\r\n        }\r\n\r\n        picker.daterangepicker({\r\n            direction: KTUtil.isRTL(),\r\n            startDate: start,\r\n            endDate: end,\r\n            opens: 'left',\r\n            applyClass: 'btn-primary',\r\n            cancelClass: 'btn-light-primary',\r\n            ranges: {\r\n                'Today': [moment(), moment()],\r\n                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],\r\n                'Last 7 Days': [moment().subtract(6, 'days'), moment()],\r\n                'Last 30 Days': [moment().subtract(29, 'days'), moment()],\r\n                'This Month': [moment().startOf('month'), moment().endOf('month')],\r\n                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]\r\n            }\r\n        }, cb);\r\n\r\n        cb(start, end, '');\r\n    }\r\n\r\n    // Stats widgets\r\n    var _initStatsWidget7 = function () {\r\n        var element = document.getElementById(\"kt_stats_widget_7_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 45, 32, 70, 40]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: 150,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base']['success']]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light']['success']],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['success']],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base']['success']],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initStatsWidget8 = function () {\r\n        var element = document.getElementById(\"kt_stats_widget_8_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 45, 32, 70, 40]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: 150,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base']['danger']]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light']['danger']],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['danger']],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base']['danger']],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initStatsWidget9 = function () {\r\n        var element = document.getElementById(\"kt_stats_widget_9_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 45, 32, 70, 40]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: 150,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base']['primary']]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light']['primary']],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['primary']],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base']['primary']],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initStatsWidget10 = function () {\r\n        var element = document.getElementById(\"kt_stats_widget_10_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var color = KTUtil.hasAttr(element, 'data-color') ? KTUtil.attr(element, 'data-color') : 'info';\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [40, 40, 30, 30, 35, 35, 50]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base'][color]]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 55,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base'][color]],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initStatsWidget11 = function () {\r\n        var element = document.getElementById(\"kt_stats_widget_11_chart\");\r\n\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var color = KTUtil.hasAttr(element, 'data-color') ? KTUtil.attr(element, 'data-color') : 'success';\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [40, 40, 30, 30, 35, 35, 50]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: 150,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base'][color]]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Aug', 'Sep'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 55,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base'][color]],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initStatsWidget12 = function () {\r\n        var element = document.getElementById(\"kt_stats_widget_12_chart\");\r\n\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var color = KTUtil.hasAttr(element, 'data-color') ? KTUtil.attr(element, 'data-color') : 'primary';\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [40, 40, 30, 30, 35, 35, 50]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base'][color]]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Aug', 'Sep'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 55,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base'][color]],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    // Charts widgets\r\n    var _initChartsWidget1 = function () {\r\n        var element = document.getElementById(\"kt_charts_widget_1_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [44, 55, 57, 56, 61, 58]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [76, 85, 101, 98, 87, 105]\r\n            }],\r\n            chart: {\r\n                type: 'bar',\r\n                height: 350,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: false,\r\n                    columnWidth: ['30%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                show: true,\r\n                width: 2,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                opacity: 1\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['base']['success'], KTApp.getSettings()['colors']['gray']['gray-300']],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initChartsWidget2 = function () {\r\n        var element = document.getElementById(\"kt_charts_widget_2_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [44, 55, 57, 56, 61, 58]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [76, 85, 101, 98, 87, 105]\r\n            }],\r\n            chart: {\r\n                type: 'bar',\r\n                height: 350,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: false,\r\n                    columnWidth: ['30%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                show: true,\r\n                width: 2,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                opacity: 1\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['base']['warning'], KTApp.getSettings()['colors']['gray']['gray-300']],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initChartsWidget3 = function () {\r\n        var element = document.getElementById(\"kt_charts_widget_3_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 40, 40, 90, 90, 70, 70]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: 350,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base']['info']]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['theme']['base']['info'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light']['info']],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            },\r\n            markers: {\r\n                //size: 5,\r\n                //colors: [KTApp.getSettings()['colors']['theme']['light']['danger']],\r\n                strokeColor: KTApp.getSettings()['colors']['theme']['base']['info'],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initChartsWidget4 = function () {\r\n        var element = document.getElementById(\"kt_charts_widget_4_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [60, 50, 80, 40, 100, 60]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [70, 60, 110, 40, 50, 70]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: 350,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth'\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['theme']['light']['success'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['base']['success'], KTApp.getSettings()['colors']['theme']['base']['warning']],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            },\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['success'], KTApp.getSettings()['colors']['theme']['light']['warning']],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['light']['success'], KTApp.getSettings()['colors']['theme']['light']['warning']],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initChartsWidget5 = function () {\r\n        var element = document.getElementById(\"kt_charts_widget_5_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [40, 50, 65, 70, 50, 30]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [-30, -40, -55, -60, -40, -20]\r\n            }],\r\n            chart: {\r\n                type: 'bar',\r\n                stacked: true,\r\n                height: 350,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: false,\r\n                    columnWidth: ['12%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                show: true,\r\n                width: 2,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: -80,\r\n                max: 80,\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                opacity: 1\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['base']['info'], KTApp.getSettings()['colors']['theme']['base']['primary']],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initChartsWidget6 = function () {\r\n        var element = document.getElementById(\"kt_charts_widget_6_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                type: 'bar',\r\n                stacked: true,\r\n                data: [40, 50, 65, 70, 50, 30]\r\n            }, {\r\n                name: 'Revenue',\r\n                type: 'bar',\r\n                stacked: true,\r\n                data: [20, 20, 25, 30, 30, 20]\r\n            }, {\r\n                name: 'Expenses',\r\n                type: 'area',\r\n                data: [50, 80, 60, 90, 50, 70]\r\n            }],\r\n            chart: {\r\n                stacked: true,\r\n                height: 350,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    stacked: true,\r\n                    horizontal: false,\r\n                    endingShape: 'rounded',\r\n                    columnWidth: ['12%']\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 2,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                max: 120,\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                opacity: 1\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['base']['info'], KTApp.getSettings()['colors']['theme']['base']['primary'], KTApp.getSettings()['colors']['theme']['light']['primary']],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                },\r\n                padding: {\r\n                    top: 0,\r\n                    right: 0,\r\n                    bottom: 0,\r\n                    left: 0\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initChartsWidget7 = function () {\r\n        var element = document.getElementById(\"kt_charts_widget_7_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 30, 50, 50, 35, 35]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [55, 20, 20, 20, 70, 70]\r\n            }, {\r\n                name: 'Expenses',\r\n                data: [60, 60, 40, 40, 30, 30]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: 300,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 2,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base']['warning'], 'transparent', 'transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light']['warning'], KTApp.getSettings()['colors']['theme']['light']['info'], KTApp.getSettings()['colors']['gray']['gray-100']],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            },\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['warning'], KTApp.getSettings()['colors']['theme']['light']['info'], KTApp.getSettings()['colors']['gray']['gray-100']],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base']['warning'], KTApp.getSettings()['colors']['theme']['base']['info'], KTApp.getSettings()['colors']['gray']['gray-500']],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initChartsWidget8 = function () {\r\n        var element = document.getElementById(\"kt_charts_widget_8_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 30, 50, 50, 35, 35]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [55, 20, 20, 20, 70, 70]\r\n            }, {\r\n                name: 'Expenses',\r\n                data: [60, 60, 40, 40, 30, 30]\r\n            },],\r\n            chart: {\r\n                type: 'area',\r\n                height: 300,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 2,\r\n                colors: ['transparent', 'transparent', 'transparent']\r\n            },\r\n            xaxis: {\r\n                x: 0,\r\n                offsetX: 0,\r\n                offsetY: 0,\r\n                padding: {\r\n                    left: 0,\r\n                    right: 0,\r\n                    top: 0,\r\n                },\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                y: 0,\r\n                offsetX: 0,\r\n                offsetY: 0,\r\n                padding: {\r\n                    left: 0,\r\n                    right: 0\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light']['success'], KTApp.getSettings()['colors']['theme']['light']['danger'], KTApp.getSettings()['colors']['theme']['light']['info']],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                padding: {\r\n                    top: 0,\r\n                    bottom: 0,\r\n                    left: 0,\r\n                    right: 0\r\n                }\r\n            },\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['success'], KTApp.getSettings()['colors']['theme']['light']['danger'], KTApp.getSettings()['colors']['theme']['light']['info']],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base']['success'], KTApp.getSettings()['colors']['theme']['base']['danger'], KTApp.getSettings()['colors']['theme']['base']['info']],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initChartsWidget9 = function () {\r\n        var element = document.getElementById(\"kt_charts_widget_9_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [20, 30, 45, 35, 25]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [-20, -30, -45, -35, -25]\r\n            }],\r\n            chart: {\r\n                type: 'bar',\r\n                stacked: true,\r\n                height: 350,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: false,\r\n                    columnWidth: ['17%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                width: 0,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                lines: {\r\n                    show: false,\r\n                },\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                lines: {\r\n                    show: false,\r\n                },\r\n                min: -50,\r\n                max: 50,\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                opacity: 1\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['base']['info'], KTApp.getSettings()['colors']['theme']['base']['primary']],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    // Mixed widgets\r\n    var _initMixedWidget1 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_1_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var strokeColor = '#D13647';\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 45, 32, 70, 40, 40, 40]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                },\r\n                dropShadow: {\r\n                    enabled: true,\r\n                    enabledOnSeries: undefined,\r\n                    top: 5,\r\n                    left: 0,\r\n                    blur: 3,\r\n                    color: strokeColor,\r\n                    opacity: 0.5\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 0\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [strokeColor]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 80,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                },\r\n                marker: {\r\n                    show: false\r\n                }\r\n            },\r\n            colors: ['transparent'],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['danger']],\r\n                strokeColor: [strokeColor],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget2 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_2_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var strokeColor = '#287ED7';\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 45, 32, 70, 40, 40, 40]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                },\r\n                dropShadow: {\r\n                    enabled: true,\r\n                    enabledOnSeries: undefined,\r\n                    top: 5,\r\n                    left: 0,\r\n                    blur: 3,\r\n                    color: strokeColor,\r\n                    opacity: 0.5\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 0\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [strokeColor]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 80,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                },\r\n                marker: {\r\n                    show: false\r\n                }\r\n            },\r\n            colors: ['transparent'],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['info']],\r\n                strokeColor: [strokeColor],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget3 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_3_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var strokeColor = KTApp.getSettings()['colors']['theme']['base']['white'];\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 45, 32, 70, 40, 40, 40]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                },\r\n                dropShadow: {\r\n                    enabled: true,\r\n                    enabledOnSeries: undefined,\r\n                    top: 5,\r\n                    left: 0,\r\n                    blur: 3,\r\n                    color: strokeColor,\r\n                    opacity: 0.3\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 0\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [strokeColor]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 80,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                },\r\n                marker: {\r\n                    show: false\r\n                }\r\n            },\r\n            colors: ['transparent'],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['dark']],\r\n                strokeColor: [strokeColor],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget4 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_4_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [35, 65, 75, 55, 45, 60, 55]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [40, 70, 80, 60, 50, 65, 60]\r\n            }],\r\n            chart: {\r\n                type: 'bar',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                },\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: false,\r\n                    columnWidth: ['30%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                show: true,\r\n                width: 1,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 100,\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                type: ['solid', 'solid'],\r\n                opacity: [0.25, 1]\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                },\r\n                marker: {\r\n                    show: false\r\n                }\r\n            },\r\n            colors: ['#ffffff', '#ffffff'],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                },\r\n                padding: {\r\n                    left: 20,\r\n                    right: 20\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget5 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_5_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [35, 65, 75, 55, 45, 60, 55]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [40, 70, 80, 60, 50, 65, 60]\r\n            }],\r\n            chart: {\r\n                type: 'bar',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                },\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: false,\r\n                    columnWidth: ['30%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                show: true,\r\n                width: 1,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 100,\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                type: ['solid', 'solid'],\r\n                opacity: [0.25, 1]\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                },\r\n                marker: {\r\n                    show: false\r\n                }\r\n            },\r\n            colors: ['#ffffff', '#ffffff'],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                },\r\n                padding: {\r\n                    left: 20,\r\n                    right: 20\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget6 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_6_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [35, 65, 75, 55, 45, 60, 55]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [40, 70, 80, 60, 50, 65, 60]\r\n            }],\r\n            chart: {\r\n                type: 'bar',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                },\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: false,\r\n                    columnWidth: ['30%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                show: true,\r\n                width: 1,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 100,\r\n                labels: {\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                type: ['solid', 'solid'],\r\n                opacity: [0.25, 1]\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                },\r\n                marker: {\r\n                    show: false\r\n                }\r\n            },\r\n            colors: ['#ffffff', '#ffffff'],\r\n            grid: {\r\n                borderColor: KTApp.getSettings()['colors']['gray']['gray-200'],\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                },\r\n                padding: {\r\n                    left: 20,\r\n                    right: 20\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget13 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_13_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 25, 45, 30, 55, 55]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base']['info']]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 60,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light']['info']],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['info']],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base']['info']],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget14 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_14_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [74],\r\n            chart: {\r\n                height: height,\r\n                type: 'radialBar',\r\n            },\r\n            plotOptions: {\r\n                radialBar: {\r\n                    hollow: {\r\n                        margin: 0,\r\n                        size: \"65%\"\r\n                    },\r\n                    dataLabels: {\r\n                        showOn: \"always\",\r\n                        name: {\r\n                            show: false,\r\n                            fontWeight: '700'\r\n                        },\r\n                        value: {\r\n                            color: KTApp.getSettings()['colors']['gray']['gray-700'],\r\n                            fontSize: \"30px\",\r\n                            fontWeight: '700',\r\n                            offsetY: 12,\r\n                            show: true,\r\n                            formatter: function (val) {\r\n                                return val + '%';\r\n                            }\r\n                        }\r\n                    },\r\n                    track: {\r\n                        background: KTApp.getSettings()['colors']['theme']['light']['success'],\r\n                        strokeWidth: '100%'\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['base']['success']],\r\n            stroke: {\r\n                lineCap: \"round\",\r\n            },\r\n            labels: [\"Progress\"]\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget15 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_15_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 30, 60, 25, 25, 40]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'gradient',\r\n                opacity: 1,\r\n                gradient: {\r\n\r\n                    type: \"vertical\",\r\n                    shadeIntensity: 0.5,\r\n                    gradientToColors: undefined,\r\n                    inverseColors: true,\r\n                    opacityFrom: 1,\r\n                    opacityTo: 0.375,\r\n                    stops: [25, 50, 100],\r\n                    colorStops: []\r\n                }\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base']['danger']]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 65,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light']['danger']],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['danger']],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base']['danger']],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget16 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_16_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [60, 50, 75, 80],\r\n            chart: {\r\n                height: height,\r\n                type: 'radialBar',\r\n            },\r\n            plotOptions: {\r\n                radialBar: {\r\n                    hollow: {\r\n                        margin: 0,\r\n                        size: \"30%\"\r\n                    },\r\n                    dataLabels: {\r\n                        showOn: \"always\",\r\n                        name: {\r\n                            show: false,\r\n                            fontWeight: \"700\",\r\n                        },\r\n                        value: {\r\n                            color: KTApp.getSettings()['colors']['gray']['gray-700'],\r\n                            fontSize: \"18px\",\r\n                            fontWeight: \"700\",\r\n                            offsetY: 10,\r\n                            show: true\r\n                        },\r\n                        total: {\r\n                            show: true,\r\n                            label: 'Total',\r\n                            fontWeight: \"bold\",\r\n                            formatter: function (w) {\r\n                                // By default this function returns the average of all series. The below is just an example to show the use of custom formatter function\r\n                                return '60%';\r\n                            }\r\n                        }\r\n                    },\r\n                    track: {\r\n                        background: KTApp.getSettings()['colors']['gray']['gray-100'],\r\n                        strokeWidth: '100%'\r\n                    }\r\n                }\r\n            },\r\n            colors: [\r\n                KTApp.getSettings()['colors']['theme']['base']['info'],\r\n                KTApp.getSettings()['colors']['theme']['base']['danger'],\r\n                KTApp.getSettings()['colors']['theme']['base']['success'],\r\n                KTApp.getSettings()['colors']['theme']['base']['primary']\r\n            ],\r\n            stroke: {\r\n                lineCap: \"round\",\r\n            },\r\n            labels: [\"Progress\"]\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget17 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_17_chart\");\r\n        var color = KTUtil.hasAttr(element, 'data-color') ? KTUtil.attr(element, 'data-color') : 'warning';\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 25, 45, 30, 55, 55]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base'][color]]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 60,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base'][color]],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget18 = function () {\r\n        var element = document.getElementById(\"kt_mixed_widget_18_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [74],\r\n            chart: {\r\n                height: height,\r\n                type: 'radialBar',\r\n                offsetY: 0\r\n            },\r\n            plotOptions: {\r\n                radialBar: {\r\n                    startAngle: -90,\r\n                    endAngle: 90,\r\n\r\n                    hollow: {\r\n                        margin: 0,\r\n                        size: \"70%\"\r\n                    },\r\n                    dataLabels: {\r\n                        showOn: \"always\",\r\n                        name: {\r\n                            show: true,\r\n                            fontSize: \"13px\",\r\n                            fontWeight: \"700\",\r\n                            offsetY: -5,\r\n                            color: KTApp.getSettings()['colors']['gray']['gray-500']\r\n                        },\r\n                        value: {\r\n                            color: KTApp.getSettings()['colors']['gray']['gray-700'],\r\n                            fontSize: \"30px\",\r\n                            fontWeight: \"700\",\r\n                            offsetY: -40,\r\n                            show: true\r\n                        }\r\n                    },\r\n                    track: {\r\n                        background: KTApp.getSettings()['colors']['theme']['light']['primary'],\r\n                        strokeWidth: '100%'\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['base']['primary']],\r\n            stroke: {\r\n                lineCap: \"round\",\r\n            },\r\n            labels: [\"Progress\"]\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    // Tiles\r\n    var _initTilesWidget1 = function () {\r\n        var element = document.getElementById(\"kt_tiles_widget_1_chart\");\r\n        var color = KTUtil.hasAttr(element, 'data-color') ? KTUtil.attr(element, 'data-color') : 'primary';\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [20, 22, 30, 28, 25, 26, 30, 28, 22, 24, 25, 35]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'gradient',\r\n                opacity: 1,\r\n                gradient: {\r\n\r\n                    type: \"vertical\",\r\n                    shadeIntensity: 0.55,\r\n                    gradientToColors: undefined,\r\n                    inverseColors: true,\r\n                    opacityFrom: 1,\r\n                    opacityTo: 0.2,\r\n                    stops: [25, 50, 100],\r\n                    colorStops: []\r\n                }\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base'][color]]\r\n            },\r\n            xaxis: {\r\n                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 37,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base'][color]],\r\n                strokeWidth: 3\r\n            },\r\n            padding: {\r\n                top: 0,\r\n                bottom: 0\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initTilesWidget2 = function () {\r\n        var element = document.getElementById(\"kt_tiles_widget_2_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var strokeColor = KTUtil.changeColor(KTApp.getSettings()['colors']['theme']['base']['danger']);\r\n        var fillColor = KTUtil.changeColor(KTApp.getSettings()['colors']['theme']['base']['danger']);\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [10, 10, 20, 20, 12, 12]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                },\r\n                padding: {\r\n                    top: 0,\r\n                    bottom: 0\r\n                }\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [strokeColor]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 22,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                fixed: {\r\n                    enabled: false\r\n                },\r\n                x: {\r\n                    show: false\r\n                },\r\n                y: {\r\n                    title: {\r\n                        formatter: function (val) {\r\n                            return val + \"\";\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            colors: [fillColor],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['danger']],\r\n                strokeColor: [strokeColor],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initTilesWidget5 = function () {\r\n        var element = document.getElementById(\"kt_tiles_widget_5_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [10, 15, 18, 14]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [8, 13, 16, 12]\r\n            }],\r\n            chart: {\r\n                type: 'bar',\r\n                height: height,\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                },\r\n                padding: {\r\n                    left: 20,\r\n                    right: 20\r\n                }\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: false,\r\n                    columnWidth: ['25%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: ['solid', 'gradient'],\r\n                opacity: 0.25\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May']\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 20\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                fixed: {\r\n                    enabled: false\r\n                },\r\n                x: {\r\n                    show: false\r\n                },\r\n                y: {\r\n                    title: {\r\n                        formatter: function (val) {\r\n                            return val + \"\";\r\n                        }\r\n                    }\r\n                },\r\n                marker: {\r\n                    show: false\r\n                }\r\n            },\r\n            colors: ['#ffffff', '#ffffff']\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initTilesWidget8 = function () {\r\n        var element = document.getElementById(\"kt_tiles_widget_8_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var color = KTUtil.hasAttr(element, 'data-color') ? KTUtil.attr(element, 'data-color') : 'danger';\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [20, 20, 30, 15, 40, 30]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: 150,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid'\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base'][color]]\r\n            },\r\n            xaxis: {\r\n                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 45,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base'][color]],\r\n                strokeWidth: 3\r\n            },\r\n            padding: {\r\n                top: 0,\r\n                bottom: 0\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initTilesWidget17 = function () {\r\n        var element = document.getElementById(\"kt_tiles_widget_17_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [10, 20, 20, 8]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: 150,\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                },\r\n                padding: {\r\n                    top: 0,\r\n                    bottom: 0\r\n                }\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base']['success']]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 22,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                fixed: {\r\n                    enabled: false\r\n                },\r\n                x: {\r\n                    show: false\r\n                },\r\n                y: {\r\n                    title: {\r\n                        formatter: function (val) {\r\n                            return val + \"\";\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light']['success']],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light']['success']],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base']['success']],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initTilesWidget20 = function () {\r\n        var element = document.getElementById(\"kt_tiles_widget_20_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [74],\r\n            chart: {\r\n                height: 250,\r\n                type: 'radialBar',\r\n                offsetY: 0\r\n            },\r\n            plotOptions: {\r\n                radialBar: {\r\n                    startAngle: -90,\r\n                    endAngle: 90,\r\n\r\n                    hollow: {\r\n                        margin: 0,\r\n                        size: \"70%\"\r\n                    },\r\n                    dataLabels: {\r\n                        showOn: \"always\",\r\n                        name: {\r\n                            show: true,\r\n                            fontSize: \"13px\",\r\n                            fontWeight: \"400\",\r\n                            offsetY: -5,\r\n                            color: KTApp.getSettings()['colors']['gray']['gray-300']\r\n                        },\r\n                        value: {\r\n                            color: KTApp.getSettings()['colors']['theme']['inverse']['primary'],\r\n                            fontSize: \"22px\",\r\n                            fontWeight: \"bold\",\r\n                            offsetY: -40,\r\n                            show: true\r\n                        }\r\n                    },\r\n                    track: {\r\n                        background: KTUtil.changeColor(KTApp.getSettings()['colors']['theme']['base']['primary'], -7),\r\n                        strokeWidth: '100%'\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['inverse']['primary']],\r\n            stroke: {\r\n                lineCap: \"round\",\r\n            },\r\n            labels: [\"Progress\"]\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget21 = function () {\r\n        var element = document.getElementById(\"kt_tiles_widget_21_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var color = KTUtil.hasAttr(element, 'data-color') ? KTUtil.attr(element, 'data-color') : 'info';\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [20, 20, 30, 15, 30, 30]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base'][color]]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 32,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base'][color]],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var _initMixedWidget23 = function () {\r\n        var element = document.getElementById(\"kt_tiles_widget_23_chart\");\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var color = KTUtil.hasAttr(element, 'data-color') ? KTUtil.attr(element, 'data-color') : 'primary';\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [15, 25, 15, 40, 20, 50]\r\n            }],\r\n            chart: {\r\n                type: 'area',\r\n                height: 125,\r\n                toolbar: {\r\n                    show: false\r\n                },\r\n                zoom: {\r\n                    enabled: false\r\n                },\r\n                sparkline: {\r\n                    enabled: true\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [KTApp.getSettings()['colors']['theme']['base'][color]]\r\n            },\r\n            xaxis: {\r\n                categories: ['Jan, 2020', 'Feb, 2020', 'Mar, 2020', 'Apr, 2020', 'May, 2020', 'Jun, 2020'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    show: false,\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: KTApp.getSettings()['colors']['gray']['gray-300'],\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                min: 0,\r\n                max: 55,\r\n                labels: {\r\n                    show: false,\r\n                    style: {\r\n                        colors: KTApp.getSettings()['colors']['gray']['gray-500'],\r\n                        fontSize: '12px',\r\n                        fontFamily: KTApp.getSettings()['font-family']\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px',\r\n                    fontFamily: KTApp.getSettings()['font-family']\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n            markers: {\r\n                colors: [KTApp.getSettings()['colors']['theme']['light'][color]],\r\n                strokeColor: [KTApp.getSettings()['colors']['theme']['base'][color]],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    // Forms\r\n    var _initFormsWidget1 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_1_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    var _initFormsWidget2 = function() {\r\n        var formEl = KTUtil.getById(\"kt_forms_widget_2_form\");\r\n        var editorId = 'kt_forms_widget_2_editor';\r\n\r\n        // init editor\r\n        var options = {\r\n            modules: {\r\n                toolbar: {\r\n                    container: \"#kt_forms_widget_2_editor_toolbar\"\r\n                }\r\n            },\r\n            placeholder: 'Type message...',\r\n            theme: 'snow'\r\n        };\r\n\r\n        if (!formEl) {\r\n            return;\r\n        }\r\n\r\n        // Init editor\r\n        var editorObj = new Quill('#' + editorId, options);\r\n    }\r\n\r\n    var _initFormsWidget3 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_3_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    var _initFormsWidget4 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_4_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    var _initFormsWidget5 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_5_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    var _initFormsWidget6 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_6_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    var _initFormsWidget7 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_7_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    var _initFormsWidget8 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_8_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    var _initFormsWidget9 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_9_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    var _initFormsWidget10 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_10_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    var _initFormsWidget11 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_11_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    var _initFormsWidget12 = function() {\r\n        var inputEl = KTUtil.getById(\"kt_forms_widget_12_input\");\r\n\r\n        if (inputEl) {\r\n            autosize(inputEl);\r\n        }\r\n    }\r\n\r\n    // Advance Tables\r\n    var _initAdvancedTableGroupSelection = function(element) {\r\n        var table = KTUtil.getById(element);\r\n\r\n        if (!table) {\r\n            return;\r\n        }\r\n\r\n        KTUtil.on(table, 'thead th .checkbox > input', 'change', function (e) {\r\n            var checkboxes = KTUtil.findAll(table, 'tbody td .checkbox > input');\r\n\r\n            for (var i = 0, len = checkboxes.length; i < len; i++) {\r\n                checkboxes[i].checked = this.checked;\r\n            }\r\n        });\r\n    }\r\n\r\n    var _initPriceSlider = function (element) {\r\n        // init slider\r\n        var slider = document.getElementById(element);\r\n        if (typeof slider === 'undefined') {\r\n            return;\r\n        }\r\n\r\n        if (!slider) {\r\n            return;\r\n        }\r\n\r\n        noUiSlider.create(slider, {\r\n            start: [20, 60],\r\n            connect: true,\r\n            range: {\r\n                'min': 0,\r\n                'max': 100\r\n            }\r\n        });\r\n    }\r\n\r\n    // Education Show More Demo\r\n    var _initEducationShowMoreBtn = function() {\r\n        var el = KTUtil.getById('kt_app_education_more_feeds_btn');\r\n\r\n        if (!el) {\r\n            return;\r\n        }\r\n\r\n        KTUtil.addEvent(el, 'click', function(e) {\r\n            var elements = document.getElementsByClassName('education-more-feeds');\r\n\r\n            if (!elements || elements.length <= 0) {\r\n                return;\r\n            }\r\n\r\n            KTUtil.btnWait(el, 'spinner spinner-right spinner-white pr-15', 'Please wait...', true);\r\n\r\n            setTimeout(function() {\r\n                KTUtil.btnRelease(el);\r\n                KTUtil.addClass(el, 'd-none');\r\n\r\n                var item;\r\n\r\n                for (var i = 0, len = elements.length; i < len; i++) {\r\n                    item = elements[0];\r\n                    KTUtil.removeClass(elements[i], 'd-none');\r\n\r\n                    var textarea = KTUtil.find(item, 'textarea');\r\n                    if (textarea) {\r\n                        autosize(textarea);\r\n                    }\r\n                }\r\n\r\n                KTUtil.scrollTo(item);\r\n            }, 1000);\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            // General Controls\r\n            _initDaterangepicker();\r\n\r\n            // Stats Widgets\r\n            _initStatsWidget7();\r\n            _initStatsWidget8();\r\n            _initStatsWidget9();\r\n            _initStatsWidget10();\r\n            _initStatsWidget11();\r\n            _initStatsWidget12();\r\n\r\n            // Charts Widgets\r\n            _initChartsWidget1();\r\n            _initChartsWidget2();\r\n            _initChartsWidget3();\r\n            _initChartsWidget4();\r\n            _initChartsWidget5();\r\n            _initChartsWidget6();\r\n            _initChartsWidget7();\r\n            _initChartsWidget8();\r\n            _initChartsWidget9();\r\n\r\n            // Mixed Widgets\r\n            _initMixedWidget1();\r\n            _initMixedWidget2();\r\n            _initMixedWidget3();\r\n            _initMixedWidget4();\r\n            _initMixedWidget5();\r\n            _initMixedWidget6();\r\n            _initMixedWidget13();\r\n            _initMixedWidget14();\r\n            _initMixedWidget15();\r\n            _initMixedWidget16();\r\n            _initMixedWidget17();\r\n            _initMixedWidget18();\r\n\r\n            // Tiles Widgets\r\n            _initTilesWidget1();\r\n            _initTilesWidget2();\r\n            _initTilesWidget5();\r\n            _initTilesWidget8();\r\n            _initTilesWidget17();\r\n            _initTilesWidget20();\r\n            _initMixedWidget21();\r\n            _initMixedWidget23();\r\n\r\n            // Table Widgets\r\n            _initAdvancedTableGroupSelection('kt_advance_table_widget_1');\r\n            _initAdvancedTableGroupSelection('kt_advance_table_widget_2');\r\n            _initAdvancedTableGroupSelection('kt_advance_table_widget_3');\r\n            _initAdvancedTableGroupSelection('kt_advance_table_widget_4');\r\n\r\n            // Form Widgets\r\n            _initPriceSlider('kt_price_slider');\r\n\r\n            // Forms widgets\r\n            _initFormsWidget1();\r\n            _initFormsWidget2();\r\n            _initFormsWidget3();\r\n            _initFormsWidget4();\r\n            _initFormsWidget5();\r\n            _initFormsWidget6();\r\n            _initFormsWidget7();\r\n            _initFormsWidget8();\r\n            _initFormsWidget9();\r\n            _initFormsWidget10();\r\n            _initFormsWidget11();\r\n\r\n            // Education App\r\n            _initEducationShowMoreBtn();\r\n        }\r\n    }\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined') {\r\n    module.exports = KTWidgets;\r\n}\r\n\r\njQuery(document).ready(function () {\r\n    KTWidgets.init();\r\n});\r\n"], "sourceRoot": ""}