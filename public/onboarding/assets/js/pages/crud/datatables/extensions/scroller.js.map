{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///../demo3/src/js/pages/crud/datatables/extensions/scroller.js"], "names": [], "mappings": ";QAAA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;;AClFa;AACb;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,iCAAiC;AACtC,KAAK,gBAAgB;AACrB,KAAK,iBAAiB;AACtB,KAAK,oBAAoB;AACzB,KAAK,qBAAqB;AAC1B,KAAK,oBAAoB;AACzB,KAAK,iBAAiB;AACtB,KAAK,eAAe;AACpB,KAAK,aAAa;AAClB,KAAK,wCAAwC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,MAAM;AACN,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,WAAW,mDAAmD;AAC9D,WAAW,qDAAqD;AAChE,WAAW,qDAAqD;AAChE,WAAW,oDAAoD;AAC/D,WAAW,8CAA8C;AACzD,WAAW,kDAAkD;AAC7D,WAAW,oDAAoD;AAC/D;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,WAAW,qCAAqC;AAChD,WAAW,sCAAsC;AACjD,WAAW,sCAAsC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,KAAK;AACL;AACA,GAAG;AACH;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA,CAAC", "file": "js/pages/crud/datatables/extensions/scroller.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"../demo3/src/js/pages/crud/datatables/extensions/scroller.js\");\n", "\"use strict\";\r\nvar KTDatatablesExtensionsScroller = function() {\r\n\r\n\tvar initTable1 = function() {\r\n\t\tvar table = $('#kt_datatable');\r\n\r\n\t\t// begin first table\r\n\t\ttable.DataTable({\r\n\t\t\tresponsive: true,\r\n\t\t\tajax: HOST_URL + '/api/datatables/demos/server.php',\r\n\t\t\tdeferRender: true,\r\n\t\t\tscrollY: '500px',\r\n\t\t\tscrollCollapse: true,\r\n\t\t\tscroller: true,\r\n\t\t\tcolumns: [\r\n\t\t\t\t{data: 'RecordID', visible: false},\r\n\t\t\t\t{data: 'OrderID'},\r\n\t\t\t\t{data: 'ShipCity'},\r\n\t\t\t\t{data: 'ShipAddress'},\r\n\t\t\t\t{data: 'CompanyAgent'},\r\n\t\t\t\t{data: 'CompanyName'},\r\n\t\t\t\t{data: 'ShipDate'},\r\n\t\t\t\t{data: 'Status'},\r\n\t\t\t\t{data: 'Type'},\r\n\t\t\t\t{data: 'Actions', responsivePriority: -1},\r\n\t\t\t],\r\n\t\t\tcolumnDefs: [\r\n\t\t\t\t{\r\n\t\t\t\t\ttargets: -1,\r\n\t\t\t\t\ttitle: 'Actions',\r\n\t\t\t\t\torderable: false,\r\n\t\t\t\t\trender: function(data, type, full, meta) {\r\n\t\t\t\t\t\treturn '\\\r\n\t\t\t\t\t\t\t<div class=\"dropdown dropdown-inline\">\\\r\n\t\t\t\t\t\t\t\t<a href=\"javascript:;\" class=\"btn btn-sm btn-clean btn-icon\" data-toggle=\"dropdown\">\\\r\n\t                                <i class=\"la la-cog\"></i>\\\r\n\t                            </a>\\\r\n\t\t\t\t\t\t\t  \t<div class=\"dropdown-menu dropdown-menu-sm dropdown-menu-right\">\\\r\n\t\t\t\t\t\t\t\t\t<ul class=\"nav nav-hoverable flex-column\">\\\r\n\t\t\t\t\t\t\t    \t\t<li class=\"nav-item\"><a class=\"nav-link\" href=\"#\"><i class=\"nav-icon la la-edit\"></i><span class=\"nav-text\">Edit Details</span></a></li>\\\r\n\t\t\t\t\t\t\t    \t\t<li class=\"nav-item\"><a class=\"nav-link\" href=\"#\"><i class=\"nav-icon la la-leaf\"></i><span class=\"nav-text\">Update Status</span></a></li>\\\r\n\t\t\t\t\t\t\t    \t\t<li class=\"nav-item\"><a class=\"nav-link\" href=\"#\"><i class=\"nav-icon la la-print\"></i><span class=\"nav-text\">Print</span></a></li>\\\r\n\t\t\t\t\t\t\t\t\t</ul>\\\r\n\t\t\t\t\t\t\t  \t</div>\\\r\n\t\t\t\t\t\t\t</div>\\\r\n\t\t\t\t\t\t\t<a href=\"javascript:;\" class=\"btn btn-sm btn-clean btn-icon\" title=\"Edit details\">\\\r\n\t\t\t\t\t\t\t\t<i class=\"la la-edit\"></i>\\\r\n\t\t\t\t\t\t\t</a>\\\r\n\t\t\t\t\t\t\t<a href=\"javascript:;\" class=\"btn btn-sm btn-clean btn-icon\" title=\"Delete\">\\\r\n\t\t\t\t\t\t\t\t<i class=\"la la-trash\"></i>\\\r\n\t\t\t\t\t\t\t</a>\\\r\n\t\t\t\t\t\t';\r\n\t\t\t\t\t},\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\twidth: '75px',\r\n\t\t\t\t\ttargets: -3,\r\n\t\t\t\t\trender: function(data, type, full, meta) {\r\n\t\t\t\t\t\tvar status = {\r\n\t\t\t\t\t\t\t1: {'title': 'Pending', 'class': 'label-light-primary'},\r\n\t\t\t\t\t\t\t2: {'title': 'Delivered', 'class': ' label-light-danger'},\r\n\t\t\t\t\t\t\t3: {'title': 'Canceled', 'class': ' label-light-primary'},\r\n\t\t\t\t\t\t\t4: {'title': 'Success', 'class': ' label-light-success'},\r\n\t\t\t\t\t\t\t5: {'title': 'Info', 'class': ' label-light-info'},\r\n\t\t\t\t\t\t\t6: {'title': 'Danger', 'class': ' label-light-danger'},\r\n\t\t\t\t\t\t\t7: {'title': 'Warning', 'class': ' label-light-warning'},\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tif (typeof status[data] === 'undefined') {\r\n\t\t\t\t\t\t\treturn data;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn '<span class=\"label label-lg font-weight-bold' + status[data].class + ' label-inline\">' + status[data].title + '</span>';\r\n\t\t\t\t\t},\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\twidth: '75px',\r\n\t\t\t\t\ttargets: -2,\r\n\t\t\t\t\trender: function(data, type, full, meta) {\r\n\t\t\t\t\t\tvar status = {\r\n\t\t\t\t\t\t\t1: {'title': 'Online', 'state': 'danger'},\r\n\t\t\t\t\t\t\t2: {'title': 'Retail', 'state': 'primary'},\r\n\t\t\t\t\t\t\t3: {'title': 'Direct', 'state': 'success'},\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tif (typeof status[data] === 'undefined') {\r\n\t\t\t\t\t\t\treturn data;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn '<span class=\"label label-' + status[data].state + ' label-dot mr-2\"></span>' +\r\n\t\t\t\t\t\t\t'<span class=\"font-weight-bold text-' + status[data].state + '\">' + status[data].title + '</span>';\r\n\t\t\t\t\t},\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t});\r\n\t};\r\n\r\n\r\n\treturn {\r\n\r\n\t\t//main function to initiate the module\r\n\t\tinit: function() {\r\n\t\t\tinitTable1();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\njQuery(document).ready(function() {\r\n\tKTDatatablesExtensionsScroller.init();\r\n});\r\n"], "sourceRoot": ""}