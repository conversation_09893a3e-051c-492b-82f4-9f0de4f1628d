{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///../demo3/src/js/pages/crud/forms/widgets/tagify.js"], "names": [], "mappings": ";QAAA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;AClFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;;AAGb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,yBAAyB,gDAAgD;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,yBAAyB,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA,yBAAyB,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;AACT;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA,CAAC", "file": "js/pages/crud/forms/widgets/tagify.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"../demo3/src/js/pages/crud/forms/widgets/tagify.js\");\n", "// Class definition\r\nvar KTTagifyDemos = function() {\r\n    // Private functions\r\n    var demo1 = function() {\r\n        var input = document.getElementById('kt_tagify_1'),\r\n            // init Tagify script on the above inputs\r\n            tagify = new Tagify(input, {\r\n                whitelist: [\"A# .NET\", \"A# (Axiom)\", \"A-0 System\", \"A+\", \"A++\", \"ABAP\", \"ABC\", \"ABC ALGOL\", \"ABSET\", \"ABSYS\", \"ACC\", \"Accent\", \"Ace DASL\", \"ACL2\", \"Avicsoft\", \"ACT-III\", \"Action!\", \"ActionScript\", \"Ada\", \"Adenine\", \"Agda\", \"Agilent VEE\", \"Agora\", \"AIMMS\", \"Alef\", \"ALF\", \"ALGOL 58\", \"ALGOL 60\", \"ALGOL 68\", \"ALGOL W\", \"Alice\", \"Alma-0\", \"AmbientTalk\", \"Amiga E\", \"AMOS\", \"AMPL\", \"Apex (Salesforce.com)\", \"APL\", \"AppleScript\", \"Arc\", \"ARexx\", \"Argus\", \"AspectJ\", \"Assembly language\", \"ATS\", \"Ateji PX\", \"AutoHotkey\", \"Autocoder\", \"AutoIt\", \"AutoLISP / Visual LISP\", \"Averest\", \"AWK\", \"Axum\", \"Active Server Pages\", \"ASP.NET\", \"B\", \"Babbage\", \"Bash\", \"BASIC\", \"bc\", \"BCPL\", \"BeanShell\", \"Batch (Windows/Dos)\", \"Bertrand\", \"BETA\", \"Bigwig\", \"Bistro\", \"BitC\", \"BLISS\", \"Blockly\", \"BlooP\", \"Blue\", \"Boo\", \"Boomerang\", \"Bourne shell (including bash and ksh)\", \"BREW\", \"BPEL\", \"B\", \"C--\", \"C++ – ISO/IEC 14882\", \"C# – ISO/IEC 23270\", \"C/AL\", \"Caché ObjectScript\", \"C Shell\", \"Caml\", \"Cayenne\", \"CDuce\", \"Cecil\", \"Cesil\", \"Céu\", \"Ceylon\", \"CFEngine\", \"CFML\", \"Cg\", \"Ch\", \"Chapel\", \"Charity\", \"Charm\", \"Chef\", \"CHILL\", \"CHIP-8\", \"chomski\", \"ChucK\", \"CICS\", \"Cilk\", \"Citrine (programming language)\", \"CL (IBM)\", \"Claire\", \"Clarion\", \"Clean\", \"Clipper\", \"CLIPS\", \"CLIST\", \"Clojure\", \"CLU\", \"CMS-2\", \"COBOL – ISO/IEC 1989\", \"CobolScript – COBOL Scripting language\", \"Cobra\", \"CODE\", \"CoffeeScript\", \"ColdFusion\", \"COMAL\", \"Combined Programming Language (CPL)\", \"COMIT\", \"Common Intermediate Language (CIL)\", \"Common Lisp (also known as CL)\", \"COMPASS\", \"Component Pascal\", \"Constraint Handling Rules (CHR)\", \"COMTRAN\", \"Converge\", \"Cool\", \"Coq\", \"Coral 66\", \"Corn\", \"CorVision\", \"COWSEL\", \"CPL\", \"CPL\", \"Cryptol\", \"csh\", \"Csound\", \"CSP\", \"CUDA\", \"Curl\", \"Curry\", \"Cybil\", \"Cyclone\", \"Cython\", \"Java\", \"Javascript\", \"M2001\", \"M4\", \"M#\", \"Machine code\", \"MAD (Michigan Algorithm Decoder)\", \"MAD/I\", \"Magik\", \"Magma\", \"make\", \"Maple\", \"MAPPER now part of BIS\", \"MARK-IV now VISION:BUILDER\", \"Mary\", \"MASM Microsoft Assembly x86\", \"MATH-MATIC\", \"Mathematica\", \"MATLAB\", \"Maxima (see also Macsyma)\", \"Max (Max Msp – Graphical Programming Environment)\", \"Maya (MEL)\", \"MDL\", \"Mercury\", \"Mesa\", \"Metafont\", \"Microcode\", \"MicroScript\", \"MIIS\", \"Milk (programming language)\", \"MIMIC\", \"Mirah\", \"Miranda\", \"MIVA Script\", \"ML\", \"Model 204\", \"Modelica\", \"Modula\", \"Modula-2\", \"Modula-3\", \"Mohol\", \"MOO\", \"Mortran\", \"Mouse\", \"MPD\", \"Mathcad\", \"MSIL – deprecated name for CIL\", \"MSL\", \"MUMPS\", \"Mystic Programming L\"],\r\n                blacklist: [\".NET\", \"PHP\"], // <-- passed as an attribute in this demo\r\n            })\r\n\r\n\r\n        // \"remove all tags\" button event listener\r\n        document.getElementById('kt_tagify_1_remove').addEventListener('click', tagify.removeAllTags.bind(tagify));\r\n\r\n        // Chainable event listeners\r\n        tagify.on('add', onAddTag)\r\n            .on('remove', onRemoveTag)\r\n            .on('input', onInput)\r\n            .on('edit', onTagEdit)\r\n            .on('invalid', onInvalidTag)\r\n            .on('click', onTagClick)\r\n            .on('dropdown:show', onDropdownShow)\r\n            .on('dropdown:hide', onDropdownHide)\r\n\r\n        // tag added callback\r\n        function onAddTag(e) {\r\n            console.log(\"onAddTag: \", e.detail);\r\n            console.log(\"original input value: \", input.value)\r\n            tagify.off('add', onAddTag) // exmaple of removing a custom Tagify event\r\n        }\r\n\r\n        // tag remvoed callback\r\n        function onRemoveTag(e) {\r\n            console.log(e.detail);\r\n            console.log(\"tagify instance value:\", tagify.value)\r\n        }\r\n\r\n        // on character(s) added/removed (user is typing/deleting)\r\n        function onInput(e) {\r\n            console.log(e.detail);\r\n            console.log(\"onInput: \", e.detail);\r\n        }\r\n\r\n        function onTagEdit(e) {\r\n            console.log(\"onTagEdit: \", e.detail);\r\n        }\r\n\r\n        // invalid tag added callback\r\n        function onInvalidTag(e) {\r\n            console.log(\"onInvalidTag: \", e.detail);\r\n        }\r\n\r\n        // invalid tag added callback\r\n        function onTagClick(e) {\r\n            console.log(e.detail);\r\n            console.log(\"onTagClick: \", e.detail);\r\n        }\r\n\r\n        function onDropdownShow(e) {\r\n            console.log(\"onDropdownShow: \", e.detail)\r\n        }\r\n\r\n        function onDropdownHide(e) {\r\n            console.log(\"onDropdownHide: \", e.detail)\r\n        }\r\n    }\r\n\r\n    var demo1Readonly = function() {\r\n        // Readonly Mode\r\n        var input = document.getElementById('kt_tagify_1_1'),\r\n        tagify = new Tagify(input);\r\n\r\n        tagify.addTags([{value:\"laravel\", color:\"yellow\", readonly: true}]);\r\n    }\r\n\r\n    var demo2 = function() {\r\n        var input = document.getElementById('kt_tagify_2');\r\n        var tagify = new Tagify(input, {\r\n            enforceWhitelist: true,\r\n            whitelist: [\"The Shawshank Redemption\", \"The Godfather\", \"The Godfather: Part II\", \"The Dark Knight\", \"12 Angry Men\", \"Schindler's List\", \"Pulp Fiction\", \"The Lord of the Rings: The Return of the King\", \"The Good, the Bad and the Ugly\", \"Fight Club\", \"The Lord of the Rings: The Fellowship of the Ring\", \"Star Wars: Episode V - The Empire Strikes Back\", \"Forrest Gump\", \"Inception\", \"The Lord of the Rings: The Two Towers\", \"One Flew Over the Cuckoo's Nest\", \"Goodfellas\", \"The Matrix\", \"Seven Samurai\", \"Star Wars: Episode IV - A New Hope\", \"City of God\", \"Se7en\", \"The Silence of the Lambs\", \"It's a Wonderful Life\", \"The Usual Suspects\", \"Life Is Beautiful\", \"Léon: The Professional\", \"Spirited Away\", \"Saving Private Ryan\", \"La La Land\", \"Once Upon a Time in the West\", \"American History X\", \"Interstellar\", \"Casablanca\", \"Psycho\", \"City Lights\", \"The Green Mile\", \"Raiders of the Lost Ark\", \"The Intouchables\", \"Modern Times\", \"Rear Window\", \"The Pianist\", \"The Departed\", \"Terminator 2: Judgment Day\", \"Back to the Future\", \"Whiplash\", \"Gladiator\", \"Memento\", \"Apocalypse Now\", \"The Prestige\", \"The Lion King\", \"Alien\", \"Dr. Strangelove or: How I Learned to Stop Worrying and Love the Bomb\", \"Sunset Boulevard\", \"The Great Dictator\", \"Cinema Paradiso\", \"The Lives of Others\", \"Paths of Glory\", \"Grave of the Fireflies\", \"Django Unchained\", \"The Shining\", \"WALL·E\", \"American Beauty\", \"The Dark Knight Rises\", \"Princess Mononoke\", \"Aliens\", \"Oldboy\", \"Once Upon a Time in America\", \"Citizen Kane\", \"Das Boot\", \"Witness for the Prosecution\", \"North by Northwest\", \"Vertigo\", \"Star Wars: Episode VI - Return of the Jedi\", \"Reservoir Dogs\", \"M\", \"Braveheart\", \"Amélie\", \"Requiem for a Dream\", \"A Clockwork Orange\", \"Taxi Driver\", \"Lawrence of Arabia\", \"Like Stars on Earth\", \"Double Indemnity\", \"To Kill a Mockingbird\", \"Eternal Sunshine of the Spotless Mind\", \"Toy Story 3\", \"Amadeus\", \"My Father and My Son\", \"Full Metal Jacket\", \"The Sting\", \"2001: A Space Odyssey\", \"Singin' in the Rain\", \"Bicycle Thieves\", \"Toy Story\", \"Dangal\", \"The Kid\", \"Inglourious Basterds\", \"Snatch\", \"Monty Python and the Holy Grail\", \"Hacksaw Ridge\", \"3 Idiots\", \"L.A. Confidential\", \"For a Few Dollars More\", \"Scarface\", \"Rashomon\", \"The Apartment\", \"The Hunt\", \"Good Will Hunting\", \"Indiana Jones and the Last Crusade\", \"A Separation\", \"Metropolis\", \"Yojimbo\", \"All About Eve\", \"Batman Begins\", \"Up\", \"Some Like It Hot\", \"The Treasure of the Sierra Madre\", \"Unforgiven\", \"Downfall\", \"Raging Bull\", \"The Third Man\", \"Die Hard\", \"Children of Heaven\", \"The Great Escape\", \"Heat\", \"Chinatown\", \"Inside Out\", \"Pan's Labyrinth\", \"Ikiru\", \"My Neighbor Totoro\", \"On the Waterfront\", \"Room\", \"Ran\", \"The Gold Rush\", \"The Secret in Their Eyes\", \"The Bridge on the River Kwai\", \"Blade Runner\", \"Mr. Smith Goes to Washington\", \"The Seventh Seal\", \"Howl's Moving Castle\", \"Lock, Stock and Two Smoking Barrels\", \"Judgment at Nuremberg\", \"Casino\", \"The Bandit\", \"Incendies\", \"A Beautiful Mind\", \"A Wednesday\", \"The General\", \"The Elephant Man\", \"Wild Strawberries\", \"Arrival\", \"V for Vendetta\", \"Warrior\", \"The Wolf of Wall Street\", \"Manchester by the Sea\", \"Sunrise\", \"The Passion of Joan of Arc\", \"Gran Torino\", \"Rang De Basanti\", \"Trainspotting\", \"Dial M for Murder\", \"The Big Lebowski\", \"The Deer Hunter\", \"Tokyo Story\", \"Gone with the Wind\", \"Fargo\", \"Finding Nemo\", \"The Sixth Sense\", \"The Thing\", \"Hera Pheri\", \"Cool Hand Luke\", \"Andaz Apna Apna\", \"Rebecca\", \"No Country for Old Men\", \"How to Train Your Dragon\", \"Munna Bhai M.B.B.S.\", \"Sholay\", \"Kill Bill: Vol. 1\", \"Into the Wild\", \"Mary and Max\", \"Gone Girl\", \"There Will Be Blood\", \"Come and See\", \"It Happened One Night\", \"Life of Brian\", \"Rush\", \"Hotel Rwanda\", \"Platoon\", \"Shutter Island\", \"Network\", \"The Wages of Fear\", \"Stand by Me\", \"Wild Tales\", \"In the Name of the Father\", \"Spotlight\", \"Star Wars: The Force Awakens\", \"The Nights of Cabiria\", \"The 400 Blows\", \"Butch Cassidy and the Sundance Kid\", \"Mad Max: Fury Road\", \"The Maltese Falcon\", \"12 Years a Slave\", \"Ben-Hur\", \"The Grand Budapest Hotel\", \"Persona\", \"Million Dollar Baby\", \"Amores Perros\", \"Jurassic Park\", \"The Princess Bride\", \"Hachi: A Dog's Tale\", \"Memories of Murder\", \"Stalker\", \"Nausicaä of the Valley of the Wind\", \"Drishyam\", \"The Truman Show\", \"The Grapes of Wrath\", \"Before Sunrise\", \"Touch of Evil\", \"Annie Hall\", \"The Message\", \"Rocky\", \"Gandhi\", \"Harry Potter and the Deathly Hallows: Part 2\", \"The Bourne Ultimatum\", \"Diabolique\", \"Donnie Darko\", \"Monsters, Inc.\", \"Prisoners\", \"8½\", \"The Terminator\", \"The Wizard of Oz\", \"Catch Me If You Can\", \"Groundhog Day\", \"Twelve Monkeys\", \"Zootopia\", \"La Haine\", \"Barry Lyndon\", \"Jaws\", \"The Best Years of Our Lives\", \"Infernal Affairs\", \"Udaan\", \"The Battle of Algiers\", \"Strangers on a Train\", \"Dog Day Afternoon\", \"Sin City\", \"Kind Hearts and Coronets\", \"Gangs of Wasseypur\", \"The Help\"],\r\n            callbacks: {\r\n                add: console.log, // callback when adding a tag\r\n                remove: console.log // callback when removing a tag\r\n            }\r\n        });\r\n    }\r\n\r\n    var demo3 = function() {\r\n        var input = document.getElementById('kt_tagify_3');\r\n\r\n        // init Tagify script on the above inputs\r\n        var tagify = new Tagify(input);\r\n\r\n        // add a class to Tagify's input element\r\n        //tagify.DOM.input.classList.remove('tagify__input');\r\n        tagify.DOM.input.classList.add('form-control');\r\n        tagify.DOM.input.setAttribute('placeholder', 'enter tag...');\r\n\r\n        // re-place Tagify's input element outside of the  element (tagify.DOM.scope), just before it\r\n        tagify.DOM.scope.parentNode.insertBefore(tagify.DOM.input, tagify.DOM.scope);\r\n    }\r\n\r\n    var demo4 = function() {\r\n        var input = document.getElementById('kt_tagify_4');\r\n        var tagify = new Tagify(input, {\r\n            pattern: /^.{0,20}$/, // Validate typed tag(s) by Regex. Here maximum chars length is defined as \"20\"\r\n            delimiters: \", \", // add new tags when a comma or a space character is entered\r\n            maxTags: 6,\r\n            blacklist: [\"fuck\", \"shit\", \"pussy\"],\r\n            keepInvalidTags: true, // do not remove invalid tags (but keep them marked as invalid)\r\n            whitelist: [\"temple\", \"stun\", \"detective\", \"sign\", \"passion\", \"routine\", \"deck\", \"discriminate\", \"relaxation\", \"fraud\", \"attractive\", \"soft\", \"forecast\", \"point\", \"thank\", \"stage\", \"eliminate\", \"effective\", \"flood\", \"passive\", \"skilled\", \"separation\", \"contact\", \"compromise\", \"reality\", \"district\", \"nationalist\", \"leg\", \"porter\", \"conviction\", \"worker\", \"vegetable\", \"commerce\", \"conception\", \"particle\", \"honor\", \"stick\", \"tail\", \"pumpkin\", \"core\", \"mouse\", \"egg\", \"population\", \"unique\", \"behavior\", \"onion\", \"disaster\", \"cute\", \"pipe\", \"sock\", \"dialect\", \"horse\", \"swear\", \"owner\", \"cope\", \"global\", \"improvement\", \"artist\", \"shed\", \"constant\", \"bond\", \"brink\", \"shower\", \"spot\", \"inject\", \"bowel\", \"homosexual\", \"trust\", \"exclude\", \"tough\", \"sickness\", \"prevalence\", \"sister\", \"resolution\", \"cattle\", \"cultural\", \"innocent\", \"burial\", \"bundle\", \"thaw\", \"respectable\", \"thirsty\", \"exposure\", \"team\", \"creed\", \"facade\", \"calendar\", \"filter\", \"utter\", \"dominate\", \"predator\", \"discover\", \"theorist\", \"hospitality\", \"damage\", \"woman\", \"rub\", \"crop\", \"unpleasant\", \"halt\", \"inch\", \"birthday\", \"lack\", \"throne\", \"maximum\", \"pause\", \"digress\", \"fossil\", \"policy\", \"instrument\", \"trunk\", \"frame\", \"measure\", \"hall\", \"support\", \"convenience\", \"house\", \"partnership\", \"inspector\", \"looting\", \"ranch\", \"asset\", \"rally\", \"explicit\", \"leak\", \"monarch\", \"ethics\", \"applied\", \"aviation\", \"dentist\", \"great\", \"ethnic\", \"sodium\", \"truth\", \"constellation\", \"lease\", \"guide\", \"break\", \"conclusion\", \"button\", \"recording\", \"horizon\", \"council\", \"paradox\", \"bride\", \"weigh\", \"like\", \"noble\", \"transition\", \"accumulation\", \"arrow\", \"stitch\", \"academy\", \"glimpse\", \"case\", \"researcher\", \"constitutional\", \"notion\", \"bathroom\", \"revolutionary\", \"soldier\", \"vehicle\", \"betray\", \"gear\", \"pan\", \"quarter\", \"embarrassment\", \"golf\", \"shark\", \"constitution\", \"club\", \"college\", \"duty\", \"eaux\", \"know\", \"collection\", \"burst\", \"fun\", \"animal\", \"expectation\", \"persist\", \"insure\", \"tick\", \"account\", \"initiative\", \"tourist\", \"member\", \"example\", \"plant\", \"river\", \"ratio\", \"view\", \"coast\", \"latest\", \"invite\", \"help\", \"falsify\", \"allocation\", \"degree\", \"feel\", \"resort\", \"means\", \"excuse\", \"injury\", \"pupil\", \"shaft\", \"allow\", \"ton\", \"tube\", \"dress\", \"speaker\", \"double\", \"theater\", \"opposed\", \"holiday\", \"screw\", \"cutting\", \"picture\", \"laborer\", \"conservation\", \"kneel\", \"miracle\", \"primary\", \"nomination\", \"characteristic\", \"referral\", \"carbon\", \"valley\", \"hot\", \"climb\", \"wrestle\", \"motorist\", \"update\", \"loot\", \"mosquito\", \"delivery\", \"eagle\", \"guideline\", \"hurt\", \"feedback\", \"finish\", \"traffic\", \"competence\", \"serve\", \"archive\", \"feeling\", \"hope\", \"seal\", \"ear\", \"oven\", \"vote\", \"ballot\", \"study\", \"negative\", \"declaration\", \"particular\", \"pattern\", \"suburb\", \"intervention\", \"brake\", \"frequency\", \"drink\", \"affair\", \"contemporary\", \"prince\", \"dry\", \"mole\", \"lazy\", \"undermine\", \"radio\", \"legislation\", \"circumstance\", \"bear\", \"left\", \"pony\", \"industry\", \"mastermind\", \"criticism\", \"sheep\", \"failure\", \"chain\", \"depressed\", \"launch\", \"script\", \"green\", \"weave\", \"please\", \"surprise\", \"doctor\", \"revive\", \"banquet\", \"belong\", \"correction\", \"door\", \"image\", \"integrity\", \"intermediate\", \"sense\", \"formal\", \"cane\", \"gloom\", \"toast\", \"pension\", \"exception\", \"prey\", \"random\", \"nose\", \"predict\", \"needle\", \"satisfaction\", \"establish\", \"fit\", \"vigorous\", \"urgency\", \"X-ray\", \"equinox\", \"variety\", \"proclaim\", \"conceive\", \"bulb\", \"vegetarian\", \"available\", \"stake\", \"publicity\", \"strikebreaker\", \"portrait\", \"sink\", \"frog\", \"ruin\", \"studio\", \"match\", \"electron\", \"captain\", \"channel\", \"navy\", \"set\", \"recommend\", \"appoint\", \"liberal\", \"missile\", \"sample\", \"result\", \"poor\", \"efflux\", \"glance\", \"timetable\", \"advertise\", \"personality\", \"aunt\", \"dog\"],\r\n            transformTag: transformTag,\r\n            dropdown: {\r\n                enabled: 3,\r\n            }\r\n        });\r\n\r\n        function transformTag(tagData) {\r\n            var states = [\r\n                'success',\r\n                'primary',\r\n                'danger',\r\n                'success',\r\n                'warning',\r\n                'dark',\r\n                'primary',\r\n                'info'];\r\n\r\n            tagData.class = 'tagify__tag tagify__tag--' + states[KTUtil.getRandomInt(0, 7)];\r\n\r\n            if (tagData.value.toLowerCase() == 'shit') {\r\n                tagData.value = 's✲✲t'\r\n            }\r\n        }\r\n\r\n        tagify.on('add', function(e) {\r\n            console.log(e.detail)\r\n        });\r\n\r\n        tagify.on('invalid', function(e) {\r\n            console.log(e, e.detail);\r\n        });\r\n    }\r\n\r\n    var demo5 = function() {\r\n        // Init autocompletes\r\n        var toEl = document.getElementById('kt_tagify_5');\r\n        var tagifyTo = new Tagify(toEl, {\r\n            delimiters: \", \", // add new tags when a comma or a space character is entered\r\n            maxTags: 10,\r\n            blacklist: [\"fuck\", \"shit\", \"pussy\"],\r\n            keepInvalidTags: true, // do not remove invalid tags (but keep them marked as invalid)\r\n            whitelist: [\r\n                {\r\n                value : 'Chris Muller',\r\n                email : '<EMAIL>',\r\n                initials: '',\r\n                initialsState: '',\r\n                pic: './assets/media/users/100_11.jpg',\r\n                class : 'tagify__tag--primary'\r\n            }, {\r\n                value : 'Nick Bold',\r\n                email : '<EMAIL>',\r\n                initials: 'SS',\r\n                initialsState: 'warning',\r\n                pic: ''\r\n            }, {\r\n                value : 'Alon Silko',\r\n                email : '<EMAIL>',\r\n                initials: '',\r\n                initialsState: '',\r\n                pic: './assets/media/users/100_6.jpg'\r\n            }, {\r\n                value : 'Sam Seanic',\r\n                email : '<EMAIL>',\r\n                initials: '',\r\n                initialsState: '',\r\n                pic: './assets/media/users/100_8.jpg'\r\n            }, {\r\n                value : 'Sara Loran',\r\n                email : '<EMAIL>',\r\n                initials: '',\r\n                initialsState: '',\r\n                pic: './assets/media/users/100_9.jpg'\r\n            }, {\r\n                value : 'Eric Davok',\r\n                email : '<EMAIL>',\r\n                initials: '',\r\n                initialsState: '',\r\n                pic: './assets/media/users/100_13.jpg'\r\n            }, {\r\n                value : 'Sam Seanic',\r\n                email : '<EMAIL>',\r\n                initials: '',\r\n                initialsState: '',\r\n                pic: './assets/media/users/100_13.jpg'\r\n            }, {\r\n                value : 'Lina Nilson',\r\n                email : '<EMAIL>',\r\n                initials: 'LN',\r\n                initialsState: 'danger',\r\n                pic: './assets/media/users/100_15.jpg'\r\n            }],\r\n            templates: {\r\n                dropdownItem : function(tagData){\r\n                    try {\r\n                        var html = '';\r\n\r\n                        html += '<div class=\"tagify__dropdown__item\">';\r\n                        html += '   <div class=\"d-flex align-items-center\">';\r\n                        html += '       <span class=\"symbol sumbol-' + (tagData.initialsState ? tagData.initialsState : '') + ' mr-2\">';\r\n                        html += '           <span class=\"symbol-label\" style=\"background-image: url(\\''+ (tagData.pic ? tagData.pic : '') + '\\')\">' + (tagData.initials ? tagData.initials : '') + '</span>';\r\n                        html += '       </span>';\r\n                        html += '       <div class=\"d-flex flex-column\">';\r\n                        html += '           <a href=\"#\" class=\"text-dark-75 text-hover-primary font-weight-bold\">'+ (tagData.value ? tagData.value : '') + '</a>';\r\n                        html += '           <span class=\"text-muted font-weight-bold\">' + (tagData.email ? tagData.email : '') + '</span>';\r\n                        html += '       </div>';\r\n                        html += '   </div>';\r\n                        html += '</div>';\r\n\r\n                        return html;\r\n                    } catch (err) {}\r\n                }\r\n            },\r\n            transformTag: function(tagData) {\r\n                tagData.class = 'tagify__tag tagify__tag--primary';\r\n            },\r\n            dropdown : {\r\n                classname : \"color-blue\",\r\n                enabled   : 1,\r\n                maxItems  : 5\r\n            }\r\n        });\r\n    }\r\n\r\n    var demo6 = function() {\r\n        var input = document.getElementById('kt_tagify_6');\r\n        var tagify = new Tagify(input, {\r\n            pattern: /^.{0,20}$/, // Validate typed tag(s) by Regex. Here maximum chars length is defined as \"20\"\r\n            delimiters: \", \", // add new tags when a comma or a space character is entered\r\n            maxTags: 6,\r\n            blacklist: [\"fuck\", \"shit\", \"pussy\"],\r\n            keepInvalidTags: true, // do not remove invalid tags (but keep them marked as invalid)\r\n            whitelist: [\"temple\", \"stun\", \"detective\", \"sign\", \"passion\", \"routine\", \"deck\", \"discriminate\", \"relaxation\", \"fraud\", \"attractive\", \"soft\", \"forecast\", \"point\", \"thank\", \"stage\", \"eliminate\", \"effective\", \"flood\", \"passive\", \"skilled\", \"separation\", \"contact\", \"compromise\", \"reality\", \"district\", \"nationalist\", \"leg\", \"porter\", \"conviction\", \"worker\", \"vegetable\", \"commerce\", \"conception\", \"particle\", \"honor\", \"stick\", \"tail\", \"pumpkin\", \"core\", \"mouse\", \"egg\", \"population\", \"unique\", \"behavior\", \"onion\", \"disaster\", \"cute\", \"pipe\", \"sock\", \"dialect\", \"horse\", \"swear\", \"owner\", \"cope\", \"global\", \"improvement\", \"artist\", \"shed\", \"constant\", \"bond\", \"brink\", \"shower\", \"spot\", \"inject\", \"bowel\", \"homosexual\", \"trust\", \"exclude\", \"tough\", \"sickness\", \"prevalence\", \"sister\", \"resolution\", \"cattle\", \"cultural\", \"innocent\", \"burial\", \"bundle\", \"thaw\", \"respectable\", \"thirsty\", \"exposure\", \"team\", \"creed\", \"facade\", \"calendar\", \"filter\", \"utter\", \"dominate\", \"predator\", \"discover\", \"theorist\", \"hospitality\", \"damage\", \"woman\", \"rub\", \"crop\", \"unpleasant\", \"halt\", \"inch\", \"birthday\", \"lack\", \"throne\", \"maximum\", \"pause\", \"digress\", \"fossil\", \"policy\", \"instrument\", \"trunk\", \"frame\", \"measure\", \"hall\", \"support\", \"convenience\", \"house\", \"partnership\", \"inspector\", \"looting\", \"ranch\", \"asset\", \"rally\", \"explicit\", \"leak\", \"monarch\", \"ethics\", \"applied\", \"aviation\", \"dentist\", \"great\", \"ethnic\", \"sodium\", \"truth\", \"constellation\", \"lease\", \"guide\", \"break\", \"conclusion\", \"button\", \"recording\", \"horizon\", \"council\", \"paradox\", \"bride\", \"weigh\", \"like\", \"noble\", \"transition\", \"accumulation\", \"arrow\", \"stitch\", \"academy\", \"glimpse\", \"case\", \"researcher\", \"constitutional\", \"notion\", \"bathroom\", \"revolutionary\", \"soldier\", \"vehicle\", \"betray\", \"gear\", \"pan\", \"quarter\", \"embarrassment\", \"golf\", \"shark\", \"constitution\", \"club\", \"college\", \"duty\", \"eaux\", \"know\", \"collection\", \"burst\", \"fun\", \"animal\", \"expectation\", \"persist\", \"insure\", \"tick\", \"account\", \"initiative\", \"tourist\", \"member\", \"example\", \"plant\", \"river\", \"ratio\", \"view\", \"coast\", \"latest\", \"invite\", \"help\", \"falsify\", \"allocation\", \"degree\", \"feel\", \"resort\", \"means\", \"excuse\", \"injury\", \"pupil\", \"shaft\", \"allow\", \"ton\", \"tube\", \"dress\", \"speaker\", \"double\", \"theater\", \"opposed\", \"holiday\", \"screw\", \"cutting\", \"picture\", \"laborer\", \"conservation\", \"kneel\", \"miracle\", \"primary\", \"nomination\", \"characteristic\", \"referral\", \"carbon\", \"valley\", \"hot\", \"climb\", \"wrestle\", \"motorist\", \"update\", \"loot\", \"mosquito\", \"delivery\", \"eagle\", \"guideline\", \"hurt\", \"feedback\", \"finish\", \"traffic\", \"competence\", \"serve\", \"archive\", \"feeling\", \"hope\", \"seal\", \"ear\", \"oven\", \"vote\", \"ballot\", \"study\", \"negative\", \"declaration\", \"particular\", \"pattern\", \"suburb\", \"intervention\", \"brake\", \"frequency\", \"drink\", \"affair\", \"contemporary\", \"prince\", \"dry\", \"mole\", \"lazy\", \"undermine\", \"radio\", \"legislation\", \"circumstance\", \"bear\", \"left\", \"pony\", \"industry\", \"mastermind\", \"criticism\", \"sheep\", \"failure\", \"chain\", \"depressed\", \"launch\", \"script\", \"green\", \"weave\", \"please\", \"surprise\", \"doctor\", \"revive\", \"banquet\", \"belong\", \"correction\", \"door\", \"image\", \"integrity\", \"intermediate\", \"sense\", \"formal\", \"cane\", \"gloom\", \"toast\", \"pension\", \"exception\", \"prey\", \"random\", \"nose\", \"predict\", \"needle\", \"satisfaction\", \"establish\", \"fit\", \"vigorous\", \"urgency\", \"X-ray\", \"equinox\", \"variety\", \"proclaim\", \"conceive\", \"bulb\", \"vegetarian\", \"available\", \"stake\", \"publicity\", \"strikebreaker\", \"portrait\", \"sink\", \"frog\", \"ruin\", \"studio\", \"match\", \"electron\", \"captain\", \"channel\", \"navy\", \"set\", \"recommend\", \"appoint\", \"liberal\", \"missile\", \"sample\", \"result\", \"poor\", \"efflux\", \"glance\", \"timetable\", \"advertise\", \"personality\", \"aunt\", \"dog\"],\r\n            transformTag: transformTag,\r\n            dropdown: {\r\n                enabled: 3,\r\n            }\r\n        });\r\n\r\n        function transformTag(tagData) {\r\n            var states = [\r\n                'success',\r\n                'primary',\r\n                'danger',\r\n                'success',\r\n                'warning',\r\n                'dark',\r\n                'primary',\r\n                'info'];\r\n\r\n            tagData.class = 'tagify__tag tagify__tag-light--' + states[KTUtil.getRandomInt(0, 7)];\r\n\r\n            if (tagData.value.toLowerCase() == 'shit') {\r\n                tagData.value = 's✲✲t'\r\n            }\r\n        }\r\n\r\n        tagify.on('add', function(e) {\r\n            console.log(e.detail)\r\n        });\r\n\r\n        tagify.on('invalid', function(e) {\r\n            console.log(e, e.detail);\r\n        });\r\n    }\r\n\r\n\r\n    return {\r\n        // public functions\r\n        init: function() {\r\n            demo1();\r\n            demo1Readonly();\r\n\r\n            demo2();\r\n            demo3();\r\n            demo4();\r\n            demo5();\r\n            demo6();\r\n        }\r\n    };\r\n}();\r\n\r\njQuery(document).ready(function() {\r\n    KTTagifyDemos.init();\r\n});\r\n"], "sourceRoot": ""}