{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///../demo3/src/js/pages/custom/projects/list-datatable.js"], "names": [], "mappings": ";QAAA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;;AClFa;AACb;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,4BAA4B,iDAAiD;AAC7E,4BAA4B,oDAAoD;AAChF,4BAA4B,oDAAoD;AAChF,4BAA4B;AAC5B;AACA;;AAEA;AACA;;AAEA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,CAAC;;AAED;AACA;AACA,CAAC", "file": "js/pages/custom/projects/list-datatable.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"../demo3/src/js/pages/custom/projects/list-datatable.js\");\n", "\"use strict\";\r\n// Class definition\r\n\r\nvar KTAppsProjectsListDatatable = function() {\r\n    // Private functions\r\n\r\n    // basic demo\r\n    var _demo = function() {\r\n        var datatable = $('#kt_datatable').KTDatatable({\r\n            // datasource definition\r\n            data: {\r\n                type: 'remote',\r\n                source: {\r\n                    read: {\r\n                        url: HOST_URL + '/api/datatables/demos/default.php',\r\n                    },\r\n                },\r\n                pageSize: 10, // display 20 records per page\r\n                serverPaging: true,\r\n                serverFiltering: true,\r\n                serverSorting: true,\r\n            },\r\n\r\n            // layout definition\r\n            layout: {\r\n                scroll: false, // enable/disable datatable scroll both horizontal and vertical when needed.\r\n                footer: false, // display/hide footer\r\n            },\r\n\r\n            // column sorting\r\n            sortable: true,\r\n\r\n            pagination: true,\r\n\r\n            search: {\r\n                input: $('#kt_subheader_search_form'),\r\n                delay: 400,\r\n                key: 'generalSearch'\r\n            },\r\n\r\n            // columns definition\r\n            columns: [{\r\n                field: 'RecordID',\r\n                title: '#',\r\n                sortable: 'asc',\r\n                width: 40,\r\n                type: 'number',\r\n                selector: false,\r\n                textAlign: 'left',\r\n                template: function(data) {\r\n                    return '<span class=\"font-weight-bolder\">' + data.RecordID + '</span>';\r\n                }\r\n            }, {\r\n                field: 'OrderID',\r\n                title: 'Customer',\r\n                width: 250,\r\n                template: function(data) {\r\n                    var number = KTUtil.getRandomInt(1, 10);\r\n                    var img = number + '.png';\r\n\r\n\t\t\t\t\tvar skills = [\r\n\t\t\t\t        'Angular, React',\r\n\t\t\t\t        'Vue, Kendo',\r\n\t\t\t\t        '.NET, Oracle, MySQL',\r\n\t\t\t\t        'Node, SASS, Webpack',\r\n\t\t\t\t        'MangoDB, Java',\r\n\t\t\t\t        'HTML5, jQuery, CSS3',\r\n\t\t\t\t\t\t'React, Vue',\r\n\t\t\t\t\t\t'MangoDB, Node.js'\r\n\t\t\t\t    ];\r\n\r\n                    var output = '';\r\n                    if (number < 7) {\r\n                        output = '<div class=\"d-flex align-items-center\">\\\r\n\t\t\t\t\t\t\t\t<div class=\"symbol symbol-40 symbol-circle symbol-sm\">\\\r\n\t\t\t\t\t\t\t\t\t<img class=\"\" src=\"assets/media/project-logos/' + img + '\" alt=\"photo\"/>\\\r\n\t\t\t\t\t\t\t\t</div>\\\r\n\t\t\t\t\t\t\t\t<div class=\"ml-3\">\\\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-dark-75 font-weight-bolder font-size-lg mb-0\">' + data.CompanyAgent + '</div>\\\r\n\t\t\t\t\t\t\t\t\t<a href=\"#\" class=\"text-muted font-weight-bold text-hover-primary\">' +\r\n                            \t\tskills[number -1] + '</a>\\\r\n\t\t\t\t\t\t\t\t</div>\\\r\n\t\t\t\t\t\t\t</div>';\r\n                    } else {\r\n                        var stateNo = KTUtil.getRandomInt(0, 7);\r\n                        var states = [\r\n                            'success',\r\n                            'primary',\r\n                            'danger',\r\n                            'success',\r\n                            'warning',\r\n                            'dark',\r\n                            'primary',\r\n                            'info'\r\n                        ];\r\n                        var state = states[stateNo];\r\n\r\n                        output = '<div class=\"d-flex align-items-center\">\\\r\n\t\t\t\t\t\t\t\t<div class=\"symbol symbol-40 symbol-circle symbol-light-' + state + '\">\\\r\n\t\t\t\t\t\t\t\t\t<span class=\"symbol-label font-size-h4\">' + data.CompanyAgent.substring(0, 1) + '</span>\\\r\n\t\t\t\t\t\t\t\t</div>\\\r\n\t\t\t\t\t\t\t\t<div class=\"ml-3\">\\\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-dark-75 font-weight-bolder font-size-lg mb-0\">' + data.CompanyAgent + '</div>\\\r\n\t\t\t\t\t\t\t\t\t<a href=\"#\" class=\"text-muted font-weight-bold text-hover-primary\">' +\r\n                            skills[number - 4] + '</a>\\\r\n\t\t\t\t\t\t\t\t</div>\\\r\n\t\t\t\t\t\t\t</div>';\r\n                    }\r\n\r\n                    return output;\r\n                },\r\n            }, {\r\n                field: 'Country',\r\n                title: 'Country',\r\n                template: function(row) {\r\n                    var output = '';\r\n\r\n                    output += '<div class=\"font-weight-bolder font-size-lg mb-0\">' + row.Country + '</div>';\r\n                    output += '<div class=\"font-weight-bold text-muted\">Code: ' + row.ShipCountry + '</div>';\r\n\r\n                    return output;\r\n                }\r\n            }, {\r\n                field: 'ShipDate',\r\n                title: 'Ship Date',\r\n                type: 'date',\r\n                format: 'MM/DD/YYYY',\r\n                template: function(row) {\r\n                    var output = '';\r\n\r\n                    var status = {\r\n                        1: {'title': 'Paid', 'class': ' label-light-primary'},\r\n                        2: {'title': 'Approved', 'class': ' label-light-danger'},\r\n                        3: {'title': 'Pending', 'class': ' label-light-primary'},\r\n                        4: {'title': 'Rejected', 'class': ' label-light-success'}\r\n                    };\r\n                    var index = KTUtil.getRandomInt(1, 4);\r\n\r\n                    output += '<div class=\"font-weight-bolder text-primary mb-0\">' + row.ShipDate + '</div>';\r\n                    output += '<div class=\"text-muted\">' + status[index].title + '</div>';\r\n\r\n                    return output;\r\n                },\r\n            }, {\r\n                field: 'CompanyName',\r\n                title: 'Company Name',\r\n                template: function(row) {\r\n                    var output = '';\r\n\r\n                    output += '<div class=\"font-weight-bold text-muted\">' + row.CompanyName + '</div>';\r\n\r\n                    return output;\r\n                }\r\n            }, {\r\n                field: 'Status',\r\n                title: 'Status',\r\n                // callback function support for column rendering\r\n                template: function(row) {\r\n                    var status = {\r\n                        1: {\r\n                            'title': 'Pending',\r\n                            'class': ' label-light-primary'\r\n                        },\r\n                        2: {\r\n                            'title': 'Delivered',\r\n                            'class': ' label-light-danger'\r\n                        },\r\n                        3: {\r\n                            'title': 'Canceled',\r\n                            'class': ' label-light-primary'\r\n                        },\r\n                        4: {\r\n                            'title': 'Success',\r\n                            'class': ' label-light-success'\r\n                        },\r\n                        5: {\r\n                            'title': 'Info',\r\n                            'class': ' label-light-info'\r\n                        },\r\n                        6: {\r\n                            'title': 'Danger',\r\n                            'class': ' label-light-danger'\r\n                        },\r\n                        7: {\r\n                            'title': 'Warning',\r\n                            'class': ' label-light-warning'\r\n                        },\r\n                    };\r\n                    return '<span class=\"label label-lg font-weight-bold ' + status[row.Status].class + ' label-inline\">' + status[row.Status].title + '</span>';\r\n                },\r\n            }, {\r\n                field: 'Actions',\r\n                title: 'Actions',\r\n                sortable: false,\r\n                width: 130,\r\n                overflow: 'visible',\r\n                autoHide: false,\r\n                template: function() {\r\n                    return '\\\r\n                        <div class=\"dropdown dropdown-inline\">\\\r\n                            <a href=\"javascript:;\" class=\"btn btn-sm btn-default btn-text-primary btn-hover-primary btn-icon mr-2\" data-toggle=\"dropdown\">\\\r\n                                <span class=\"svg-icon svg-icon-md\">\\\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" version=\"1.1\" class=\"svg-icon\">\\\r\n                                        <g stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\\\r\n                                            <rect x=\"0\" y=\"0\" width=\"24\" height=\"24\"/>\\\r\n                                            <path d=\"M7,3 L17,3 C19.209139,3 21,4.790861 21,7 C21,9.209139 19.209139,11 17,11 L7,11 C4.790861,11 3,9.209139 3,7 C3,4.790861 4.790861,3 7,3 Z M7,9 C8.1045695,9 9,8.1045695 9,7 C9,5.8954305 8.1045695,5 7,5 C5.8954305,5 5,5.8954305 5,7 C5,8.1045695 5.8954305,9 7,9 Z\" fill=\"#000000\"/>\\\r\n                                            <path d=\"M7,13 L17,13 C19.209139,13 21,14.790861 21,17 C21,19.209139 19.209139,21 17,21 L7,21 C4.790861,21 3,19.209139 3,17 C3,14.790861 4.790861,13 7,13 Z M17,19 C18.1045695,19 19,18.1045695 19,17 C19,15.8954305 18.1045695,15 17,15 C15.8954305,15 15,15.8954305 15,17 C15,18.1045695 15.8954305,19 17,19 Z\" fill=\"#000000\" opacity=\"0.3\"/>\\\r\n                                        </g>\\\r\n                                    </svg>\\\r\n                                </span>\\\r\n                            </a>\\\r\n                            <div class=\"dropdown-menu dropdown-menu-sm dropdown-menu-right\">\\\r\n                                <ul class=\"navi flex-column navi-hover py-2\">\\\r\n                                    <li class=\"navi-header font-weight-bolder text-uppercase font-size-xs text-primary pb-2\">\\\r\n                                        Choose an action:\\\r\n                                    </li>\\\r\n                                    <li class=\"navi-item\">\\\r\n                                        <a href=\"#\" class=\"navi-link\">\\\r\n                                            <span class=\"navi-icon\"><i class=\"la la-print\"></i></span>\\\r\n                                            <span class=\"navi-text\">Print</span>\\\r\n                                        </a>\\\r\n                                    </li>\\\r\n                                    <li class=\"navi-item\">\\\r\n                                        <a href=\"#\" class=\"navi-link\">\\\r\n                                            <span class=\"navi-icon\"><i class=\"la la-copy\"></i></span>\\\r\n                                            <span class=\"navi-text\">Copy</span>\\\r\n                                        </a>\\\r\n                                    </li>\\\r\n                                    <li class=\"navi-item\">\\\r\n                                        <a href=\"#\" class=\"navi-link\">\\\r\n                                            <span class=\"navi-icon\"><i class=\"la la-file-excel-o\"></i></span>\\\r\n                                            <span class=\"navi-text\">Excel</span>\\\r\n                                        </a>\\\r\n                                    </li>\\\r\n                                    <li class=\"navi-item\">\\\r\n                                        <a href=\"#\" class=\"navi-link\">\\\r\n                                            <span class=\"navi-icon\"><i class=\"la la-file-text-o\"></i></span>\\\r\n                                            <span class=\"navi-text\">CSV</span>\\\r\n                                        </a>\\\r\n                                    </li>\\\r\n                                    <li class=\"navi-item\">\\\r\n                                        <a href=\"#\" class=\"navi-link\">\\\r\n                                            <span class=\"navi-icon\"><i class=\"la la-file-pdf-o\"></i></span>\\\r\n                                            <span class=\"navi-text\">PDF</span>\\\r\n                                        </a>\\\r\n                                    </li>\\\r\n                                </ul>\\\r\n                            </div>\\\r\n                        </div>\\\r\n                        <a href=\"javascript:;\" class=\"btn btn-sm btn-default btn-text-primary btn-hover-primary btn-icon mr-2\" title=\"Edit details\">\\\r\n                            <span class=\"svg-icon svg-icon-md\">\\\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" version=\"1.1\">\\\r\n                                    <g stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\\\r\n                                        <rect x=\"0\" y=\"0\" width=\"24\" height=\"24\"/>\\\r\n                                        <path d=\"M12.2674799,18.2323597 L12.0084872,5.45852451 C12.0004303,5.06114792 12.1504154,4.6768183 12.4255037,4.38993949 L15.0030167,1.70195304 L17.5910752,4.40093695 C17.8599071,4.6812911 18.0095067,5.05499603 18.0083938,5.44341307 L17.9718262,18.2062508 C17.9694575,19.0329966 17.2985816,19.701953 16.4718324,19.701953 L13.7671717,19.701953 C12.9505952,19.701953 12.2840328,19.0487684 12.2674799,18.2323597 Z\" fill=\"#000000\" fill-rule=\"nonzero\" transform=\"translate(14.701953, 10.701953) rotate(-135.000000) translate(-14.701953, -10.701953) \"/>\\\r\n                                        <path d=\"M12.9,2 C13.4522847,2 13.9,2.44771525 13.9,3 C13.9,3.55228475 13.4522847,4 12.9,4 L6,4 C4.8954305,4 4,4.8954305 4,6 L4,18 C4,19.1045695 4.8954305,20 6,20 L18,20 C19.1045695,20 20,19.1045695 20,18 L20,13 C20,12.4477153 20.4477153,12 21,12 C21.5522847,12 22,12.4477153 22,13 L22,18 C22,20.209139 20.209139,22 18,22 L6,22 C3.790861,22 2,20.209139 2,18 L2,6 C2,3.790861 3.790861,2 6,2 L12.9,2 Z\" fill=\"#000000\" fill-rule=\"nonzero\" opacity=\"0.3\"/>\\\r\n                                    </g>\\\r\n                                </svg>\\\r\n                            </span>\\\r\n                        </a>\\\r\n                        <a href=\"javascript:;\" class=\"btn btn-sm btn-default btn-text-primary btn-hover-primary btn-icon\" title=\"Delete\">\\\r\n                            <span class=\"svg-icon svg-icon-md\">\\\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" version=\"1.1\">\\\r\n                                    <g stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\\\r\n                                        <rect x=\"0\" y=\"0\" width=\"24\" height=\"24\"/>\\\r\n                                        <path d=\"M6,8 L6,20.5 C6,21.3284271 6.67157288,22 7.5,22 L16.5,22 C17.3284271,22 18,21.3284271 18,20.5 L18,8 L6,8 Z\" fill=\"#000000\" fill-rule=\"nonzero\"/>\\\r\n                                        <path d=\"M14,4.5 L14,4 C14,3.44771525 13.5522847,3 13,3 L11,3 C10.4477153,3 10,3.44771525 10,4 L10,4.5 L5.5,4.5 C5.22385763,4.5 5,4.72385763 5,5 L5,5.5 C5,5.77614237 5.22385763,6 5.5,6 L18.5,6 C18.7761424,6 19,5.77614237 19,5.5 L19,5 C19,4.72385763 18.7761424,4.5 18.5,4.5 L14,4.5 Z\" fill=\"#000000\" opacity=\"0.3\"/>\\\r\n                                    </g>\\\r\n                                </svg>\\\r\n                            </span>\\\r\n                        </a>\\\r\n                    ';\r\n                },\r\n            }]\r\n        });\r\n    };\r\n\r\n    return {\r\n        // public functions\r\n        init: function() {\r\n            _demo();\r\n        },\r\n    };\r\n}();\r\n\r\njQuery(document).ready(function() {\r\n    KTAppsProjectsListDatatable.init();\r\n});\r\n"], "sourceRoot": ""}