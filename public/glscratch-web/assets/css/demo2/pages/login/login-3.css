.kt-login.kt-login--v2 {
  background-size: cover;
  background-repeat: no-repeat; }
  .kt-login.kt-login--v2 .kt-login__wrapper {
    padding: 6% 2rem 1rem 2rem;
    margin: 0 auto 2rem auto;
    overflow: hidden; }
    .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container {
      width: 430px;
      margin: 0 auto; }
      .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-login__logo {
        text-align: center;
        margin: 0 auto 4rem auto; }
      .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-login__head {
        margin-top: 1rem; }
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-login__head .kt-login__title {
          text-align: center;
          font-size: 1.5rem;
          font-weight: 500;
                  color: #151515; }
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-login__head .kt-login__desc {
          margin-top: 1.5rem;
          text-align: center;
          font-size: 1.1rem;
          font-weight: 400;
          color: rgb(183, 76, 76); }
      .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form {
        margin: 4rem auto; }
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .input-group {
          padding: 0;
          margin: 0 auto; }
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .form-control {
          height: 46px;
          border-radius: 46px;
          border: none;
          padding-left: 1.5rem;
          padding-right: 1.5rem;
          margin-top: 1.5rem;
          background: rgba(67, 34, 167, 0.4);
          color: #fff; }
          .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .form-control::-moz-placeholder {
            color: rgba(255, 255, 255, 0.7);
            opacity: 1; }
          .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .form-control:-ms-input-placeholder {
            color: rgba(255, 255, 255, 0.7); }
          .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .form-control::-webkit-input-placeholder {
            color: rgba(255, 255, 255, 0.7); }
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .form-control.is-valid + .valid-feedback,
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .form-control.is-invalid + .invalid-feedback {
          font-weight: 500;
          font-size: 0.9rem;
          padding-left: 1.6rem; }
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__extra {
          padding-left: 7px;
          padding-right: 7px;
          margin: 15px auto;
          color: rgba(255, 255, 255, 0.6);
          font-size: 1rem; }
          .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__extra .kt-checkbox {
            font-size: 1rem; }
            .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__extra .kt-checkbox > span {
              border: 1px solid rgba(255, 255, 255, 0.6); }
            .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__extra .kt-checkbox input:checked ~ span {
              border: 1px solid #fff; }
              .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__extra .kt-checkbox input:checked ~ span:after {
                border: solid #fff; }
          .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__extra .kt-login__link {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.6); }
            .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__extra .kt-login__link:hover {
              color: #fff; }
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions {
          text-align: center;
          margin-top: 7%; }
          .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-primary,
          .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-secondary {
            height: 46px;
            display: inline-block;
            text-align: center;
            padding-left: 4rem;
            padding-right: 4rem;
            margin-top: 0.8rem;
            border-radius: 60px;
            background: transparent;
            color:#dd5858;
           border: 2px solid #dd5858; margin-bottom: 15px;}
            .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-primary.active, .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-primary:active, .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-primary:hover,
            .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-secondary.active,
            .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-secondary:active,
            .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-secondary:hover {
              color: #fff;
              border-color: #fff; }
          .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-secondary {
            color: rgba(255, 255, 255, 0.4);
            border-color: rgba(255, 255, 255, 0.3); }
            .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-secondary.active, .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-secondary:active, .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .kt-login__actions .kt-login__btn-secondary:hover {
              color: #fff;
              border-color: #fff; }
      .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-login__account {
        text-align: center;
        margin-top: 2rem; }
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-login__account .kt-login__account-msg {
          font-size: 1.1rem;
          font-weight: 500;
          color: #bbabf1; }
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-login__account .kt-login__account-link {
          font-size: 1.1rem;
          font-weight: 500; }

.kt-login.kt-login--v2.kt-login--signin .kt-login__signup {
  display: none; }

.kt-login.kt-login--v2.kt-login--signin .kt-login__signin {
  display: block; }

.kt-login.kt-login--v2.kt-login--signin .kt-login__forgot {
  display: none; }

.kt-login.kt-login--v2.kt-login--signup .kt-login__signup {
  display: block; }

.kt-login.kt-login--v2.kt-login--signup .kt-login__signin {
  display: none; }

.kt-login.kt-login--v2.kt-login--signup .kt-login__forgot {
  display: none; }

.kt-login.kt-login--v2.kt-login--signup .kt-login__account {
  display: none; }

.kt-login.kt-login--v2.kt-login--forgot .kt-login__signup {
  display: none; }

.kt-login.kt-login--v2.kt-login--forgot .kt-login__signin {
  display: none; }

.kt-login.kt-login--v2.kt-login--forgot .kt-login__forgot {
  display: block; }

@media (max-width: 1024px) {
  .kt-login.kt-login--v2 .kt-login__wrapper {
    padding-top: 2rem;
    width: 100%; }
    .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container {
      margin: 0 auto; }
      .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-login__account {
        margin-top: 10rem; } }

@media (max-width: 768px) {
  .kt-login.kt-login--v2 .kt-login__wrapper {
    width: 100%; }
    .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container {
      width: 100%;
      max-width: 400px;
      margin: 0 auto; }
      .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form {
        width: 100%;
        margin: 0 auto; }
        .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-form .form-control {
        background: rgb(160, 92, 130);}
      .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-login__actions .kt-login__btn-submit {
        padding: 0.8rem 3rem; }
      .kt-login.kt-login--v2 .kt-login__wrapper .kt-login__container .kt-login__account {
        margin-top: 1.5rem; } }






#buttons {
    border-radius: 5px;
    padding: 14px 7px;
    background: white;
    /* width: 660px; */
    overflow: hidden;
    /* margin: 150px auto 0; */
    box-shadow: 0 2px 3px rgba(71, 71, 71, 0.31);
}


.button {
    background: #DCE0E0;
    position: relative;
    display: block;
    float: left;
    height: 40px;
    margin: 0 8px;
    overflow: hidden;
    width: 28%;
    border-radius: 3px;
    -o-border-radius: 3px;
    -ms-border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
}

.icon {
    display: block;
    float: left;
    position: relative;
    z-index: 3;
    height: 100%;
    vertical-align: top;
    width: 38px;
    -moz-border-radius-topleft: 3px;
    -moz-border-radius-topright: 0px;
    -moz-border-radius-bottomright: 0px;
    -moz-border-radius-bottomleft: 3px;
    -webkit-border-radius: 3px 0px 0px 3px;
    border-radius: 3px 0px 0px 3px;
    text-align: center;
}

.icon i {
    color: #fff;
    line-height: 42px;
}

.slide {
  
    display: block;
    margin: 0;
    height: 100%;
    /* left: 38px; */
    /* position: absolute; */
    width: inherit;
    -moz-border-radius-topleft: 0px;
    -moz-border-radius-topright: 3px;
    -moz-border-radius-bottomright: 3px;
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-radius: 0px 3px 3px 0px;
    border-radius: 0px 3px 3px 0px;
    width: 100%;
}

.slide p {
     
    font-weight: 400;
    border-left: 1px solid #fff;
    border-left: 1px solid rgba(255,255,255,0.35);
    color: #fff;
    font-size: 16px;
    /* left: 0; */
    margin: 0;
    /* position: absolute; */
    text-align: center;
    /* top: 10px; */
    width: 100%;
    font-size: 13px;
    line-height: 37px;
}

.button .slide {
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

.facebook iframe {
    display: block;
    position: absolute;
    right: 16px;
    top: 10px;
    z-index: 1;
}

.twitter iframe {
    width: 90px !important;
    right: 5px;
    top: 10px;
    z-index: 1;
    display: block;
    position: absolute;
}

.google #___plusone_0 {
    width: 70px !important;
    top: 10px;
    right: 15px;
    position: absolute;
    display: block;
    z-index: 1;
}

.linkedin .IN-widget {
    top: 10px;
    right: 22px;
    position: absolute;
    display: block;
    z-index: 1;
}

.facebook:hover .slide {
    left: 150px;
}

.twitter:hover .slide {
    top: -40px;
}

.google:hover .slide {
    bottom: -40px;
}

.linkedin:hover .slide {
    left: -150px;
}

.facebook .icon, .facebook .slide {
    background: #305c99;
}

.twitter .icon, .twitter .slide {
    background: #00cdff;
}

.whatsapp .icon, .whatsapp .slide {
    background: #079e40;
}

.linkedin .icon, .linkedin .slide {
    background: #007bb6;
}
.twitter-share-button {
  margin-left: 60px;
  margin-top: 10px;
}
#buttons a{color:#fff}


.scratchpad {
	width: 300px;
	height: 274px;
	margin: 0 auto;
}
.scratch-container {
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	width: 100%;
}
.promo-container {
	background:none;
	border-radius: 5px;
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	 
	padding: 20px;
	margin: 0 auto;
	text-align: center;
	color: #fff;
	font-size: 16px;
	margin-top: 20px;
}
	.promo-code{    font-weight: bold;
    font-size: 22px;
	color: #dd5858;
	}
.btn {
	background: #56CFD2;
	color: #FFF;
	padding: 10px 25px;
	display: inline-block;
	margin-top: 15px;
	text-decoration: none;
	font-weight: 600;
	text-transform: uppercase;
	border-radius: 3px;
	-moz-border-radius: 3px;
	-webkit-border-radiuss: 3px;
}
a {
	 color: #6b6b6b;
}
a:hover {
	color: #dcdcdc;
}

@media only screen and (max-width : 480px) {
.scratchpad {
	width: 300px;
	height: 274px;
}
.scratch-container {
	/*width: 400px !important;*/
}
}

@media only screen and (max-width : 320px) {
.scratchpad {
	width: 300px;
	height: 274px;
}
.scratch-container {
	/*width: 290px !important;*/
}
}
.kt-link:hover:after {
	border-bottom: 0px !important;
	opacity: 0.3;
}
