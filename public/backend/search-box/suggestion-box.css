.search-lens{pointer-events: none;
    top: 8px;
    left: 8px;
    transition: fill .1s ease-in;position: absolute
} 
.crm-searchbar {
    /* display: flex;
    align-items: center;
    justify-content: space-between;
    width: 400px;
 
    position: relative;
 
    display: block; */

    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 400px;
    /* margin: 200px auto; */
    position: relative;
    /* margin-right: 30px; */
    display: block;
    float: right !important;
      z-index: 1; 
    
}

.crm-searchbar input {
    width: 100%;
    font-size: 13px;
    border-radius: 30px;
    border: 1px solid #D9D9D9;
    background: #FFF;
    padding: 10px 10px 10px 40px;
    outline: none;
    margin-bottom: 0px;
}

.crm-searchbar button {
    background: #ff4b4b;
    border-radius: 0px 6px 6px 0px;
    border: none;
    outline: none;
    padding: 14px 18px;
}
.crm-searchbar button i {
    color: #fff;
}

#suggestion-box {
    color: #000;
    display: none;
    font-family: inherit;
    border: 1px solid #ccc;
    background: #fff;
    padding: 0;
    margin: 4px 0px 0 0;
    border-radius: 12px;
    font-family: 'Poppins', sans-serif;
    z-index: 999999;
}

#suggestion-box #no-suggestions {
    padding: 10px
}

#suggestion-box ul,
#suggestion-box ul>li {
    padding: 0;
    margin: 0
}

#suggestion-box ul {
    list-style: none;
    width: 100%;
    clear: both
}

#suggestion-box ul>li a {
    display: block;
    height: 100%;
    color: inherit;
    text-decoration: none;
    padding: 10px;
    font-size: 13px;
    text-transform: capitalize;
}

#suggestion-box ul>li:last-child a:hover {
    background-color: #eee;
    border-radius: 0px 0px 12px 12px !important;
}



#suggestion-box .selected {
    background-color: #eee
}

#suggestion-box #suggestion-header {
    margin-right: 10px;
    margin-top: 5px;
    font-weight: 700;
    font-size: .8em;
    color: #999;
    float: right;
}