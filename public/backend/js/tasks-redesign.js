/* ===================================
   TASKS PAGE ENHANCEMENTS
   =================================== */

// Ensure proper initialization of enhanced components
$(document).ready(function() {
    
    // Enhance Select2 initialization with custom styling
    if ($.fn.select2) {
        $('.select2').select2({
            theme: 'default',
            width: '100%'
        });
    }
    
    // Add smooth animations to dashboard cards
    $('.dash-stats').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
    });
    
    // Enhance filter toggle behavior
    $('.main-filter-btn').on('click', function() {
        var icon = $(this).find('i');
        setTimeout(function() {
            if ($('#collapseExample').hasClass('show')) {
                icon.css('transform', 'rotate(180deg)');
            } else {
                icon.css('transform', 'rotate(0deg)');
            }
        }, 50);
    });
    
    // Add loading states to action buttons
    $('.main-round-btn, .filter-round-btn').on('click', function() {
        var $btn = $(this);
        var $icon = $btn.find('i');
        var originalClass = $icon.attr('class');
        
        // Don't add loading to filter toggle
        if (!$btn.hasClass('main-filter-btn')) {
            $icon.attr('class', 'fa fa-spinner fa-spin');
            
            // Reset after 2 seconds (adjust based on actual processing time)
            setTimeout(function() {
                $icon.attr('class', originalClass);
            }, 2000);
        }
    });
    
    // Enhance bulk action buttons visibility
    var updateBulkButtons = function() {
        var checkedCount = $('.form-check-assign input[type="checkbox"]:checked').length;
        var $deleteBtn = $('#delete_tasks');
        var $completeBtn = $('#complete_tasks');
        
        if (checkedCount > 0) {
            $deleteBtn.fadeIn(200);
            $completeBtn.fadeIn(200);
        } else {
            $deleteBtn.fadeOut(200);
            $completeBtn.fadeOut(200);
        }
    };
    
    // Monitor checkbox changes for bulk actions
    $(document).on('change', '.form-check-assign input[type="checkbox"], #checkAll', updateBulkButtons);
    
    // Add hover effects to table rows
    $('#tasks_data_table').on('mouseenter', 'tbody tr', function() {
        $(this).addClass('table-row-hover');
    }).on('mouseleave', 'tbody tr', function() {
        $(this).removeClass('table-row-hover');
    });
    
    // Enhance DataTable initialization if needed
    if ($.fn.DataTable) {
        // Add custom styling to DataTable controls when they're created
        setTimeout(function() {
            $('.dataTables_length select, .dataTables_filter input').addClass('form-control-sm');
        }, 500);
    }
    
});

// Add CSS for additional enhancements - compact version
var additionalCSS = `
    <style>
    .dash-stats {
        animation: fadeInUp 0.4s ease-out forwards;
        opacity: 0;
        transform: translateY(10px);
    }
    
    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .table-row-hover {
        background-color: var(--task-bg-light) !important;
    }
    
    .main-filter-btn i {
        transition: transform 0.2s ease;
    }
    
    .btn.loading {
        pointer-events: none;
        opacity: 0.7;
    }
    
    /* Additional compact spacing */
    .layout-wrapper {
        gap: 6px;
    }
    
    /* Ensure buttons don't have extra margins */
    .main-round-btn, .filter-round-btn {
        margin: 0 !important;
    }
    </style>
`;

// Inject additional CSS
$('head').append(additionalCSS);