/* ===================================
   TASKS PAGES REDESIGN - UNIFIED BUTTON SYSTEM
   Applies to: /user/tasks, /user/call-tasks, /user/campaign-task, /user/deal-tasks
   Single source of truth for all button styling
   =================================== */

/* CSS Variables using design guide colors */
:root {
    --task-primary: #FC5E6C;        /* Brand pink-red */
    --task-success: #3699FF;        /* Blue (from design guide) */
    --task-warning: #FFA800;        /* Orange (from design guide) */
    --task-danger: #F64E60;         /* Red (from design guide) */
    --task-info: #6993FF;           /* Light blue (from design guide) */
    
    --task-violet: #3699FF;         /* Using blue from guide */
    --task-yellow: #FFA800;         /* Using orange from guide */
    --task-blue: #6993FF;           /* Using light blue from guide */
    
    --task-text-primary: #3A3D4B;   /* From existing design */
    --task-text-secondary: #7995B7; /* From existing design */
    --task-text-muted: #526185;     /* From existing design */
    
    --task-bg-light: #F3F6F9;       /* Light (from design guide) */
    --task-bg-white: #ffffff;
    --task-border: #E4E6EF;         /* Secondary (from design guide) */
    
    --task-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --task-shadow-md: 0 2px 4px 0 rgb(0 0 0 / 0.06);
    --task-shadow-lg: 0 4px 6px 0 rgb(0 0 0 / 0.07);
}

/* ===================================
   UNIFIED BUTTON SYSTEM
   Single base class that all buttons inherit from
   =================================== */

/* Base Button Class - All buttons use this */
.task-btn-base {
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    color: #ffffff;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    cursor: pointer;
    text-decoration: none;
    min-width: auto;
    letter-spacing: 0;
    height: 32px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: var(--task-info); /* Default color */
}

.task-btn-base:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    filter: brightness(1.05);
    color: #ffffff;
    text-decoration: none;
}

.task-btn-base:active {
    transform: translateY(1px) scale(0.97);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    filter: brightness(0.95);
}

.task-btn-base a {
    color: #ffffff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 6px;
}

.task-btn-base i {
    font-size: 12px;
    margin-right: 0;
}

/* ===================================
   COLOR VARIANTS
   =================================== */
.task-btn-primary {
    background: var(--task-primary);
}

.task-btn-success {
    background: var(--task-success);
}

.task-btn-warning {
    background: var(--task-warning);
}

.task-btn-danger {
    background: var(--task-danger);
}

.task-btn-info {
    background: var(--task-info);
}

/* ===================================
   SIZE VARIANTS
   =================================== */
.task-btn-bulk {
    height: 28px;
    width: 28px;
    padding: 4px 8px;
    font-size: 12px;
}

/* ===================================
   LEGACY SUPPORT - MAIN ROUND BUTTONS
   Map existing classes to new unified system
   =================================== */
.main-round-btn {
    /* Copy all base styles */
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    color: #ffffff;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    cursor: pointer;
    text-decoration: none;
    min-width: auto;
    letter-spacing: 0;
    height: 32px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: var(--task-info); /* Default color */
}

.main-round-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    filter: brightness(1.05);
    color: #ffffff;
    text-decoration: none;
}

.main-round-btn:active {
    transform: translateY(1px) scale(0.97);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    filter: brightness(0.95);
}

.main-round-btn a {
    color: #ffffff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 6px;
}

.main-round-btn i {
    font-size: 12px;
    margin-right: 0;
}

/* Legacy color variants */
.main-round-btn.primary-btn,
.main-round-btn.bdr-primary {
    background: var(--task-primary);
}

.main-round-btn.green-btn,
.main-round-btn.bdr-green {
    background: var(--task-success);
}

.main-round-btn.orange-btn,
.main-round-btn.bdr-orange {
    background: var(--task-warning);
}

.main-round-btn.red-btn,
.main-round-btn.bdr-red {
    background: var(--task-danger);
}

.main-round-btn.blue-btn,
.main-round-btn.bdr-blue {
    background: var(--task-info);
}



/* ===================================
   BULK ACTION BUTTONS
   =================================== */
.d-flex.align-items-center.mb-2 {
    margin-bottom: 4px !important;
}

#delete_tasks,
#complete_tasks {
    /* Copy base styles but smaller size */
    border: none !important;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.15s ease;
    min-width: auto !important;
    height: 28px;
    width: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    color: #ffffff !important;
    cursor: pointer;
}

#delete_tasks:hover,
#complete_tasks:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    filter: brightness(1.05);
}

#delete_tasks:active,
#complete_tasks:active {
    transform: translateY(1px) scale(0.97);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    filter: brightness(0.95);
}

#delete_tasks {
    background: var(--task-danger) !important;
}

#complete_tasks {
    background: var(--task-success) !important;
}

#delete_tasks i,
#complete_tasks i {
    font-size: 12px;
    margin: 0;
}

/* ===================================
   FILTER BUTTONS (Design Guide Implementation)
   =================================== */

/* Filter Round Buttons */
.filter-round-btn,
.main-filter-btn {
    background: var(--task-info);
    color: #ffffff;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    cursor: pointer;
    text-decoration: none;
    min-width: auto;
    height: 32px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-round-btn:hover,
.main-filter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    filter: brightness(1.05);
    color: #ffffff;
    text-decoration: none;
}

.filter-round-btn:active,
.main-filter-btn:active {
    transform: translateY(1px) scale(0.97);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    filter: brightness(0.95);
}

.filter-round-btn a,
.main-filter-btn a {
    color: #ffffff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 6px;
}

.filter-round-btn i,
.main-filter-btn i {
    font-size: 12px;
    margin-right: 0;
}

/* Filter Action Buttons (Dark Style) */
.filter-action-btn {
    background: var(--task-text-primary);
    color: #ffffff;
    border: none;
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    cursor: pointer;
    text-decoration: none;
    min-width: 80px;
    height: 32px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    filter: brightness(1.05);
    color: #ffffff;
    text-decoration: none;
}

.filter-action-btn:active {
    transform: translateY(1px) scale(0.97);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    filter: brightness(0.95);
    color: #ffffff;
}

/* Ensure clear button has white text */
.filter-action-btn#clear,
.filter-action-btn#clear:hover,
.filter-action-btn#clear:active,
.filter-action-btn#clear:focus,
.filter-action-btn#clear:visited {
    color: #ffffff !important;
    text-decoration: none !important;
}

/* Sort Dropdown Buttons */
.dropdown-toggle {
    background: var(--task-info) !important;
    color: #ffffff !important;
    border: none !important;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    cursor: pointer;
    text-decoration: none;
    min-width: auto;
    height: 32px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dropdown-toggle:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    filter: brightness(1.05) !important;
    color: #ffffff !important;
    text-decoration: none !important;
    background: var(--task-info) !important;
}

.dropdown-toggle:active,
.dropdown-toggle:focus {
    transform: translateY(1px) scale(0.97) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    filter: brightness(0.95) !important;
    background: var(--task-info) !important;
    color: #ffffff !important;
}

.dropdown-toggle i {
    font-size: 12px;
    margin-right: 0;
}

/* ===================================
   SPECIFIC BUTTON OVERRIDES
   =================================== */

/* Start button (call-tasks page) */
#list.main-round-btn,
button#list.main-round-btn {
    background: var(--task-success) !important;
    font-weight: 600;
}

/* Actions dropdown button */
.main-action-btn {
    background: var(--task-info) !important;
    border: none !important;
    color: #ffffff !important;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* Inherit all other styles from base */
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    cursor: pointer;
    text-decoration: none;
    height: 32px;
}

.main-action-btn:hover {
    background: var(--task-info) !important;
    color: #ffffff !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    filter: brightness(1.05);
}

.main-action-btn:active {
    transform: translateY(1px) scale(0.97);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    filter: brightness(0.95);
}

/* Assign To Button */
.main-round-btn:has(i.fa-user) {
    background: var(--task-warning) !important;
}

/* Deal Task Add Button */
.deal_task_add.main-round-btn {
    background: var(--task-success) !important;
}

/* ===================================
   EXISTING STYLES (UNCHANGED)
   Dashboard, tables, forms, etc.
   =================================== */

/* TASK PANEL HEADER */
.task-panel {
    background: transparent;
    padding: 8px 16px;
    border-bottom: none;
    margin-bottom: 0;
}

.task-panel h5 {
    font-size: 18px;
    font-weight: 600;
    color: var(--task-text-primary);
    margin: 0;
}

/* Minimal content section padding */
.content-section {
    padding: 8px 12px !important;
}

/* DASHBOARD STATISTICS CARDS - HORIZONTAL SCROLLABLE */
.dashboard-sec {
    margin-bottom: 6px;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 8px;
}

.dashboard-sec .row {
    display: flex;
    flex-wrap: nowrap !important;
    margin: 0;
    gap: 12px;
    min-width: max-content;
}

.dashboard-sec .col-lg-3,
.dashboard-sec .col-md-6,
.dashboard-sec .col-sm-12 {
    flex: 0 0 auto;
    min-width: 200px;
    max-width: 250px;
    padding: 0;
}

.dashboard-sec .dash-stats {
    background: var(--task-bg-white);
    border: 1px solid var(--task-border);
    border-radius: 6px;
    padding: 12px 16px;
    height: 70px;
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--task-shadow-sm);
    width: 100%;
}

.dashboard-sec .dash-stats:hover {
    transform: translateY(-2px);
    box-shadow: var(--task-shadow-md);
    border-color: var(--task-primary);
}

.dashboard-sec .dash-stats h5 {
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.dashboard-sec .dash-stats p {
    font-size: 12px;
    font-weight: 500;
    color: var(--task-text-secondary);
    margin: 2px 0 0 0;
    text-transform: none;
    line-height: 1.2;
}

/* Update the color classes */
.dash-text-drk-violet {
    color: var(--task-primary);
}

.dash-text-violet {
    color: var(--task-violet);
}

.dash-text-yellow {
    color: var(--task-warning);
}

.dash-text-blue {
    color: var(--task-info);
}

/* Remove old bottom bars */
.dash-stats-btm-bar {
    display: none;
}

/* Hide scrollbar completely for clean look */
.dashboard-sec::-webkit-scrollbar {
    display: none;
}

.dashboard-sec {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

/* ACTION BUTTONS SECTION */
.tbl-h-details {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 6px;
    flex-wrap: wrap;
}


/* Override default spacing */
.main-wrapper,
.main-wrapper-2 {
    background: var(--task-bg-light);
}

/* Remove excessive margins */
.row {
    margin-left: -6px;
    margin-right: -6px;
}

.col-lg-3, .col-md-6, .col-sm-12 {
    padding-left: 6px;
    padding-right: 6px;
}

/* Compact form groups */
.form-group {
    margin-bottom: 4px;
}

/* Smooth transitions */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Remove any conflicting styles */
.main-round-btn[style] {
    background-color: initial !important;
}

/* Ensure consistent link colors */
.main-round-btn a.clr-white {
    color: inherit !important;
}

/* Additional compact overrides */
.table {
    margin-bottom: 0;
}

/* Compact modal adjustments if any */
.modal-content {
    border-radius: 4px;
}

/* Ensure no extra spacing */
#tasks_data_table_wrapper .row {
    margin-left: -4px;
    margin-right: -4px;
}

#tasks_data_table_wrapper .col-sm-12 {
    padding-left: 4px;
    padding-right: 4px;
}

/* ===================================
   DATA TABLE STYLES (UNCHANGED)
   =================================== */
.gl-table.table-responsive,
.gl-table-2.table-responsive,
.gl-table-v3.table-responsive {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fc 100%);
    border: none;
    border-radius: 16px;
    padding: 0;
    overflow: hidden;
    margin-top: 8px;
    box-shadow: 
        0 4px 24px rgba(0, 0, 0, 0.04),
        0 1px 3px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(20px);
    position: relative;
}

#tasks_data_table_wrapper {
    padding: 20px;
    position: relative;
}

#tasks_data_table {
    border: none;
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    background: transparent;
}

#tasks_data_table thead th {
    background: linear-gradient(135deg, 
        rgba(252, 94, 108, 0.1) 0%, 
        rgba(54, 153, 255, 0.08) 50%, 
        rgba(255, 168, 0, 0.06) 100%);
    border-top: 1px solid rgba(228, 230, 239, 0.6);
    border-bottom: 2px solid rgba(228, 230, 239, 0.8);
    color: var(--task-text-primary);
    font-weight: 700;
    font-size: 13px;
    padding: 14px 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: sticky;
    top: 0;
    z-index: 10;
    vertical-align: middle;
    backdrop-filter: blur(10px);
    white-space: nowrap;
    height: 50px;
    box-sizing: border-box;
    text-align: left !important;
}

#tasks_data_table tbody td {
    padding: 14px 12px;
    font-size: 13px;
    color: var(--task-text-primary);
    vertical-align: middle;
    line-height: 1.5;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
    position: relative;
    font-weight: 500;
    text-align: left !important;
    height: 50px;
    box-sizing: border-box;
}

/* Checkbox styling */
#tasks_data_table input[type="checkbox"] {
    width: 18px;
    height: 18px;
    border: 2px solid var(--task-border);
    border-radius: 6px;
    margin: 0;
    padding: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: linear-gradient(145deg, #ffffff, #f1f3f4);
    box-shadow: 
        inset 0 1px 3px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.05);
    display: inline-block;
    vertical-align: middle;
    min-width: 18px;
    min-height: 18px;
    max-width: 18px;
    max-height: 18px;
    box-sizing: border-box;
}

#tasks_data_table input[type="checkbox"]:checked {
    background: linear-gradient(145deg, var(--task-primary), #e11d48);
    border-color: var(--task-primary);
    box-shadow: 
        0 0 20px rgba(252, 94, 108, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    width: 18px;
    height: 18px;
}

#tasks_data_table input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: 700;
}

/* RESPONSIVE ADJUSTMENTS */
@media (max-width: 768px) {
    .content-section {
        padding: 6px 8px !important;
    }
    
    .task-panel {
        padding: 6px 12px;
    }
    
    .task-panel h5 {
        font-size: 16px;
    }
    
    /* All buttons smaller on mobile */
    .task-btn-base,
    .main-round-btn {
        padding: 3px 6px;
        font-size: 10px;
        height: 24px;
    }
    
    .task-btn-bulk,
    #delete_tasks,
    #complete_tasks {
        height: 20px;
        width: 20px;
        padding: 2px 4px;
        font-size: 10px;
    }
}