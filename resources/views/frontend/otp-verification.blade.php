@extends('layouts.master')
@section('content')
<section class="login-wrap">
    @if(session('flash_notification'))
    <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
    @endif
    <div class="container pad-xs-0">
        <div class="d-flex row-wrap login-container ">
            <div class="col-lg-6 bg-grey login-bg row-wrap">
                <div>
                    <h4>Welcome</h4>
                    <p>Don’t have an account? Create your account, it takes less than a minute.</p>
                </div>

                <div class="d-flex justify-content-center">
                    <img src="{{ url('/backend/images/sign_up-left-bg.png') }}">
                </div>
            </div>
            <div class="col-lg-6 pad-0">
                <div class="login-form">

                    <img src="{{ url('/backend/images/logo-getlead.png') }}">
                    <h4>Get Started With <span>GetLead </span><span class="fill">Create your account</span></h4>
                    <form action="{{ url('/verify-otp') }}" method="post" class="form mg-tp25">
                        {{ csrf_field() }}
                        <div class="d-flex row-wrap">
                            <div class="form-feild-row  ">
                                <input autocomplete="off" class='text-input' name="otp" placeholder="Enter OTP"  value="{{ old('otp') }}" type="text">
                            </div>
                            @if ($errors->has('otp'))
                            <span class="help-block">
                              <center> <strong>{{ $errors->first('otp') }}</strong></center> 
                          </span>
                          @endif
                            <div class="d-flex justify-content-between fill align-items-center mg-tp15">
                            <button class="button" type="submit">Verify Mobile</button>
                            </div>
                        </div>
                    </form>
                    <div class="text-left mt-4">
                    <a href="{{url('/login')}}"><small>&nbsp; &larr; Back to homepage</small></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection