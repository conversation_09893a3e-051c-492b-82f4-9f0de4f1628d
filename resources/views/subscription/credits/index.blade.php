@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Credit Management</h5>
                </div>
                @if(session('flash_notification'))
                    <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
                @endif
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.credit-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="container-fluid">
                    <section class="mastersms-sec">
                        <div class="row">
                            <div class="col-lg-6 col-md-8 pd-lf-5">
                                <div class="sms-details-block">
                                    <div class="heading">
                                        <h4>Add Credit</h4>
                                    </div>
                                    <div class="sms-details">
                                        <form action="{{url('admin/credits')}}" method="POST">
                                            {{csrf_field()}}
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">Amount</p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="row col-sm-12 form-group">
                                                        <input type="number" id="amt" class="form-control mg-bt-0" name="amount"
                                                            placeholder="Enter Amount" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">Tax(percentage)</p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="row col-sm-12 form-group">
                                                        <input type="number" id="tax_perc" class="form-control mg-bt-0" name="tax"
                                                            placeholder="Enter Tax(percentage)" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">Tax(amount)</p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="row col-sm-12 form-group">
                                                        <input type="number" id="tax_amt" class="form-control mg-bt-0" name="tax"
                                                            placeholder="Tax(amount)" readonly required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">Total(amount)</p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="row col-sm-12 form-group">
                                                        <input type="number" id="final_amt" class="form-control mg-bt-0"
                                                            placeholder="Total(amount)" readonly required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">Currency </p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">

                                                    <div class="row col-sm-12 form-group">

                                                        <select class="form-control select2" style="width: 100%;"
                                                                name="currency_type">
                                                            <option value="INR">INR</option>
                                                            <option value="USD">USD</option>
                                                        </select>
                                                        @if ($errors->has('currency_type'))
                                                            <span class="text-danger"><strong> * {{ $errors->first('currency_type') }}</strong></span>
                                                        @endif</div>
                                                </div>
                                            </div>
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">User </p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">

                                                    <div class="row col-sm-12 form-group">

                                                        <select class="form-control select2"  style="width: 100%;" name="user_id">
                                                            <option value="">Select User</option>
                                                            @foreach (\App\User::getUsers() as $item)
                                                                <option value="{{$item->id}}">{{$item->user_name_with_phone}}</option>
                                                            @endforeach
                                                        </select>
                                                        @if ($errors->has('credit'))
                                                            <span class="text-danger"><strong> * {{ $errors->first('credit') }}</strong></span>
                                                        @endif</div>
                                                </div>

                                            </div>

                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">Description </p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="row col-sm-12 form-group">
                                                        <textarea required  class="form-control mg-bt-0" name="description" placeholder="Enter Description"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">Status</p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">

                                                    <div class="row col-sm-12 form-group">

                                                        <select class="form-control select2" style="width: 100%;" name="status" required>
                                                            <option value="">Select Status</option>
                                                            <option value="2">Paid</option>
                                                            <option value="0">Unpaid</option>

                                                        </select>

                                                </div>

                                            </div>
                                        </div>
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="row col-sm-12 form-group">
                                                        <input type="submit" value="Add"
                                                            class="demo-btn sms-btn transclass">
                                                    </div>
                                                </div>
                                            </div>
                                        
                                        </form>
                                    </div>
                                </div>
                            </div>
                        

                            <div class="col-lg-6 col-md-8 pd-lf-5">
                                <div class="sms-details-block">
                                    <div class="heading">
                                        <h4>Deduct  Credit</h4>
                                    </div>
                                    <div class="sms-details">
                                        <form action="{{url('admin/deduct-credits')}}" method="POST">
                                            {{csrf_field()}}
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">Credit </p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="row col-sm-12 form-group">
                                                        <input type="number" class="form-control mg-bt-0" name="credit_deduct"
                                                            placeholder="Enter Credit Value">
                                                            <input type="hidden" class="form-control mg-bt-0" name="total_credits" value="@if($total_credits){{ $total_credits->total_credit }} @else 0  @endif"
                                                            placeholder="Enter Credit Value">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">Currency </p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">

                                                    <div class="row col-sm-12 form-group">

                                                        <select class="form-control select2" style="width: 100%;"
                                                                name="currency_type_deduct">
                                                            <option value="INR">INR</option>
                                                            <option value="USD">USD</option>
                                                        </select>
                                                        @if ($errors->has('currency_type'))
                                                            <span class="text-danger"><strong> * {{ $errors->first('currency_type') }}</strong></span>
                                                        @endif</div>
                                                </div>
                                            </div>
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">User </p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">

                                                    <div class="row col-sm-12 form-group">

                                                        <select class="form-control select2" style="width: 100%;"
                                                                name="user_id_deduct" required>
                                                                <option value="">Select User</option>
                                                            @foreach (\App\User::getUsers() as $item)
                                                                <option value="{{$item->id}}">{{$item->user_name_with_phone}}</option>
                                                            @endforeach
                                                        </select>
                                                        @if ($errors->has('credit'))
                                                            <span class="text-danger"><strong> * {{ $errors->first('credit') }}</strong></span>
                                                        @endif</div>
                                                </div>

                                            </div>

                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                    <p class="item-n">Reason</p>
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="row col-sm-12 form-group">
                                                        <textarea required  class="form-control mg-bt-0" name="description_deduct" placeholder="Enter Reason"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="row sms-item">
                                                <div class="col-lg-3 col-md-3">
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="row col-sm-12 form-group">
                                                        <input type="submit" value="Deduct"
                                                            class="demo-btn sms-btn transclass">
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </section>
                    <div class="row">
                        <section class="sms-data-tab-sec">
                            <div class="heading">
                                <h4>History</h4>
                                <p>Last updated
                                    @if($total_credits)
                                        {{ $total_credits->created_at->diffForHumans() }}
                                    @endif
                                </p>
                            </div>
                            <div class="sm-scroll">
                                <ul class="nav nav-tabs" id="myTab" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" id="crdthistory-tab" data-toggle="tab"
                                        href="#crdthistory" role="tab" aria-controls="crdthistory" aria-selected="true">
                                            History</a>
                                    </li>
                                    <!-- <li class="nav-item">
                                    <a class="nav-link" id="plan-tab" data-toggle="tab" href="#plan" role="tab" aria-controls="plan" aria-selected="false">Plan</a>
                                    </li>-->
                                </ul>
                            </div>
                            <div class="tab-content" id="myTabContent">
                                <div class="tab-pane fade show active" id="crdthistory" role="tabpanel"
                                    aria-labelledby="crdthistory-tab">
                                    <div class="smscredit-tab-block">
                                        <div class="mg-bt-15">
                                            <div class="smscredit-tab-inner gl-table">
                                                <table id="credit-history-table"
                                                    class="table table-striped table-bordered nowrap table-custom"
                                                    cellspacing="0" width="100%">
                                                    <thead>
                                                    <tr>
                                                        <th>&nbsp;</th>
                                                        <th>Credit</th>
                                                        <th>Transaction Type</th>
                                                        <th>Name</th>
                                                        <th>Email</th>
                                                        <th>Phone No</th>
                                                        <th>Created At</th>
                                                    </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection
@push('footer.script')
    <script type="text/javascript">
        $(document).ready(function () {
            BASE_URL = window.location.origin;
            $('#credit-history-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                ajax: BASE_URL + '/admin/get-credit-history',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'credit', name: 'credit'},
                    {data: 'transaction_type', name: 'transaction_type'},
                    {data: 'user.vchr_user_name', name: 'user.vchr_user_name'},
                    {data: 'user.email', name: 'user.email'},
                    {data: 'user.vchr_user_mobile', name: 'user.vchr_user_mobile'},
                    {data: 'created_at', name: 'created_at'},
                ],
            });
            $('#tax_perc,#amt').on('change',function(){
                var amt =  $('#amt').val();
                var perc = $('#tax_perc').val();
                if(amt!='' && perc!=''){
                    $('#tax_amt').val(((amt*perc)/100).toFixed(2))
                    $('#final_amt').val(parseFloat(amt)+ parseFloat($('#tax_amt').val()))
                }
                
            })
        });
    </script>

@endpush
