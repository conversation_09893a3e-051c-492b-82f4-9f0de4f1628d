@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>SMS OTP</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.api-sidebar')
            @if(session('flash_notification'))
                <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
            @endif
            <div class="content-section p-0">
                <section class="bulk-push-sec">
                    <div class="row">
                        <div class="col-md-7">
                            <div class="col-md-12">
                                <div class="row bg-white">
                                    <div class="col-sm-12">
                                        <div class="row form-group">
                                            <div class="col-md-12">
                                                <h6>SMS : OTP API </h6>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="row form-group">
                                            <div class="col-md-12">
                                                <div class="input-group mb-2">
                                                    <div class="input-group-prepend">
                                                        <div class="input-group-text">SENDERID *</div>
                                                    </div>
                                                    <select class="form-control" id="senderIdValue"
                                                            required
                                                            name="sender_id">
{{--                                                        <option value="">Choose Sender Id</option>--}}
                                                        @if(count($senderids)===0)
                                                            <option value="GTLEAD">GTLEAD [Default Sender id]</option>
                                                        @endif
                                                        @foreach($senderids as $senderid)
                                                            <option value="{{$senderid->name}}">{{$senderid->name}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="row form-group">
                                            <div class="col-md-12">
                                                <div class="input-group mb-2">
                                                    <input class="form-control" type="text" name="input_mobile"
                                                           id="input_mobile" required
                                                           placeholder=" Enter Mobile Number">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="row form-group">
                                            <div class="col-md-12">
                                                <div class="input-group mb-2">

                                                    <input class="form-control" type="text" name="input_message"
                                                           id="input_otp" required maxlength="6"
                                                           onkeyup="messagePreview();" placeholder=" Enter OTP">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="row form-group">
                                            <div class="col-md-12">
                                                <div class="input-group mb-2">
                                                    <input class="form-control" type="text" name="input_message"
                                                           id="input_purpose" required onkeyup="messagePreview();"
                                                           placeholder="Purpose">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="row form-group">
                                            <div class="col-md-12">
                                                <div class="input-group mb-2">
                                                    <input class="form-control" type="text" name="input_message"
                                                           id="input_company" placeholder="Company Name"
                                                           onkeyup="messagePreview();">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="row form-group">
                                            <div class="col-md-12">
                                                <div class="input-group mb-2">
                                                    <div class="input-group-prepend">
                                                        <div class="input-group-text">Preview</div>
                                                    </div>
                                                    <textarea class="form-control" name="message" id="messageText"
                                                              readonly rows="4"
                                                              {{--placeholder="Hi User <XXXX> is the OTP for your Request for <PURPOSE>  through < COMPANY NAME >. Please do not share."--}}></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-12" id="show-api">
                                        <div class="">
                                            <textarea class="form-control show_api"
                                                      id="exampleFormControlTextarea1 show_api" rows="6"
                                                      placeholder=""></textarea>
                                        </div>
                                    </div>
                                    <div class="copy main-round-btn ml-lg-3" id="copy-api">
                                        <a href="#">Copy to clipboard</a>
                                    </div>
                                </div>
                                <br>
                                <div class="row form-group">
                                    <div class="col-lg-12 col-sm-12  btn-holder justify-content-center">
                                        <input type="submit" value="Generate API" class="main-round-btn"
                                               id="btn_generate_api"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5">
                            @include('gl-otp.api-instructions')
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>
@endsection
@push('footer.script')

    <script>
        $(document).ready(function () {
            BASE_URL = window.location.origin;

            $('#instructionContainer').slimScroll({
                height: '500px',
                size: '3px'
            });
            $("#show-api").hide();
            $("#copy-api").hide();


            $(".token").on("click", function (e) {
                e.preventDefault();
                if (confirm("Do you want to show/generate token!")) {
                    jQuery.ajax
                    ({
                        url: "{{url('/generate-apitoken')}}", //php
                        data: "", //the data "caller=name1&&callee=name2"
                        dataType: 'json', //data format
                        success: function (data) {
                            $('p#show-token').empty();
                            $('p#show-token').text(data.token);
                        }
                    });
                } else {
                    return false;
                }
            });

            $(document).on('click', '#btn_generate_api', function (e) {
                $("#show-api").show();
                $("#copy-api").show();
                var BASE_URL = window.location.origin;
                var NAME = ('{{Auth::user()->vchr_user_mobile }}' === "") ? "" : '{{Auth::user()->vchr_user_mobile }}';
                var APITOKEN = ($('#apitoken').val() === "") ? "" : $('#apitoken').val();
                var PRIORITY = '{{\App\Common\Variables::OTP_PRIORITY}}';
                var SENDERID = ($('#senderIdValue').val() === "") ? "" : $('#senderIdValue').val();
                var MOBILENO = ($('#input_mobile').val() === "") ? "" : $('#input_mobile').val();
                var OTP = ($('#input_otp').val() === "") ? "" : $('#input_otp').val();
                var PURPOSE = ($('#input_purpose').val() === "") ? "" : $('#input_purpose').val();
                var COMPANY = ($('#input_company').val() === "") ? "" : $('#input_company').val();
                var api = BASE_URL + '/' + 'api/push-otp?username=' + NAME + '&token=' + APITOKEN + '&sender=' + SENDERID + '&to=' + MOBILENO + '&otp=' + OTP + '&purpose=' + PURPOSE + '&company=' + COMPANY + '&priority=' + PRIORITY;
                $(".show_api").text(api);
            });
        });

        function messagePreview() {
            var otp = ($('#input_otp').val() === "") ? "< XXXX >" : $('#input_otp').val();
            var purpose = ($('#input_purpose').val() === "") ? "<PURPOSE>" : $('#input_purpose').val();
            var company = ($('#input_company').val() === "") ? "<COMPANY NAME>" : $('#input_company').val();
            var content = otp + ' is the OTP for your request for ' + purpose + ' through ' + company;
            if ($('#senderIdValue').val() == "GTLEAD") {
                content = 'Hi,\n' +
                    otp + ' is the OTP for your request for ' + purpose + ' through ' + company;
            }

            $('#messageText').text(content);
        }
    </script>
@endpush