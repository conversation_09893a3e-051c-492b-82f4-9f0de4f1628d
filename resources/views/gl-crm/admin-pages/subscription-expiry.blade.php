@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Subscription Expiry List</h5>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.adminsidebar')
            <div class="content-section ">


                <div class="row">
                    <section class="sms-data-tab-sec" style="padding-top: 0px;margin-top: 0px;">
                        <div class="heading">
                            <p></p>
                        </div>
                        <div class="sm-scroll">
                            <ul class="nav nav-tabs" id="myTab" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="crdthistory-tab" data-toggle="tab"
                                       href="#crdthistory" role="tab" aria-controls="crdthistory" aria-selected="true">Expired
                                        List</a>
                                </li>

                                <li class="nav-item">
                                    <a class="nav-link" id="debit-tab" data-toggle="tab" href="#debit" role="tab"
                                       aria-controls="debit" aria-selected="false">Expiry List</a>
                                </li>
                            </ul>
                        </div>
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="crdthistory" role="tabpanel"
                                 aria-labelledby="crdthistory-tab">
                                <div class="smscredit-tab-block">
                                    <div class="mg-bt-15">
                                        <div class="smscredit-tab-inner">
                                            <div class="gl-table">
                                                <table id="expired_data"
                                                       class="table table-striped table-bordered nowrap table-custom"
                                                       cellspacing="0" width="100%">
                                                    <thead>
                                                    <tr>
                                                        <th>Sl No</th>
                                                        <th>User</th>
                                                        <th>Plan</th>
                                                        <th>Expiry Date</th>
                                                        <th></th>
                                                    </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade show" id="debit" role="tabpanel" aria-labelledby="debit-tab">
                                <div class="smscredit-tab-block">
                                    <div class="mg-bt-15">
                                        <div class="smscredit-tab-inner">
                                            <div class="gl-table">
                                                <table id="to_be_expire"
                                                       class="table table-striped table-bordered nowrap table-custom"
                                                       cellspacing="0" width="100%">
                                                    <thead>
                                                    <tr>
                                                        <th>Sl No</th>
                                                        <th>User</th>
                                                        <th>Plan</th>
                                                        <th>Expiry Date</th>
                                                        <th></th>
                                                    </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>


                <!--  /Your content goes here/ -->
            </div>

        </div>
    </main>

@endsection
@push('footer.script')
    <script type="text/javascript">


        $(document).ready(function () {
            BASE_URL = {!! json_encode(url('/')) !!}
            $('.error').hide();
            /*-----Get Data------*/
            $('#to_be_expire').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                ajax: BASE_URL + '/admin/subscription-expiry-data',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'vchr_user_name', name: 'vchr_user_name'},
                    {data: 'name', name: 'name'},
                    {data: 'date_to', name: 'date_to'},
                    {data: 'days', name: 'days'},
                ],
            });
            $('#expired_data').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                ajax: BASE_URL + '/admin/subscription-expired-data',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'vchr_user_name', name: 'vchr_user_name'},
                    {data: 'name', name: 'name'},
                    {data: 'date_to', name: 'date_to'},
                    {data: 'days', name: 'days'},
                ],
            });
        });

    </script>
@endpush
