@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>SMS-Credits</h5>
                </div>
                <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminbulkpushsidebar')
            <div class="content-section p-3 bg-white">

                <!--  /Your content goes here/ -->
                <table id="xml_api_data_table" class="table table-striped table-bordered nowrap table-custom"
                       cellspacing="0" width="100%">
                    <thead>
                    <tr>
                        <th>GL Id</th>
                        <th>User</th>
                        <th>SMS</th>

                    </tr>
                    </thead>
                    <tbody>
                    @foreach($sms_counts as  $sms_count)
                        <tr>
                            <td>{{$sms_count['customer_id']}}</td>
                            <td>{{$sms_count['name']}}</td>
                            <td>
                                <div>
                                    <div class="row">
                                        <table>
                                            <tr>
                                                <th>Route</th>
                                                <th>Balance</th>
                                            </tr>
                                            @if(array_key_exists(0,$sms_count))
                                                <tr>
                                                    <td>{{$sms_count[0]['sms_route']}}</td>
                                                    <td>{{$sms_count[0]['total_count']- $sms_count[0]['used_sms_count']}}</td>
                                                </tr>
                                            @endif
                                            @if(array_key_exists(1,$sms_count))
                                                <tr>
                                                    <td>{{$sms_count[1]['sms_route']}}</td>
                                                    <td>{{$sms_count[1]['total_count']- $sms_count[1]['used_sms_count']}}</td>
                                                </tr>
                                            @endif @if(array_key_exists(2,$sms_count))
                                                <tr>
                                                    <td>{{$sms_count[2]['sms_route']}}</td>
                                                    <td>{{$sms_count[2]['total_count']- $sms_count[2]['used_sms_count']}}</td>
                                                </tr>
                                            @endif @if(array_key_exists(3,$sms_count))
                                                <tr>
                                                    <td>{{$sms_count[3]['sms_route']}}</td>
                                                    <td>{{$sms_count[3]['total_count']- $sms_count[3]['used_sms_count']}}</td>
                                                </tr>
                                            @endif @if(array_key_exists(4,$sms_count))
                                                <tr>
                                                    <td>{{$sms_count[4]['sms_route']}}</td>
                                                    <td>{{$sms_count[4]['total_count']- $sms_count[4]['used_sms_count']}}</td>
                                                </tr>
                                            @endif
                                        </table>


                                    </div>
                                </div>
                            </td>
                        </tr>
                    @endforeach

                    </tbody>
                </table>
            </div>
        </div>
    </main>
@endsection
@push('footer.script')
    <script type="text/javascript">

        $(document).ready(function () {
            BASE_URL = window.location.origin;
            $('.error').hide();

            $('#xml_api_data_table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },

            });

        });


    </script>
@endpush





