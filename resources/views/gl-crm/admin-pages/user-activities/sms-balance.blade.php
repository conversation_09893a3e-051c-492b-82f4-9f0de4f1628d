@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <style type="text/css">
        .val-error {
            color: red;
        }
    </style>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>SMS Balance</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.adminsidebar')
            <div class="content-section">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="tbl-h-details">
                        </div>
                    </div>
                </div>
                <div class="gl-table">
                    <table id="users-table" class="table table-striped table-bordered nowrap table-custom"
                        cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Name</th>
                            <th>Route</th>
                            <th>Balance</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </main>
@endsection
@push('footer.script')
    <script type="text/javascript">
        $(document).ready(function () {
            var BASE_URL = window.location.origin;

            $('#users-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "150px",
                    "targets": [1, 2, 3]
                },
                    {
                        "width": "50px",
                        "targets": [0]
                    },

                ],
                ajax: BASE_URL + '/admin/get-user-sms-balance-data',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'vchr_user_name', name: 'vchr_user_name'},
                    {data: 'vchr_sms_route', name: 'vchr_sms_route'},
                    {data: 'total_balance', name: 'total_balance'},
                ],

            });

        });
    </script>
@endpush
