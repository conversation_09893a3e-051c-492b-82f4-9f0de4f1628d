@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Auto Assign Agent</h5>
                </div>
                <div class="col-lg-6 text-right">
                    <div class="task-nav"></div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.ivr-sidebar')
            <div class="content-section p-3 bg-white">
                <div class="p-3">  
                    <form action="{{url('user/auto-assign-agent')}}" method="POST">
                        {{csrf_field()}}
                        <!-- <div class="row">
                            <div class="col-12 col-lg-6 form-group">
                                <label for="default-input" class="form-control-label"> Assigned Agent</label>
                                <input type="text" @if($agent) value="{{$agent->vchr_user_name}}" @endif disabled
                                    class="form-control" placeholder="">
                            </div>

                        </div> -->
                        <div class="row">

                            <div class="col-sm-6 form-group">
                                <label for="default-input" class="form-control-label"> Agent</label>
                                <select class="form-control" name="agent_id" id="id_for_agent">
                                    <option value="">------ Select Agent -------</option>
                                    @foreach(\App\User::getAgentList() as $sagent)
                                        <option @if($agent && $agent->pk_int_user_id==$sagent->id) selected @endif value="{{$sagent->id}}">{{$sagent->name}}</option>
                                    @endforeach

                                </select>
                                @if($errors->first('agent_id'))
                                    <p class="text-danger">{{$errors->first('agent_id')}}</p>
                                @endif

                                <input type="submit" value="Submit" class="main-round-btn green-btn ml-0">
                            </div>
                            
                        </div>
                        
                    </form>
                    <form action="{{url('user/set-sticky-agent')}}"  id="sticky_agent_form" method="POST">
                        {{csrf_field()}}
                    
                        <div class="row">

                        <div class="col-12 col-lg-6 form-group">
                                <label for="default-input" class="form-control-label"> Sticky Agent</label>
                                <select class="form-control" style="display:none" name="status" id="id_for_sticky_agent">
                                        <option value="1" @if($vendor->sticky_agent==1) selected @endif >Enable</option>
                                        <option value="0" @if($vendor->sticky_agent==0) selected @endif >Disable</option>

                                </select>


                                <div class="custom-switch custom-switch-label-yesno">
                    <input class="custom-switch-input" id="sticky_agent_tick" value="1" @if($vendor->sticky_agent==1) checked="checked" @endif type="checkbox">
                    <label class="custom-switch-btn" for="sticky_agent_tick"></label>
                    <!-- <div class="custom-switch-content-checked">
                        <span class="text-success">Enable</span>
                    </div>
                    <div class="custom-switch-content-unchecked">
                        <span class="text-danger">Disable</span>
                    </div> -->
                </div>
            </div>
                            
                        </div>
                        <!-- <div class="col-12 col-lg-6 form-group pt-4">
                            <input type="submit" value="Submit" class="btn demo-btn">
                        </div> -->
                    </div>
                    
                </form>

            </div>
        </div>
    </main>
    <!------------ /update modal/  -------------->
    @include('gl-crm.pages.user.leads.enquiry-source.create-enquiry-source-modal')
    @include('gl-crm.pages.user.leads.enquiry-source.edit-enquiry-source-modal')

@endsection
@push('footer.script')
    <script type="text/javascript">


        $(document).ready(function () {
            $('#sticky_agent_tick').on('change',function(){
                if($(this).is(':checked'))
                    $('#id_for_sticky_agent').val(1)
                else
                    $('#id_for_sticky_agent').val(0)
                $('form#sticky_agent_form').submit();
            })
            $('#id_for_agent').select2();
            BASE_URL = {!! json_encode(url('/')) !!}
            $('.error').hide();
            /*-----Get Data------*/
            $('#enquiry_source_data_table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                ajax: BASE_URL + '/user/enquiry-source-data',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'added_by', name: 'added_by'},
                    {data: 'vchr_enquiry_type', name: 'vchr_enquiry_type'},
                    {data: 'show', name: 'show', orderable: false, searchable: false}
                ],
            });
            /*-----Add------*/
            $(document).on('submit', '#enquirySourceAdd', function (event) {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/enquiry-source',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                })
                    .done(function (res) {
                        if (res.status == 'success') {
                            // $($('.ks-izi-modal-trigger').data('target')).iziModal('close');
                            //  $("#testimonialAdd")[0].reset();
                            $('#ks-izi-modal-large').modal('toggle');
                            $.alert({
                                title: 'Success',
                                type: 'green',
                                content: res.msg,
                            });
                        } else {
                            $.each(res.msg, function (index, val) {
                                //iterate through array or object
                                console.log(val);
                                console.log(index);
                                $('.' + index).html(val);
                                $('.' + index).show();
                            });
                        }
                    }).fail(function () {
                }).always(function (com) {
                    $('#enquiry_source_data_table').DataTable().ajax.reload(null, false);
                });
            });
            /*-----Edit------*/
            $(document).on('submit', '#enquirySourceEdit', function (event) {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                var id = $('#pk_int_enquiry_type_id').val();
                $.ajax({
                    url: BASE_URL + '/user/enquiry-source/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        //  $("#testimonialAdd")[0].reset();
                        $('#enquiry_source_edit_modal').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            console.log(val);
                            console.log(index);
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                }).always(function (com) {
                    $('#enquiry_source_data_table').DataTable().ajax.reload(null, false);
                });
            });
            /*-----Activate/Deactivate------*/
            $('#enquiry_source_data_table').on('click', '.enq-source-act', function (event) {
                event.preventDefault();
                if ($(this).hasClass('btn-activate')) {
                    var url = BASE_URL + '/user/enquiry-source-deactivate/';
                    action = 'Deactivate';
                } else {
                    var url = BASE_URL + '/user/enquiry-source-activate/';
                    action = 'Activate';
                }
                var enquirySourceId = $(this).attr('data-enq-source-id');
                url = url + enquirySourceId;
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                }).done(function (res) {
                                    if (res.status == 'success') {
                                        $.alert({title: 'Success', type: 'green', content: res.msg,});
                                    } else {
                                        $.alert({title: 'Failed', type: 'red', content: res.msg,});
                                    }
                                }).fail(function (err) {
                                }).always(function (com) {
                                    $('#enquiry_source_data_table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
            /*-----Delete------*/
            $('#enquiry_source_data_table').on('click', '.enq-source-delete', function (event) {
                event.preventDefault();
                var id = $(this).attr('data-enq-source-id');
                var destinationPath = BASE_URL + '/user/enquiry-source/' + id;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                }).done(function (res) {
                                    if (res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                    .fail(function (err) {
                                    })
                                    .always(function (com) {
                                        $('#enquiry_source_data_table').DataTable().ajax.reload(null, false);
                                    });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });
        /*-----View------*/
        $('#enquiry_source_data_table').on('click', '.enq_source', function (event) {
            var enquirySourceId = $(this).attr('data-enq-source-id');
            $.ajax({
                url: BASE_URL + '/user/enquiry-source/' + enquirySourceId,
                type: 'GET',
                dataType: 'JSON',
            }).done(function (res) {
                if (res.status == "success") {
                    data = res.data;
                    $("[name='vchr_enquiry_type']").val(data.vchr_enquiry_type);
                    $('#pk_int_enquiry_type_id').val(data.pk_int_enquiry_type_id);
                    $('#enquiry_source_edit_modal').modal('toggle');
                }
            }).fail(function (err) {
            }).always(function (com) {
                $('#enquiry_source_data_table').DataTable().ajax.reload(null, false);
            });
        });
    </script>
@endpush
