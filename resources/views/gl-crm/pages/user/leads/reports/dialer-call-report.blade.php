@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <style type="text/css">
        .val-error {
            color: red;
        }
        #myCanvas {    
           width: 24px;
           height: 24px;
           display: block;
           float: left;
           background-color: #b5d7ca;
           margin: 5px;
           line-height: 24px;
        }
        #myCanvas2 {    
           width: 24px;
           height: 24px;
           display: block;
           float: left;
           background-color: #FEA8A8 ;
           margin: 5px;
           line-height: 24px;
        }
    </style>
    <main class="main-wrapper main-wrapper-2">
        @if(session('flash_notification'))
            <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
        @endif
        <div class="task-panel">
            <div class="">
                <div class="add-y-team">
                    <div class="row dashboard-row">
                       <div class="col-lg-5 col-sm-12 col-md-12">
                          <div class="y-team-header y-team-header-V2">
                             <a href="{{ url()->previous() }}">
                                
                                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                      <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z" fill="#4D5459"/>
                                   </svg>
                                  Dialer Call Report
                               
                             </a>
                          </div>
                       </div>
                       <div class="col-lg-7 col-sm-12 col-md-12" ></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.reportsidebar')
            
            <div class="content-section">
                
                <div class="row">
                   
                    <div class="col-sm-12">
                        <div id="dvExcel" class="table gl-table">
                        <table id="enquiry-info-table" class="table table-striped table-bordered table-responsive" width="100%">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Customer Name</th>
                                    <th>Customer Number</th>
                                    <th>Duration</th>
                                    <th>Call Status</th>
                                    <th>Call Direction</th>
                                    <th>Call Date</th>
                                    <th>Download</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($call_logs as $ke => $l)
                                <tr>
                                    <td>{{$ke+1}}</td>
                                    <td>{{$l->customer_name}}</td>
                                    <td>{{$l->caller_number}}</td>
                                    <td>{{$l->duration}}</td>
                                    <td>{{$l->call_status}}</td>
                                    <td>{{$l->call_direction}}</td>
                                    <td>{{ \Carbon\Carbon::parse($l->created_at)->format('d M Y h:i A')}}</td>
                                    <td><a href="{{ Storage::disk('s3')->url($l->recordings) }}" ><i class="fa fa-download"></i> Download File</a></td>                                        
                                    
                                </tr>
                                @endforeach
                            </tbody>
                            
                        </table>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection
@push('footer.script')
<script type="text/javascript">
        

$(document).ready(function () {
   var table = $('#enquiry-info-table').DataTable(
    {
        "processing":true,
        "paging":true,
		
		language: {
			searchPlaceholder: 'Search',
			sSearch: '',
			lengthMenu: '_MENU_ page',
        },
    }
   );

  BASE_URL = {!! json_encode(url('/')) !!}
  $('#enquiry-info-table').on('click', '.lead-delete', function (event) {
                event.preventDefault();
                var leadId = $(this).attr('lead-id');
                var destinationPath = BASE_URL + '/user/lead-import/' + leadId;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                }).done(function (res) {
                                    if (res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });

                               } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                    .fail(function (err) {
                                    })
                                    .always(function (com) {
                                      window.location.reload();
                                    //    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                    });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });
</script>
@endpush


