@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="add-y-team">
                    <div class="dashboard-row">
                          <div class="y-team-header y-team-header-V2">
                             <a href="{{ url('user/gl-connect') }}">
                                
                                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                      <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z" fill="#4D5459"/>
                                   </svg>
                                   WebHook
                               
                             </a>
                          </div>
                       </div>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li>
                                    <a class="main-round-btn bdr1 bdr-blue mg-lft-25 clr-white"
                                       data-toggle="modal" data-target="#web-hook-create-modal"
                                       id="addWebHook" href="#"><i class="fa fa-plus"></i> WebHook</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            {{-- @include ('backend.layout.connectsidebar') --}}
            @include ('backend.layout.sidebar-v2.glconnect-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table">
                    <table id="webhook_data_table" class="table table-striped table-bordered nowrap table-custom"
                        cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Name</th>
                            <th>Url</th>
                            <th>Added By</th>
                            <th width="10%">Actions</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </main>
    <!------------ /update modal/  -------------->
    <div class="modal fade" id="web-hook-create-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="webhookAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add WebHook</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Name</label>
                            <input type="text" name="name" autocomplete="off" class="form-control" placeholder="Name"
                                   required>
                            <span class="error name"></span>
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Url</label>
                            <input type="text" name="url" autocomplete="off" class="form-control" placeholder="Url"
                                   required>
                            <span class="error url"></span>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <button class="main-round-btn"><i class="fa fa-plus"></i> Add</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal fade" id="edit_modal_web_hook" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="webhookEdit" enctype="multipart/form-data">
            {{method_field('PUT')}}
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Webhook</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Name</label>
                            <input type="text" name="name" class="form-control" disabled id="web_name" placeholder="Name">
                            <input type="hidden" name="_method" value="PUT" class="form-control" placeholder="">
                            <input type="hidden" name="id" class="form-control" placeholder=""
                                   id="webhook_id">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Url</label>
                            <input type="text" name="url" class="form-control" id="web_url" placeholder="Url">
                            <span class="error url"></span>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button class="main-round-btn">Update</button>
                    </div>
                </div>
            </div>
        </form>
    </div>


@endsection
@push('footer.script')
    <script type="text/javascript">
        function readURL1(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $('#profile-img-tag').attr('src', e.target.result);
                    $('#profile-img-tag').hide();
                    $('#profile-img-tag').fadeIn(650);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }

        function readURL(input) {

            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $('#blah').attr('src', e.target.result);
                    $('#blah').hide();
                    $('#blah').fadeIn(650);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }


        $(document).ready(function () {
            BASE_URL = {!! json_encode(url('/')) !!}
            $('.error').hide();
            /*-----Get Data------*/
            $('#webhook_data_table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                ajax: BASE_URL + '/user/webhook-data',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'name', name: 'name'},
                    {data: 'url', name: 'url'},
                    {data: 'added_by', name: 'added_by'},
                    {data: 'show', name: 'show', orderable: false, searchable: false}
                ],
            });
            /*-----Add------*/
            $(document).on('submit', '#webhookAdd', function (event) {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                $.ajax({
                    url: BASE_URL + '/user/webhook',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        $('#web-hook-create-modal').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                        $('#webhook_data_table').DataTable().ajax.reload(null, false);

                    } else if (res.status == 'exists') {
                        $('#web-hook-create-modal').modal('toggle');
                        $.alert({
                            title: 'Failed',
                            type: 'red',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                }).always(function (com) {
                    $('#webhook_data_table').DataTable().ajax.reload(null, false);
                });
            });
            /*-----Edit------*/
            $(document).on('submit', '#webhookEdit', function (event) {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                var id = $('#webhook_id').val();
                $.ajax({
                    url: BASE_URL + '/user/webhook/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        $('#edit_modal_web_hook').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                }).always(function (com) {
                    $('#webhook_data_table').DataTable().ajax.reload(null, false);
                });
            });

            $('#webhook_data_table').on('click', '.webhook-delete', function (event) {
                event.preventDefault();
                var id = $(this).attr('web_hook_id');
                var destinationPath = BASE_URL + '/user/webhook/' + id;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                }).done(function (res) {
                                    if (res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                    .fail(function (err) {
                                    })
                                    .always(function (com) {
                                        $('#webhook_data_table').DataTable().ajax.reload(null, false);
                                    });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
            /*-----View------*/
            $('#webhook_data_table').on('click', '.web_hook', function (event) {
                var webhookId = $(this).attr('data-webhook-id');
                $.ajax({
                    url: BASE_URL + '/user/webhook/' + webhookId,
                    type: 'GET',
                    dataType: 'JSON',
                }).done(function (res) {
                    if (res.status == "success") {
                        var data = res.data;
                        $("#web_name").val(data.name);
                        $("#web_url").val(data.url);
                        $('#webhook_id').val(data.id);
                    }
                }).fail(function (err) {
                }).always(function (com) {
                    $('#webhook_data_table').DataTable().ajax.reload(null, false);
                });
            });
        });

    </script>
@endpush
