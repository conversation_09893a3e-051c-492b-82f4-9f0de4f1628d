<!--
    Date Range Picker Component
    Author: Riyas
    Params : start_date => date in yyyy-mm-dd format Example: 2021-01-31
             id => string Example: all_date
-->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
<div class="input-group with-addon-icon-left" id="range_date">
    <input type="text" class="form-control date_picker"
            placeholder="Date" name="{{$id}}" autocomplete="off"
            id="{{$id}}">
    <span class="input-group-append">
            <span class="input-group-text">
                <i class="fa fa-calendar"></i>
            </span>
    </span>
    <div class="invalid-feedback"></div>
</div>
@push('footer.script')
<script type="text/javascript" src="{{ url('backend/js/daterangepicker.js')}}"></script>
<script>
    var start_dt = @json($start_date == '' ? today()->subDays(29)->format('m-d-Y') : $start_date);

    var start = moment(start_dt);
    var end = moment();

    function cb(start, end) {
        $('#{{$id}} span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
    }

    $('#{{$id}}').daterangepicker({
        startDate: start,
        endDate: end,
        showDropdowns:true,
        locale: {
            format: 'YYYY-MM-DD'
        },
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            'All time': [moment('2015-01-01'), moment()]
        }
    }, cb);
</script>
@endpush