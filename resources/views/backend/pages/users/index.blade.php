@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
<style type="text/css">
    .val-error
    {
        color:red;
    }
 </style>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Users</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.admin-users-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
               
                    <div class="row">
                    
                        <div class="col-lg-12">
                            <div class="tbl-h-details">
                                
                                  
                                        <!-- <button class="btn main-round-btn" type="submit" >
                                            <span class="la la-check ks-icon"></span>
                                            <span class="fa fa-download"> Export</span>
                                        </button>
                               -->
                               <form method="post" action="{{ url('admin/export-user-details') }}"  id="Add" enctype="multipart/form-data">

                                    
                                   

                                 
                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    <div class="form-group" id="short_code_save">
                                        <div class="">
                                            <!-- <button class="main-round-btn" type="submit" >
                                                <i class="fa fa-download"></i> Export
                                            </button> -->
                                        </div>
                                    </div>
                                </form> 
                            </div>    
                        </div> 
                    
                    </div>  
                 <div class="gl-table">
                        <table id="enquiry-info-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                            <thead>
                                <tr>
                                    <th>Sl No</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Number</th>
                                    <th>Category</th>
                                    <th>Last Login</th>
                                    <th>Customer Id</th>
                                    <th>User Id</th>
                                    <th>Scratch Balance</th>
                                    <th>Scratch Total Limit</th>
                                    <th>Add new scratch limit</th>
                                    <th width="10%">Actions</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
            </div>
        </div>
    </main>
@endsection 
@push('footer.script')
{{-- <link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css" />
<script type="text/javascript" src="https://cdn.datatables.net/buttons/1.7.1/js/dataTables.buttons.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js" ></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/1.7.1/js/buttons.html5.min.js" ></script> --}}
 <script type="text/javascript">
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};

            $('.error').hide();

            // $('#ks-izi-modal-large').iziModal({
            //     autoOpen: false,
            //     padding: 20,
            //     headerColor: '#3a529b',
            //     restoreDefaultContent: true,
            //     title: "Testimonial",
            //     fullscreen: true,
            //     subtitle: 'View user details',
            //     transitionIn: 'fadeInDown'
            // });

            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, 'All'],
                ],
                columnDefs: [{
                    "width": "150px",
                    "targets": [0, 1,2,3,4]
                },

                ],
                // dom: 'lBfrtip',
                // buttons: [
                //     'excelHtml5',
                //     // 'csvHtml5'
                //     ],
                ajax: BASE_URL + '/admin/get-total-users',
                columns: [
                    {data: 'slno',name: 'slno'},
                    {data: 'vchr_user_name',name: 'vchr_user_name'},
                    {data: 'email',name: 'email'},
                    {data: 'vchr_user_mobile',name: 'vchr_user_mobile'},
                    {data: 'category',name: 'category'},
                    {data: 'datetime_last_login',name: 'datetime_last_login'},
                    {data: 'customer_id',name: 'customer_id'},
                    {data: 'pk_int_user_id',name: 'pk_int_user_id'},
                    {data: 'scratches_balance',name: 'scratches_balance'},
                    {data: 'max_scratches_allowed',name: 'max_scratches_allowed'},
                    {data: 'addLimit',name: 'addLimit',orderable: false,searchable: false},
                    {data: 'show',name: 'show',orderable: false,searchable: false}
                ],
                
            });









            $('#enquiry-info-table').on('click', '.user-act', function(event) {
                event.preventDefault();
                if($(this).hasClass('btn-activate')) {
                    var url = BASE_URL + '/admin/user-deactivate/';
                    action = 'Deactivate';
                } else {
                    var url = BASE_URL + '/admin/user-activate/';
                    action = 'Activate';
                }
                var feedbackId = $(this).attr('user-id');
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

        });  
    </script>
@endpush
