@extends('backend.layout.master')
@section('page-header')
<style>
   .category_div .select2-container{
   width: 80% !important;
   }
</style>
@endsection
@section('content')
<main class="main-wrapper">
   <div class="task-panel">
      {{-- <div class="row justify-content-between ">
         <div class="">
            <h5></h5>
         </div>
         <div>
            <div class="task-nav">
               <div class="dropdown-navigation">
                  <ul>
                     <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3 ks-izi-modal-trigger"  data-target="#ks-izi-modal-large1" id="addEnquiry" name="button" title="Click here to edit subscription messages">Add Enquiry</button>
                  </ul>
               </div>
            </div>
         </div>
      </div> --}}
   </div>
   <div class="layout-wrapper">
      <!--   / Side menu included /  -->
      @include ('backend.layout.sidebar-v2.admin-users-sidebar')
      <div class="content-section">
        <div class="add-y-team">
            <div class="row dashboard-row">
                <div class="col-sm-12 y-team-header y-team-header-V2">
                    <a href="{{ url()->previous() }}">
                    
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                            <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z" fill="#4D5459"/>
                        </svg>
                        USER ACCOUNT
                    </a>
                </div>
            </div>
        </div>
         <!--  /Your content goes here/ -->
         <section class="admin-user-sec bg-white">
            <div class="row">
               <div class="user-info">
                  <div class="col-lg-4 col-md-5 pad-right">
                     <div class="content-1" >
                        @if($users->vchr_logo)
                        <div class="user-image">
                           <img src="{{ url($users->vchr_logo)}}" alt="logo"/>
                        </div>
                        @else
                        <div class="user-image">
                           <img src="{{ url('backend/images/user-image.png')}}" alt="logo"/>
                        </div>
                        @endif
                        <div class="user-name">
                           <h5>{{ strtoupper($users->vchr_user_name)}}</h5>
                           <p></p>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-8 col-md-7 pad-left">
                     <div class="content-2">
                        <ul>
                           <li>
                              <h5></h5>
                              <P>TOTAL MISSED CALLS</P>
                           </li>
                           <li>
                              <h5></h5>
                              <P>TOTAL SMS</P>
                           </li>
                           <li>
                              <h5></h5>
                              <P> SMS Balance</P>
                           </li>
                           <li>
                              <h5>{{$senderid}}</h5>
                              <P>TOTAL SENDER ID</P>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row">
               <section class="admin-user-tab-sec">
                  <div class="sm-scroll">
                     <ul class="nav nav-tabs" id="myTab" role="tablist">
                        <li class="nav-item">
                           <a class="nav-link active" id="overview-tab" data-toggle="tab" href="#overview" role="tab" aria-controls="overview" aria-selected="true">Overview</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="plan-tab" data-toggle="tab" href="#plan" role="tab" aria-controls="plan" aria-selected="false">Plan</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="missedcall-tab" data-toggle="tab" href="#missedcall" role="tab" aria-controls="missedcall" aria-selected="false">Missedcalls</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="senderid-tab" data-toggle="tab" href="#senderid" role="tab" aria-controls="senderid" aria-selected="false">Sender ID</a>
                        </li>
                        {{--
                        <li class="nav-item">--}}
                           {{--<a class="nav-link" id="sms-balance-tab" data-toggle="tab" href="#sms-balance" role="tab" aria-controls="sms-balance" aria-selected="false">SMS Balance</a>--}}
                           {{--
                        </li>
                        --}}
                        <li class="nav-item">
                           <a class="nav-link" id="auto-respond-tab" data-toggle="tab" href="#autorespond" role="tab" aria-controls="autorespond" aria-selected="false">Auto Respond</a>
                        </li>
                        {{-- 
                        <li class="nav-item">
                           <a class="nav-link" id="settings-tab" data-toggle="tab" href="#settings" role="tab" aria-controls="settings" aria-selected="false">Settings</a>
                        </li>
                        --}}
                        <li class="nav-item">
                           <a class="nav-link" id="api-settings-tab" data-toggle="tab" href="#api-settings" role="tab" aria-controls="api-settings" aria-selected="false">SMS Api Settings</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="smstemplate-tab" data-toggle="tab" href="#smstemplate" role="tab" aria-controls="smstemplate" aria-selected="false">SMS Templates</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="apitemplate-tab" data-toggle="tab" href="#apitemplate" role="tab" aria-controls="apitemplate" aria-selected="false">API Templates</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="dlt-entity-tab" data-toggle="tab" href="#dlt-entity" role="tab" aria-controls="dlt-entity" aria-selected="false">DLT Entities</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="dlt-header-tab" data-toggle="tab" href="#dlt-header" role="tab" aria-controls="dlt-header" aria-selected="false">DLT Headers</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="dlt-template-tab" data-toggle="tab" href="#dlt-template" role="tab" aria-controls="dlt-template" aria-selected="false">DLT Templates</a>
                        </li>
                        {{-- 
                        <li class="nav-item">
                           <a class="nav-link" id="sub-sms-tab" data-toggle="tab" href="#sub-sms" role="tab" aria-controls="sub-sms" aria-selected="false">Subscription SMS Templates</a>
                        </li>
                        --}}
                        <li class="nav-item">
                           <a class="nav-link" id="staff-tab" data-toggle="tab" href="#staff-details" role="tab" aria-controls="staff-details" aria-selected="false">Agents</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="scrath-tab" data-toggle="tab" href="#scratch-details" role="tab" aria-controls="scratch-details" aria-selected="false">GL Scratch</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="promo-tab" data-toggle="tab" href="#promo-details" role="tab" aria-controls="promo-details" aria-selected="false">GL Promo</a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" id="verification-tab" data-toggle="tab" href="#verification-details" role="tab" aria-controls="verification-details" aria-selected="false">GL Verification</a>
                        </li>
                     </ul>
                  </div>
                  <div class="tab-content" id="myTabContent">
                     <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                        <div class="aduser-tab-inner">
                           <div class="row">
                              <div class="col-lg-12">
                                 <div class="user-details-block">
                                    <div class="heading">
                                       <h4>User Details</h4>
                                    </div>
                                    <div class="user-details">
                                       <div class="row user-item">
                                          <div class="col-md-4">
                                             <p class="item-n">Name</p>
                                          </div>
                                          <div class="col-md-7">
                                             <p class="item-desc">{{ strtoupper($users->vchr_user_name)}}</p>
                                          </div>
                                       </div>
                                       <div class="row user-item">
                                          <div class="col-md-4">
                                             <p class="item-n">Email </p>
                                          </div>
                                          <div class="col-md-7">
                                             <p class="item-desc"><label>{{ $users->email}} </label> &nbsp;<a href="#" data-toggle="modal" data-target="#editEmModal" id="editEm"><i class="fa fa-pencil"></i></a></p>
                                          </div>
                                       </div>
                                       <div class="row user-item">
                                          <div class="col-md-4">
                                             <p class="item-n">Mobile Number</p>
                                          </div>
                                          <div class="col-md-7">
                                             <p class="item-desc"><label>{{ $users->vchr_user_mobile}}</label>&nbsp;<a href="#" data-toggle="modal" data-target="#editphmodal" id="editph"><i class="fa fa-pencil"></i></a></p>
                                          </div>
                                       </div>
                                       <div class="row user-item">
                                          <div class="col-md-4">
                                             <p class="item-n">IMEI</p>
                                          </div>
                                          <div class="col-md-7">
                                             <p class="item-desc">{{ $users->vchr_user_imei}}</p>
                                          </div>
                                       </div>
                                       <div class="row user-item">
                                          <div class="col-md-4">
                                             <p class="item-n">Date of Registration</p>
                                          </div>
                                          <div class="col-md-7">
                                             <p class="item-desc"> {{ $users->created_at}}</p>
                                          </div>
                                       </div>
                                       <div class="row user-item">
                                          <div class="col-md-4">
                                             <p class="item-n">Date of Expiry </p>
                                          </div>
                                          {{--
                                          <div class="col-md-7">
                                             --}}
                                             {{--
                                             <p class="item-desc">{{ $users->subscription->date_plan_to_date}}</p>
                                             --}}
                                             {{--
                                          </div>
                                          --}}
                                       </div>
                                       <div class="row user-item">
                                          <div class="col-md-4">
                                             <p class="item-n">Category </p>
                                          </div>
                                          <div class="col-md-7">
                                             <p class="item-desc"><label>{{ $users->category ? $users->category->userCategory->category_name : "" }} </label> &nbsp;<a href="#" data-toggle="modal" data-target="#editCategModal" id="editCateg"><i class="fa fa-pencil"></i></a></p>
                                          </div>
                                       </div>
                                       <div class="row user-item">
                                          <div class="col-md-4">
                                             <p class="item-n">Account Status</p>
                                          </div>
                                          <div class="col-md-7">
                                             <p class="item-desc">@if($users->int_status==1)Active @else Inactive @endif</p>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="plan" role="tabpanel" aria-labelledby="plan-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner gl-table">
                                 <table id="plan-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Plan name</th>
                                          <th>Plan amount</th>
                                          <th>Grand Total</th>
                                          <th>Expiry Date</th>
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="missedcall" role="tabpanel" aria-labelledby="plan-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner gl-table">
                                 <table id="missedcall-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Mobile Number</th>
                                          <th>Missedcall Date $ Time</th>
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="senderid" role="tabpanel" aria-labelledby="senderid-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner gl-table">
                                 <table id="senderid-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Sender id Name</th>
                                          <th>Sender id</th>
                                          <th width="10%">Actions</th>
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="sms-balance" role="tabpanel" aria-labelledby="sms-balance-tab">
                        <div class="aduser-tab-inner">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner" style="padding-top:10px; align-content: center">
                                 <div class="row">
                                    <div class="col-sm-6">
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="autorespond" role="tabpanel" aria-labelledby="plan-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner gl-table">
                                 <table id="autorespond-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Username</th>
                                          <th>Auto responde code</th>
                                          <th>Auto responde  message</th>
                                          <th width="10%">Actions</th>
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                        <div class="aduser-tab-inner">
                           <div class="row">
                              <div class="aduser-tab-inner">
                                 content 7
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="api-settings" role="tabpanel" aria-labelledby="api-settings-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              {{--  <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3 ks-izi-modal-trigger5" data-target="#ks-izi-modal-large5" data-toggle="modal" id="addTemplate" name="button">Add New Template</button> --}}
                              <div class="aduser-tab-inner gl-table">
                                 <table id="api-settings-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Sms Route</th>
                                          <th>Domain</th>
                                          <th>Credentials</th>
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="smstemplate" role="tabpanel" aria-labelledby="smstemplate-tab">
                        <div class="aduser-tab-block">
                           <div class="row">
                              <div class="aduser-tab-inner gl-table">
                                 <table id="smstemplate-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Username</th>
                                          <th>SMS Template Name</th>
                                          <th>SMS Template</th>
                                          <th>Default</th>
                                          <th width="10%">Actions</th>
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="apitemplate" role="tabpanel" aria-labelledby="apitemplate-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner gl-table">
                                 <table id="apitemplate-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Username</th>
                                          <th>API Template Name</th>
                                          <th>API Template</th>
                                          <th>Default</th>
                                          <th width="10%">Actions</th>
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <!-- -->
                     <div class="tab-pane fade" id="sub-sms" role="tabpanel" aria-labelledby="sub-sms-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner">
                                 <table id="sub-sms-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Username</th>
                                          <th>Subscription SMS</th>
                                          <th>Unsubscription SMS</th>
                                          <th width="10%">Actions</th>
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="staff-details" role="tabpanel" aria-labelledby="staff-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner gl-table">
                                 <table id="staff-details-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Agent Name</th>
                                          <th>Phone</th>
                                          <th>Email</th>
                                          <!-- <th width="10%">Actions</th> -->
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="scratch-details" role="tabpanel" aria-labelledby="staff-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner gl-table">
                                 <table id="scratch-details-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Customer Name</th>
                                          <th>Mobile Number</th>
                                          <!-- <th>Offer</th> -->
                                          <!-- <th width="10%">Actions</th> -->
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="promo-details" role="tabpanel" aria-labelledby="staff-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner gl-table">
                                 <table id="promo-details-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Mobil Number</th>
                                          <th>Call Date & Time</th>
                                          <!-- <th>Offer</th> -->
                                          <!-- <th width="10%">Actions</th> -->
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="tab-pane fade" id="verification-details" role="tabpanel" aria-labelledby="staff-tab">
                        <div class="aduser-tab-block">
                           <div class="row mg-bt-15">
                              <div class="aduser-tab-inner gl-table">
                                 <table id="verification-details-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                    <thead>
                                       <tr>
                                          <th>Sl No</th>
                                          <th>Mobil Number</th>
                                          <th>Call Date & Time</th>
                                          <!-- <th>Offer</th> -->
                                          <!-- <th width="10%">Actions</th> -->
                                       </tr>
                                    </thead>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </section>
            </div>
         </section>
      </div>
   </div>
</main>
{{-- Senderid Rejection --}}
<div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
   aria-hidden="true">
   <form  id="senderidRejection" enctype="multipart/form-data">
      <div class="modal-dialog" role="document">
         <div class="modal-content">
            <div class="modal-header text-center">
               <h4 class="modal-title w-100 font-weight-bold g-clr">SENDERID REJECTION</h4>
               <button type="button" class="close" data-dismiss="modal" aria-label="Close">
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
            <div class="modal-body mx-3">
               <div class="md-form mb-1">
                  <label for="default-input" class="form-control-label">Reason</label>
                  <textarea name="reason" class="form-control" placeholder="Description" rows="10" required="required"></textarea>
                  <input type="hidden" name="id" id="pk_int_sender_id" value="">
                  <span class="error  vchr_description"></span>
               </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
               <input type="hidden" name="_token" value="{{ csrf_token() }}">
               <button class="btn btn-deep-orange demo-btn">Add</button>
            </div>
         </div>
      </div>
   </form>
</div>
{{-- Autorespond Rejection --}}
<div class="modal fade" id="ks-izi-modal-large2" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
   aria-hidden="true">
   <form  id="autorespondRejection" enctype="multipart/form-data">
      <div class="modal-dialog" role="document">
         <div class="modal-content">
            <div class="modal-header text-center">
               <h4 class="modal-title w-100 font-weight-bold g-clr">AUTORESPOND REJECTION</h4>
               <button type="button" class="close" data-dismiss="modal" aria-label="Close">
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
            <div class="modal-body mx-3">
               <div class="md-form mb-1">
                  <label for="default-input" class="form-control-label">Reason</label>
                  <textarea name="reason" class="form-control" placeholder="Description" rows="10" required="required"></textarea>
                  <input type="hidden" name="id" id="id4">
                  <span class="error  vchr_description"></span>
               </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
               <input type="hidden" name="_token" value="{{ csrf_token() }}">
               <button class="btn btn-deep-orange demo-btn">Add</button>
            </div>
         </div>
      </div>
   </form>
</div>
{{-- Sms Template --}}
<div class="modal fade" id="ks-izi-modal-large3" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
   aria-hidden="true">
   <form  id="smstemplateRejection" enctype="multipart/form-data">
      <div class="modal-dialog" role="document">
         <div class="modal-content">
            <div class="modal-header text-center">
               <h4 class="modal-title w-100 font-weight-bold g-clr">SMS TEMPLATE REJECTION</h4>
               <button type="button" class="close" data-dismiss="modal" aria-label="Close">
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
            <div class="modal-body mx-3">
               <div class="md-form mb-1">
                  <label for="default-input" class="form-control-label">Reason</label>
                  <textarea name="reason" class="form-control" placeholder="Description" rows="10" required="required"></textarea>
                  <input type="text" name="id" id="pk_int_sms_template_id">
                  <span class="error  vchr_description"></span>
               </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
               <input type="hidden" name="_token" value="{{ csrf_token() }}">
               <button class="btn btn-deep-orange demo-btn">Add</button>
            </div>
         </div>
      </div>
   </form>
</div>
{{-- SUBSCRIPTION SMS TEMPLATE --}}
<div class="modal fade" id="ks-izi-modal-large4" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
   aria-hidden="true">
   <form  id="subscriptionsmstemplateRejection" method="post" enctype="multipart/form-data">
      <div class="modal-dialog" role="document">
         <div class="modal-content">
            <div class="modal-header text-center">
               <h4 class="modal-title w-100 font-weight-bold g-clr">SUBSCRIPTION SMS TEMPLATE REJECTION</h4>
               <button type="button" class="close" data-dismiss="modal" aria-label="Close">
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
            <div class="modal-body mx-3">
               <div class="md-form mb-1">
                  <label for="default-input" class="form-control-label">Reason</label>
                  <textarea name="reason" class="form-control" placeholder="Description" rows="10" required="required"></textarea>
                  <input type="hidden" name="id" id="pk_int_subscription_message_id">
                  <span class="error  vchr_description"></span>
               </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
               <input type="hidden" name="_token" value="{{ csrf_token() }}">
               <button class="btn btn-deep-orange demo-btn">Add</button>
            </div>
         </div>
      </div>
   </form>
</div>
{{-- SMS API SETTINGS --}}
<div class="modal fade" id="ks-izi-modal-large5" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
   aria-hidden="true">
   <form  id="addTemplate" enctype="multipart/form-data">
      <div class="modal-dialog" role="document">
         <div class="modal-content">
            <div class="modal-header text-center">
               <h4 class="modal-title w-100 font-weight-bold g-clr">SMS API TeMPLATE</h4>
               <button type="button" class="close" data-dismiss="modal" aria-label="Close">
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
            <div class="modal-body mx-3">
               <div class="md-form mb-1">
                  <label for="default-input" class="form-control-label">Select Route</label>
                  <select class="form-control" data-placeholder="Select" name="fk_int_sms_route_id" required>
                     <option value="">Select Route</option>
                     @foreach($route as $key => $route1)
                     <option value="{{ $route1->pk_int_sms_route_id }}">{{ $route1->vchr_sms_route }}</option>
                     @endforeach
                  </select>
                  <span class="error fk_int_sms_route_id"></span>
                  <input type="hidden" name="id" id="pk_int_sender_id" value="">
               </div>
               <p style="color: red;">Use these tags : For message - <b>|MESSAGE|</b>, for Mobile - <b>|MOBILE|</b>, for Route - <b>|ROUTE|</b>, for Senderid - <b>|SENDERID|</b></p>
               <div class="md-form mb-1">
                  <label for="default-input" class="form-control-label">Message Api</label>
                  <textarea name="text_sms_api_template_url" class="form-control" placeholder="" rows="10" required></textarea>
                  <span class="error text_sms_api_template_url"></span>
               </div>
               <p style="color: red;">Use these tags : For messageid - <b>|MESSAGEID|</b></p>
               <div class="md-form mb-1">
                  <label for="default-input" class="form-control-label">Status Api</label>
                  <textarea name="text_sms_api_template_status_url" class="form-control" placeholder="" rows="10" required></textarea>
                  <span class="error text_sms_api_template_status_url"></span>
               </div>
               <p style="color: red;">Use these tags : for Route -<b>|ROUTE|</b></p>
               <div class="md-form mb-1">
                  <label for="default-input" class="form-control-label">Balance Api</label>
                  <textarea name="text_sms_api_template_balance_url" class="form-control" placeholder="" rows="10" required></textarea>
                  <span class="error text_sms_api_template_balance_url"></span>
               </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
               <input type="hidden" name="_token" value="{{ csrf_token() }}">
               <button class="btn btn-deep-orange demo-btn">Add</button>
            </div>
         </div>
      </div>
   </form>
</div>
<!-- edit phone number modal -->
<div class="modal" tabindex="-1" role="dialog" id="editphmodal">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
         <div class="alert alert-danger" style="display:none"></div>
         <div class="modal-header">
            <h5 class="modal-title">Edit Phone Number</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            </button>
         </div>
         <div class="modal-body">
            <div class="form-group">
               <label>Phone Number</label>
               <div class="row" style="margin-left: 1px;">
                  <select name="country_code" id="country_code" class="form-control col-md-6 select2" required>
                     <option value="{{$user->countrycode}}">{{$user->countrycode}}</option>
                     <option data-countryCode="US" value="44">UK (+44)</option>
                     <option data-countryCode="DZ" value="213">Algeria (+213)</option>
                     <option data-countryCode="AD" value="376">Andorra (+376)</option>
                     <option data-countryCode="AO" value="244">Angola (+244)</option>
                     <option data-countryCode="AI" value="1264">Anguilla (+1264)</option>
                     <option data-countryCode="AG" value="1268">Antigua &amp; Barbuda
                        (+1268)
                     </option>
                     <option data-countryCode="AR" value="54">Argentina (+54)</option>
                     <option data-countryCode="AM" value="374">Armenia (+374)</option>
                     <option data-countryCode="AW" value="297">Aruba (+297)</option>
                     <option data-countryCode="AU" value="61">Australia (+61)</option>
                     <option data-countryCode="AT" value="43">Austria (+43)</option>
                     <option data-countryCode="AZ" value="994">Azerbaijan (+994)</option>
                     <option data-countryCode="BS" value="1242">Bahamas (+1242)</option>
                     <option data-countryCode="BH" value="973">Bahrain (+973)</option>
                     <option data-countryCode="BD" value="880">Bangladesh (+880)</option>
                     <option data-countryCode="BB" value="1246">Barbados (+1246)</option>
                     <option data-countryCode="BY" value="375">Belarus (+375)</option>
                     <option data-countryCode="BE" value="32">Belgium (+32)</option>
                     <option data-countryCode="BZ" value="501">Belize (+501)</option>
                     <option data-countryCode="BJ" value="229">Benin (+229)</option>
                     <option data-countryCode="BM" value="1441">Bermuda (+1441)</option>
                     <option data-countryCode="BT" value="975">Bhutan (+975)</option>
                     <option data-countryCode="BO" value="591">Bolivia (+591)</option>
                     <option data-countryCode="BA" value="387">Bosnia Herzegovina
                        (+387)
                     </option>
                     <option data-countryCode="BW" value="267">Botswana (+267)</option>
                     <option data-countryCode="BR" value="55">Brazil (+55)</option>
                     <option data-countryCode="BN" value="673">Brunei (+673)</option>
                     <option data-countryCode="BG" value="359">Bulgaria (+359)</option>
                     <option data-countryCode="BF" value="226">Burkina Faso (+226)
                     </option>
                     <option data-countryCode="BI" value="257">Burundi (+257)</option>
                     <option data-countryCode="KH" value="855">Cambodia (+855)</option>
                     <option data-countryCode="CM" value="237">Cameroon (+237)</option>
                     <option data-countryCode="CA" value="1">Canada (+1)</option>
                     <option data-countryCode="CV" value="238">Cape Verde Islands
                        (+238)
                     </option>
                     <option data-countryCode="KY" value="1345">Cayman Islands (+1345)
                     </option>
                     <option data-countryCode="CF" value="236">Central African Republic
                        (+236)
                     </option>
                     <option data-countryCode="CL" value="56">Chile (+56)</option>
                     <option data-countryCode="CN" value="86">China (+86)</option>
                     <option data-countryCode="CO" value="57">Colombia (+57)</option>
                     <option data-countryCode="KM" value="269">Comoros (+269)</option>
                     <option data-countryCode="CG" value="242">Congo (+242)</option>
                     <option data-countryCode="CK" value="682">Cook Islands (+682)
                     </option>
                     <option data-countryCode="CR" value="506">Costa Rica (+506)</option>
                     <option data-countryCode="HR" value="385">Croatia (+385)</option>
                     <option data-countryCode="CU" value="53">Cuba (+53)</option>
                     <option data-countryCode="CY" value="90392">Cyprus North (+90392)
                     </option>
                     <option data-countryCode="CY" value="357">Cyprus South (+357)
                     </option>
                     <option data-countryCode="CZ" value="42">Czech Republic (+42)
                     </option>
                     <option data-countryCode="DK" value="45">Denmark (+45)</option>
                     <option data-countryCode="DJ" value="253">Djibouti (+253)</option>
                     <option data-countryCode="DM" value="1809">Dominica (+1809)</option>
                     <option data-countryCode="DO" value="1809">Dominican Republic
                        (+1809)
                     </option>
                     <option data-countryCode="EC" value="593">Ecuador (+593)</option>
                     <option data-countryCode="EG" value="20">Egypt (+20)</option>
                     <option data-countryCode="SV" value="503">El Salvador (+503)
                     </option>
                     <option data-countryCode="GQ" value="240">Equatorial Guinea (+240)
                     </option>
                     <option data-countryCode="ER" value="291">Eritrea (+291)</option>
                     <option data-countryCode="EE" value="372">Estonia (+372)</option>
                     <option data-countryCode="ET" value="251">Ethiopia (+251)</option>
                     <option data-countryCode="FK" value="500">Falkland Islands (+500)
                     </option>
                     <option data-countryCode="FO" value="298">Faroe Islands (+298)
                     </option>
                     <option data-countryCode="FJ" value="679">Fiji (+679)</option>
                     <option data-countryCode="FI" value="358">Finland (+358)</option>
                     <option data-countryCode="FR" value="33">France (+33)</option>
                     <option data-countryCode="GF" value="594">French Guiana (+594)
                     </option>
                     <option data-countryCode="PF" value="689">French Polynesia (+689)
                     </option>
                     <option data-countryCode="GA" value="241">Gabon (+241)</option>
                     <option data-countryCode="GM" value="220">Gambia (+220)</option>
                     <option data-countryCode="GE" value="7880">Georgia (+7880)</option>
                     <option data-countryCode="DE" value="49">Germany (+49)</option>
                     <option data-countryCode="GH" value="233">Ghana (+233)</option>
                     <option data-countryCode="GI" value="350">Gibraltar (+350)</option>
                     <option data-countryCode="GR" value="30">Greece (+30)</option>
                     <option data-countryCode="GL" value="299">Greenland (+299)</option>
                     <option data-countryCode="GD" value="1473">Grenada (+1473)</option>
                     <option data-countryCode="GP" value="590">Guadeloupe (+590)</option>
                     <option data-countryCode="GU" value="671">Guam (+671)</option>
                     <option data-countryCode="GT" value="502">Guatemala (+502)</option>
                     <option data-countryCode="GN" value="224">Guinea (+224)</option>
                     <option data-countryCode="GW" value="245">Guinea - Bissau (+245)
                     </option>
                     <option data-countryCode="GY" value="592">Guyana (+592)</option>
                     <option data-countryCode="HT" value="509">Haiti (+509)</option>
                     <option data-countryCode="HN" value="504">Honduras (+504)</option>
                     <option data-countryCode="HK" value="852">Hong Kong (+852)</option>
                     <option data-countryCode="HU" value="36">Hungary (+36)</option>
                     <option data-countryCode="IS" value="354">Iceland (+354)</option>
                     <option data-countryCode="IN" value="91">India (+91)</option>
                     <option data-countryCode="ID" value="62">Indonesia (+62)</option>
                     <option data-countryCode="IR" value="98">Iran (+98)</option>
                     <option data-countryCode="IQ" value="964">Iraq (+964)</option>
                     <option data-countryCode="IE" value="353">Ireland (+353)</option>
                     <option data-countryCode="IL" value="972">Israel (+972)</option>
                     <option data-countryCode="IT" value="39">Italy (+39)</option>
                     <option data-countryCode="JM" value="1876">Jamaica (+1876)</option>
                     <option data-countryCode="JP" value="81">Japan (+81)</option>
                     <option data-countryCode="JO" value="962">Jordan (+962)</option>
                     <option data-countryCode="KZ" value="7">Kazakhstan (+7)</option>
                     <option data-countryCode="KE" value="254">Kenya (+254)</option>
                     <option data-countryCode="KI" value="686">Kiribati (+686)</option>
                     <option data-countryCode="KP" value="850">Korea North (+850)
                     </option>
                     <option data-countryCode="KR" value="82">Korea South (+82)</option>
                     <option data-countryCode="KW" value="965">Kuwait (+965)</option>
                     <option data-countryCode="KG" value="996">Kyrgyzstan (+996)</option>
                     <option data-countryCode="LA" value="856">Laos (+856)</option>
                     <option data-countryCode="LV" value="371">Latvia (+371)</option>
                     <option data-countryCode="LB" value="961">Lebanon (+961)</option>
                     <option data-countryCode="LS" value="266">Lesotho (+266)</option>
                     <option data-countryCode="LR" value="231">Liberia (+231)</option>
                     <option data-countryCode="LY" value="218">Libya (+218)</option>
                     <option data-countryCode="LI" value="417">Liechtenstein (+417)
                     </option>
                     <option data-countryCode="LT" value="370">Lithuania (+370)</option>
                     <option data-countryCode="LU" value="352">Luxembourg (+352)</option>
                     <option data-countryCode="MO" value="853">Macao (+853)</option>
                     <option data-countryCode="MK" value="389">Macedonia (+389)</option>
                     <option data-countryCode="MG" value="261">Madagascar (+261)</option>
                     <option data-countryCode="MW" value="265">Malawi (+265)</option>
                     <option data-countryCode="MY" value="60">Malaysia (+60)</option>
                     <option data-countryCode="MV" value="960">Maldives (+960)</option>
                     <option data-countryCode="ML" value="223">Mali (+223)</option>
                     <option data-countryCode="MT" value="356">Malta (+356)</option>
                     <option data-countryCode="MH" value="692">Marshall Islands (+692)
                     </option>
                     <option data-countryCode="MQ" value="596">Martinique (+596)</option>
                     <option data-countryCode="MR" value="222">Mauritania (+222)</option>
                     <option data-countryCode="YT" value="269">Mayotte (+269)</option>
                     <option data-countryCode="MX" value="52">Mexico (+52)</option>
                     <option data-countryCode="FM" value="691">Micronesia (+691)</option>
                     <option data-countryCode="MD" value="373">Moldova (+373)</option>
                     <option data-countryCode="MC" value="377">Monaco (+377)</option>
                     <option data-countryCode="MN" value="976">Mongolia (+976)</option>
                     <option data-countryCode="MS" value="1664">Montserrat (+1664)
                     </option>
                     <option data-countryCode="MA" value="212">Morocco (+212)</option>
                     <option data-countryCode="MZ" value="258">Mozambique (+258)</option>
                     <option data-countryCode="MN" value="95">Myanmar (+95)</option>
                     <option data-countryCode="NA" value="264">Namibia (+264)</option>
                     <option data-countryCode="NR" value="674">Nauru (+674)</option>
                     <option data-countryCode="NP" value="977">Nepal (+977)</option>
                     <option data-countryCode="NL" value="31">Netherlands (+31)</option>
                     <option data-countryCode="NC" value="687">New Caledonia (+687)
                     </option>
                     <option data-countryCode="NZ" value="64">New Zealand (+64)</option>
                     <option data-countryCode="NI" value="505">Nicaragua (+505)</option>
                     <option data-countryCode="NE" value="227">Niger (+227)</option>
                     <option data-countryCode="NG" value="234">Nigeria (+234)</option>
                     <option data-countryCode="NU" value="683">Niue (+683)</option>
                     <option data-countryCode="NF" value="672">Norfolk Islands (+672)
                     </option>
                     <option data-countryCode="NP" value="670">Northern Marianas (+670)
                     </option>
                     <option data-countryCode="NO" value="47">Norway (+47)</option>
                     <option data-countryCode="OM" value="968">Oman (+968)</option>
                     <option data-countryCode="PW" value="680">Palau (+680)</option>
                     <option data-countryCode="PA" value="507">Panama (+507)</option>
                     <option data-countryCode="PG" value="675">Papua New Guinea (+675)
                     </option>
                     <option data-countryCode="PY" value="595">Paraguay (+595)</option>
                     <option data-countryCode="PE" value="51">Peru (+51)</option>
                     <option data-countryCode="PH" value="63">Philippines (+63)</option>
                     <option data-countryCode="PL" value="48">Poland (+48)</option>
                     <option data-countryCode="PT" value="351">Portugal (+351)</option>
                     <option data-countryCode="PR" value="1787">Puerto Rico (+1787)
                     </option>
                     <option data-countryCode="QA" value="974">Qatar (+974)</option>
                     <option data-countryCode="RE" value="262">Reunion (+262)</option>
                     <option data-countryCode="RO" value="40">Romania (+40)</option>
                     <option data-countryCode="RU" value="7">Russia (+7)</option>
                     <option data-countryCode="RW" value="250">Rwanda (+250)</option>
                     <option data-countryCode="SM" value="378">San Marino (+378)</option>
                     <option data-countryCode="ST" value="239">Sao Tome &amp; Principe
                        (+239)
                     </option>
                     <option data-countryCode="SA" value="966">Saudi Arabia (+966)
                     </option>
                     <option data-countryCode="SN" value="221">Senegal (+221)</option>
                     <option data-countryCode="CS" value="381">Serbia (+381)</option>
                     <option data-countryCode="SC" value="248">Seychelles (+248)</option>
                     <option data-countryCode="SL" value="232">Sierra Leone (+232)
                     </option>
                     <option data-countryCode="SG" value="65">Singapore (+65)</option>
                     <option data-countryCode="SK" value="421">Slovak Republic (+421)
                     </option>
                     <option data-countryCode="SI" value="386">Slovenia (+386)</option>
                     <option data-countryCode="SB" value="677">Solomon Islands (+677)
                     </option>
                     <option data-countryCode="SO" value="252">Somalia (+252)</option>
                     <option data-countryCode="ZA" value="27">South Africa (+27)</option>
                     <option data-countryCode="ES" value="34">Spain (+34)</option>
                     <option data-countryCode="LK" value="94">Sri Lanka (+94)</option>
                     <option data-countryCode="SH" value="290">St. Helena (+290)</option>
                     <option data-countryCode="KN" value="1869">St. Kitts (+1869)
                     </option>
                     <option data-countryCode="SC" value="1758">St. Lucia (+1758)
                     </option>
                     <option data-countryCode="SD" value="249">Sudan (+249)</option>
                     <option data-countryCode="SR" value="597">Suriname (+597)</option>
                     <option data-countryCode="SZ" value="268">Swaziland (+268)</option>
                     <option data-countryCode="SE" value="46">Sweden (+46)</option>
                     <option data-countryCode="CH" value="41">Switzerland (+41)</option>
                     <option data-countryCode="SI" value="963">Syria (+963)</option>
                     <option data-countryCode="TW" value="886">Taiwan (+886)</option>
                     <option data-countryCode="TJ" value="7">Tajikstan (+7)</option>
                     <option data-countryCode="TH" value="66">Thailand (+66)</option>
                     <option data-countryCode="TG" value="228">Togo (+228)</option>
                     <option data-countryCode="TO" value="676">Tonga (+676)</option>
                     <option data-countryCode="TT" value="1868">Trinidad &amp; Tobago
                        (+1868)
                     </option>
                     <option data-countryCode="TN" value="216">Tunisia (+216)</option>
                     <option data-countryCode="TR" value="90">Turkey (+90)</option>
                     <option data-countryCode="TM" value="7">Turkmenistan (+7)</option>
                     <option data-countryCode="TM" value="993">Turkmenistan (+993)
                     </option>
                     <option data-countryCode="TC" value="1649">Turks &amp; Caicos
                        Islands
                        (+1649)
                     </option>
                     <option data-countryCode="TV" value="688">Tuvalu (+688)</option>
                     <option data-countryCode="UG" value="256">Uganda (+256)</option>
                     <!-- <option data-countryCode="GB" value="44">UK (+44)</option> -->
                     <option data-countryCode="UA" value="380">Ukraine (+380)</option>
                     <option data-countryCode="AE" value="971">United Arab Emirates
                        (+971)
                     </option>
                     <option data-countryCode="UY" value="598">Uruguay (+598)</option>
                     <option data-countryCode="US" value="1">USA (+1)</option>
                     <option data-countryCode="UZ" value="7">Uzbekistan (+7)</option>
                     <option data-countryCode="VU" value="678">Vanuatu (+678)</option>
                     <option data-countryCode="VA" value="379">Vatican City (+379)
                     </option>
                     <option data-countryCode="VE" value="58">Venezuela (+58)</option>
                     <option data-countryCode="VN" value="84">Vietnam (+84)</option>
                     <option data-countryCode="VG" value="84">Virgin Islands - British
                        (+1284)
                     </option>
                     <option data-countryCode="VI" value="84">Virgin Islands - US (+1340)
                     </option>
                     <option data-countryCode="WF" value="681">Wallis &amp; Futuna (+681)
                     </option>
                     <option data-countryCode="YE" value="969">Yemen (North)(+969)
                     </option>
                     <option data-countryCode="YE" value="967">Yemen (South)(+967)
                     </option>
                     <option data-countryCode="ZM" value="260">Zambia (+260)</option>
                     <option data-countryCode="ZW" value="263">Zimbabwe (+263)</option>
                  </select>
                  <input type="text" class="form-control col-md-9" name="ed_phone_number" id="ed_phone_number" value="{{$users->mobile}}" style="width: 70%;">
               </div>
            </div>
            <div class="modal-footer">
               <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
               <button  class="btn btn-success ajaxSubmit" data-tag="mobile" data-message="Phone Number">Save changes</button>
            </div>
         </div>
      </div>
   </div>
</div>
<!-- edit email modal -->
<div class="modal" tabindex="-1" role="dialog" id="editEmModal">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
         <div class="alert alert-danger" style="display:none"></div>
         <div class="modal-header">
            <h5 class="modal-title">Edit Email Id</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            </button>
         </div>
         <div class="modal-body">
            <div class="form-group">
               <label>Email Id</label>
               <div class="row" style="margin-left: 1px;">
                  <input type="email" class="form-control col-md-9" name="ed_email_id" id="ed_email_id" value="{{$users->email}}" style="width: 70%;">
               </div>
            </div>
            <div class="modal-footer">
               <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
               <button  class="btn btn-success ajaxSubmit" data-tag="email" data-message="Email Id">Save changes</button>
            </div>
         </div>
      </div>
   </div>
</div>
<!--Edit category model --->
<div class="modal" tabindex="-1" role="dialog" id="editCategModal">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
         <div class="alert alert-danger" style="display:none"></div>
         <div class="modal-header">
            <h5 class="modal-title">Edit Category</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            </button>
         </div>
         <div class="modal-body">
            <div class="form-group">
               <label>Category</label>
               <div class="row category_div" style="margin-left: 1px;">
                  <select name="category" id="category" class="form-control col-md-6 select2" style="100%">
                  @foreach ($categories as $item)
                  <option value="{{$item->id}}"  
                  @if ($users->category)
                  @if ($users->category->category_id == $item->id)
                  selected
                  @endif
                  @endif
                  >{{$item->category_name}}</option>
                  @endforeach
                  </select>
               </div>
            </div>
            <div class="modal-footer">
               <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
               <button  class="btn btn-success ajaxSubmit" data-tag="category" data-message="Category">Save changes</button>
            </div>
         </div>
      </div>
   </div>
</div>
<!-- DLT Entity  -->
@include('backend.user-pages.sms-dlt-entity.parts')
<!--  -->
<!-- DLT Header  -->
@include('backend.user-pages.sms-dlt-header.parts')
<!--  -->
<!-- DLT Template  -->
@include('backend.user-pages.sms-dlt-template.parts')
<!--  -->
@endsection
@push('footer.script')
<script type="text/javascript">
   $(document).ready(function() {
       var firstName = '{{$users->vchr_user_name}}';
   
       // console.log(firstName);
   
       var intials = firstName.charAt(0);
       var profileImage = $('#profileImage').text(intials);
   
       BASE_URL ='';
       var id='{{$id}}';
       //plan
       $('#plan-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1]
               },
               {
                    "width": "140px",
                    "targets": [2,3]
               }
           ],
             ajax: BASE_URL + '/admin/get-user-plan-subscription/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'name',
                       name: 'name'
                   },
                   {
               
               
               
                       data: 'amount',
                       name: 'amount'
                   },
                   {
                       data: 'total',
                       name: 'total',
               
                   },
               
               
                   {
                       data: 'duration',
                       name: 'duration',
               
                   },
               ],
       });
   
       //Missedcall
     $('#missedcall-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1]
               },
               {
                    "width": "140px",
                    "targets": [2]
               }
           ],
             ajax: BASE_URL + '/admin/get-missedcalls-details/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'vchr_mobile_number',
                       name: 'vchr_mobile_number'
                   },
   
                 
                   {
                       data: 'created_at',
                       name: 'created_at'
                   },
   
                 
               ],
       });
     //Senderid
       $('#senderid-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1]
               },
               {
                    "width": "140px",
                    "targets": [2,3]
               }
           ],
            ajax: BASE_URL + '/admin/get-user-senderid/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'vchr_sender_id_name',
                       name: 'vchr_sender_id_name'
                   },
                   {
   
   
                       data: 'vchr_sender_id',
                       name: 'vchr_sender_id'
                   },
   
                   
   
   
                   {
                       data: 'show',
                       name: 'show',
                       orderable: false,
                       searchable: false
                   }
               ],
       });
   
        $('#senderid-table').on('click', '.senderid-delete', function (event) {
               event.preventDefault();
               var feedbackId = $(this).attr('senderid-id');
               var destinationPath = BASE_URL + '/admin/user-senderid/' + feedbackId;
              $.confirm({
                   title: 'Deletion',
                   content: 'Are you sure you want to delete ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: destinationPath,
                                   type: 'DELETE',
                               })
                               .done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               })
                               .fail(function(err) {
                               })
                               .always(function(com) {
                                   $('#senderid-table').DataTable().ajax.reload(null, false);
                               });
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
           });
   
   
         $('#senderid-table').on('click', '.senderid-act', function(event) {
                 event.preventDefault();
                   var templateId = $(this).attr('senderid-id');
                   if($(this).hasClass('btn-activate')) {
                       $('#id').val(templateId);
                      $('#ks-izi-modal-large1').modal('toggle');
                   }
                   else
                   {
                       var url = BASE_URL + '/admin/senderid-activate/';
                       action = 'Approve';
   
                       var feedbackId = $(this).attr('senderid-id');
                       url = url + feedbackId;
               // console.log(url);
               $.confirm({
                   title: action,
                   content: 'Are you sure you want to ' + action + ' ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                    'confirm': {
                        text: 'Proceed',
                        btnClass: 'btn-info',
                        action: function () {
                           $.ajax({
                               url: url,
                               type: 'GET',
                           })
                           .done(function(res) {
                               if(res.status == 'success') {
                                   $.alert({
                                       title: 'Success',
                                       type: 'green',
                                       content: res.msg,
                                   });
                               } else {
                                   $.alert({
                                       title: 'Failed',
                                       type: 'red',
                                       content: res.msg,
                                   });
                               }
                           })
                           .fail(function(err) {
                           })
                           .always(function(com) {
                               $('#senderid-table').DataTable().ajax.reload(null, false);
                           });
                       }
                   },
                   cancel: function () {
                       $.alert('Operation <strong>canceled</strong>');
                   }
               }
           });
           }
           });
   
         $('#senderid-table').on('click', '.ks-izi-modal-trigger1', function(event) {
   
           var testimonialId = $(this).attr('senderid-id');
           $.ajax({
                   url: BASE_URL + '/admin/show-senderid/' + testimonialId,
                   type: 'GET',
                   dataType: 'JSON',
               })
           .done(function(res) {
               if(res.status == "success") {
                       data = res.data;
                       $('#pk_int_sender_id').val(data.pk_int_sender_id);
                       //$('#ks-izi-modal-large1').modal('toggle');
   
                   }
           })
           .fail(function(err) {
   
           })
           .always(function(com) {
               $('#senderid-table').DataTable().ajax.reload(null, false);
   
           });
    });
   
     $(document).on('submit', '#senderidRejection', function(event) {
               event.preventDefault();
               // $('').on('click', function (e) {
   
               // console.log(enquiryId);
               //$('.addReason1').prop('disabled','true');
               $.ajax({
                   url: BASE_URL + '/admin/senderid-deactivate',
                   type: 'POST',
                   dataType: 'JSON',
                   data:  new FormData(this),
                   contentType: false,
                   processData:false,
               })
               .done(function(res) {
                   //$('.addReason1').prop('disabled','false');
                   if(res.status == 'success'){
                       $('#ks-izi-modal-large1').modal('toggle');
                   swal(res.msg);
               }else{
                   $.alert({
                           title: 'Failed',
                           type: 'red',
                           content: res.msg,
                       });
   
               }
               })
               .fail(function() {
               })
                .always(function(com) {
               $('#senderid-table').DataTable().ajax.reload(null, false);
   
               });
           });
   
           //Autorespond
           $('#autorespond-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1]
               },
               {
                    "width": "140px",
                    "targets": [2,3]
               }
           ],
              ajax: BASE_URL + '/admin/get-user-autorespond/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'vchr_user_name',
                       name: 'vchr_user_name'
                   },
   
   
                   {
                       data: 'vchr_auto_respond_message_code',
                       name: 'vchr_auto_respond_message_code'
                   },
                   {
   
                       data: 'text_auto_respond_message',
                       name: 'text_auto_respond_message'
                   },
   
                   {
                       data: 'show',
                       name: 'show',
                       orderable: false,
                       searchable: false
                   }
               ],
       });
   
   
            $('#autorespond-table').on('click', '.autorespond-delete', function (event) {
               event.preventDefault();
               var feedbackId = $(this).attr('autorespond-id');
               var destinationPath = BASE_URL + '/admin/autorespond/' + feedbackId;
               // console.log(destinationPath);
               // $("#delete-confirm-modal").modal('show');
               $.confirm({
                   title: 'Deletion',
                   content: 'Are you sure you want to delete ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: destinationPath,
                                   type: 'DELETE',
                               })
                               .done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               })
                               .fail(function(err) {
                               })
                               .always(function(com) {
                                   $('#autorespond-table').DataTable().ajax.reload(null, false);
                               });
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
           });
   
   
   
               $('#autorespond-table').on('click', '.autorespond-act', function(event) {
                 event.preventDefault();
                   var templateId = $(this).attr('autorespond-id');
                   if($(this).hasClass('btn-activate')) {
   
                       // var url = BASE_URL + '/admin/smstemplate-deactivate/';
                       // action = 'Reject';
                       $('#id4').val(templateId);
                       $('#ks-izi-modal-large2').modal('toggle');
   
   
   
               }else
               {
               var url = BASE_URL + '/admin/autorespond-activate/';
               action = 'Approve';
   
               var feedbackId = $(this).attr('autorespond-id');
               url = url + feedbackId;
               // console.log(url);
               $.confirm({
                   title: action,
                   content: 'Are you sure you want to ' + action + ' ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: url,
                                   type: 'GET',
                               })
                               .done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               })
                               .fail(function(err) {
                               })
                               .always(function(com) {
                                   $('#autorespond-table').DataTable().ajax.reload(null, false);
                               });
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
           }
           });
   
        $('#autorespond-table').on('click', '.ks-izi-modal-trigger2', function(event) {
   
           var testimonialId = $(this).attr('autorespond-id');
           $.ajax({
                   url: BASE_URL + '/admin/show-autorespond/' + testimonialId,
                   type: 'GET',
                   dataType: 'JSON',
               })
           .done(function(res) {
               if(res.status == "success") {
                       data = res.data;
                       $('#id4').val(data.pk_int_auto_respond_id);
                       //$('#ks-izi-modal-large1').modal('toggle');
   
                   }
           })
           .fail(function(err) {
   
           })
           .always(function(com) {
               $('#autorespond-table').DataTable().ajax.reload(null, false);
   
           });
    });
   
     $(document).on('submit', '#autorespondRejection', function(event) {
               event.preventDefault();
               // $('').on('click', function (e) {
   
               // console.log(enquiryId);
               //$('.addReason1').prop('disabled','true');
               $.ajax({
                   url: BASE_URL + '/admin/autorespond-deactivate',
                   type: 'POST',
                   dataType: 'JSON',
                   data:  new FormData(this),
                   contentType: false,
                   processData:false,
               })
               .done(function(res) {
                   //$('.addReason1').prop('disabled','false');
                   if(res.status == 'success'){
                       $('#ks-izi-modal-large2').modal('toggle');
                   swal(res.msg);
               }else{
                   $.alert({
                           title: 'Failed',
                           type: 'red',
                           content: res.msg,
                       });
   
               }
               })
               .fail(function() {
               })
                .always(function(com) {
               $('#autorespond-table').DataTable().ajax.reload(null, false);
   
               });
           });
   
   
     //SMS Template
   
      $('#smstemplate-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1]
               },
               {
                    "width": "140px",
                    "targets": [2,3]
               }
           ],
             ajax: BASE_URL + '/admin/get-user-smstemplates/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'vchr_user_name',
                       name: 'vchr_user_name'
                   },
                   {
                       data: 'vchar_sms_template_title',
                       name: 'vchar_sms_template_title'
                   },
                   {
                       data: 'text_sms_template_description',
                       name: 'text_sms_template_description'
                   },
                   {
                       data: 'default',
                       name: 'default',
                       orderable: false,
                       searchable: false
                   },
   
                   {
                       data: 'show',
                       name: 'show',
                       orderable: false,
                       searchable: false
                   }
               ],
       });
   
   
   
       $('#smstemplate-table').on('click', '.template-delete', function (event) {
               event.preventDefault();
               var feedbackId = $(this).attr('template-id');
               var destinationPath = BASE_URL + '/admin/sms-user-template/' + feedbackId;
               // console.log(destinationPath);
               // $("#delete-confirm-modal").modal('show');
               $.confirm({
                   title: 'Deletion',
                   content: 'Are you sure you want to delete ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: destinationPath,
                                   type: 'DELETE',
                               })
                               .done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               })
                               .fail(function(err) {
                               })
                               .always(function(com) {
                                   $('#smstemplate-table').DataTable().ajax.reload(null, false);
                               });
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
           });
   
   
       $('#smstemplate-table').on('click', '.template-act', function(event) {
               event.preventDefault();
               var templateId = $(this).attr('template-id');
   
               if($(this).hasClass('btn-activate')) {
   
                   // var url = BASE_URL + '/admin/smstemplate-deactivate/';
                   // action = 'Reject';
                   $('#pk_int_sms_template_id').val(templateId);
                     $('#ks-izi-modal-large3').modal('toggle');
   
   
   
               } else {
                   var url = BASE_URL + '/admin/smstemplate-activate/';
                   action = 'Approve';
                   url = url + templateId;
                   // console.log(url);
                $.confirm({
                   title: action,
                   content: 'Are you sure you want to ' + action + ' ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: url,
                                   type: 'GET',
                               })
                               .done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               })
                               .fail(function(err) {
                               })
                               .always(function(com) {
                                   $('#smstemplate-table').DataTable().ajax.reload(null, false);
                               });
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
   
               }
           });
   
   
   
        $('#smstemplate-table').on('click', '.ks-izi-modal-trigger3', function(event) {
   
           var testimonialId = $(this).attr('template-id');
           $.ajax({
                   url: BASE_URL + '/admin/show-smstemplate/' + testimonialId,
                   type: 'GET',
                   dataType: 'JSON',
               })
           .done(function(res) {
               if(res.status == "success") {
                       data = res.data;
                       $('#pk_int_sms_template_id').val(data.pk_int_sms_template_id);
                       //$('#ks-izi-modal-large1').modal('toggle');
   
                   }
           })
           .fail(function(err) {
   
           })
           .always(function(com) {
               $('#smstemplate-table').DataTable().ajax.reload(null, false);
   
           });
    });
   
   
         $(document).on('submit', '#smstemplateRejection', function(event) {
               event.preventDefault();
               // $('').on('click', function (e) {
   
               // console.log(enquiryId);
               $.ajax({
                   url: BASE_URL + '/admin/smstemplate-deactivate',
                   type: 'POST',
                   dataType: 'JSON',
                   data:  new FormData(this),
                   contentType: false,
                   processData:false,
               })
               .done(function(res) {
                   $('#ks-izi-modal-large3').modal('toggle');
                   if(res.status == 'success'){
   
                   swal(res.msg);
               }else{
                   $.alert({
                           title: 'Failed',
                           type: 'red',
                           content: res.msg,
                       });
   
               }
               })
               .fail(function() {
               })
                .always(function(com) {
               $('#smstemplate-table').DataTable().ajax.reload(null, false);
   
               });
           });
   
          $('#sub-sms-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    // "width": "140px",
                    // "targets": [0, 1]
               },
               {
                    // "width": "140px",
                    // "targets": [2,3]
               }
           ],
              ajax: BASE_URL + '/admin/get-user-subscription-messages/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'vchr_user_name',
                       name: 'vchr_user_name'
                   },
                   {
   
   
                       data: 'text_subscription_message',
                       name: 'text_subscription_message'
                   },
                   {
                       data: 'text_unsubscription_message',
                       name: 'text_unsubscription_message'
                   },
   
                   {
                       data: 'show',
                       name: 'show',
                       orderable: false,
                       searchable: false
                   }
               ],
       });
       // staff
       $('#staff-details-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    // "width": "140px",
                    // "targets": [0, 1]
               },
               {
                    // "width": "140px",
                    // "targets": [2,3]
               }
           ],
              ajax: BASE_URL + '/admin/get-user-staff-details/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'vchr_user_name',
                       name: 'vchr_user_name'
                   },
                   {
   
   
                       data: 'vchr_user_mobile',
                       name: 'vchr_user_mobile'
                   },
                   {
                       data: 'email',
                       name: 'email'
                   }
   
                 
               ],
       });
   
       // end staff
       // scratch
       $('#scratch-details-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    "width": "30px",
                    "targets": [0]
               },
               {
                    "width": "140px",
                    "targets": [1,2,]
               },
              
           ],
              ajax: BASE_URL + '/admin/get-user-scratch-details/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'vchr_name',
                       name: 'vchr_name'
                   },
                   {
   
   
                       data: 'vchr_mobno',
                       name: 'vchr_mobno'
                   },
                   // {
                   //     data: 'fk_int_offer_id',
                   //     name: 'fk_int_offer_id'
                   // }
   
                 
               ],
       });
   
       // end scratch
       // promo
       $('#promo-details-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    "width": "30px",
                    "targets": [0]
               },
               {
                    "width": "140px",
                    "targets": [1,2,]
               },
              
           ],
              ajax: BASE_URL + '/admin/get-user-promo-details/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'vchr_gl_promo_missedcall_number',
                       name: 'vchr_gl_promo_missedcall_number'
                   },
                   {
   
   
                       data: 'created_at',
                       name: 'created_at'
                   },
                  
   
                 
               ],
       });
       // end promo
       // gl verification
       $('#verification-details-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    "width": "30px",
                    "targets": [0]
               },
               {
                    "width": "140px",
                    "targets": [1,2,]
               },
              
           ],
              ajax: BASE_URL + '/admin/get-user-verification-details/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'vchr_mobile',
                       name: 'vchr_mobile'
                   },
                   {
   
   
                       data: 'created_at',
                       name: 'created_at'
                   },
                  
   
                 
               ],
       });
       // end gl verification
   
   
           $('#sub-sms-table').on('click', '.sms-delete', function (event) {
               event.preventDefault();
               var feedbackId = $(this).attr('sms-id');
               var destinationPath = BASE_URL + '/admin/user-subscription-message/' + feedbackId;
               // console.log(destinationPath);
               // $("#delete-confirm-modal").modal('show');
               $.confirm({
                   title: 'Deletion',
                   content: 'Are you sure you want to delete ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: destinationPath,
                                   type: 'DELETE',
                               })
                               .done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               })
                               .fail(function(err) {
                               })
                               .always(function(com) {
                                   $('#sub-sms-table').DataTable().ajax.reload(null, false);
                               });
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
           });
   
   
   
             $('#sub-sms-table').on('click', '.sms-act', function(event) {
                 event.preventDefault();
                   var templateId = $(this).attr('sms-id');
                   if($(this).hasClass('btn-activate')) {
   
                       // var url = BASE_URL + '/admin/smstemplate-deactivate/';
                       // action = 'Reject';
                       $('#pk_int_subscription_message_id').val(templateId);
                        $('#ks-izi-modal-large4').modal('toggle');
   
   
   
               }else
               {
               var url = BASE_URL + '/admin/user-subscription-message-activate/';
               action = 'Approve';
   
               var feedbackId = $(this).attr('sms-id');
               url = url + feedbackId;
               // console.log(url);
               $.confirm({
                   title: action,
                   content: 'Are you sure you want to ' + action + ' ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: url,
                                   type: 'GET',
                               })
                               .done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               })
                               .fail(function(err) {
                               })
                               .always(function(com) {
                                   $('#sub-sms-table').DataTable().ajax.reload(null, false);
                               });
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
           }
           });
   
   
   
              $('#sub-sms-table').on('click', '.ks-izi-modal-trigger4', function(event) {
   
           var testimonialId = $(this).attr('sms-id');
           $.ajax({
                   url: BASE_URL + '/admin/show-subsmstemplate/' + testimonialId,
                   type: 'GET',
                   dataType: 'JSON',
               })
           .done(function(res) {
               if(res.status == "success") {
                       data = res.data;
                       $('#pk_int_subscription_message_id').val(data.pk_int_subscription_message_id);
                       //$('#ks-izi-modal-large1').modal('toggle');
   
                   }
           })
           .fail(function(err) {
   
           })
           .always(function(com) {
               $('#sub-sms-table').DataTable().ajax.reload(null, false);
   
           });
    });
   
   
         $(document).on('submit', '#subscriptionsmstemplateRejection', function(event) {
               event.preventDefault();
               // $('').on('click', function (e) {
   
               // console.log(enquiryId);
               $.ajax({
                   url: BASE_URL + '/admin/user-subscription-message-deactivate/',
                   type: 'POST',
                   dataType: 'JSON',
                   data:  new FormData(this),
                   contentType: false,
                   processData:false,
               })
               .done(function(res) {
                   $('#ks-izi-modal-large4').modal('toggle');
                   if(res.status == 'success'){
   
                   swal(res.msg);
               }else{
                   $.alert({
                           title: 'Failed',
                           type: 'red',
                           content: res.msg,
                       });
   
               }
               })
               .fail(function() {
               })
                .always(function(com) {
               $('#sub-sms-table').DataTable().ajax.reload(null, false);
   
               });
           });
   
         // SMS API SETTINGS
         $('#api-settings-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1]
               },
               {
                    "width": "140px",
                    "targets": [2,3]
               }
           ],
               ajax: BASE_URL + '/admin/get-user-api-template/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'route',
                       name: 'route'
                   },
                   {
   
   
                       data: 'domain',
                       name: 'domain'
                   },
                   {
   
   
                       data: 'credentials',
                       name: 'credentials'
                   },
                   
               ],
       });
   
   
          $(document).on('submit', '#addTemplate', function(event) {
               event.preventDefault();
              $('.error').html('');
               $('.error').hide();
               // console.log(enquiryId);
               $.ajax({
                   url: BASE_URL + '/admin/sms-api-settings',
                   type: 'POST',
                   dataType: 'JSON',
                   data:  new FormData(this),
                   contentType: false,
                   processData:false,
               })
               .done(function(res) {
                   if(res.status == 'success'){
                   $('#ks-izi-modal-large5').modal('toggle');
                    $.alert({
                           title: 'Success',
                           type: 'green',
                           content: res.msg,
                       });
               }else{
                   $.each(res.msg, function(index, val) {
                       //iterate through array or object
                       console.log(val);
                       console.log(index);
                       $('.'+index).html(val);
                       $('.'+index).show();
                   });
                   // swal(res.msg.vchr_enquiry_type[0]);
               }
               })
               .fail(function() {
               })
                .always(function(com)
                {
               $('#api-settings-table').DataTable().ajax.reload(null, false);
               });
   
           });
   
   
   
           $('#api-settings-table').on('click', '.api-delete', function (event) {
               event.preventDefault();
               var feedbackId = $(this).attr('api-id');
   
               var destinationPath = BASE_URL + '/admin/user-api-template-delete/' + feedbackId;
               // console.log(destinationPath);
               // $("#delete-confirm-modal").modal('show');
               $.confirm({
                   title: 'Deletion',
                   content: 'Are you sure you want to delete ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: destinationPath,
                                   type: 'DELETE',
                               })
                               .done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               })
                               .fail(function(err) {
                               })
                               .always(function(com) {
                                   $('#api-settings-table').DataTable().ajax.reload(null, false);
                               });
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
           });
   
   
           $('#api-settings-table').on('click', '.testimonial-act', function(event) {
               event.preventDefault();
               if($(this).hasClass('btn-activate')) {
                   var url = BASE_URL + '/admin/user-api-template-deactivate/';
                   action = 'Deactivate';
               } else {
                   var url = BASE_URL + '/admin/user-api-template-activate/';
                   action = 'Activate';
               }
               var feedbackId = $(this).attr('api-id');
               url = url + feedbackId;
               // console.log(url);
               $.confirm({
                   title: action,
                   content: 'Are you sure you want to ' + action + ' ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: url,
                                   type: 'GET',
                               })
                               .done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               })
                               .fail(function(err) {
                               })
                               .always(function(com) {
                                   $('#api-settings-table').DataTable().ajax.reload(null, false);
                               });
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
           });
           // API TEMPLATE
           $('#apitemplate-table').DataTable({
           scrollX: true,
           fixedColumns: {
                leftColumns: 1
           },
           scrollCollapse: true,
           autoWidth: true,
           paging: true,
           language: {
               searchPlaceholder: 'Search',
               sSearch: '',
               lengthMenu: '_MENU_ Page',
           },
           columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1]
               },
               {
                    "width": "140px",
                    "targets": [2,3]
               }
           ],
            ajax: BASE_URL + '/admin/get-user-apitemplates/'+id,
               columns: [
                   {
                       data: 'slno',
                       name: 'slno'
                   },
                   {
                       data: 'vchr_user_name',
                       name: 'vchr_user_name'
                   },
                   {
                       data: 'vchr_api_template_title',
                       name: 'vchr_api_template_title'
                   },
                   {
                       data: 'text_api_template_description',
                       name: 'text_api_template_description'
                   },
                   {
                       data: 'default',
                       name: 'default',
                       orderable: false,
                       searchable: false
                   },
   
                   {
                       data: 'show',
                       name: 'show',
                       orderable: false,
                       searchable: false
                   }
               ],
       });
   
           $('#apitemplate-table').on('click', '.template1-delete', function (event) {
               event.preventDefault();
               var feedbackId = $(this).attr('template1-id');
               var destinationPath = BASE_URL + '/admin/api-user-template/' + feedbackId;
               // console.log(destinationPath);
               // $("#delete-confirm-modal").modal('show');
               $.confirm({
                   title: 'Deletion',
                   content: 'Are you sure you want to delete ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                    'confirm': {
                        text: 'Proceed',
                        btnClass: 'btn-info',
                        action: function () {
                           $.ajax({
                               url: destinationPath,
                               type: 'DELETE',
                           })
                           .done(function(res) {
                               if(res.status == 'success') {
                                   $.alert({
                                       title: 'Success',
                                       type: 'green',
                                       content: res.msg,
                                   });
                               } else {
                                   $.alert({
                                       title: 'Failed',
                                       type: 'red',
                                       content: res.msg,
                                   });
                               }
                           })
                           .fail(function(err) {
                           })
                           .always(function(com) {
                               $('#apitemplate-table').DataTable().ajax.reload(null, false);
                           });
                       }
                   },
                   cancel: function () {
                       $.alert('Operation <strong>canceled</strong>');
                   }
               }
           });
           });
   
            $('#apitemplate-table').on('click', '.default-apitemplate', function (event) {
               event.preventDefault();
               var feedbackId = $(this).attr('apitemplate-id');
               var destinationPath = BASE_URL + '/admin/default-apitemplate/' + feedbackId;
               // console.log(destinationPath);
               // $("#delete-confirm-modal").modal('show');
               $.confirm({
                   title: 'Default API Template id',
                   content: 'Are you sure want to make API Template as default ?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: destinationPath,
                                   type: 'GET',
                               })
                               .done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               })
                               .fail(function(err) {
                               })
                               .always(function(com) {
                                   $('#apitemplate-table').DataTable().ajax.reload(null, false);
                               });
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
           });
   
   
       $('a[data-toggle="tab"]').on('shown.bs.tab', function(e){
           $($.fn.dataTable.tables(true)).DataTable()
               .columns.adjust()
               //.fixedColumns().relayout();
       });
       //  DLT Entity 
   
       // 
   
       //  DLT Header 
   
       //
   
       //  DLT Template 
   
       //
       $('.ajaxSubmit').on('click',function(){ 
           var message = $(this).data('message');
           var tag = $(this).data('tag');
   
           if(tag == 'email'){
               var url = '{{url("/")}}' + '/admin/user-edit-email/{{$user->pk_int_user_id}}';
               var data = {
                               ed_email_id:$('#ed_email_id').val(),
                           };
           }else if(tag == 'category'){
               var url = '{{url("/")}}' + '/admin/user-edit-category/{{$user->pk_int_user_id}}';
               var data = {
                           category_id:$('#category').val(),
                           };
           }else{
               var url = '{{url("/")}}' + '/admin/user-edit-phone/{{$user->pk_int_user_id}}';
               var data = {
                               ed_phone_number:$('#ed_phone_number').val(),
                               country_code:$('#country_code').val(),
                           };    
           }
          
           $.confirm({
                   title: 'Edit '+message,
                   content: 'Are you sure want to change '+message+'?',
                   icon: 'la la-question-circle',
                   animation: 'scale',
                   closeAnimation: 'scale',
                   opacity: 0.5,
                   buttons: {
                      'confirm': {
                          text: 'Proceed',
                          btnClass: 'btn-info',
                          action: function () {
                               $.ajax({
                                   url: url,
                                   type: 'GET',
                                   data:data
                               }).done(function(res) {
                                   if(res.status == 'success') {
                                       $.alert({
                                           title: 'Success',
                                           type: 'green',
                                           content: res.msg,
                                       });
                                       // $('.item-desc').html(res.value)
                                   } else {
                                       $.alert({
                                           title: 'Failed',
                                           type: 'red',
                                           content: res.msg,
                                       });
                                   }
                               }).fail(function(err) {
                               }).always(function(res){
                                   location.reload();
                               })
                           }
                       },
                       cancel: function () {
                           $.alert('Operation <strong>canceled</strong>');
                       }
                   }
               });
       });
   });
</script>
@endpush