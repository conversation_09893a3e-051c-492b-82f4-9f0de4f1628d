@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
 <style type="text/css">
    .val-error
    {
        color:red;
    }
 </style>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Active Users</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.admin-users-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table" style="overflow: auto">
               <table id="enquiry-info-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Number</th>
                            <th>Last Login</th>
                            <th width="10%">Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
            </div>
        </div>
    </main>

@endsection 
@push('footer.script')
<script type="text/javascript">
        $(document).ready(function() {
           BASE_URL ={!! json_encode(url('/')) !!};

            $('.error').hide();


            $('#enquiry-info-table').DataTable({
                 scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "150px",
                    "targets": [0, 1,2,3,4]
                },

                ],
                ajax: BASE_URL + '/admin/get-active-users/1',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'vchr_user_name',
                        name: 'vchr_user_name'
                    },
                    {

                        data: 'email',
                        name: 'email'
                    },
                    {
                        data: 'vchr_user_mobile',
                        name: 'vchr_user_mobile'
                    },
                    {
                        data: 'datetime_last_login',
                        name: 'datetime_last_login'
                    },
                    
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
                
            });









            $('#enquiry-info-table').on('click', '.user-act', function(event) {
                event.preventDefault();
                if($(this).hasClass('btn-activate')) {
                    var url = BASE_URL + '/admin/user-deactivate/';
                    action = 'Deactivate';
                } else {
                    var url = BASE_URL + '/admin/user-activate/';
                    action = 'Activate';
                }
                var feedbackId = $(this).attr('user-id');
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

        }); 
        
    </script>
@endpush
