@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
<style type="text/css">
    .val-error
    {
        color:red;
    }
 </style>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Total Users</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.admin-users-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table" style="overflow: auto">
                <table id="enquiry-info-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Agent Name</th>
                            <th>Company</th>
                            <th>Staff Count</th>
                            <th>Subscription Count</th>
                            <th>Registered Mobile</th>
                            <th>Last Login - Web</th>
                            <th>Last Login - App</th>
                            <th>App Version</th>
                        </tr>
                    </thead>
                </table>
            </div>
            </div>
        </div>
    </main>

@endsection 
@push('footer.script')
<script type="text/javascript">
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};

            $('.error').hide();


            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, 'All'],
                ],
                columnDefs: [{
                    "width": "150px",
                    "targets": [0, 1,2,3,4]
                },

                ],
                ajax: BASE_URL + '/admin/getTotalUser',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'vchr_user_name', name: 'vchr_user_name'},
                    {data: 'company', name: 'company'},
                    {data: 'staff_count', name: 'staff_count'},
                    {data: 'sub_count', name: 'sub_count'},
                    {data: 'vchr_user_mobile', name: 'vchr_user_mobile'},
                    {data: 'diff_for_humans', name: 'diff_for_humans'},
                    {data: 'diff_for_humans_app', name: 'diff_for_humans_app'},
                    {data: 'last_app_version', name: 'last_app_version'},
                ],
                
            });



        }); 
        
    </script>
@endpush
