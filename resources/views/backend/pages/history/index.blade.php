@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Settings</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminsmssidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <ul class="nav ks-nav-tabs ks-tabs-page-default ks-tabs-full-page">
                <li class="nav-item">
                    <a class="nav-link active" href="#" data-toggle="tab" data-target="#system-settings">
                       ADMIN CREDITED
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-toggle="tab" data-target="#smtp-settings">
                       USER CREDIT
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-toggle="tab" data-target="#smtp-settings1">
                       OTP VERIFICATION
                    </a>
                </li>
            </ul>
            @if(session('flash_notification'))
                <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
            @endif
                <div class="tab-content">
                    <div class="tab-pane active ks-column-section" id="system-settings" role="tabpanel">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card" style="padding-left:10px">
                                    <div class="card-block">
                                        <div class="row">
                                            <h5 class="card-title m-3">ADMIN CREDITED</h5>
                                        </div>
                                         <div class="col-sm-12 py-3">
                                            <table id="enquiry1-info-table" class="table table-striped table-bordered" width="100%">
                                                <thead>
                                                    <tr>
                                                    <th>Sl No</th>
                                                    <th>Statement</th>
                                                    <th>Date and Time</th>
                                                    </tr>
                                                </thead>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" id="smtp-settings" role="tabpanel">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card">
                                    <div class="card-block">
                                        <div class="row">
                                            <h5 class="card-title" style="padding-left:30px;">USER CREDIT</h5>
                                        </div>
                                         <div class="col-sm-12 py-3">
                                            <table id="enquiry2-info-table" class="table table-striped table-bordered" width="100%">
                                                <thead>
                                                    <tr>
                                                         <th>Sl No</th>
                                                    <th>Statement</th>
                                                    <th>Date and Time</th>
                                                    </tr>
                                                </thead>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" id="smtp-settings1" role="tabpanel">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card">
                                    <div class="card-block">
                                        <div class="row">
                                            <h5 class="card-title" style="padding-left:30px"> OTP VERIFICATION</h5>
                                        </div>
                                         <div class="col-sm-12 py-3">
                                        <table id="enquiry3-info-table" class="table table-striped table-bordered" width="100%">
                                            <thead>
                                                <tr>
                                                <th>Sl No</th>
                                                <th>Statement</th>
                                                <th>Date and Time</th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>


@endsection 
@push('footer.script')
<script type="text/javascript">
        $(document).ready(function() {
           BASE_URL ={!! json_encode(url('/')) !!};

            $('#enquiry1-info-table').DataTable({
                autoWidth: 1,
                lengthChange: 1,
                // buttons: ["print", "excel", "pdf"],
                select: !1,
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                processing: true,
                serverSide: true,
                searching: true,
                orderable: true,
                ajax: BASE_URL + '/admin/get-mastermsms-history/'+3,
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {

                        data: 'vchr_master_sms_history_statement',
                        name: 'vchr_master_sms_history_statement'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at'
                    },
                    
                ],
                "initComplete": function () {
                    
                }
            });

            $('#enquiry2-info-table').DataTable({
                autoWidth: 1,
                lengthChange: 1,
                // buttons: ["print", "excel", "pdf"],
                select: !1,
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                processing: true,
                serverSide: true,
                searching: true,
                orderable: true,
                ajax: BASE_URL + '/admin/get-mastermsms-history/'+1,
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {

                        data: 'vchr_master_sms_history_statement',
                        name: 'vchr_master_sms_history_statement'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at'
                    },
                    
                ],
                "initComplete": function () {
                    
                }
            });

            $('#enquiry3-info-table').DataTable({
                autoWidth: 1,
                lengthChange: 1,
                // buttons: ["print", "excel", "pdf"],
                select: !1,
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                processing: true,
                serverSide: true,
                searching: true,
                orderable: true,
                ajax: BASE_URL + '/admin/get-mastermsms-history/'+4,
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {

                        data: 'vchr_master_sms_history_statement',
                        name: 'vchr_master_sms_history_statement'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at'
                    },
                    
                ],
                "initComplete": function () {
                    
                }
            });

        });

        
    </script>
@endpush
