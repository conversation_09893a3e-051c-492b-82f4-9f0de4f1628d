@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>SMS Templates</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <a type="button" class="btn btn-sm btn-primary mx-2 my-3" id="sms_add" name="button" href="{{ url('/admin/add-sms-template') }}">Add SMS Template</a>
                    <a type="button" class="btn btn-sm btn-primary mx-2 my-3" id="sms_edit" name="button" href="#" onclick="return editTemplate();">Edit SMS Template</a>
                    <a type="button" class="btn btn-sm btn-primary mx-2 my-3" id="sms_delete" name="button" href="#" onclick="return deleteTemplate();">Delete SMS Template</a>
                    <div class="col-sm-12 py-3">
                        <div class="ks-tabs-container ks-tabs-default ks-tabs-no-separator ks-tabs-vertical" id="no-data">
                            <ul class="nav ks-nav-tabs nav-stacked" id="templist">
                            </ul>
                            <div class="tab-content" id="tempcode">
                            </div>
                        </div>
                        <div class="" id="no-data-message">
                            <h3>No templates created yet.</h3>
                        </div>
                    </div>
            </div>
        </div>
    </main>

@endsection 
@push('footer.script')
<script type="text/javascript">
        $(document).ready(function() {
            $('#no-data-message').hide();
            BASE_URL ={!! json_encode(url('/')) !!};
            getsmslist();
        });

        function getsmslist() {
            $('#templist').empty();
            $('#tempcode').empty();
            $.ajax({
                url: BASE_URL + '/admin/getsmslist',
                type: 'get',
                dataType: 'JSON',
            })
            .done(function(res) {
                list = '';
                content = '';
                if(res.status == 'success') {
                    if(res.data.length) {
                        $.each(res.data, function(index, val) {
                            //iterate through array or object
                            if(index == 0){
                                list = list + '<li class="nav-item"><a class="nav-link temp-selection active" data-temp-id="' + val.pk_int_sms_template_id + '" href="#" data-toggle="tab" data-target="#' + index + '">' + val.vchr_sms_template_name + '</a></li>'
                                content = content + '<div class="tab-pane active" id="' + index + '" role="tabpanel">' + val.vchr_sms_template_code + '</div>';
                            }else{
                                list = list + '<li class="nav-item"><a class="nav-link temp-selection" data-temp-id="' + val.pk_int_sms_template_id + '" href="#" data-toggle="tab" data-target="#' + index + '">' + val.vchr_sms_template_name + '</a></li>'
                                content = content + '<div class="tab-pane" id="' + index + '" role="tabpanel">' + val.vchr_sms_template_code + '</div>';
                            }
                        });

                        $('#templist').append(list);
                        $('#tempcode').append(content);
                    } else {
                        $('#no-data').hide();
                        $('#no-data-message').show();
                    }
                } else {

                }
            })
            .fail(function(err) {
                console.log("error");
            })
            .always(function(com) {
            });
        }
        function editTemplate() {
            id = $('.temp-selection.active').data('temp-id');
            // console.log(id);
            url = BASE_URL + '/admin/edit-sms-template/' + id;
            window.location = url;
        }

        function deleteTemplate() {
            id = $('.temp-selection.active').data('temp-id');
            templateName = $('.temp-selection.active').html();
            console.log(templateName);
            url = BASE_URL + '/admin/delete-sms-template/' + id;
            $.confirm({
                title: 'Deletion',
                content: 'Are you sure you want to delete "' + templateName + '"?',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                    'confirm': {
                        text: 'Proceed',
                        btnClass: 'btn-info',
                        action: function () {
                            $.ajax({
                                url: url,
                                type: 'DELETE',
                                dataType: 'JSON',
                            })
                            .done(function(res) {
                                console.log("success");
                                console.log(res);
                                if(res.status == 'success') {
                                    $.alert({
                                        title: 'Success',
                                        type: 'green',
                                        content: res.msg,
                                    });
                                } else {
                                    $.alert({
                                        title: 'Failed',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                }
                            })
                            .fail(function(err) {
                                console.log("error");
                            })
                            .always(function(com) {
                                console.log("complete");
                                getsmslist();
                            });
                        }
                    },
                    cancel: function () {
                        $.alert('Operation <strong>canceled</strong>');
                    }
                }
            });
        }
    </script>
@endpush