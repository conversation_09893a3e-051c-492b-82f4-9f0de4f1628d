@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>SMS Template</h5>
                </div>
                {{-- <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add SMS Template </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card my-3">
                            <div class="card-block">
                                <div class="row">
                                    <h5 class="card-title">Sms Templete Creation</h5>
                                    {{-- <button type="button" class="btn btn-sm btn-primary ml-auto m-3" id="system_info_edit" name="button">Edit</button>
                                    <button type="button" class="btn btn-sm btn-primary ml-auto m-3" id="system_info_edit_cancel" name="button">Cancel</button> --}}
                                </div>
                                {{-- <form method="post" action="{{ url('/admin/add-sms-template') }}" id="sms_template_creation"> --}}
                                {{ Form::open(array('url' => '/admin/add-sms-template', 'method' => 'POST')) }}
                                    <div class="form-group row">
                                        <div class="col-sm-2">
                                            <label for="default-input" class="form-control-label">SMS Template Name</label>
                                        </div>
                                        <div class="col-sm-10">
                                            {{-- <input type="text" name="temp_name" class="form-control" placeholder=""> --}}
                                            {{ Form::text('temp_name', '', ['class' => 'form-control']) }}
                                            @if ($errors->has('vchr_sms_template_name'))
                                                <span class="help-block">
                                                    <strong style="color:red">{{ $errors->first('vchr_sms_template_name') }}</strong>
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-2">
                                            <label for="default-input" class="form-control-label">Sms Template Code</label><br>
                                            <div class="char-count form-control-label">
                                            </div>
                                        </div>
                                        <div class="col-sm-10">
                                            {{-- <textarea name="temp_code" id="temp_code"></textarea> --}}
                                            {{ Form::textarea('temp_code', '', ['class' => 'form-control', 'id' => 'temp_code']) }}
                                            @if ($errors->has('vchr_sms_template_code'))
                                                <span class="help-block">
                                                    <strong style="color:red">{{ $errors->first('vchr_sms_template_code') }}</strong>
                                                </span>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="form-group row" id="sms_template_creation_save">
                                        <div class="col-sm-4 offset-sm-4 center">
                                            <button class="btn btn-primary">
                                                <span class="la la-check ks-icon"></span>
                                                <span class="ks-text">save</span>
                                            </button>
                                        </div>
                                    </div>
                                {{ Form::close() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection 
@push('footer.script')
<script type="text/javascript">
        $(document).ready(function() {

            BASE_URL ={!! json_encode(url('/')) !!};

            $('#temp_code').keyup(function() {
                val = $(this).val();
                console.log(val.length);
                text = "Character count = " + val.length + "<br>";
                text = text + "Sms count = " + Math.ceil(val.length / 160) ;
                $('.char-count').html(text);
            });
            getShortCodeList();
        });
        function getShortCodeList() {
            $.ajax({
                url: BASE_URL + '/admin/getshortcodelist',
                type: 'GET',
                dataType: 'JSON',
            })
            .done(function(res) {
                text = '';
                $.each(res.data, function(index, val) {
                    //iterate through array or object
                    text = text + '<span class="badge badge-success m-2" data-toggle="tooltip" data-placement="bottom" title="' + val.vchr_short_code_description + '">[ ' + val.vchr_short_code_template + ' ]</span>'
                });
                $('#shortCodeList').append(text);
            })
            .fail(function(err) {
            })
            .always(function(com) {
                $('[data-toggle="tooltip"]').tooltip();
            });

        }
    </script>
@endpush
