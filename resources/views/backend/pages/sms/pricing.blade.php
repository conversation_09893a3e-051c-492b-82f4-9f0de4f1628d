@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>SMS Pricing</h5>
                </div>
                <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large1" id="addEnquiry" href="#">Add Sms Pricing</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminbulkpushsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <table id="enquiry-info-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                   <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Route</th>
                            <th>Range</th>
                            <th>Rate/sms</th>
                            <th width="10%">Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </main>
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="testimonialAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Sms Pricing</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label"> Select Route</label>
                            <select class="form-control" data-placeholder="Select" name="vchr_sms_pricing_route" required>
                                <option value="">Select Route</option>
                                 @foreach($route as $key => $route1)
                                <option value="{{ $route1->pk_int_sms_route_id }}">{{ $route1->vchr_sms_route }}</option>
                                @endforeach
                            </select>
                            <span class="val-error vchr_sms_pricing_route"></span> 
                        </div>
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">Range From</label>
                            <input type="text" pattern="[0-9]*" name="vchr_sms_pricing_rangefrom" class="form-control" placeholder="" required >
                            <span class="val-error vchr_sms_pricing_rangefrom"></span>
                        </div> 
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">Range To</label>
                            <input type="text" pattern="[0-9]*" name="vchr_sms_pricing_rangeto" class="form-control" placeholder="" required>
                            <span class="val-error vchr_sms_pricing_rangeto"></span>
                        </div>
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="col-sm-3 form-control-label">Rate/Sms</label>
                            <input type="text" name="vchr_sms_pricing_rate_sms" class="form-control" placeholder="" required>
                            <span class="val-error vchr_sms_pricing_rate_sms"></span>
                        </div>
                        
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button class="btn btn-deep-orange demo-btn">Add</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="testimonialEdit" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update SMS Pricing</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label"> Select Route</label>
                            <select class="form-control" data-placeholder="Select" name="vchr_sms_pricing_route" required>
                                <option value="">Select Route</option>
                                 @foreach($route as $key => $route1)
                                <option value="{{ $route1->pk_int_sms_route_id }}">{{ $route1->vchr_sms_route }}</option>
                                @endforeach
                            </select>
                            <span class="val-error vchr_sms_pricing_route"></span> 
                        </div>
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">Range From</label>
                            <input type="text" pattern="[0-9]*" name="vchr_sms_pricing_rangefrom" class="form-control" placeholder="" required >
                            <span class="val-error vchr_sms_pricing_rangefrom"></span>
                        </div> 
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">Range To</label>
                            <input type="text" pattern="[0-9]*" name="vchr_sms_pricing_rangeto" class="form-control" placeholder="" required>
                            <span class="val-error vchr_sms_pricing_rangeto"></span>
                            <input type="hidden" name="_method"  value="PUT" class="form-control" placeholder="" >
                            <input type="hidden" name="pk_int_sms_pricing_id" class="form-control" placeholder=""  id="pk_int_sms_pricing_id">
                        </div>
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="col-sm-3 form-control-label">Rate/Sms</label>
                            <input type="text" name="vchr_sms_pricing_rate_sms" class="form-control" placeholder="" required>
                            <span class="val-error vchr_sms_pricing_rate_sms"></span>
                        </div>           
                    </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


@endsection 
@push('footer.script')
<script type="text/javascript">
     function readURL1(input) {
      if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
          $('#profile-img-tag').attr('src', e.target.result);
          $('#profile-img-tag').hide();
          $('#profile-img-tag').fadeIn(650);

        }

        reader.readAsDataURL(input.files[0]);
      }
    }
    function readURL(input) {

      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function(e) {
          $('#blah').attr('src', e.target.result);

          $('#blah').hide();
          $('#blah').fadeIn(650);

        }

        reader.readAsDataURL(input.files[0]);
      }
    }
    $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};

            $('.error').hide();

            $('#enquiry-info-table').DataTable({
               scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "170px",
                    "targets": [0, 1,2]
                },

                ],
                ajax: BASE_URL + '/admin/getsmspricing',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'route',
                        name: 'route'
                    },
                    {

                        data: 'range',
                        name: 'range'
                    },
                    {
                        data: 'rate_sms',
                        name: 'rate_sms'
                    },
                   
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
               
            });

            $(document).on('submit', '#testimonialAdd', function(event) {
                event.preventDefault();
                // $('').on('click', function (e) {
               $('.val-error').html('');
              $('.val-error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/smspricing',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    $('#ks-izi-modal-large1').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });

            $(document).on('submit', '#testimonialEdit', function(event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id=$('#pk_int_sms_pricing_id').val();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/edit-smspricing/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                   $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });


            $('#enquiry-info-table').on('click', '.testimonial-act', function(event) {
                event.preventDefault();
                if($(this).hasClass('btn-activate')) {
                    var url = BASE_URL + '/admin/smspricing-deactivate/';
                    action = 'Deactivate';
                } else {
                    var url = BASE_URL + '/admin/smspricing-activate/';
                    action = 'Activate';
                }
                var feedbackId = $(this).attr('testimonial-id');
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $('#enquiry-info-table').on('click', '.testimonial-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('testimonial-id');
                var destinationPath = BASE_URL + '/admin/smspricing-delete/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
    });
    $('#enquiry-info-table').on('click', '.ks-izi-modal-trigger1', function(event) {

            var testimonialId = $(this).attr('testimonial-id'); 
            $.ajax({
                    url: BASE_URL + '/admin/show-smspricing/' + testimonialId,
                    type: 'GET',
                    dataType: 'JSON',
                })
            .done(function(res) {
                if(res.status == "success") {
                        data = res.data;

                        $("[name='vchr_sms_pricing_route']").val(data.fk_int_sms_route);
                        $("[name='vchr_sms_pricing_rangefrom']").val(data.vchr_sms_pricing_rangefrom);
                        $("[name='vchr_sms_pricing_rangeto']").val(data.vchr_sms_pricing_rangeto);
                        $("[name='vchr_sms_pricing_rate_sms']").val(data.vchr_sms_pricing_rate_sms);
                        $('#pk_int_sms_pricing_id').val(data.pk_int_sms_pricing_id);

                        $('#ks-izi-modal-large').modal('toggle');
                        
                    }
            })
            .fail(function(err) {

            })
            .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });
    });
</script>
@endpush





