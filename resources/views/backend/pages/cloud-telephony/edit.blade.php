<div id="edit_details" class="modal fade" role="dialog">
    <div class="modal-dialog">
      <!-- Modal content-->
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">Edit Credientails</h4>
            <button type="button" class="close" data-dismiss="modal">&times;</button>
          </div>
          <div class="modal-body">
            <form  action="" method="post" id="editdetails" name="editdetails">
              @csrf
              <div class="form-row">
               <div class="col-md-12 mb-12 field" id="platform">
                  <label for="validationCustom02">In which platform do you have account</label>
                  <select class="form-control" onchange="showPlatformEdit(this)" name="edit_platform" id="edit_platform">
                     <option>-- Select Platform --</option>
                     <option value="1">Message Bird</option></option>
                     <option value="2">GupShup</option>
                  </select>
                </div>
                <div id="gup_shup_edit" style="width: 100%;">
                <div class="col-md-12 mb-12 field">
                  <label for="validationCustom02">Api Key</label>
                  <input type="text" name="gupshup_edit_api_key" id="gupshup_edit_api_key" class="form-control" placeholder="Api Key linked with gupshup" value="">
                </div>
                <div class="col-md-12 mb-12 field">
                  <label for="validationCustom02">Source Mobile Number</label>
                  <input type="number" class="form-control" placeholder="Source mobile number (please add country code) 91" name="edit_source_mobile_num" id="edit_source_mobile_num"/>
                </div>
                <div class="col-md-12 mb-12 field">
                  <label for="validationCustom02">Template Name</label>
                  <input type="text" class="form-control" placeholder="Template Name which is approved by watsapp" name="edit_template_name_gupshup" id="gupshup_edit_template_name"/>
                </div>
                </div>
                <div id="message_bird_edit" style="width: 100%;">
                <div class="col-md-12 mb-12 field">
                  <label for="validationCustom02">Access Key</label>
                  <input type="text" name="edit_access_key" class="form-control" id="edit_access_key" placeholder="Access Key linked with message bird" value="">
              
                  </div>
                  <div class="col-md-12 mb-12 field">
                  <label for="validationCustom02">Channel Id</label>
                  <input type="text" class="form-control" id="edit_channel_id" placeholder="Channel Id linked with the message bird" name="edit_key" value=""/>
                  </div>
                  <div class="col-md-12 mb-12 field">
                  <label for="validationCustom02">Name Space</label>
                  <input type="text" class="form-control name-space" id="edit_name_space" placeholder="Name space Id linked with the message bird"  value=""/>
                  </div>
                  <div class="col-md-12 mb-12 field">
                  <label for="validationCustom02">Template Name</label>
                  <input type="text" id="edit_template_name" class="form-control" name="edit_template_name" placeholder="Template Name which is approved by watsapp" />
                </div>
                </div>
                </div>   
            </form>
          </div>
          <div class="modal-footer">
              <input type="hidden" id="edit_id" name="id">
              <input type="hidden" id="edit_status" name="status">
              <button class="btn" data-dismiss="modal">cancel</button>
              <button type="submit" class="btn btn-primary" onclick="editCredientails()"> <i class="fa fa-refresh fa-spin" id="editspin"></i>&nbsp;Update</button>
          </div>
        </div>
      </div>
    </div>