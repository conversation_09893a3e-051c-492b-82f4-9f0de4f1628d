
<style type="text/css">

::-webkit-scrollbar {
	width: 6px;
	height: 6px;
	background-color: transparent;
	position: relative;
	z-index: 0;
}

::-webkit-scrollbar-track {
	display: none;
	overflow: hidden;
}

</style>

<div class="modal fade" id="ks-izi-modal-large1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
   <form id="enquirySubmitEdit">
      <div class="modal-dialog modal-lg" role="document">
         <div class="modal-content">
            <div class="modal-header text-center">
               <h4 class="modal-title w-100 font-weight-bold g-clr">Update Lead</h4>
               <button type="button" class="close" id="closebutton" data-dismiss="modal" aria-label="Close">
               <span aria-hidden="true">&times;</span>
               </button>
            </div>
            <div class="modal-body mx-3">
               <h3 class="card-title"><img src="/backend/images/customer.svg" class="mr-6 pr-2">Customer Details</h3>
               <div class="row">
                  @if(!is_array($active_fields) || !in_array('name',$active_fields))
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Customer Name</label>
                     @if(is_array($required_fields) && in_array('name',$required_fields))
                     <span class="muted text-danger">* Required</span>
                     @endif
                     <input type="text" name="vchr_customer_name" class="form-control" placeholder="Customer Name"  @if(is_array($required_fields) && in_array('name',$required_fields)) required @endif >
                     <div class="error vchr_customer_name">
                        <span class="muted"></span>
                     </div>
                  </div>
                  @endif
                  @if (App\User::getVendorId() == 880)
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Model</label>
                     <select class="form-control model_id" name="model_id" id="model_id">
                        <option value="">Select Model</option>
                        @foreach($models as $model)
                        <option value="{{$model->id }}">{{$model->name }}</option>
                        @endforeach
                     </select>
                     <div class="error model_id">
                        <span class="muted"></span>
                     </div>
                  </div>
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Purchase Plan</label>
                     <select class="form-control" name="purchase_plan" id="purchase_plan" onchange="showFields(this)">
                        <option value=""> Select Plan</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                     </select>
                     <div class="error purchase_plan">
                        <span class="muted"></span>
                     </div>
                  </div>
                  <div class="md-form mb-1 col-md-4" id="div_date_of_purchase">
                     <label for="default-input" class="form-control-label">Date Of Purchase</label>
                     <input type="date" name="date_of_purchase" autocomplete="off" value="{{ old('date_of_purchase') }}" class="form-control date_of_purchase" id="date_of_purchase">
                     <small class="text-danger">{{$errors->first('date_of_purchase') }}</small>
                     <div class="error date_of_purchase ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  <div class="md-form mb-1 col-md-4" id="div_remarks">
                     <label for="default-input" class="form-control-label">Remarks</label>
                     <input type="text" name="remarks" autocomplete="off" value="{{ old('remarks') }}" class="form-control" id="remarks">
                     <small class="text-danger">{{$errors->first('remarks') }}</small>
                     <div class="error remarks ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Live Deal</label>
                     <!-- <span class="muted text-danger">* Required</span> -->
                     <select class="form-control" name="live_deal" id="live_deal" onchange="showCompetingModel(this)">
                        <option value="">Select Deal</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                     </select>
                     <div class="error live_deal">
                        <span class="muted"></span>
                     </div>
                  </div>
                  <div class="md-form mb-1 col-md-4" id="div_competing_model">
                     <label for="default-input" class="form-control-label">Competing Model</label>
                     <!-- <span class="muted text-danger">* Required</span> -->
                     <select class="form-control" name="competing_model" id="competing_model">
                        <option value="">Select Competing Model</option>
                     </select>
                     <div class="error competing_model">
                        <span class="muted"></span>
                     </div>
                  </div>
                  @endif
                  @if(!is_array($active_fields) || !in_array('company_name',$active_fields))
                  <div class="md-form mb-1 col-md-3">
                     <label for="default-input" class="form-control-label">Company Name</label>
                     <input type="text" name="vchr_customer_company_name" class="form-control" placeholder="Customer Company Name">
                     <div class="error vchr_customer_company_name ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  @endif
                  @if(!is_array($active_fields) || !in_array('phone',$active_fields))
                  <div class="md-form mb-1 col-md-5">
                     <label for="default-input" class="form-control-label">Phone</label>
                     <div class="row m-0 justify-content-between" style="width:100%;">
                        <select name="country_code" id="" class="form-control col-md-4 select2" style="padding: 10px 0;">
                        @foreach($countrys as $country)
                        <option data-countryCode="{{ $country->country_code }}" value="{{ $country->code }}" @if($countryCode==$country->code) selected @endif>
                        (+{{ $country->code  }})
                        </option>
                        @endforeach
                        </select>
                        {{-- <div class="col-md-1"></div> --}}
                        <input type="text" name="vchr_customer_mobile" class="form-control col-md-7 customer_mobile" placeholder=""  onkeypress="return /[0-9]/i.test(event.key)">
                     </div>
                     <div class="error vchr_customer_mobile ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  @endif
                  @if(!is_array($active_fields) || !in_array('landline',$active_fields))
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Landline Number</label>
                     <input type="text" name="landline_number" class="form-control" placeholder="Land Line"  onkeypress="return /[0-9]/i.test(event.key)">
                     <div class="error landline_number ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  <!-- end landline -->
                  @endif
                  @if(!is_array($active_fields) || !in_array('more_phone_numbers',$active_fields))
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">More Phone Numbers</label>
                     <a id="add_more_numbers_div" href="javascript:void(0);" class="add_field_button" title="Add field">
                     <i class="fa fa-plus-circle ml-2 " style="font-size:18px;color:green" id="add_more_number"></i></a>
                     <div id="more_numbers_a">
                        <div class="row m-0">
                        </div>
                     </div>
                     <div class="error more_phone_numbers ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  <!-- new landline -->
                  @endif
                  <!-- @if(!is_array($active_fields) || !in_array('date_of_birth',$active_fields))
                     <div class="md-form mb-1 col-md-4">
                         <label for="default-input" class="form-control-label">Date of Birth</label>
                         <input type="date" name="date_of_birth" class="form-control" placeholder="">
                         <div class="error date_of_birth ">
                             <span class="muted"></span>
                         </div>
                     </div>
                     @endif -->
                  @if(!is_array($active_fields) || !in_array('email',$active_fields))
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Customer Email</label>
                     <input type="text" name="vchr_customer_email" class="form-control" placeholder="Customer Email">
                     <div class="error vchr_customer_email ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  @endif
                  @if (Auth::user()->int_role_id == App\User::USERS || Auth::user()->int_role_id == App\User::ADMIN || Auth::user()->int_role_id == App\User::STAFF)
                  @if (App\User::getVendorId() == 880)
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Team Assigned</label>
                     <select class="form-control" name="staff_id" id="staff_id">
                        <option value=""> Select Team </option>
                        @foreach($staffId as $key=>$staff_id)
                        <option value="{{ $staff_id->pk_int_user_id }}">{{ $staff_id->vchr_user_name }}</option>
                        @endforeach
                     </select>
                     <div class="error staff_id ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  @else
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Agent</label>
                     <select class="form-control" name="staff_id" id="staff_id">
                        <option value=""> Select Agent</option>
                        @foreach($agents as $key=>$staff_id)
                        <option value="{{ $staff_id->pk_int_user_id }}">{{ $staff_id->vchr_user_name }}</option>
                        @endforeach
                     </select>
                     <div class="error staff_id ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  @endif
                  @else
                  {{-- <input type="hidden" name="staff_id" value="{{ Auth::user()->pk_int_user_id }}" --}}
                  @endif
                  @if($district->count()>0)
                  @if(!is_array($active_fields) || !in_array('district',$active_fields))
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">District</label>
                     <select name="district_id" id="district_id" class="form-control district_id">
                        <option>--Select Districts--</option>
                        @foreach($district as $key=> $districts)
                        <option value="{{ $districts->id}}">{{ $districts->name}}</option>
                        @endforeach
                     </select>
                  </div>
                  @endif
                  @if($taluk->count()>0)
                  @if(!is_array($active_fields) || !in_array('taluk',$active_fields))
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Select Taluk</label>
                     <select name="taluk_id" id="taluk_id" class="form-control taluk_id">
                        <option>Select Taluk</option>
                     </select>
                  </div>
                  @endif
                  @endif
                  @endif

                  @if($agencies->count()>0)
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Agency</label>
                     <select name="agency_id" id="agency_id" class="form-control agency_id">
                        <option>--Select Agency--</option>
                        @foreach($agencies as $key=> $agency)
                        <option value="{{ $agency->id}}">{{ $agency->name}}</option>
                        @endforeach
                     </select>
                  </div>
                  @endif
               </div>
               <h3 class="card-title"><img src="/backend/images/enquiry.svg" class="mr-6 pr-2">Lead information</h3>
               <div class="row">
                  @if(!is_array($active_fields) || !in_array('address',$active_fields))
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Address</label>
                     <textarea name="address" id="address" class="form-control" placeholder="Customer Address"></textarea>
                     <div class="error vchr_enquiry_feedback ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  @endif
                  <!-- @if(!is_array($active_fields) || !in_array('designation',$active_fields)) --}}
                     <div class="md-form mb-1 col-md-4">
                         <label for="default-input" class="form-control-label">Designation</label>
                         <select class="form-control" name="designation_id" id="designation_id_edit">
                             <option value="">-- Select Designation --</option>
                             @foreach(App\Common\Variables::getDesignations() as $key=>$designation)
                             <option value="{{ $designation->pk_int_designation_id }}">{{ $designation->vchr_designation }}</option>
                             @endforeach
                         </select>
                         <div class="error staff_id ">
                             <span class="muted"></span>
                         </div>
                     </div>
                     @endif -->
                  @if(!is_array($active_fields) || !in_array('source',$active_fields))
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Lead Source</label>
                     <select class="form-control" name="fk_int_enquiry_type_id" required="" id="enquiryTypeSelectEdit"  {{ ($canEditSource ?? true) ?  '': 'disabled'  }}>
                        <option value="">Select Lead source</option>
                        @foreach($types as $key=>$type)
                        <option value="{{ $type->pk_int_enquiry_type_id }}">{{ $type->vchr_enquiry_type }}</option>
                        @endforeach
                     </select>
                  </div>
                  @endif
                  @if(!is_array($active_fields) || !in_array('purpose',$active_fields))
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Lead Purpose</label>
                     <input type="hidden" name="_method" value="PUT">
                     <input type="hidden" name="pk_int_enquiry_id" id="pk_int_enquiry_id">
                     <select class="form-control enquiryPurposeSelect" name="fk_int_purpose_id" id="enquiryPurposeSelectEdit1">
                        <option value=""> Select Lead purpose</option>
                        @foreach($purposes as $key=>$purpose)
                        <option value="{{ $purpose->pk_int_purpose_id }}">{{ $purpose->vchr_purpose }}</option>
                        @endforeach
                     </select>
                     <div class="error fk_int_purpose_id ">
                        <span class="muted"></span>
                     </div>
                  </div>

                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Enquiry Status</label>
                     <select class="form-control" name="feedback_status" id="feedback_status">
                        <option value="">-- Select Status --</option>
                        @foreach($enquiry_status as $status)
                        <option value="{{ $status->pk_int_feedback_status_id }}">{{ $status->vchr_status }}
                        </option>
                        @endforeach
                     </select>
                  </div>

                  @endif
                  @if(!is_array($active_fields) || !in_array('lead_type',$active_fields))
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Lead Type</label>
                     <select class="form-control" name="lead_type_id" id="lead_type_id_edit">
                        <option value="">Select Lead Type</option>
                        @foreach(App\Common\Variables::getLeadTypes() as $key=>$lead_type)
                        <option value="{{ $lead_type->id }}">{{ $lead_type->name }}</option>
                        @endforeach
                     </select>
                  </div>
                  @endif
                  @if(\App\User::getVendorId()==\App\Common\Variables::APOLO_USER_ID)
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Purchase Date</label>
                     <input type="date" name="purchase_date" class="form-control" placeholder="">
                     <div class="error purchase_date">
                        <span class="muted"></span>
                     </div>
                  </div>
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Expected weight in grams</label>
                     <input type="text" name="exp_wt_grams" class="form-control" placeholder="">
                     <div class="error exp_wt_grams">
                        <span class="muted"></span>
                     </div>
                  </div>
                  <div class="md-form mb-1 col-md-4">
                     <label for="default-input" class="form-control-label">Function Date</label>
                     <input type="date" name="function_date" class="form-control" placeholder="">
                     <div class="error function_date">
                        <span class="muted"></span>
                     </div>
                  </div>
                  @endif
               </div>
               <h3 class="card-title"><img src="/backend/images/feedback.svg" class="mr-6 pr-2">Notes</h3>
               <div class="row">
                  @if(!is_array($active_fields) || !in_array('feedback',$active_fields))
                  <div class="md-form mb-1 col-md-12">
                     <label for="default-input" class="form-control-label">Customer Feedback</label>
                     <input name="vchr_enquiry_feedback" class="form-control" type="text" placeholder="Customer Feedback">
                     <div class="error vchr_enquiry_feedback ">
                        <span class="muted"></span>
                     </div>
                  </div>
                  @endif
               </div>
               @if(count($additional_fields)>0)
               <h3 class="card-title"><img src="/backend/images/enquiry.svg" class="mr-6 pr-2" style="height:40px">Additional Fields</h3>

                        <div class="">
                           {{--
                           <p>
                              --}}
                              <!-- <button class="btn btn-primary" type="button" data-toggle="collapse" data-target="#collapseExample1" aria-expanded="false" aria-controls="collapseExample1">
                                 Additional Details
                                 </button> -->
                              {{--
                           </p>
                           --}}
                           {{--
                           <div class="collapse" id="collapseExample1">
                              --}}
                              <div class="card card-body p-0">
                                 <div class="row" id="add_details">
                                 </div>
                              </div>
                              {{--
                           </div>
                           --}}
                        </div>
               @endif
               <div class="modal-footer d-flex justify-content-center col-md-12">
                  <button class="btn btn-deep-orange demo-btn btn-info">Update</button>
               </div>
            </div>
         </div>
      </div>
   </form>
</div>