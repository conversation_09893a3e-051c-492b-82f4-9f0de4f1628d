@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">


                <div class="add-y-team">
                    <div class="dashboard-row">
                          <div class="y-team-header y-team-header-V2">
                             <a href="{{ url()->previous() }}">
                                
                                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                      <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z" fill="#4D5459"/>
                                   </svg>
                                   Send SMS
                               
                             </a>
                          </div>
                    </div>
                </div>

                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class="">
                                    <a class=" main-round-btn bdr1 bdr-blue mg-lft-25 clr-white"
                                       href="{{url('user/smstemplates')}}">Add Sms Template </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @if ($roleId == 2)
                @include ('backend.layout.sidebar-v2.crmsidebar-v2')
            @else
                @include ('backend.layout.sidebar-v2.admincrmsidebar')
            @endif
            <div class="content-section mt-2 p-3 bg-white">
                <!--  /Your content goes here/ -->
                @if(session('flash_notification'))
                    <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
                @endif
                <section class="w-template-sec">
                    <div class="row d-flex justify-content-center">
                        <div class="col-lg-10 col-md-7">
                            @if ($roleId == 2)
                                <form method="post" action="{{ url('user/crm-sms') }}" id="Add"
                                      enctype="multipart/form-data">
                                    @else
                                        <form method="post" action="{{ url('admin/send-whatsapp') }}" id="Add"
                                              enctype="multipart/form-data">
                                            @endif

                                            <div class="user-details-block">
                                                <div class="heading">
                                                    <h4>Select Any One Template/Text Message</h4>
                                                </div>
                                                @if ($template1)
                                                    <div class="user-details">
                                                        <div class="row item-title">
                                                            <div class="col-md-2"></div>
                                                            <div class="col-md-4">
                                                                <p class="item-n">Template Name</p>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <p class="item-desc">Template</p>
                                                            </div>
                                                        </div>
                                                        @foreach($template as $key=>$temp)
                                                            <div class="row user-item">
                                                                <div class="col-md-2">
                                                                    <!-- <input type="checkbox" class="chk" /> -->
                                                                    <input type="hidden" name="_token"
                                                                           value="{{ csrf_token() }}">
                                                                    <input type="hidden" name="id" value="{{ $id }}">
                                                                    <div class="custom-control custom-checkbox">
                                                                        <input type="checkbox"
                                                                               class="custom-control-input chk"
                                                                               id="customCheck{{ $key }}"
                                                                               name="sms_template_code" @if($key==0) checked
                                                                               @endif value="{{ $temp->sms_template_code}}">
                                                                        <label class="custom-control-label"
                                                                               for="customCheck{{ $key }}"></label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <p class="item-n">{{ $temp->vchr_sms_template_title }}</p>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <p class="item-desc">{{ $temp->sms_template_code }}</p>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                        @if ($errors->has('template'))
                                                            <span class="text-danger"><strong> * Choose a template.</strong></span>
                                                        @endif

                                                        <div class="row d-flex justify-content-center mg-tp-20">
                                                            <button type="submit" class="main-round-btn transclass"
                                                                    href="#">Send
                                                            </button>
                                                        </div>

                                                    </div>
                                            </div>
                                       

                                        @else
                                           {{--  <div class="row d-flex justify-content-center mg-tp-20">
                                                <div class="col-md-6 text-center">
                                                    <h5> No Approved Template Available</h5>
                                                </div>
                                            </div> --}}

                                            <div class="form-group row  d-flex justify-content-center align-items-center">
                                             <div class="col-sm-6 mt-5">
                                              <textarea name="sms_template_code" class="form-control"  rows="10" cols="12" required="" placeholder="Message"></textarea>
                                              <input type="hidden" name="_token"
                                                     value="{{ csrf_token() }}">
                                              <input type="hidden" name="id" value="{{ $id }}">
                                              <span class="val-error sms_template_code"></span>
                                          </div>
                                          
                                </div>
                                <div class="form-group row  d-flex justify-content-center align-items-center">
                                <div class="col-sm-2">
                                            <button type="submit" class="demo-btn sms-btn transclass"
                                            href="#">Send
                                        </button>
                                    </div>
                                </div>
                            </form>

                                
                            @endif
                             
                        </div>

                    </div>
                </section>
            </div>
        </div>
    </main>
@endsection
@push('footer.script')
    <script type="text/javascript">

        $('input.chk').on('change', function () {
            $('input.chk').not(this).prop('checked', false);
        });
    </script>
@endpush