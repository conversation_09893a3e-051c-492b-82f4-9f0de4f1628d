@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>FESTIVAL OFFER PAYMENTS</h5>
                </div>
                {{-- <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add SMS Template </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <table id="enquiry-info-table" class="table table-striped table-bordered" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Username</th>
                            <th >Mobile</th>
                            <th>EMail</th>
                            <th>Plan</th>
                            <th>Amount</th>
                            <th>SMS Required</th>
                            <th>Is Paid</th>
                            <th width="10%">Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </main>


@endsection 
@push('footer.script')
 <script type="text/javascript">
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};


             

            $('.error').hide();
            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "170px",
                    "targets": [0, 1,2]
                },

                ],
                ajax: BASE_URL + '/admin/get-smspurachase-info',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'vchr_username',
                        name: 'vchr_username'
                    },
                    {
                        data: 'vchr_mobile',
                        name: 'vchr_mobile'
                    },
                    {
                        data: 'vchr_email',
                        name: 'vchr_email'
                    },
                    {
                        data: 'vchr_plan',
                        name: 'vchr_plan'
                    },
                    {
                        data: 'int_amount',
                        name: 'int_amount'
                    },
                    {
                        data: 'int_sms_required',
                        name: 'int_sms_required'
                    },
                    {
                        data: 'payment',
                        name: 'payment'
                    },
                
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ]
            });

           

             $('#enquiry-info-table').on('click', '.purchase-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('purchase-id');
                var destinationPath = BASE_URL + '/admin/sms-purchase/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });


            $('#enquiry-info-table').on('click', '.senderid-act', function(event) {
                  event.preventDefault();
                    var templateId = $(this).attr('senderid-id');
                    if($(this).hasClass('btn-activate')) {

                        // var url = BASE_URL + '/admin/smstemplate-deactivate/';
                        // action = 'Reject';
                        $('#id').val(templateId);
                        $($(this).data('target')).modal('toggle');



                }else 
                {
                var url = BASE_URL + '/admin/senderid-activate/';
                action = 'Approve';

                var feedbackId = $(this).attr('senderid-id');
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            }
            });

 });

 </script>
@endpush
