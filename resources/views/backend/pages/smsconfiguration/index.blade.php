@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>SMS API CONFIGURATION</h5>
                </div>
                {{-- <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add SMS Template </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                @if(!empty($api))
                   <div class="row">
                    <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3  pull-right"  id="editApiData" name="button">EDIT SMS API</button>
                    <div class="col-sm-12 py-3">
                    <form id="submitApi">
                        <table id="enquiry-info-table" class="table  table-bordered" width="80%">
                            <thead>
                                <tr>
                                    <th width="30%">Field</th>
                                    <th  width="70%">Data</th>
                                </tr>
                            </thead>
{{--                             vchr_sms_configuration_name
                                    text_sms_configuration_api
                                    text_sms_configuration_delivery_api
                                    int_sms_configuration_default
                                    int_sms_configuration_status
                                    vchr_sms_configuration_senderid --}}
                            <tbody>
                                <tr>
                                    <td>SMS API CONFIGURATION NAME</td>
                                    <td>
                                    <div class="col-sm-9">
                                        <textarea name="vchr_sms_configuration_name" class="form-control" placeholder="" rows="2" >{{$api->vchr_sms_configuration_name}}
                                        </textarea>
                                        <span class="val-error  vchr_sms_configuration_name"></span>
                                        <input type="hidden" name="id" class="form-control" placeholder="" value="{{$api->pk_sms_configuration_id}}" id="id">
                                        {{csrf_field()}}
                                        <input type="hidden" name="_method" class="form-control" placeholder="" value="PUT" id="">
                                    </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>SMS CONFIGURATION API </td>
                                    <td>
                                        <div class="col-sm-9">
                                        <textarea name="text_sms_configuration_api" class="form-control" placeholder="" rows="5" > {{$api->text_sms_configuration_api}}</textarea>
                                        <span class="val-error  text_sms_configuration_api"></span>
                                        {{-- <input type="text" name="short_code_description" class="form-control" placeholder=""> --}}
                                    </div>
                                    </td>
                                </tr>
                                 <tr>
                                    <td>SMS CONFIGURATION SENDERID</td>
                                    <td>
                                         <div class="col-sm-9">
                                        <textarea name="vchr_sms_configuration_senderid" class="form-control" placeholder="" rows="2" > {{$api->vchr_sms_configuration_senderid}}</textarea>
                                        <span class="val-error  vchr_sms_configuration_senderid"></span>
                                        {{-- <input type="text" name="short_code_description" class="form-control" placeholder=""> --}}
                                    </div>
                                    </td>
                                </tr>
                                 <tr>
                                    <td>SMS CONFIGURATION DELIVERY API</td>
                                    <td>
                                        <div class="col-sm-9">
                                        <textarea name="text_sms_configuration_delivery_api" class="form-control" placeholder="" rows="5" > {{$api->text_sms_configuration_delivery_api}}</textarea>
                                        <span class="val-error  text_sms_configuration_delivery_api"></span>
                                        {{-- <input type="text" name="short_code_description" class="form-control" placeholder=""> --}}
                                    </div>
                                    </td>
                                </tr>
                                 <tr>
                                    <td>SMS API CONFIGURATION STATUS(Active/Inactive)</td>
                                    <td> 

                                        <input type="hidden" name="int_sms_configuration_status" id="hidden-checkbox" value="{{$api->int_sms_configuration_status}}">
                                         <label id="active-data-label" class="ks-checkbox-slider ks-on-off ks-solid   @if($api->int_sms_configuration_status==1){{'ks-success'}} @else{{'ks-danger'}}@endif ">
                                                <input type="checkbox"  name="" value="{{$api->int_sms_configuration_status}}" id="active-data" @if($api->int_sms_configuration_status==1){{'checked'}} @else{{''}}@endif >
                                                <span class="ks-indicator"></span>
                                                <span class="ks-on">YES</span>
                                                <span class="ks-off">NO</span>
                                            </label>

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="col-sm-12">
                           <div class="row"> 
                             <button type="submit" class="btn  btn-primary btn-md"  id="submitApi1" name="button" style="margin-left: 40%;">UPDATE </button>
                           </div>
                        </div>

                    </form>
                    </div>
                </div>
             @else
              {{-- http://sms.webqua.in/httpapi/smsapi?uname=getlead&password=Secret12&sender=gl_sender&receiver=gl_number&group=&route=TA&msgtype=1&sms=gl_message --}}
                 <div class="row">
                    <div class="ks-column ks-page">
                        <div class=" ks-404">
                            <div class="ks-error-page">
                                <div class="ks-error-code">404</div>
                                <div class="ks-error-description">Sorry, SMS API is not found</div>
                                <a  class="btn btn-primary ks-light ks-izi-modal-trigger"
                                    data-target="#ks-izi-modal-large1">Add new SMS API settings</a>
                            </div>
                        </div>
                    </div>
                 </div>
             @endif
                
            </div>
        </div>
    </main>

    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
       <form  id="testimonialAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">SMS API CONFIGURATION NAME</label>
                            <input type="text" name="vchr_sms_configuration_name" class="form-control" placeholder="" >
                            <span class="val-error vchr_sms_configuration_name"></span>
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">SMS CONFIGURATION API </label>                    
                            <textarea name="text_sms_configuration_api" class="form-control" placeholder="" rows="5" ></textarea>
                            <span class="val-error  text_sms_configuration_api"></span>
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="col-sm-3 form-control-label">SMS CONFIGURATION SENDERID</label>
                            <textarea name="vchr_sms_configuration_senderid" class="form-control" placeholder="" rows="2" ></textarea>
                            <span class="val-error  vchr_sms_configuration_senderid"></span>                
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="col-sm-3 form-control-label">SMS CONFIGURATION DELIVERY API</label>
                            <textarea name="text_sms_configuration_delivery_api" class="form-control" placeholder="" rows="5" ></textarea>
                            <span class="val-error  text_sms_configuration_delivery_api"></span>              
                        </div>             
                    </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection 
@push('footer.script')
<script type="text/javascript">

    
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};

            $('input').prop('disabled',true);
            $('textarea').prop('disabled',true);
            $('#submitApi1').hide();

            // $('#ks-izi-modal-large1').iziModal({
            //     autoOpen: false,
            //     padding: 20,
            //     headerColor: '#3a529b',
            //     restoreDefaultContent: true,
            //     title: "SMS API Configuration",
            //     fullscreen: true,
            //     subtitle: 'Add SMS API Configuration',
            //     transitionIn: 'fadeInDown'
            // });

             $('.ks-izi-modal-trigger').on('click', function (e) {
                $('input').prop('disabled',false);
                $('textarea').prop('disabled',false);
                // $($(this).data('target')).iziModal('open');
                $('#ks-izi-modal-trigger').modal('toggle');
            });

            $(document).on('submit', '#testimonialAdd', function(event) {
                event.preventDefault();
                // $('').on('click', function (e) {
               $('.val-error').html('');
              $('.val-error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/sms-api-configuration',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    // $($('.ks-izi-modal-trigger').data('target')).iziModal('close');
                    // swal(res.msg);
                     $('#ks-izi-modal-trigger').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });

                    setTimeout(function(){ location.reload(); }, 2000);
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                    

                });
            });

            
                 $(document).on('submit', '#submitApi', function(event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id=$('#id').val();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/sms-api-configuration/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                  
                    // swal(res.msg);
                     $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                // location.reload();

            });

            });

            $(document).on('click', '#editApiData', function(event) {
                $('input').prop('disabled',false);
                $('textarea').prop('disabled',false);
                $('#submitApi1').show();
            });

            $(document).on('change', '#active-data', function(event) {
               var checkedVal=$('#active-data').val();
               if(checkedVal==1)
               {
                    $('#active-data').val(0);
                    $('#hidden-checkbox').val(0);
                    $('#active-data-label').addClass('ks-danger');
                    $('#active-data-label').removeClass('ks-success');

               }
               else
               {
                    $('#active-data').val(1);
                    $('#hidden-checkbox').val(1);
                    $('#active-data-label').addClass('ks-success');
                    $('#active-data-label').removeClass('ks-danger');
               }

            });
            
        });
    </script>
@endpush
