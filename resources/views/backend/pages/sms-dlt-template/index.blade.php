@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
@include('backend.pages.sms-dlt-template.approve')
@include('backend.pages.sms-dlt-template.deactivate')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>SMS DLT TEMPLATE APPROVAL</h5>
                </div>
                
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.adminapprovalsidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table" style="overflow: auto">
                    <table id="enquiry-table" class="table table-striped table-bordered  table-custom" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th>Sl No</th>
                                <th>Title</th>
                                <th>Template</th>
                                <th>Header Id</th>
                                <th width="10%">Actions</th>
                            </tr>
                        </thead>
                    </table>
            </div>
            </div>
        </div>
    </main>
@endsection 
@push('footer.script')
<script type="text/javascript">
    $(document).ready(function(){
        $('#approve_spin').hide();
        
        BASE_URL ={!! json_encode(url('/')) !!}
      
        $('#enquiry-table').DataTable({
           scrollX: true,
           paging: true,
            language: {
                searchPlaceholder: 'Search',
                sSearch: '',
                lengthMenu: '_MENU_ page',
            },
            columnDefs: [{
                    "width": "140px",
                    "targets": [0, 4]
                },
            ],
            ajax: BASE_URL + '/admin/view-sms-dlt-template',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'title',
                        name: 'title'
                    },
                    {
                        data: 'template',
                        name: 'template'
                    },
                    {
                        data: 'header_id',
                        name: 'header_id'
                    },
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
        });
    });

// setting values for approve popup
    $(document).on('click', '.approve_btn', function () {
            $('#approve_spin').hide();
            $('#id').val($(this).data('id'));
            $('#title').val($(this).data('title'));
            $('#template').val($(this).data('template'));
            $('#entity_id').val($(this).data('entity_id'));
            $('#header_id').val($(this).data('header_id'));
            $('#template_id').val($(this).data('template_id'));
            $('#sender_id').val($(this).data('sender_id'));
        });
// ends

// setting values for deactivate popup
$(document).on('click', '.deactivate-btn', function () {
            $('#deactivate_spin').hide();
            $('#deactivate_id').val($(this).data('id'));
            $('#deactivate_title').val($(this).data('title'));
            $('#deactivate_template').val($(this).data('template'));
            $('#deactivate_entity_id').val($(this).data('entity_id'));
            $('#deactivate_header_id').val($(this).data('header_id'));
            $('#deactivate_template_id').val($(this).data('template_id'));
            $('#deactivate_sender_id').val($(this).data('sender_id'));
        });
// ends

// change the status 
 $(document).on('submit', '#approve', function(event) {
            $('#approve_spin').show();
            event.preventDefault();
                $.ajax({
                    url: BASE_URL + '/admin/sms-dlt-templates/approve',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                        $('#approve_spin').hide();
                        $("#approve")[0].reset();
                        $('#approve_popup').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    }else{
                        $('#approve_spin').hide();
                    }
                })
                .fail(function() {
                })
                .always(function(com) {
                   $('#enquiry-table').DataTable().ajax.reload(null, false);
                });
            });
// end

// change the status (for deactivate)
$(document).on('submit', '#deactivate', function(event) {
  
            $('#deactivate_spin').show();
            event.preventDefault();
                $.ajax({
                    url: BASE_URL + '/admin/sms-dlt-templates/de-approve',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                        $('#deactivate_spin').hide();
                        $('#deactivate_popup').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    }else{
                        $('#deactivate_spin').hide();
                    }
                })
                .fail(function() {
                })
                .always(function(com) {
                   $('#enquiry-table').DataTable().ajax.reload(null, false);
                });
            });
// end

// delete sms dlt data
$('#enquiry-table').on('click', '.smddlttemplate-delete', function (event) {
            event.preventDefault();
            var id = $(this).attr('smstemplate-id');
            var deleteUrl = BASE_URL + '/admin/sms-dlt-template/' + id;
          
            $.confirm({
                title: 'Deletion',
                content: 'Are you sure you want to delete ?',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                   'confirm': {
                       text: 'Proceed',
                       btnClass: 'btn-info',
                       action: function () {
                            $.ajax({
                                url: deleteUrl,
                                type: 'DELETE',
                            })
                            .done(function(res) {
                                if(res.status == 'success') {
                                    $.alert({
                                        title: 'Success',
                                        type: 'green',
                                        content: res.msg,
                                    });
                                } else {
                                    $.alert({
                                        title: 'Failed',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                }
                            })
                            .fail(function(err) {
                            })
                            .always(function(com) {
                                $('#enquiry-table').DataTable().ajax.reload(null, false);
                            });
                        }
                    },
                    cancel: function () {
                        $.alert('Operation <strong>canceled</strong>');
                    }
                }
            });
        });
//ends
 </script>
@endpush

