@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>SENDER ID MANAGEMENT</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminbulkpushsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <div class="p-3">
                    <ul class="nav ks-nav-tabs ks-tabs-page-default ks-tabs-full-page">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" data-toggle="tab" data-target="#system-settings">
                                ADMIN SENDERID
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-toggle="tab" data-target="#smtp-settings">
                                ADD SENDERID FOR USER
                            </a>
                        </li>
                        {{--<li class="nav-item">--}}
                            {{--<a class="nav-link" href="#" data-toggle="tab" data-target="#history">--}}
                                {{--SENDERID HISTORY--}}
                            {{--</a>--}}
                        {{--</li>--}}
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane active ks-column-section" id="system-settings" role="tabpanel">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="card" style="padding-left: 10px;">
                                        <div class="card-block" style="padding-left: 10px;
                                             padding-right: 10px;">
                                            <div class="row">
                                                <h5 class="card-title m-3">ADD  SENDER ID FOR ADMIN</h5>
                                                 <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3 ks-izi-modal-trigger"  data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" name="button">Add Senderid</button>
                                            </div>
                                              
                                            
                                                {{-- table --}}
                                                <div class="col-sm-12 py-3 gl-table">
                                                <table id="enquiry-info-table" class="table table-striped table-bordered enquiry-info-table" width="100%">
                                                    <thead>
                                                        <tr>
                                                        <th>Sl No</th>
                                                        <th>Senderid Name</th>
                                                        <th>Senderid</th>
                                                        <th>Default Senderid</th>
                                                        <th width="10%">Actions</th>
                                                        </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane" id="smtp-settings" role="tabpanel">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="card" style="padding-left: 10px;">
                                        <div class="card-block">
                                            {{-- table --}}
                                             <div class="row">
                                            <div class="col-sm-12 py-3">
                                                <div class="row">
                                                    <div class="col-lg-6 ks-panels-column-section">
                                                        <div class="card" style="padding-left: 10px;">
                                                            <div class="card-block" style="padding-top:10px;padding-right:10px;">
                                                                <h5 class="card-title">ADD SENDERID FOR USER</h5>
                                                                <form id="senderidAdd" method="POST">
                                                                    <div class="form-group row">
                                                                        <label for="default-input-rounded" class="col-sm-3 form-control-label">Select user</label>
                                                                        <div class="col-sm-9">
                                                                            <select class="form-control selectpicker" data-show-subtext="true" data-live-search="true" id="users" name="fk_int_user_id">
                                                                                
                                                                            </select>
                                                                            <span class="val-error fk_int_user_id"></span>
                                                                        </div>
                                                                        {{-- <div class="col-sm-3">
                                                                       
                                                                           <button type="submit" class="btn btn-primary" id="mastersmsAdd1">ADD</button>
                                                                        </div> --}}
                                                                        {{csrf_field()}}
                                                                    </div>
                                                                <div class="form-group row">
                                                                    <input type="hidden" name="pk_int_short_code_id">
                                                                     <label for="default-input" class="col-sm-3 form-control-label">Senderid Name</label>
                                                                    <div class="col-sm-9">
                                                                       <input type="text" name="sendername" class="form-control" placeholder="" >
                                                                        <span class="val-error sendername"></span>
                                                                    </div>
                                                                </div>
                                                                <div class="form-group row">
                                                                    <input type="hidden" name="pk_int_short_code_id">
                                                                     <label for="default-input" class="col-sm-3 form-control-label">Senderid</label>
                                                                    <div class="col-sm-9">
                                                                       <input type="text" name="senderid" class="form-control" placeholder="" >
                                                                        <span class="val-error senderid"></span>
                                                                    </div>
                                                                </div>
                                                                    <div class="form-group row">
                                                                    <label for="default-input" class="col-sm-3 form-control-label"></label>
                                                                    <div class="col-sm-3">
                                                                   
                                                                       <button type="submit" class="btn btn-primary" id="mastersmsAdd1">ADD</button>
                                                                    </div>

                                                                </div>

                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-lg-6 ks-panels-column-section">
                                                        <div class="card" style="padding-left: 10px;">
                                                            <div class="card-block">
                                                                <h5 class="card-title">USER SENDERID TABLE</h5><span style="color: red;">Only <b>ACTIVE SENDERID</b>'s  are shown</span>
                                                                <div>
                                                                    <div class="col-sm-12 py-3">
                                                                     <div class="table-responsive gl-table">
                                                                            <table id="enquiry2-info-table" class="table table-striped table-bordered enquiry-info-table" width="100%">
                                                                            <thead>
                                                                                <tr>
                                                                                <th>Sl No</th>
                                                                                <th>Name</th>
                                                                                <th>Senderid</th>
                                                                               
                                                                                <th width="10%">DELETE</th>
                                                                                </tr>
                                                                            </thead>
                                                                        </table>
                                                                     </div>
                                                                    </div>
                                                                           
                                                                </div>
                                                            </div>
                                                        </div>

                                                       
                                                    </div>

                                                </div>
                                            </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        <div class="tab-pane" id="history" role="tabpanel">
                            <br>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="card">
                                        <div class="card-block">
                                            <div class="row">
                                                <h5 class="card-title" style="padding-left:30px;">SENDERID HISTORY</h5>
                                  
                                            </div>
                                            {{-- table --}}
                                            <div class="col-sm-12 py-3 gl-table">
                                                <table id="enquiry1-info-table" class="table table-striped table-bordered" width="100%">
                                                    <thead>
                                                        <tr> 
                                                           <th>Sl No</th>
                                                            <th>History statement</th>
                                                            <th>Created at</th>
                                                        </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!------------ /add modal/  -------------->

    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
       <form  id="testimonialAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Sender Id For Admin</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                                <label for="default-input" class="form-control-label">Senderid Name</label>
                                <input type="text" name="sendername" class="form-control" placeholder="" >
                                <span class="val-error sendername"></span>
                        </div>
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">Senderid</label>
                            <input type="text" name="senderid" class="form-control" placeholder="" >
                            <span class="val-error senderid"></span>
                        </div>             
                    </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button class="btn btn-deep-orange demo-btn">Add</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="Edit">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update SMS Template</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">SMS Template Name</label>
                                <input type="text" name="smstemplatetitle" class="form-control" placeholder="" id="sender_id_name">
                                 <span class="val-error smstemplatetitle"></span>        
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">SMS Template</label>
                                <textarea rows="4" type="text" name="smstemplatebody" class="form-control" placeholder="" id="smstemplatebody">
                                </textarea>
                                <span class="val-error text_sms_template_description"></span>
                                <input type="hidden" name="_method"  value="PUT" class="form-control" placeholder="" >
                                <input type="hidden" name="pk_int_sender_id" class="form-control" placeholder=""  id="pk_int_sender_id">
                            </div>             
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
        </div>
    </main>
@endsection 
@push('footer.script')
<script type="text/javascript">
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};
    //get user data
        $.ajax({
                    url: BASE_URL + '/admin/get-users',
                    type: 'GET',
                    dataType: 'JSON',
                })
                .done(function(res) {
                    if(res.status == 'success'){

                        // console.log(res.data);
                         $("#users").append("<option value=''>Selct User</option>");
                    $.each(res.data,function(key, value)
                {
                    $("#users").append('<option value=' + value.id + '>' + value.name + '</option>');
                });
                }else{
                    $("#users").append("<option value=''>No user found</option>");
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
               
         });

            // $('#ks-izi-modal-large').iziModal({
            //     autoOpen: false,
            //     padding: 20,
            //     headerColor: '#3a529b',
            //     restoreDefaultContent: true,
            //     title: "Senderid",
            //     fullscreen: true,
            //     subtitle: 'Edit Senderid',
            //     transitionIn: 'fadeInDown'
            // });

            // $('#ks-izi-modal-large1').iziModal({
            //     autoOpen: false,
            //     padding: 20,
            //     headerColor: '#3a529b',
            //     restoreDefaultContent: true,
            //     title: "Senderid",
            //     fullscreen: true,
            //     subtitle: 'Add Senderid',
            //     transitionIn: 'fadeInDown'
            // });

            // $('.ks-izi-modal-trigger').on('click', function (e) {
            //     $($(this).data('target')).iziModal('open');

            // });

            $('#enquiry-info-table').DataTable({
                autoWidth: 1,
                lengthChange: 1,
                // buttons: ["print", "excel", "pdf"],
                select: !1,
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                processing: true,
                serverSide: true,
                searching: true,
                orderable: true,
                ajax: BASE_URL + '/admin/get-admin-senderid',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },



                    {
                        data: 'vchr_sender_id_name',
                        name: 'vchr_sender_id_name'
                    },
                    {

                        data: 'vchr_sender_id',
                        name: 'vchr_sender_id'
                    },
                    {
                        data: 'default',
                        name: 'default'
                    },
                   
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
                "initComplete": function () {
                    $('.dataTables_wrapper select').select2({
                        minimumResultsForSearch: Infinity
                    });
                }
            });

             $('#enquiry2-info-table').DataTable({
                autoWidth: 1,
                lengthChange: 1,
                // buttons: ["print", "excel", "pdf"],
                select: !1,
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                processing: true,
                serverSide: true,
                searching: true,
                orderable: true,
                ajax: BASE_URL + '/admin/get-single-senderid?id=',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },


                    {
                        data: 'vchr_sender_id_name',
                        name: 'vchr_sender_id_name'
                    },
                    {

                        data: 'vchr_sender_id',
                        name: 'vchr_sender_id'
                    },
                    
                   
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
                "initComplete": function () {
                    $('.dataTables_wrapper select').select2({
                        minimumResultsForSearch: Infinity
                    });
                }
            });

            $('#enquiry1-info-table').DataTable({
                autoWidth: 1,
                lengthChange: 1,
                // buttons: ["print", "excel", "pdf"],
                select: !1,
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                processing: true,
                serverSide: true,
                searching: true,
                orderable: true,
                ajax: BASE_URL + '/admin/get-admin-senderidhistory',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },



                    {
                        data: 'text_senderid_statement',
                        name: 'text_senderid_statement'
                    },
                    {

                        data: 'created_at',
                        name: 'created_at'
                    },

                ],
                "initComplete": function () {
                    $('.dataTables_wrapper select').select2({
                        minimumResultsForSearch: Infinity
                    });
                }
            });

        $(document).on('change', '#users', function(event) {
                 event.preventDefault();
                 var id=$('#users').find(":selected").val();
                 var tbl=$('#enquiry2-info-table').DataTable();
                 if(id=='')
                 {
                   url='/admin/get-single-senderid';
                    tbl.ajax.url(BASE_URL + url).load();
                 }
                 else
                 {
                    url='/admin/get-single-senderid?id='+id;
                    tbl.ajax.url(BASE_URL + url).load();
                 }
                
                 });


            $(document).on('submit', '#testimonialAdd', function(event) {
                event.preventDefault();
                // $('').on('click', function (e) {
               $('.val-error').html('');
              $('.val-error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/admin-senderid',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    // $($('.ks-izi-modal-trigger').data('target')).iziModal('close');
                    // swal(res.msg);
                    $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    if(typeof res.msg =='object')
                    {
                        $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    }
                    else
                    {
                         $.alert({
                            title: 'Error',
                            type: 'red',
                            content: res.msg,
                        });
                    }
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                $('#enquiry1-info-table').DataTable().ajax.reload(null, false);


            });

            });

            //senderid add for user
            $(document).on('submit', '#senderidAdd', function(event) {
                event.preventDefault();
                // $('').on('click', function (e) {
               $('.val-error').html('');
              $('.val-error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/admin-senderid-add',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    // $($('.ks-izi-modal-trigger').data('target')).iziModal('close');
                    $('input').val('');


                    swal(res.msg);
                }else{
                    if(typeof res.msg =='object')
                    {
                        $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    }
                    else
                    {
                         $.alert({
                            title: 'Error',
                            type: 'red',
                            content: res.msg,
                        });
                    }
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry2-info-table').DataTable().ajax.reload(null, false);
                $('#enquiry1-info-table').DataTable().ajax.reload(null, false);


            });

            });

            
        //EDIT
        $('#enquiry-info-table').on('click', '.ks-izi-modal-trigger1', function(event) {

            var testimonialId = $(this).attr('senderid-id'); 
            $.ajax({
                    url: BASE_URL + '/admin/admin-senderid/' + testimonialId,
                    type: 'GET',
                    dataType: 'JSON',
                })
            .done(function(res) {
                if(res.status == "success") {
                        data = res.data;

                        $("[name='sendername']").val(data.vchr_sender_id_name);
                        $("[name='senderid']").val(data.vchr_sender_id);
                       
                        $('#pk_int_sender_id').val(data.pk_int_sender_id);

                        
                        $($('.ks-izi-modal-trigger1').data('target')).iziModal('open');
                        
                    }
            })
            .fail(function(err) {

            })
            .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });
        });

        //update
        $(document).on('submit', '#testimonialEdit', function(event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id=$('#pk_int_sender_id').val();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/senderid/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    $($('.ks-izi-modal-trigger1').data('target')).iziModal('close');
                    swal(res.msg);
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });


        //senderid delete
           $('.enquiry-info-table').on('click', '.senderid-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('senderid-id');
                var destinationPath = BASE_URL + '/admin/admin-senderid/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                    $('#enquiry2-info-table').DataTable().ajax.reload(null, false);

                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

           //default sender id
            $('#enquiry-info-table').on('click', '.default-senderid', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('senderid-id');
                var destinationPath = BASE_URL + '/admin/admin-default-senderid/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Dafault sender id',
                    content: 'Are you sure want to make sender id as default ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'GET',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            //activatedeactivate
            $('#enquiry-info-table').on('click', '.senderid-act', function(event) {
                  event.preventDefault();
                    var templateId = $(this).attr('senderid-id');
                    if($(this).hasClass('btn-activate')) {
                        var url = BASE_URL + '/admin/admin-senderid-deactivate/';
                        action = 'Reject';
                }else 
                {
                var url = BASE_URL + '/admin/admin-senderid-activate/';
                action = 'Approve';
                }

                var feedbackId = $(this).attr('senderid-id');
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            
            });
 }); 
</script>
@endpush