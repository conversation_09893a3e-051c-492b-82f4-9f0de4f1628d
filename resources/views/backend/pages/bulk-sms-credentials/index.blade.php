@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>SMS-Credentials</h5>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class="">
                                    <a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger"
                                       href="{{url('admin/bulk-sms-credentials/create')}}">Add Credentials</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.admin-credentials.sidebar')
            <div class="content-section p-3 bg-white">

                <div class="col-sm-12 justify-content-center">
                    <h5 class="justify-content-center">{{$route_name}}</h5>
                </div>


                <!--  /Your content goes here/ -->
                <table id="xml_api_data_table" class="table table-striped table-bordered nowrap table-custom"
                       cellspacing="0" width="100%">
                    <thead>
                    <tr>
                        <th>Sl No</th>
                        <th>Users</th>
{{--                        <th>Route</th>--}}
                        <th>Credentials</th>
                        <th width="10%">Actions</th>
                    </tr>
                    </thead>
                    <tbody>

                    </tbody>
                </table>
            </div>
        </div>
    </main>
@endsection
@push('footer.script')
    <script type="text/javascript">

        $(document).ready(function () {
            BASE_URL = window.location.origin;
            $('.error').hide();
            var priority = getUrlParameter('priority');

            $('#xml_api_data_table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [
                    {"width": "20px", "targets": [2]},
                ],
                ajax: BASE_URL + '/admin/get-bulk-sms-api-credentials?priority=' + priority,
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'user', name: 'user'},
                    // {data: 'route', name: 'route'},
                    {data: 'credentials', name: 'credentials'},
                    {data: 'show', name: 'show', orderable: false, searchable: false}
                ],
            });
            $('#xml_api_data_table').on('click', '.designation-act', function (event) {
                event.preventDefault();
                if ($(this).hasClass('btn-activate')) {
                    var url = BASE_URL + '/admin/bulk-sms-api-credentials-deactivate/';
                    action = 'Deactivate';
                } else {
                    var url = BASE_URL + '/admin/bulk-sms-api-credentials-activate/';
                    action = 'Activate';
                }
                var feedbackId = $(this).attr('designation-id');
                url = url + feedbackId;
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                }).done(function (res) {
                                    if (res.status === 'success') {
                                        $.alert({
                                            title: 'Success', type: 'green', content: res.msg,
                                        });
                                    } else {
                                        $.alert({title: 'Failed', type: 'red', content: res.msg,});
                                    }
                                }).fail(function (err) {
                                }).always(function (com) {
                                    $('#xml_api_data_table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $('#xml_api_data_table').on('click', '.designation-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('designation-id');
                var destinationPath = BASE_URL + '/admin/bulk-sms-credentials/' + feedbackId;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                }).done(function (res) {
                                    if (res.status === 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                }).fail(function (err) {
                                }).always(function (com) {
                                    $('#xml_api_data_table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });

        var getUrlParameter = function getUrlParameter(sParam) {
            var sPageURL = window.location.search.substring(1),
                sURLVariables = sPageURL.split('&'),
                sParameterName,
                i;

            for (i = 0; i < sURLVariables.length; i++) {
                sParameterName = sURLVariables[i].split('=');

                if (sParameterName[0] === sParam) {
                    return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
                }
            }
        };
    </script>
@endpush





