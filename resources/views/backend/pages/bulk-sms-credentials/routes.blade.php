@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Bulk SMS Domain Routes</h5>
                </div>
               
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminbulkpushsidebar')
            <div class="content-section p-3 bg-white">
                <div class="col-sm-12 justify-content-center">
                    <h5 class="justify-content-center">{{$route_name}}</h5>
                </div>
                <!--  /Your content goes here/ -->
                <table id="sms_api_credentials_table" class="table table-striped table-bordered nowrap table-custom"
                       cellspacing="0" width="100%">
                    <thead>
                    <tr>
                        <th>Sl No</th>
                        <th>Users</th>
                        <th>Domain</th>
                        <th>Route</th>
                        <th>API Username</th>
                        <th width="10%">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </main>
    <!------------ /update modal/  -------------->
    <div class="modal fade" id="edit-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="domainEdit" >
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update Domain</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Select Domain</label>
                            <select name="default_id" id="default_id" class="form-control " style="width: 100%" required>
                                
                            </select>
                            <span class="error vendor_id"></span>
                        </div>
                    </div>
                    <input type="hidden" name="id" value="" id="edit-id">
                    <div class="modal-footer d-flex justify-content-center">
                        @csrf
                        <button class="btn btn-deep-orange demo-btn">Update</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection
@push('footer.script')
    <script type="text/javascript">
        $(document).ready(function () {
            BASE_URL ={!! json_encode(url('/')) !!};
            $('.error').hide();
            var priority = getUrlParameter('priority');

            // $('#ks-izi-modal-large1').modal('toggle');
            $('#sms_api_credentials_table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    // "width": "200px",
                    // "targets": [0, 1,2,3,4,5]
                },
                ],
                ajax: BASE_URL + '/admin/show-bulksms-api-routes',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'user', name: 'user'},
                    {data: 'domain', name: 'domain'},
                    {data: 'route', name: 'route'},
                    {data: 'username', name: 'username'},
                    {data: 'edit', name: 'edit', orderable: false, searchable: false}
                ],

            });


            $(document).on('submit', '#domainEdit', function (event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id = $('#edit-id').val();
                // var testimonialId = $(this).attr('designation-id'); 
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/change-bulksms-api-domain-route/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        $('#ks-izi-modal-large1').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            console.log(val);
                            console.log(index);
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                }).always(function (com) {
                    $('#sms_api_credentials_table').DataTable().ajax.reload(null, false);
                });
            });

            $('#sms_api_credentials_table').on('click', '.btn-edit', function (event) {
                event.preventDefault();
                $('#edit-id').val($(this).attr('credential-id'));
                var options = JSON.parse($(this).attr('data-domains'));
                var opts='';
                options.forEach(function(val,index){
                    opts += '<option value="'+val.id+'">'+val.title+' ('+val.username+')'+'</option>';
                });
                $('#default_id').html(opts);
                $('#edit-modal').modal('show');
            });

        });
        
        var getUrlParameter = function getUrlParameter(sParam) {
            var sPageURL = window.location.search.substring(1),
                sURLVariables = sPageURL.split('&'),
                sParameterName,
                i;

            for (i = 0; i < sURLVariables.length; i++) {
                sParameterName = sURLVariables[i].split('=');

                if (sParameterName[0] === sParam) {
                    return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
                }
            }
        };
    </script>
@endpush