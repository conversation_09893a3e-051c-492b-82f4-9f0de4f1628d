@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>SMS-Credentials</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminbulkpushsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <form action="{{url('admin/bulk-sms-credentials/'.$xml_api->id)}}" method="POST">
                    <div class="row">
                        {{method_field('PUT')}}
                        {{csrf_field()}}
                        <div class="col-sm-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Select User</label>
                                <select name="vendor_id" class="form-control select2" style="width: 100%" required>
                                    <option value="">Select User</option>
                                    <option value="0" @if($xml_api->vendor_id== 0) selected @endif>All Users</option>
                                    @foreach(\App\User::getUsers() as $user)
                                        <option value="{{ $user->pk_int_user_id }}"
                                                @if($xml_api->vendor_id== $user->id) selected @endif>{{ $user->name}}</option>
                                    @endforeach

                                </select>
                                <span class="error vendor_id"></span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Select Domain</label>
                                <select name="domain_id" class="form-control" required>
                                    <option value="">Select Domain</option>
                                    @foreach($domains as $domain)
                                        <option value="{{ $domain->id }}"
                                                @if($xml_api->domain_id== $domain->id) selected @endif>{{ $domain->title }}
                                            ({{ $domain->domain }})
                                        </option>
                                    @endforeach
                                </select>
                                <span class="error title"></span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Select Route</label>
                                <select name="route_id" class="form-control" required>
                                    <option value="">Select Route</option>
                                    @foreach($routes as $route)
                                        <option value="{{ $route->pk_int_sms_route_id }}"
                                                @if($xml_api->route_id== $route->pk_int_sms_route_id) selected @endif>{{ $route->vchr_sms_route }}</option>
                                    @endforeach
                                </select>
                                <span class="error domain"></span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">UserName</label>
                                <input type="text" name="api_username" placeholder="Username" class="form-control"
                                       value="{{$xml_api->api_username}}"
                                       required>
                                <span class="error username"></span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Password</label>
                                <input type="text" name="password" value="{{$xml_api->password}}"
                                       placeholder="Password.." class="form-control"
                                       required>
                                <span class="error password"></span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Api Password/Key</label>
                                <input type="text" name="api_password" value="{{$xml_api->api_password}}"
                                       placeholder="Api Password" class="form-control"
                                       required>
                                <span class="error api_password"></span>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Balance API</label>
                                <input type="text" name="balance_api"
                                       placeholder="Eg: http://bulksms.getlead.co.uk/pushsms.php"
                                       value="{{$xml_api->balance_api}}" class="form-control">
                                <span class="error password"></span>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Message API</label>
                                <input type="text" name="message_api"
                                       placeholder="Eg: http://bulksms.getlead.co.uk/pushsms.php"
                                       value="{{$xml_api->message_api}}" class="form-control">
                                <span class="error password"></span>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Xml API</label>
                                <input type="text" name="xml_api"
                                       placeholder="Eg: http://bulksms.getlead.co.uk/pushsms.php"
                                       value="{{$xml_api->xml_api}}" class="form-control">
                                <span class="error password"></span>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Status API</label>
                                <input type="text" name="status_api"
                                       placeholder="Eg: http://bulksms.getlead.co.uk/pushsms.php"
                                       value="{{$xml_api->status_api}}" class="form-control">
                                <span class="error password"></span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <input type="submit" class="btn btn-deep-orange demo-btn" value="Add">
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </main>
@endsection
@push('footer.script')
    <script type="text/javascript">
    </script>
@endpush





