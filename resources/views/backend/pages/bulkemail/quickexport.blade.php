<?php
header("Content-type: application/octet-stream");
header("Content-Disposition: attachment; filename=EMAIL_REPORT.xls");
header("Pragma: no-cache");
header("Expires: 0");
?>

<h3>Bulk Email Report</h3>
GL Track Id: {{ $id }}<br>
User: {{ $getUser->vchr_user_name }}({{ $getUser->vchr_user_mobile }})<br>

<table border='1' width="100%">
  <tr>
    <td><b>Sl.No<b></td>
    <td><b>Date<b></td>
    <td><b>Email Address</b></th>
    <td><b>Status</b></td>
   
  </tr>
    <?php
      foreach ($sms as $key => $value)
    {
    ?>
  <tr>
    <td>{{ ++$key}}</td>
    <td>{{ $value->created_at }}</td>
    <td>{{ $value->email_address }}</td>
    <td>{{ $value->status }}</td>
   
    
  </tr>
  <?php 
  } 

?>
</table>




