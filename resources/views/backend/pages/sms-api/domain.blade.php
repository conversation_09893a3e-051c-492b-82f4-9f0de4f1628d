@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>SMS-Domain</h5>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add Domain</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.adminbulkpushsidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table">
                    <table id="enquiry-info-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                    <thead>
                            <tr>
                                <th>Sl No</th>
                                <th>Title</th>
                                <th>Domain</th>
                                <th width="10%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>
    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="testimonialAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Domain</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Title</label>
                            <select name="title" class="form-control">
                            <option value="">Select SMS Panel</option>
                            @foreach($panels as $panel)
                            <option value="{{ $panel->name }}">{{ $panel->name }}</option>
                            @endforeach
                            </select>
                            <span class="error title"></span>
                        </div>
                                  
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Url</label>
                            <input type="text" name="domain" class="form-control" placeholder="Domain">
                            <span class="error domain"></span>
                        </div>
                                  
                    </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button class="btn btn-deep-orange demo-btn">Add</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>




    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="testimonialEdit" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update Domain</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <input type="hidden" name="id" id="id">
                            <label for="default-input" class="form-control-label">Title</label>
                            <select name="title" class="form-control" id="title">
                            <option value="">Select SMS Panel</option>
                            @foreach($panels as $panel)
                            <option value="{{ $panel->name }}">{{ $panel->name }}</option>
                            @endforeach
                            </select>
                            <span class="val-error title"></span>
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="col-sm-3 form-control-label">Url</label>
                            <input type="text" id="domain" name="domain" class="form-control" placeholder="Domain" required="required">
                            <span class="val-error domain"></span>
                        </div>             
                    </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

           


@endsection 
@push('footer.script')
    <script type="text/javascript">
    
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};

            $('.error').hide();
            

             // $('#ks-izi-modal-large1').modal('toggle');


            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "170px",
                    "targets": [0, 1,2]
                },

                ],
                ajax: BASE_URL + '/admin/get-sms-domain',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'title',
                        name: 'title'
                    },
                    {
                        data: 'domain',
                        name: 'domain'
                    },
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
                
            });

            $(document).on('submit', '#testimonialAdd', function(event) {
                event.preventDefault();
                $("#testimonialEdit")[0].reset();
                // $('').on('click', function (e) {
               $('.val-error').html('');
              $('.val-error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/sms-domain',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });

            $(document).on('submit', '#testimonialEdit', function(event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id=$('#id').val();
                // var testimonialId = $(this).attr('designation-id'); 
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/edit-sms-domain/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    $('#ks-izi-modal-large1').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });


            $('#enquiry-info-table').on('click', '.designation-act', function(event) {
                event.preventDefault();
                if($(this).hasClass('btn-activate')) {
                    var url = BASE_URL + '/admin/sms-domain-deactivate/';
                    action = 'Deactivate';
                } else {
                    var url = BASE_URL + '/admin/sms-domain-activate/';
                    action = 'Activate';
                }
                var feedbackId = $(this).attr('designation-id');
                //alert(feedbackId);
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $('#enquiry-info-table').on('click', '.designation-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('designation-id');
                var destinationPath = BASE_URL + '/admin/delete-sms-domain/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'get',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });

        

   
     $('#enquiry-info-table').on('click', '.ks-izi-modal-trigger1', function(event) {

            var testimonialId = $(this).attr('designation-id'); 
            $.ajax({
                    url: BASE_URL + '/admin/show-sms-domain/' + testimonialId,
                    type: 'GET',
                    dataType: 'JSON',
                })
            .done(function(res) {
                if(res.status == "success") {
                        data = res.data;
                        $("#id").val(data.id);
                        $("#title").val(data.title);
                        $("#domain").val(data.domain);
                        $('#ks-izi-modal-large1').modal('toggle');
                        
                    }
            })
            .fail(function(err) {

            })
            .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });
     });
            
        
    </script>
@endpush





