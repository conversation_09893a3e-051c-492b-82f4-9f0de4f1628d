@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>SMS-Credentials</h5>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class="">
                                    <a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger"
                                       data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry"
                                       href="#">Add Credentials</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.admin-credentials-sidebar')
            <div class="content-section">
                <div class="justify-content-center">
                    <h5 class="justify-content-center">{{$route_name}}</h5>
                </div>
                <!--  /Your content goes here/ -->
                <div class="gl-table">
                    <table id="sms_api_credentials_table" class="table table-striped table-bordered nowrap table-custom"
                        cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Users</th>
    {{--                        <th>Route</th>--}}
                            <th>Domain</th>
                            <th>Credentials</th>
                            <th width="10%">Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>
    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="testimonialAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Credentials</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Select User</label>
                            <select name="vendor_id" class="form-control select2" style="width: 100%" required>
                                <option value="">Select User</option>
                                <option value="0">All Users</option>
                                @foreach(\App\User::getUsers() as $user)
                                    <option value="{{ $user->id }}">{{ $user->name}}</option>
                                @endforeach
                            </select>
                            <span class="error vendor_id"></span>
                        </div>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Select Domain</label>
                            <select name="sms_domain_id" class="form-control" required>
                                <option value="">Select Domain</option>
                                @foreach($domains as $domain)
                                    <option value="{{ $domain->id }}">{{ $domain->title }}({{ $domain->domain }})
                                    </option>
                                @endforeach
                            </select>
                            <span class="error title"></span>
                        </div>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Select Route</label>
                            <select name="route_id" class="form-control" required>
                                <option value="">Select Route</option>
                                @foreach($routes as $route)
                                    <option value="{{ $route->pk_int_sms_route_id }}">{{ $route->vchr_sms_route }}</option>
                                @endforeach
                            </select>
                            <span class="error domain"></span>
                        </div>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">UserName</label>
                            <input type="text" name="username" class="form-control" required>
                            <span class="error username"></span>
                        </div>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Password</label>
                            <input type="text" name="password" class="form-control" required>
                            <span class="error password"></span>
                        </div>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Api Password/Key</label>
                            <input type="text" name="api_password" class="form-control" required>
                            <span class="error api_password"></span>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <button class="btn btn-deep-orange demo-btn">Add</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="testimonialEdit" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update Credentials</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    {{--  <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Select User</label>
                            <select name="vendor_id" id="vendor_id" class="form-control " style="width: 100%" required>
                                <option value="">Select User</option>
                                <option value="0">All Users</option>
                                @foreach($users as $user)
                                <option value="{{ $user->pk_int_user_id }}">{{ $user->vchr_user_name}}</option>
                                @endforeach
                            </select>
                            <span class="error vendor_id"></span>
                        </div>
                    </div> --}}
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Select Domain</label>
                            <input type="text" name="title" id="sms_domain_id" class="form-control"
                                   value="{{ $domain->title }}({{ $domain->domain }})" readonly>
                            {{-- <select name="sms_domain_id" class="form-control" id="sms_domain_id" required>
                                <option value="">Select Domain</option>
                                @foreach($domains as $domain)
                                <option value="{{ $domain->id }}">{{ $domain->title }}</option>
                                @endforeach
                            </select> --}}
                            <span class="error title"></span>
                        </div>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Select Route</label>
                            <select name="route_id" class="form-control" id="route_id" required>
                                <option value="">Select Route</option>
                                @foreach($routes as $route)
                                    <option value="{{ $route->pk_int_sms_route_id }}">{{ $route->vchr_sms_route }}</option>
                                @endforeach
                            </select>
                            <span class="error domain"></span>
                        </div>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">UserName</label>
                            <input type="text" name="username" id="username" class="form-control" required>
                            <input type="hidden" id="id">
                            <span class="error username"></span>
                        </div>

                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Password</label>
                            <input type="text" name="password" id="password" class="form-control" required>
                            <span class="error password"></span>
                        </div>

                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Api Password/Key</label>
                            <input type="text" id="api_password" name="api_password" class="form-control" required>
                            <span class="error api_password"></span>
                        </div>

                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <button class="btn btn-deep-orange demo-btn">Update</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection
@push('footer.script')
    <script type="text/javascript">
        $(document).ready(function () {
            BASE_URL ={!! json_encode(url('/')) !!};
            $('.error').hide();
            var priority = getUrlParameter('priority');

            // $('#ks-izi-modal-large1').modal('toggle');
            $('#sms_api_credentials_table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    // "width": "200px",
                    // "targets": [0, 1,2,3,4,5]
                },
                ],
                ajax: BASE_URL + '/admin/get-sms-api-credentials?priority=' + priority,
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'user', name: 'user'},
               //     {data: 'route', name: 'route'},
                    {data: 'domain', name: 'domain'},
                    {data: 'credentials', name: 'credentials'},
                    {data: 'show', name: 'show', orderable: false, searchable: false}
                ],

            });

            $(document).on('submit', '#testimonialAdd', function (event) {
                event.preventDefault();
                $("#testimonialEdit")[0].reset();
                // $('').on('click', function (e) {
                $('.val-error').html('');
                $('.val-error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/sms-api-credentials',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            console.log(val);
                            console.log(index);
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                        // swal(res.msg.vchr_enquiry_type[0]);
                    }
                })
                    .fail(function () {
                    })
                    .always(function (com) {
                        $('#sms_api_credentials_table').DataTable().ajax.reload(null, false);
                    });

            });

            $(document).on('submit', '#testimonialEdit', function (event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id = $('#id').val();
                // var testimonialId = $(this).attr('designation-id'); 
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/edit-sms-api-credentials/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        $('#ks-izi-modal-large1').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            console.log(val);
                            console.log(index);
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                }).always(function (com) {
                    $('#sms_api_credentials_table').DataTable().ajax.reload(null, false);
                });
            });

            $('#sms_api_credentials_table').on('click', '.designation-act', function (event) {
                event.preventDefault();
                if ($(this).hasClass('btn-activate')) {
                    var url = BASE_URL + '/admin/sms-api-credentials-deactivate/';
                    action = 'Deactivate';
                } else {
                    var url = BASE_URL + '/admin/sms-api-credentials-activate/';
                    action = 'Activate';
                }
                var feedbackId = $(this).attr('designation-id');
                //alert(feedbackId);
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                }).done(function (res) {
                                    if (res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                }).fail(function (err) {
                                }).always(function (com) {
                                    $('#sms_api_credentials_table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $('#sms_api_credentials_table').on('click', '.designation-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('designation-id');
                var destinationPath = BASE_URL + '/admin/delete-sms-api-credentials/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'get',
                                })
                                    .done(function (res) {
                                        if (res.status == 'success') {
                                            $.alert({
                                                title: 'Success',
                                                type: 'green',
                                                content: res.msg,
                                            });
                                        } else {
                                            $.alert({
                                                title: 'Failed',
                                                type: 'red',
                                                content: res.msg,
                                            });
                                        }
                                    }).fail(function (err) {
                                }).always(function (com) {
                                    $('#sms_api_credentials_table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });
        $('#sms_api_credentials_table').on('click', '.ks-izi-modal-trigger1', function (event) {
            var testimonialId = $(this).attr('designation-id');
            $.ajax({
                url: BASE_URL + '/admin/show-sms-api-credentials/' + testimonialId,
                type: 'GET',
                dataType: 'JSON',
            }).done(function (res) {
                if (res.status === "success") {
                    data = res.data;
                    //alert(data.vendor_id);
                    $("#id").val(data.id);
                    $("#vendor_id").val(data.vendor_id);
                    $("#route_id").val(data.route_id);
                    $("#sms_domain_id").val(data.title + '(' + data.domain + ')');
                    $("#username").val(data.username);
                    $("#password").val(data.password);
                    $("#api_password").val(data.api_password);
                    $('#ks-izi-modal-large1').modal('toggle');
                }
            }).fail(function (err) {
            }).always(function (com) {
                $('#sms_api_credentials_table').DataTable().ajax.reload(null, false);
            });
        });
        var getUrlParameter = function getUrlParameter(sParam) {
            var sPageURL = window.location.search.substring(1),
                sURLVariables = sPageURL.split('&'),
                sParameterName,
                i;

            for (i = 0; i < sURLVariables.length; i++) {
                sParameterName = sURLVariables[i].split('=');

                if (sParameterName[0] === sParam) {
                    return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
                }
            }
        };
    </script>
@endpush