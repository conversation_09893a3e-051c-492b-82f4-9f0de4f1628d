@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Short Code Templates</h5>
                </div>
                {{-- <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add SMS Template </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <button type="button"  class="btn btn-sm btn-primary mx-2 my-3 ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="short_code_add" name="button" >Add Short Code Template</button>
                    <button type="button" class="btn btn-sm btn-primary mx-2 my-3" id="short_code_edit" name="button">Edit Short Code Template</button>
                    <a type="button" class="btn btn-sm btn-primary mx-2 my-3" id="short_code_delete" name="button" href="#" onclick="return deleteTemplate();">Delete Short Code Template</a>
                    <div class="col-sm-12 py-3">
                        <div class="ks-tabs-container ks-tabs-default ks-tabs-no-separator ks-tabs-vertical" id="no-data">
                            <ul class="nav ks-nav-tabs nav-stacked" id="templist">
                            </ul>
                            <div class="tab-content" id="tempcode">
                            </div>
                        </div>
                        <div class="" id="no-data-message">
                            <h3>No templates created yet.</h3>
                        </div>
                    </div>
            </div>
        </div>
    </main>
    <!------------ /add modal/  -------------->

    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  onsubmit="return shortCodeSubmit();" id="enquiry_type_edit_form">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Short Code Template</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Short Code template name</label>
                
                            <input type="text" name="short_code_name" class="form-control" placeholder="">
                            <span class="val-error vchr_short_code_name"></span>
                  
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Short Code template description</label>
                            <textarea name="short_code_description" style="width:100%;"></textarea>
                            <span class="val-error  vchr_short_code_description"></span>
                        </div> 
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Short Code template Code</label>
                            <input type="text" name="short_code_template_code" class="form-control" placeholder="">
                            <span class="val-error vchr_short_code_template"></span>
                        </div>            
                    </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button class="btn btn-deep-orange demo-btn">Add</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  onsubmit="return shortCodeEdit();" id="enquiry_type_edit_form">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update Short Code Template</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>

                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Short Code template name</label>
                            <input type="text" name="vchr_short_code_name" class="form-control" placeholder="">
                            <span class="val-error vchr_short_code_name"></span>
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Short Code template description</label>
                            <textarea name="vchr_short_code_description" style="width:100%;"></textarea>
                            <span class="val-error  vchr_short_code_description"></span>
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Short Code template Code</label>
                            <input type="text" name="vchr_short_code_template" class="form-control" placeholder="">
                            <span class="val-error vchr_short_code_template"></span>            
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection 
@push('footer.script')
<script type="text/javascript">
        $(document).ready(function() {
            $('#no-data-message').hide();
            BASE_URL ={!! json_encode(url('/')) !!};
            $('.val-error').hide();
            getShortCodeList();

            // $('#ks-izi-modal-large').iziModal({
            //     autoOpen: false,
            //     padding: 20,
            //     headerColor: '#3a529b',
            //     restoreDefaultContent: true,
            //     title: "Enquiry type.",
            //     fullscreen: true,
            //     subtitle: 'Add enquiry type',
            //     transitionIn: 'fadeInDown'
            // });
            // $('#ks-izi-modal-large1').iziModal({
            //     autoOpen: false,
            //     padding: 20,
            //     headerColor: '#3a529b',
            //     restoreDefaultContent: true,
            //     title: "Enquiry type.",
            //     fullscreen: true,
            //     subtitle: 'Add enquiry type',
            //     transitionIn: 'fadeInDown'
            // });

            // $('.ks-izi-modal-trigger').on('click', function (e) {
            //     console.log("Clicked");
            //     $($(this).data('target')).iziModal('open');
            // });

        });

        function getShortCodeList() {
            $('#templist').empty();
            $('#tempcode').empty();
            $.ajax({
                url: BASE_URL + '/admin/getshortcodelist',
                type: 'get',
                dataType: 'JSON',
            })
            .done(function(res) {

                list = '';
                content = '';
                if(res.status == 'success') {
                    if(res.data.length){

                        $.each(res.data, function(index, val) {
                            //iterate through array or object
                            if(index == 0){
                                list = list + '<li class="nav-item"><a class="nav-link temp-selection active" data-temp-id="' + val.pk_int_short_code_id + '" href="#" data-toggle="tab" data-target="#' + index + '">' + val.vchr_short_code_name + '</a></li>'
                                content = content + '<div class="tab-pane active" id="' + index + '" role="tabpanel"><div class="row">Short code : ' + val.vchr_short_code_template + '</div><div class="row">Description : ' + val.vchr_short_code_description + '</div></div>';
                            }else{
                                list = list + '<li class="nav-item"><a class="nav-link temp-selection" data-temp-id="' + val.pk_int_short_code_id + '" href="#" data-toggle="tab" data-target="#' + index + '">' + val.vchr_short_code_name + '</a></li>'
                                content = content + '<div class="tab-pane" id="' + index + '" role="tabpanel"><div class="row">Short code : ' + val.vchr_short_code_template + '</div><div class="row">Description : ' + val.vchr_short_code_description + '</div></div>';
                            }
                        });

                        $('#templist').append(list);
                        $('#tempcode').append(content);
                        $('#no-data').show();
                        $('#no-data-message').hide();
                    } else {
                        $('#no-data').hide();
                        $('#no-data-message').show();
                    }
                } else {

                }
            })
            .fail(function(err) {
                console.log("error");
            })
            .always(function(com) {
                console.log("complete");
            });
        }

        var shortCode = {};
        function shortCodeSubmit() {
            $('.val-error').html('');
            $('.val-error').hide();
            shortCode = {};
            shortCode.vchr_short_code_name = $("[name='short_code_name']").val();
            shortCode.vchr_short_code_template = $("[name='short_code_template_code']").val();
            shortCode.vchr_short_code_description = $("[name='short_code_description']").val();
            shortCode._token = $("[name='_token']").val();
            console.log(shortCode);
            var url="{{url('/admin/shortcodes/')}}";
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                data: shortCode
            })
            .done(function(res) {
                if(res.status == 'success'){
                    // $($('.ks-izi-modal-trigger').data('target')).iziModal('close');
                    $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    getShortCodeList();
                }else {
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
            })
            .fail(function(err) {

            })
            .always(function(com) {
                $('#enquiry-type-table').DataTable().ajax.reload(null, false);

            });

            return false;
        }

        function shortCodeEdit() {
            $('.val-error').html('');
            $('.val-error').hide();
            shortCode = {};
            shortCode.pk_int_short_code_id = $("[name='pk_int_short_code_id']").val();
            shortCode.vchr_short_code_name = $("[name='vchr_short_code_name']").val();
            shortCode.vchr_short_code_template = $("[name='vchr_short_code_template']").val();
            shortCode.vchr_short_code_description = $("[name='vchr_short_code_description']").val();
            shortCode._token = $("[name='_token']").val();
            console.log(shortCode);
            $.ajax({
                url: BASE_URL + '/admin/shortcodes/'+ shortCode.pk_int_short_code_id,
                type: 'PUT',
                dataType: 'json',
                data: shortCode
            })
            .done(function(res) {
                if(res.status == 'success'){
                    // $($('.ks-izi-modal-trigger').data('target')).iziModal('close');
                  $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    getShortCodeList();
                }else {
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
            })
            .fail(function(err) {

            })
            .always(function(com) {
                $('#enquiry-type-table').DataTable().ajax.reload(null, false);

            });

            return false;
        }

        $('#short_code_edit').on('click', function (e) {
            id = $('.temp-selection.active').data('temp-id');
            console.log(id);
            $.ajax({
                url: BASE_URL + '/admin/shortcodes/'+id,
                type: 'GET',
                dataType: 'JSON',
            })
            .done(function(res) {

                if(res.status == "success") {
                    $("[name='pk_int_short_code_id']").val(res.data.pk_int_short_code_id);
                    $("[name='vchr_short_code_name']").val(res.data.vchr_short_code_name);
                    $("[name='vchr_short_code_template']").val(res.data.vchr_short_code_template);
                    $("[name='vchr_short_code_description']").val(res.data.vchr_short_code_description);
                    $('#ks-izi-modal-large1').iziModal('open');
                } else {
                    swal(res.msg);
                }
            })
            .fail(function(err) {
                console.log("error");
            })
            .always(function(com) {
                console.log("complete");
            });


        });
        function editTemplate() {
            // console.log(id);
            url = BASE_URL + '/admin/edit-template/' + id;
            window.location = url;
        }

        function deleteTemplate() {
            id = $('.temp-selection.active').data('temp-id');
            templateName = $('.temp-selection.active').html();
            console.log(templateName);
            url = BASE_URL + '/admin/shortcodes/' + id;
            $.confirm({
                title: 'Deletion',
                content: 'Are you sure you want to delete "' + templateName + '"?',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                    'confirm': {
                        text: 'Proceed',
                        btnClass: 'btn-info',
                        action: function () {
                            $.ajax({
                                url: url,
                                type: 'DELETE',
                                dataType: 'JSON',
                            })
                            .done(function(res) {
                                if(res.status == 'success') {
                                    $.alert({
                                        title: 'Success',
                                        type: 'green',
                                        content: res.msg,
                                    });
                                } else {
                                    $.alert({
                                        title: 'Failed',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                }
                            })
                            .fail(function(err) {
                                console.log("error");
                            })
                            .always(function(com) {
                                console.log("complete");
                                getShortCodeList();
                            });
                        }
                    },
                    cancel: function () {
                        $.alert('Operation <strong>canceled</strong>');
                    }
                }
            });
        }
    </script>
@endpush
