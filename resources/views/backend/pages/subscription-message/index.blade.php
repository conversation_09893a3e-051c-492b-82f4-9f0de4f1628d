@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>SUBSCRIPTION MESSAGE APPROVAL</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminapprovalsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <div class="col-md-12" style="overflow: auto">
                <table id="enquiry-info-table" class="table table-striped table-bordered" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Username</th>
                            <th>Subscription message</th>
                            <th>Unsubscription message</th>
                            <th width="10%">Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
            </div>
        </div>
    </main>

    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="addReason" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">SUBSCRIPTION MESSAGE REJECTION</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="col-sm-2 form-control-label">Reason</label>
                            <textarea name="reason" class="form-control" placeholder="" rows="10" required></textarea>
                            <input type="hidden" name="id" id="id">
                        </div>            
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                            <button class="btn btn-deep-orange demo-btn">Add</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
                        

@endsection 
@push('footer.script')
<script type="text/javascript">
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};

            $('.error').hide();

            //  $('#ks-izi-modal-large').iziModal({
            //     autoOpen: false,
            //     padding: 20,
            //     headerColor: '#3a529b',
            //     restoreDefaultContent: true,
            //     title: "SUBSCRIPTION MESSAGE REJECTION",
            //     fullscreen: true,
            //     subtitle: 'Please mention the reason',
            //     transitionIn: 'fadeInDown'
            // });

            // $('.ks-izi-modal-trigger').on('click', function (e) {
            //      $('#ks-izi-modal-large').modal('toggle');

            //     $(".test").on('click', function (e) {
            //         //alert('hi');
            //     });
              
               

            // });
           


            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "170px",
                    "targets": [0, 1,2]
                },

                ],
                ajax: BASE_URL + '/admin/get-subscription-message',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'vchr_user_name',
                        name: 'vchr_user_name'
                    },
                    {

                        data: 'text_subscription_message',
                        name: 'text_subscription_message'
                    },
                    {
                        data: 'text_unsubscription_message',
                        name: 'text_unsubscription_message'
                    },
                   
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
                
            });

                        $(document).on('submit', '#addReason', function(event) {
                event.preventDefault();
                // $('').on('click', function (e) {
                 
                // console.log(enquiryId);
                // $('.addReason1').prop('disabled','true');
                $.ajax({
                    url: BASE_URL + '/admin/user-subscription-message-deactivate',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    $('.addReason1').prop('disabled','false');

                    $('#ks-izi-modal-large').modal('toggle');
                    if(res.status == 'success'){
                    
                    swal(res.msg);
                }else{
                    $.alert({
                            title: 'Failed',
                            type: 'red',
                            content: res.msg,
                        });
                    
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

                });
            });

            $('#enquiry-info-table').on('click', '.sms-act', function(event) {
                  event.preventDefault();
                    var templateId = $(this).attr('sms-id');
                    if($(this).hasClass('btn-activate')) {

                        // var url = BASE_URL + '/admin/smstemplate-deactivate/';
                        // action = 'Reject';
                        $('#id').val(templateId);
                        $('#ks-izi-modal-large').modal('toggle');
                }else 
                {
                var url = BASE_URL + '/admin/user-subscription-message-activate/';
                action = 'Approve';

                var feedbackId = $(this).attr('sms-id');
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            }
            });

            $('#enquiry-info-table').on('click', '.sms-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('sms-id');
                var destinationPath = BASE_URL + '/admin/user-subscription-message/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });   
        });   
    </script>
@endpush
