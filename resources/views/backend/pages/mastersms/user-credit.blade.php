@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Master SMS Management</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminsmssidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <div class="row">
                    <div class="col-sm-12 py-3">
                        <div class="row">
                            <div class="col-lg-6 ks-panels-column-section">
                                <div class="card">
                                    <div class="card-block" style="padding-left: 20px;">
                                        <h5 class="card-title">MASTER SMS COUNT</h5>
                                        <form id="mastersmsAdd" method="POST">
                                            <div class="form-group row">
                                                {{csrf_field()}}
                                                <label for="default-input" class="col-sm-3 form-control-label">SMS COUNT</label>
                                                <div class="col-sm-10">
                                                    <input type="number" class="form-control" id="default-input" placeholder="Enter SMS COUNT" required min="0" name="int_master_sms_count">
                                                </div>
                                            </div>
                                             <div class="form-group row">
                                                <label for="default-input" class="col-sm-3 form-control-label"></label>
                                                <div class="col-sm-3">
                                                    <input type="hidden" name="id" value="{{$master->pk_int_master_sms_id}}">
                                                   <button type="submit" class="btn btn-primary" id="mastersmsAdd1">ADD</button>
                                                </div>
                                            </div>

                                        </form>
                                    </div>
                                </div>
                             
                            </div>
                            <div class="col-lg-6 ks-panels-column-section">
                                <div class="card">
                                    <div class="card-block" style="padding-left: 20px;">
                                        <h5 class="card-title">MASTER SMS TOTAL COUNT</h5>
                                        <div>
                                            <div class="form-group row">
                                                <label for="input-group-text" class="col-sm-5 form-control-label">SMS AVAILABLE</label>
                                                <div class="col-sm-10">
                                                    <button class="btn btn-success btn-lg col-sm-3" id="count">{{$master->int_master_sms_count}}</button>
                                                </div>
                                            </div>
                                           
                                        </div>
                                    </div>
                                </div>
                               
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="ks-page-content">
                            <div class="ks-page-content-body ks-tabs-page-container">
                                <div class="ks-tabs-container-description" style="padding-top:10px">
                                    <h3>CREDITED HISTORY</h3>
                                    {{--@if(isset($diff_date))--}}
                                    {{--<p>Last updated   {{$diff_date}}</p>--}}
                                    {{--@else--}}
                                    {{--<p>No Records Found</p>--}}
                                    {{--@endif--}}
                                </div>
                                <ul class="nav ks-nav-tabs ks-tabs-page-default ks-tabs-full-page">
                                    <li class="nav-item">
                                        {{--<a class="nav-link active show" href="#" data-toggle="tab" data-target="#in-patient">--}}
                                            {{--CREDITED HISTORY--}}
                                        {{--</a>--}}
                                    </li>
                                </ul>
                                <div class="tab-content">
                                    <div class="tab-pane ks-column-section active show" id="in-patient" role="tabpanel">
                                    <div class="row">
                                        <div class="col-sm-12 py-3">
                                            <table id="enquiry-info-table" class="table table-striped table-bordered" width="100%">
                                                <thead>
                                                    <tr>
                                                        <th>Sl No</th>
                                                        <th>Statement</th>
                                                        <th>Date and Time</th>
                                                    </tr>
                                                </thead>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
                                                    

@endsection 
@push('footer.script')
<script type="text/javascript">

    $(function(){
    
    BASE_URL ={!! json_encode(url('/')) !!};
    $('#mastersmsAdd').on('submit', function(e){
        $('#mastersmsAdd1').prop('disabled','true');
      e.preventDefault();
      $.ajax({
                    url: BASE_URL + '/admin/mastersms',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                    $('#mastersmsAdd1').removeAttr('disabled');
                    $('#count').text(res.data);
                        swal(res.msg);
                        $('#default-input').val('');
                }else{
                    var error=res.msg;
                            sweetAlert("Oops..."," "+error+" ","error"); 
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });
    });

    $('#enquiry-info-table').DataTable({
                autoWidth: 1,
                lengthChange: 1,
                // buttons: ["print", "excel", "pdf"],
                select: !1,
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                processing: true,
                serverSide: true,
                searching: true,
                orderable: true,
                ajax: BASE_URL + '/admin/get-mastermsms-history/'+3,
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {

                        data: 'vchr_master_sms_history_statement',
                        name: 'vchr_master_sms_history_statement'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at'
                    },
                    
                ],
                "initComplete": function () {
                    
                }
            });
});    


     $(document).ready(function() {
           
        });
</script>
@endpush
