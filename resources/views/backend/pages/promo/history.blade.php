@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>GL Promo History</h5>
                </div>
                <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <select class="ml-auto mr-3 my-3 " id="fk_int_user_id" data-placeholder="Select" name="fk_int_user_id" required>
                                    <option value="0">Select user</option>
                                    @foreach($user as $key => $user1)
                                    <option value="{{ $user1->pk_int_user_id }}">{{ $user1->vchr_user_name }}({{  $user1->vchr_user_mobile }})</option>
                                    @endforeach
                                </select>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
                {{-- <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add Enquiry Purpose </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminmissedcallsidebar')
            <div class="content-section p-3 bg-white">
                
                <!--  /Your content goes here/ -->
                <div class="col-md-12" style="overflow: auto">
                <table id="enquiry-info-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                           <th>Sl No</th>
                            <th>Date & Time</th>
                            <th>User Name</th>
                            <th>Mobile No</th>
                            <th>Package</th>
                            <th>Remarks</th>
                        </tr>
                    </thead>
                   
                </table>
            </div>
            </div>
        </div>
    </main>

@endsection 
@push('footer.script')
    <script type="text/javascript">

     function readURL1(input) {
      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function(e) {
          $('#profile-img-tag').attr('src', e.target.result);

          $('#profile-img-tag').hide();
          $('#profile-img-tag').fadeIn(650);

        }

        reader.readAsDataURL(input.files[0]);
      }
    }

    function readURL(input) {

      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function(e) {
          $('#blah').attr('src', e.target.result);

          $('#blah').hide();
          $('#blah').fadeIn(650);

        }

        reader.readAsDataURL(input.files[0]);
      }
    }

    
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};

            $('.error').hide();
            

            $('.ks-izi-modal-trigger').on('click', function (e) {
                 $('#ks-izi-modal-large').modal('toggle');

                $(".test").on('click', function (e) {
                    //alert('hi');
                });
               $("#fileInput").change(function() {
                    readURL(this);
                   // alert('hi');
                });

               $("#fileInput1").change(function() {
                    readURL1(this);
                   alert('hi');
                });
               

            });

        function gl_history(userid)
        {
            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "170px",
                    "targets": [0, 1,2]
                },

                ],
                ajax: BASE_URL + '/admin/getglpromo-history'+'/'+userid,
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'date',
                        name: 'date'
                    },
                    {
                        data: 'username',
                        name: 'username'
                    },
                    {

                        data: 'mobno',
                        name: 'mobno'
                    },
                    {
                        data: 'package',
                        name: 'package'
                    },
                    {
                        data: 'remarks',
                        name: 'remarks'
                    }
                   
                    
                ],
                "bDestroy": true,
                
            });
        }
        var userid=$('#fk_int_user_id').val();
        gl_history(userid);

        $('#fk_int_user_id').change(function()
                {
                  var userid=$('#fk_int_user_id').val();
                  //alert(userid);
                  gl_history(userid);

                });
    });

            

            
            
        
    </script>
@endpush
