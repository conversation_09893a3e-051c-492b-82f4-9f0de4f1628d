@extends('backend.layout.master')
 @yield('after-styles-end')
 <style type="text/css">
    .val-error
    {
        color:red;
    }
 </style>
@section('page-header')

<div class="ks-page-header">
    <section class="ks-title">
        <h3>Lead status</h3>
        <div class="ks-controls">
            <nav class="breadcrumb ks-default">
                <a class="breadcrumb-item ks-breadcrumb-icon" href="{{ url('/') }}">
                    <span class="la la-home ks-icon"></span>
                </a>
                {{-- <a href="{{ url('/admin/email-template') }}" class="breadcrumb-item">Enquiries</a> --}}
                <span class="breadcrumb-item active" href="#">Lead status</span>
            </nav>

            {{-- <button class="btn btn-outline-primary ks-light ks-content-nav-toggle" data-block-toggle=".ks-content-nav > .ks-nav">Menu</button> --}}
        </div>
    </section>
</div>
@endsection
@section('content')
    <div class="ks-page-content-body ks-billing ks-body-wrap">
        <div class="ks-body-wrap-container">
            <div class="container-fluid">
                <div class="row">
                    <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3 ks-izi-modal-trigger" data-target="#ks-izi-modal-large1" id="addEnquiry" name="button">Add Lead status</button>
                    <div class="col-sm-12 py-3">
                        <div class="col-md-12" style="overflow: auto">
                        <table id="enquiry-info-table" class="table table-striped table-bordered" width="100%">
                            <thead>
                                <tr>
                                    <th>Sl No</th>
                                    <th>Enquiry Details</th>
                                    <th>Description</th>
                                    <th width="10%">Actions</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <div id="ks-izi-modal-large1" class="ks-izi-modal">
            <form  id="testimonialAdd" enctype="multipart/form-data">
                <div class="form-group row">
    
                 <label for="default-input" class="col-sm-3 form-control-label">Enquiry</label>
                <div class="col-sm-9">
                   <select name="fk_int_enquiry_id" class="js-example-basic-single1 enquiryPurposeSelect" id="enquiry-data" placeholder></select>

                    <span class="val-error fk_int_enquiry_id"></span>
                </div>
            </div>
            <div class="form-group row">
                <input type="hidden" name="pk_int_short_code_id">
                 <label for="default-input" class="col-sm-3 form-control-label">Status</label>
                <div class="col-sm-7">
                   <textarea type="text" name="vchr_status" class="form-control" placeholder="" ></textarea>
                    <span class="val-error vchr_status"></span>
                </div>
            </div>


            <input type="hidden" name="_token" value="{{ csrf_token() }}">
          

            <div class="form-group row" id="short_code_save">
                <div class="col-sm-4 offset-sm-4 center">
                    <button class="btn btn-primary" type="submit" >
                        <span class="la la-check ks-icon"></span>
                        <span class="ks-text">Add</span>
                    </button>
                </div>
            </div>
        </form> 
    </div>

    <div id="ks-izi-modal-large" class="ks-izi-modal">
        <form  id="testimonialEdit" enctype="multipart/form-data">
            
           <div class="form-group row">
                  <label for="default-input" class="col-sm-3 form-control-label">Enquiry</label>
                <div class="col-sm-7">
                   <input type="text" name="fk_int_enquiry_id" id="fk_int_enquiry_id1" readonly="" class="form-control">
                    <span class="val-error fk_int_enquiry_id"></span>
                </div>
            </div>
  
            <div class="form-group row">
             <label for="default-input" class="col-sm-3 form-control-label">Status</label>
                <div class="col-sm-7">
                   <textarea type="text" name="vchr_status" id="vchr_status" class="form-control" placeholder="" ></textarea>
                    <span class="val-error vchr_status"></span>
                </div>
            </div>
               <input type="hidden" name="_method"  value="PUT" class="form-control" placeholder="" >
            <input type="hidden" name="pk_int_enquiry_id" class="form-control" placeholder=""  id="pk_int_enquiry_id">

                <div class="form-group row" id="system_info_save">
                <div class="col-sm-4 offset-sm-4 center">
                    <button class="btn btn-primary" type="submit">
                        <span class="la la-check ks-icon"></span>
                        <span class="ks-text">Update</span>
                    </button>
                </div>
            </div>

        </form>
    </div>

@endsection
@push('footer.script')
    <script type="text/javascript">
        function enquiry()
            {
                $(".enquiryPurposeSelect").empty();
                  //get main category data
                  BASE_URL = $('body').data('base-url');
                  @if(Auth::user()->int_role_id==1)
                  var url='{{url("/admin/get-enquiry-list/")}}';
                  @else
                  var url='{{url("/user/get-enquiry-list/")}}';
                  @endif
                  $('.js-example-basic-single1').select2({
                    width: '80%',
                    "language": {
                        "noResults": function() {
                            return "No enquiry  found.";
                        }
                    },
                });

                    $.ajax({
                            url: url,
                            type: 'GET',
                            dataType: 'JSON',
                        })
                        .done(function(res) {
                            if(res.status == 'success'){

                                // console.log(res.data);
                                 $(".enquiryPurposeSelect").append("<option value=''>Select  enquiry </option>");
                            $.each(res.data,function(key, value)
                        {
                            $(".enquiryPurposeSelect").append('<option value=' + value.pk_int_enquiry_id + '>' + value.vchr_customer_name + '-'+value.vchr_purpose+'</option>');
                        });
                        }else{
                            $(".enquiryPurposeSelect").append("<option value=''>No  Enquiry List  found</option>");
                        }
                        })
                        .fail(function() {
                        })
                         .always(function(com) {
                       
                 });
            }

        $(document).ready(function() {
            BASE_URL = $('body').data('base-url');

            $('.error').hide();
 
            $('#ks-izi-modal-large').iziModal({
                autoOpen: false,
                padding: 20,
                headerColor: '#3a529b',
                restoreDefaultContent: true,
                title: "Lead Status",
                fullscreen: true,
                subtitle: 'Edit Lead Status',
                transitionIn: 'fadeInDown'
            });

            $('#ks-izi-modal-large1').iziModal({
                autoOpen: false,
                padding: 20,
                headerColor: '#3a529b',
                restoreDefaultContent: true,
                title: "Lead Status",
                fullscreen: true,
                subtitle: 'Add Lead Status',
                transitionIn: 'fadeInDown'
            });

            $('.ks-izi-modal-trigger').on('click', function (e) {
                $($(this).data('target')).iziModal('open');

                $(".test").on('click', function (e) {
                    //alert('hi');
                });
               enquiry();
               

            });


            $('#enquiry-info-table').DataTable({
                autoWidth: 1,
                lengthChange: 1,
                // buttons: ["print", "excel", "pdf"],
                select: !1,
                lengthMenu: [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                processing: true,
                serverSide: true,
                searching: true,
                orderable: true,
                ajax: BASE_URL + '/user/get-enquiry-status',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {

                        data: 'vchr_status',
                        name: 'vchr_status'
                    },
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
                "initComplete": function () {
                    $('.dataTables_wrapper select').select2({
                        minimumResultsForSearch: Infinity
                    });
                }
            });






            $(document).on('submit', '#testimonialAdd', function(event) {
                event.preventDefault();
                // $('').on('click', function (e) {
               $('.val-error').html('');
              $('.val-error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/enquiry-status',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    $($('.ks-izi-modal-trigger').data('target')).iziModal('close');
                    swal(res.msg);
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });


            $(document).on('submit', '#testimonialEdit', function(event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id=$('#pk_int_enquiry_id').val();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/enquiry-status/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    $($('.ks-izi-modal-trigger1').data('target')).iziModal('close');
                    swal(res.msg);
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });


            

            $('#enquiry-info-table').on('click', '.enquiry-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('enquiry-id');
                var destinationPath = BASE_URL + '/user/enquiry-status/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });

        


   
     $('#enquiry-info-table').on('click', '.ks-izi-modal-trigger1', function(event) {
            enquiry();
            var testimonialId = $(this).attr('enquiry-id'); 
            $.ajax({
                    url: BASE_URL + '/user/enquiry-status/' + testimonialId,
                    type: 'GET',
                    dataType: 'JSON',
                })
            .done(function(res) {
                if(res.status == "success") {
                        data = res.data;
                        //alert(data);

                        $("#fk_int_enquiry_id1").val(data.vchr_customer_name+'-'+data.vchr_purpose);
                        $("#vchr_status").val(data.vchr_status);
                       
                        $('#pk_int_enquiry_id').val(data.pk_int_enquiry_status_id);

                        
                        $($('.ks-izi-modal-trigger1').data('target')).iziModal('open');
                        
                    }
            })
            .fail(function(err) {

            })
            .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });
     });
            
        
    </script>
@endpush
