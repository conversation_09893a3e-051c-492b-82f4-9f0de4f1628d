@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>SENDERID APPROVAL</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.adminapprovalsidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table">
                    <table id="enquiry-info-table" class="table table-striped table-bordered" width="100%">
                        <thead>
                            <tr>
                                <th>Sl No</th>
                                <th>Username</th>
                                <th >Senderid Name</th>
                                <th >Sender id</th>
                                <th width="10%">Actions</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="addReason" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">SenderId Rejection</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Reason</label>
                                <textarea name="reason" class="form-control" placeholder="" rows="10" required></textarea>
                                <input type="hidden" name="id" id="id">
                        </div>             
                    </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button class="btn btn-deep-orange demo-btn">Reject</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="Edit">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update SMS Template</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            
                        </div>
                        <div class="md-form mb-1">
                            
                            </div>             
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


@endsection 
@push('footer.script')
<script type="text/javascript">
        $(document).ready(function() {

            BASE_URL ={!! json_encode(url('/')) !!};
            $('.error').hide();
           
            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "170px",
                    "targets": [0, 1,2]
                },

                ],
                ajax: BASE_URL + '/admin/get-user-senderid',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'vchr_user_name',
                        name: 'vchr_user_name'
                    },
                    {
                        data: 'vchr_sender_id_name',
                        name: 'vchr_sender_id_name'
                    },
                    {
                        data: 'vchr_sender_id',
                        name: 'vchr_sender_id'
                    },
                
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ]
            });

            $(document).on('submit', '#addReason', function(event) {
                event.preventDefault();
                // $('').on('click', function (e) {
                 
                // console.log(enquiryId);
                $('.addReason1').prop('disabled','true');
                $.ajax({
                    url: BASE_URL + '/admin/senderid-deactivate',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    $('.addReason1').prop('disabled','false');

                     $('#ks-izi-modal-large1').modal('toggle');
                    if(res.status == 'success'){
                    
                    $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.alert({
                            title: 'Failed',
                            type: 'red',
                            content: res.msg,
                        });
                    
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

                });
            });

            $('#enquiry-info-table').on('click', '.senderid-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('senderid-id');
                var destinationPath = BASE_URL + '/admin/user-senderid/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $('#enquiry-info-table').on('click', '.senderid-act', function(event) {
                  event.preventDefault();
                    var templateId = $(this).attr('senderid-id');
                    if($(this).hasClass('btn-activate')) {

                        // var url = BASE_URL + '/admin/smstemplate-deactivate/';
                        // action = 'Reject';
                        $('#id').val(templateId);
                       $('#ks-izi-modal-large1').modal('toggle');
                }else 
                {
                var url = BASE_URL + '/admin/senderid-activate/';
                action = 'Approve';

                var feedbackId = $(this).attr('senderid-id');
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            }
            });
        });
</script>
@endpush
