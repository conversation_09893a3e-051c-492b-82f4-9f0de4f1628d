@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Api Template</h5>
                </div>
                @if(session('flash_notification'))
                    <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
                @endif
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminbulkpushsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->

                <div class="p-3">
                  
                    <div class="tab-content">
                        <div class="tab-pane active ks-column-section" id="system-settings" role="tabpanel">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="card" style="padding-left: 10px;">
                                        <div class="card-block" style="padding-left: 10px;
                                             padding-right: 10px;">
                                            <div class="row">
                                               
                                                
                                                 <div class="main-round-btn green-btn ml-auto mr-3 my-3">
                                                    <a  
                                                    href="{{url('admin/sms-xml-api-template/create')}}"><i class="fa fa-plus"></i>Api Template</a>
                                                </div>
                                            </div>
                                              
                                            
                                              
                                                
                                          
             



                        
                            <ul class="nav nav-tabs" id="myTab" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="sms-api-tab" data-toggle="tab"
                                       href="#sms-api" role="tab" aria-controls="sms-api" aria-selected="true">SMS API</a>
                                </li>

                              
                                <li class="nav-item">
                                    <a class="nav-link" id="xml-api-tab" data-toggle="tab" href="#xml-api"
                                       role="tab"
                                       aria-controls="xml-api" aria-selected="false">XML API</a>
                                </li>
                            </ul>
                        </div>
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="sms-api" role="tabpanel"
                                 aria-labelledby="sms-api-tab">
                                <div class="smscredit-tab-block">
                                    <div class=" ">
                                        <div class="smscredit-tab-inner">
                                            <table id="sms-api-info-table"
                                                   class="table table-striped table-bordered nowrap table-custom"
                                                   cellspacing="0" width="100%">
                                                <thead>
                                               
                                                    <tr>
                                                    <th>Sl No</th>
                                                    <th>User Name</th>
                                                    <th>SMS Route</th>
                                                    <th>SMS Api Template</th>
                                                    <th>Template Status</th>
                                                    <th>Template Balance</th>
                                                    <th width="10%">Actions</th>
                                                    </tr>
                                                </thead>
                                               

                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                  
                            <div class="tab-pane fade show" id="xml-api" role="tabpanel"
                                 aria-labelledby="xml-api-tab">
                                <div class="smscredit-tab-block">
                                            
                                            
                                            <table id="xml-api-info-table"
                                                   class="table table-striped table-bordered nowrap table-custom"
                                                   cellspacing="0" width="100%">
                                                <thead>
                                                <tr>
                                                    <th>Sl No</th>
                                                    <th>User Name</th>
                                                    <th>Api User Name</th>
                                                    <th>Password </th>
                                                    <th>Api Password</th>
                                                    <th>Message Api</th>
                                                    <th>Status Api</th>
                                                    <th>Balance Api</th>
                                                    <th width="10%">Actions</th>
                                                </tr>
                                                </thead>

                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> 
                     
    </main>
@endsection 
@push('footer.script')
<script type="text/javascript"
            src="https://cdn.datatables.net/fixedcolumns/3.2.1/js/dataTables.fixedColumns.min.js"></script>
<script type="text/javascript">
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};
    //get user data
    //  view sms api table
    $('#sms-api-info-table').DataTable({
                    scrollX:true,
                    paging: true,
                    language:{
                        searchPlaceholder: 'Search',
                        sSearch:'',
                        lengthMenu:'_MENU_ page',

                    },
                    columnDefs:[
                        {
                         "width":"50px",
                         "targets":[0],
                        },
                        {
                            "width":"140px",
                            "targets":[1, 2, 3, 4],
                        },
                        {
                            "width":"80px",
                            "targets":[5],
                        },
                        
                        
                        ],
                    ajax: BASE_URL +'/admin/get-sms-api-template',
                    columns:[
                        {
                            data:'slno', 
                            name:'slno',
                        },
                        {
                            data:'vchr_user_name',
                            name:'vchr_user_name',
                        },
                        {
                            data:'vchr_sms_route',
                            name:'vchr_sms_route',
                        },
                       
                        {
                            data:'text_sms_api_template_url',
                            name:'text_sms_api_template_url',

                        },
                        {
                            data:'text_sms_api_template_status_url',
                            name:'text_sms_api_template_status_url',
                        }, 
                        {
                            data:'text_sms_api_template_balance_url',
                            name:'text_sms_api_template_balance_url',

                        },
                        {
                            data: 'action',
                            name: 'action', 
                            orderable: false, 
                            searchable: false,
                         }
                           
                            
                    ],
                    
    });
   
                $('#xml-api-info-table').DataTable({
                    scrollX:true,
                    paging: true,
                    language:{
                        searchPlaceholder: 'Search',
                        sSearch:'',
                        lengthMenu:'_MENU_ page',

                    },
                    columnDefs:[
                        {
                         "width":"50px",
                         "targets":[0],
                        },
                        {
                            "width":"140px",
                            "targets":[1, 2, 3, 4],
                        },
                        {
                            "width":"80px",
                            "targets":[5],
                        },
                        
                        
                        ],
                    ajax: BASE_URL +'/admin/get-xml-api-template',
                    columns:[
                        {
                            data:'slno', 
                            name:'slno',
                        },
                        {
                            data:'vchr_user_name',
                            name:'vchr_user_name',
                        },
                        {
                            data:'api_username',
                            name:'api_username',
                        },
                       
                        {
                            data:'password',
                            name:'password',

                        },
                        {
                            data:'api_password',
                            name:'api_password',
                        }, 
                        {
                            data:'message_api',
                            name:'message_api',

                        },
                        {
                            data:'status_api',
                            name:'status_api',

                        },
                        {
                            data:'balance_api',
                            name:'balance_api',

                        },
                        {
                            data: 'action',
                            name: 'action', 
                            orderable: false, 
                            searchable: false,
                         }
                            
                    ],
                    
                });
      
        // delete sms-api  template
        $('#sms-api-info-table').on('click', '.testimonial-delete', function (event) {
                event.preventDefault();
               
               
                    var apiId = $(this).attr('sms-api-id');
                    
                    
                    alert(apiId);
                    var destinationPath = BASE_URL + '/admin/sms-xml-api-template/' + apiId;
                    $.confirm({
                        title: 'Deletion',
                        content: 'Are you sure you want to delete ?',
                        icon: 'la la-question-circle',
                        animation: 'scale',
                        closeAnimation: 'scale',
                        opacity: 0.5,
                        buttons: {
                            'confirm': {
                                text: 'Proceed',
                                btnClass: 'btn-info',
                                action: function () {
                                    $.ajax({
                                        url: destinationPath,
                                        type: 'DELETE',
                                    }).done(function (res) {
                                        if (res.status == 'success') {
                                            $.alert({
                                                title: 'Success',
                                                type: 'green',
                                                content: res.msg,
                                            });
                                        } else {
                                            $.alert({
                                                title: 'Failed',
                                                type: 'red',
                                                content: res.msg,
                                            });
                                        }
                                    })
                                        .fail(function (err) {
                                        })
                                        .always(function (com) {
                                            $('#sms-api-info-table').DataTable().ajax.reload(null, false);
                                        });
                                }
                            },
                            cancel: function () {
                                $.alert('Operation <strong>canceled</strong>');
                            }
                        }
                    });
                  
        });
        // dletet xml api 
        $('#xml-api-info-table').on('click', '.testimonial-delete', function (event) {
                event.preventDefault();
               
               
                    var apiId =$(this).attr('xml-api-id');
                    // var apiId =$(this).attr('sms-api-id');
            
                   
                    var destinationPath = BASE_URL + '/admin/delete-xml-api-template/' + apiId ;
                    // alert(destinationPath);
                    $.confirm({
                        title: 'Deletion',
                        content: 'Are you sure you want to delete?',
                        icon: 'la la-question-circle',
                        animation: 'scale',
                        closeAnimation: 'scale',
                        opacity: 0.5,
                        buttons: {
                            'confirm': {
                                text: 'Proceed',
                                btnClass: 'btn-info',
                                action: function () {
                                    $.ajax({
                                        url: destinationPath,
                                        type: 'DELETE',
                                    }).done(function (res) {
                                        if (res.status == 'success') {
                                            $.alert({
                                                title: 'Success',
                                                type: 'green',
                                                content: res.msg,
                                            });
                                        } else {
                                            $.alert({
                                                title: 'Failed',
                                                type: 'red',
                                                content: destinationPath,
                                            });
                                        }
                                    })
                                        .fail(function (err) {
                                        })
                                        .always(function (com) {
                                            $('#xml-api-info-table').DataTable().ajax.reload(null, false);
                                        });
                                }
                            },
                            cancel: function () {
                                $.alert('Operation <strong>canceled</strong>');
                            }
                        }
                    });
                  
        });
        // activate deactivate
        $('#sms-api-info-table').on('click','#make-btn',function(event){
                event.preventDefault();

              
                var smsApiId= $(this).attr('sms-api-id');
              
                   if($(this).hasClass('btn-activate')){
               
                    var url =BASE_URL + '/admin/deactive-sms-api-template/' + smsApiId;
                    action='Make Deactivate';
                   
                }else{
                 
                    var url =BASE_URL + '/admin/active-sms-api-template/' + smsApiId;
                    action='Make Activate';
                }
                    
                 
             
                $.confirm({
                    title:action,
                    content:'Are you sure want to ' + action + '?',
                    icon:'la la-question-circle',
                    animation:'scale',
                    closeAnimation:'scale',
                    opacity:0.5,
                    buttons:{
                        'confirm':{
                            text:'Procced',
                            btnClass:'btn-info',
                            action:function(){
                                $.ajax({
                                    url:url,
                                    type:'GET',
                                })
                                .done(function (res){
                                    if(res.status == 'success'){
                                        $.alert({
                                            title:'Success',
                                            type:'green',
                                            content:res.msg,
                                        });
                                    }else{
                                        $.alert({
                                               title:'Failed',
                                               type:'red',
                                               content:res.msg, 
                                        
                                        });
                                    }
                                })
                                .fail(function(err){
                                })
                                .always(function(com){
                                    $('#sms-api-info-table').DataTable().ajax.reload(null,false);
                                });
                            }
                        },
                        cancel:function(){
                            $.alert('Operation<stron>canceled</strong>');
                        }
                    }

                });


        });
        $('#xml-api-info-table').on('click','#make-btn',function(event){
                event.preventDefault();

              
                var xmlApiId= $(this).attr('xml-api-id');
                alert(xmlApiId); 
                   if($(this).hasClass('btn-activate')){
                    // alert('deactivate');
                    var url =BASE_URL + '/admin/deactive-xml-api-template/' + xmlApiId;
                    action='Make Deactivate';
                   
                }else{
                    // alert('activate');
                    var url =BASE_URL + '/admin/active-xml-api-template/' + xmlApiId;
                    action='Make Activate';
                }
                    
                  alert(url);  
             
                $.confirm({
                    title:action,
                    content:'Are you sure want to ' + action + '?',
                    icon:'la la-question-circle',
                    animation:'scale',
                    closeAnimation:'scale',
                    opacity:0.5,
                    buttons:{
                        'confirm':{
                            text:'Procced',
                            btnClass:'btn-info',
                            action:function(){
                                $.ajax({
                                    url:url,
                                    type:'GET',
                                })
                                .done(function (res){
                                    if(res.status == 'success'){
                                        $.alert({
                                            title:'Success',
                                            type:'green',
                                            content:res.msg,
                                        });
                                    }else{
                                        $.alert({
                                               title:'Failed',
                                               type:'red',
                                               content:res.msg, 
                                        
                                        });
                                    }
                                })
                                .fail(function(err){
                                })
                                .always(function(com){
                                    $('#xml-api-info-table').DataTable().ajax.reload(null,false);
                                });
                            }
                        },
                        cancel:function(){
                            $.alert('Operation<stron>canceled</strong>');
                        }
                    }

                });


        });

 }); 
</script>
@endpush