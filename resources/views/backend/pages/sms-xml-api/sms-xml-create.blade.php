            
            
 @extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Add Api Template</h5>
                </div>

               
             
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminbulkpushsidebar')
            <div class="content-section p-3 bg-white">
               
                <!--  /Your content goes here/ -->
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                       Api Template Type
                    </button>
                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <a class="dropdown-item" id="api-sms" href="#">SMS</a>
                        <a class="dropdown-item" id ="api-xml" href="#">XML</a>
                       
                    </div>
                </div>

                <div id="add-api-template">
                </div>
              
            </div>
        </div>
    </main>
   
@endsection
@push('footer.script')
    <script type="text/javascript">
        $(document).ready(function () {
            BASE_URL = window.location.origin;


            $(document).on('click', '#api-sms', function (event) {
                event.preventDefault();
               
                var code = `  <form method="POST" action="{{url('admin/sms-xml-api-template')}}">
                    {{csrf_field()}}
                    <input type="hidden"  name="api_type" value="sms">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="row">
                                <div class=" col-md-6 form-group">
                                    <label>Select User</label>
                                    <select name="fk_int_user_id" id="" class="form-control">
                                       @foreach(\App\User::getUsers() as $user)
                                         <option  value="{{$user->id}}">{{$user->name}}</option>
                                       @endforeach  
                                    </select>
                                    @if ($errors->has('fk_int_user_id'))
                                        <span class="text-danger"><strong> * {{ $errors->first('fk_int_user_id') }}</strong></span>
                                    @endif

                                    
                                </div>
                                <div class=" col-md-6 form-group">
                                    <label>Select Route</label>

                                    <select name="fk_int_sms_route_id" id="" class="form-control">
                                     @foreach($routes as $route)
                                            <option  value="{{$route->pk_int_sms_route_id}}">{{$route->vchr_sms_route}}</option>

                                     @endforeach  
                                    </select>
                                    @if ($errors->has('fk_int_sms_route_id'))
                                        <span class="text-danger"><strong> * {{ $errors->first('fk_int_sms_route_id') }}</strong></span>
                                    @endif
                                </div>
                               
                                
                                <div class=" col-md-6 form-group">
                                    <label>SMS Api Template Url</label>
                                    <input type="url" class="form-control" placeholder="Enter Sms api Template url " name="text_sms_api_template_url" value="{{ old('text_sms_api_template_url') }}">
                                    @if ($errors->has('text_sms_api_template_url'))
                                        <span class="text-danger"><strong> * {{ $errors->first('text_sms_api_template_url') }}</strong></span>
                                    @endif
                                </div>
                              

                                <div class=" col-md-6 form-group">
                                    <label>SMS Api Template Status Url</label>
                                    <input type="url" class="form-control" placeholder="Enter Sms api Template status url" name="text_sms_api_template_status_url" value="{{ old('text_sms_api_template_status_url') }}">
                                    @if ($errors->has('text_sms_api_template_status_url'))
                                        <span class="text-danger"><strong> * {{ $errors->first('text_sms_api_template_status_url') }}</strong></span>
                                    @endif
                                </div>
                               
                                <div class=" col-md-6 form-group">
                                    <label>SMS Api Template Balance Url </label>
                                    <input type="url" class="form-control" placeholder="Enter Sms api Template Balance Url" name="text_sms_api_template_balance_url" value="{{ old('text_sms_api_template_balance_url') }}">
                                    @if ($errors->has('text_sms_api_template_balance_url'))
                                        <span class="text-danger"><strong> * {{ $errors->first('text_sms_api_template_balance_url') }}</strong></span>
                                    @endif
                                </div>
                               
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-2">
                            <input type="submit" value="Add" class="main-round-btn py-1">
                        </div>
                    </div>
                </form>   `;         
                    //    var codes = $(code).text();
                    $("#add-api-template").html(code);

            }); 
            $(document).on('click', '#api-xml', function (event) {
                event.preventDefault();
                
                var code = `  <form method="POST" action="{{url('admin/sms-xml-api-template')}}">
                    {{csrf_field()}}
                    <input type="hidden"  name="api_type" value="xml">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="row">
                                <div class=" col-md-6 form-group">
                                    <label>Select User</label>
                                    <select name="vendor_id" id="" class="form-control">
                                       @foreach(\App\User::getUsers() as $user)
                                         <option  value="{{$user->id}}">{{$user->name}}</option>
                                       @endforeach  
                                    </select>
                                    @if ($errors->has('vendor_id'))
                                        <span class="text-danger"><strong> * {{ $errors->first('vendor_id') }}</strong></span>
                                    @endif

                                    
                                </div>
                                <div class=" col-md-6 form-group">
                                    <label>Api Username</label>
                                    <input type="text" class="form-control" placeholder="Enter Api Username " name="api_username" value="{{ old('api_username') }}">
                                    @if ($errors->has('api_username'))
                                        <span class="text-danger"><strong> * {{ $errors->first('api_username') }}</strong></span>
                                    @endif
                                </div>
                               
                                
                                <div class=" col-md-6 form-group">
                                    <label>Password</label>
                                    <input type="text" class="form-control" placeholder="Enter password " name="password" value="{{ old('password') }}">
                                    @if ($errors->has('password'))
                                        <span class="text-danger"><strong> * {{ $errors->first('password') }}</strong></span>
                                    @endif
                                </div>
                              

                                <div class=" col-md-6 form-group">
                                    <label>Api Password</label>
                                    <input type="text" class="form-control" placeholder="Enter Api Password " name="api_password" value="{{ old('api_password') }}">
                                    @if ($errors->has('api_password'))
                                        <span class="text-danger"><strong> * {{ $errors->first('api_password') }}</strong></span>
                                    @endif
                                </div>
                               
                                <div class=" col-md-6 form-group">
                                    <label>Message Api Url </label>
                                    <input type="url" class="form-control" placeholder="Enter Message Api Url" name="message_api" value="{{ old('message_api') }}">
                                    @if ($errors->has('message_api'))
                                        <span class="text-danger"><strong> * {{ $errors->first('message_api') }}</strong></span>
                                    @endif
                                </div>
                                <div class=" col-md-6 form-group">
                                    <label>Status Api Url </label>
                                    <input type="url" class="form-control" placeholder="Enter Sms Status Api Url " name="status_api" value="{{ old('status_api') }}">
                                    @if ($errors->has('status_api'))
                                        <span class="text-danger"><strong> * {{ $errors->first('status_api') }}</strong></span>
                                    @endif
                                </div>
                                <div class=" col-md-6 form-group">
                                    <label>Balance Api Url </label>
                                    <input type="url" class="form-control" placeholder="Enter Balance Api Url " name="balance_api" value="{{ old('balance_api') }}">
                                    @if ($errors->has('balance_api'))
                                        <span class="text-danger"><strong> * {{ $errors->first('balance_api') }}</strong></span>
                                    @endif
                                </div>
                               
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-2">
                            <input type="submit" value="Add" class="main-round-btn py-1">
                        </div>
                    </div>
                </form>   `;         
                    //    var codes = $(code).text();
                    $("#add-api-template").html(code);

            }); 
        });
    </script>
@endpush     
                
            
            
       