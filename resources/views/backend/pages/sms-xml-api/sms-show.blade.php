@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Edit SMS Api Template</h5>
                </div>


            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminbulkpushsidebar')
            <div class="content-section p-3 bg-white">

                <!--  /Your content goes here/ -->

                <form method="POST" action="{{url('admin/sms-xml-api-template',$sms_api->pk_int_sms_api_template_id)}}">
                    {{csrf_field()}}
                    {{method_field('PUT')}}
                    <input type="hidden" name="api_type" value="sms">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="row">
                                <div class=" col-md-6 form-group">
                                    <label>Select User</label>
                                    <select name="fk_int_user_id" id="" class="form-control">
                                        @foreach(\App\User::getUsers() as $user)
                                            @if($user->id == $sms_api->fk_int_user_id)
                                                <option value="{{$user->id}}" selected>{{$user->name}}</option>
                                            @else
                                                <option value="{{$user->id}}">{{$user->name}}</option>
                                            @endif
                                        @endforeach
                                    </select>
                                    @if ($errors->has('fk_int_user_id'))
                                        <span class="text-danger"><strong> * {{ $errors->first('fk_int_user_id') }}</strong></span>
                                    @endif


                                </div>
                                <div class=" col-md-6 form-group">
                                    <label>Select Route</label>

                                    <select name="fk_int_sms_route_id" id="" class="form-control">
                                        @foreach($routes as $route)
                                            @if($route->pk_int_sms_route_id ==$sms_api->fk_int_sms_route_id)
                                                <option value="{{$route->pk_int_sms_route_id}}"
                                                        selected>{{$route->vchr_sms_route}}</option>


                                            @else
                                                <option value="{{$route->pk_int_sms_route_id}}">{{$route->vchr_sms_route}}</option>
                                                @endelse
                                            @endif
                                        @endforeach
                                    </select>
                                    @if ($errors->has('fk_int_sms_route_id'))
                                        <span class="text-danger"><strong> * {{ $errors->first('fk_int_sms_route_id') }}</strong></span>
                                    @endif
                                </div>


                                <div class=" col-md-6 form-group">
                                    <label>SMS Api Template Url</label>
                                    <input type="url" class="form-control" placeholder="Enter Sms api Template url "
                                           name="text_sms_api_template_url"
                                           value="{{$sms_api->text_sms_api_template_url}}">
                                    @if ($errors->has('text_sms_api_template_url'))
                                        <span class="text-danger"><strong> * {{ $errors->first('text_sms_api_template_url') }}</strong></span>
                                    @endif
                                </div>


                                <div class=" col-md-6 form-group">
                                    <label>SMS Api Template Status Url</label>
                                    <input type="url" class="form-control"
                                           placeholder="Enter Sms api Template status url"
                                           name="text_sms_api_template_status_url"
                                           value="{{ $sms_api->text_sms_api_template_status_url }}">
                                    @if ($errors->has('text_sms_api_template_status_url'))
                                        <span class="text-danger"><strong> * {{ $errors->first('text_sms_api_template_status_url') }}</strong></span>
                                    @endif
                                </div>

                                <div class=" col-md-6 form-group">
                                    <label>SMS Api Template Balance Url </label>
                                    <input type="url" class="form-control"
                                           placeholder="Enter Sms api Template Balance Url"
                                           name="text_sms_api_template_balance_url"
                                           value="{{ $sms_api->text_sms_api_template_balance_url }}">
                                    @if ($errors->has('text_sms_api_template_balance_url'))
                                        <span class="text-danger"><strong> * {{ $errors->first('text_sms_api_template_balance_url') }}</strong></span>
                                    @endif
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-2">
                            <input type="submit" value="Update" class="main-round-btn py-1">
                        </div>
                    </div>
                </form>


            </div>
        </div>
    </main>

@endsection
@push('footer.script')
    <script type="text/javascript">
        $(document).ready(function () {
            BASE_URL = window.location.origin;
        });
    </script>
@endpush