            
            
 @extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Edit XML Api Template</h5>
                </div>

               
             
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.adminbulkpushsidebar')
            <div class="content-section p-3 bg-white">
               
                <!--  /Your content goes here/ -->
              
                <form method="POST" action="{{url('admin/sms-xml-api-template',$xml_api->id)}}">
                    {{csrf_field()}}
                    {{method_field('PUT')}}
                    <input type="hidden"  name="api_type" value="xml">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="row">
                                <div class=" col-md-6 form-group">
                                    <label>Select User</label>
                                    <select name="vendor_id" id="" class="form-control">
                                       @foreach(\App\User::getUsers() as $user)
                                            @if($user->id ==$xml_api->vendor_id)
                                                <option  value="{{$user->id}}"selected>{{$user->name}}</option>
                                            @else
                                                <option  value="{{$user->id}}">{{$user->name}}</option>
                                            @endelse
                                            @endif
                                       @endforeach  
                                    </select>
                                    @if ($errors->has('vendor_id'))
                                        <span class="text-danger"><strong> * {{ $errors->first('vendor_id') }}</strong></span>
                                    @endif

                                    
                                </div>
                                <div class=" col-md-6 form-group">
                                    <label>Api Username</label>
                                    <input type="text" class="form-control" placeholder="Enter Api Username " name="api_username" value="{{ $xml_api->api_username}}">
                                    @if ($errors->has('api_username'))
                                        <span class="text-danger"><strong> * {{ $errors->first('api_username') }}</strong></span>
                                    @endif
                                </div>
                               
                                
                                <div class=" col-md-6 form-group">
                                    <label>Password</label>
                                    <input type="text" class="form-control" placeholder="Enter password " name="password" value="{{ $xml_api->password }}">
                                    @if ($errors->has('password'))
                                        <span class="text-danger"><strong> * {{ $errors->first('password') }}</strong></span>
                                    @endif
                                </div>
                              

                                <div class=" col-md-6 form-group">
                                    <label>Api Password</label>
                                    <input type="text" class="form-control" placeholder="Enter Api Password " name="api_password" value="{{ $xml_api->api_password }}">
                                    @if ($errors->has('api_password'))
                                        <span class="text-danger"><strong> * {{ $errors->first('api_password') }}</strong></span>
                                    @endif
                                </div>
                               
                                <div class=" col-md-6 form-group">
                                    <label>Message Api Url </label>
                                    <input type="url" class="form-control" placeholder="Enter Message Api Url" name="message_api" value="{{ $xml_api->message_api }}">
                                    @if ($errors->has('message_api'))
                                        <span class="text-danger"><strong> * {{ $errors->first('message_api') }}</strong></span>
                                    @endif
                                </div>
                                <div class=" col-md-6 form-group">
                                    <label>Status Api Url </label>
                                    <input type="url" class="form-control" placeholder="Enter Sms Status Api Url " name="status_api" value="{{ $xml_api->status_api }}">
                                    @if ($errors->has('status_api'))
                                        <span class="text-danger"><strong> * {{ $errors->first('status_api') }}</strong></span>
                                    @endif
                                </div>
                                <div class=" col-md-6 form-group">
                                    <label>Balance Api Url </label>
                                    <input type="url" class="form-control" placeholder="Enter Balance Api Url " name="balance_api" value="{{ $xml_api->balance_api }}">
                                    @if ($errors->has('balance_api'))
                                        <span class="text-danger"><strong> * {{ $errors->first('balance_api') }}</strong></span>
                                    @endif
                                </div>
                               
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-2">
                            <input type="submit" value="Update" class="main-round-btn py-1">
                        </div>
                    </div>
                </form>   
             
              
            </div>
        </div>
    </main>
   
@endsection
@push('footer.script')
    <script type="text/javascript">
        $(document).ready(function () {
            BASE_URL = window.location.origin;
        });
    </script>
@endpush