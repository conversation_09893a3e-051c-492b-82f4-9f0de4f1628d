<div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true">
    <form id="testimonialEdit" enctype="multipart/form-data">
        <div class="modal-dialog  modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h4 class="modal-title w-100 font-weight-bold g-clr">Update Plans</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body mx-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Select User</label>
                                <select type="text" name="vendor_id" id="vendor_id" class="form-control mselect2-2"
                                        placeholder="Select User" required>
                                    <option value="">Select User</option>
                                    <option value="0">All Users</option>
                                    @foreach(\App\User::getUsers() as $user)
                                        <option value="{{ $user->id }}">{{ $user->user_name_with_phone }}</option>
                                    @endforeach
                                </select>

                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Plan Name</label>
                                <input type="text" name="name" class="form-control" placeholder="Plan name"
                                       id="planName">
                                <span class="error name"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Actual Price</label>
                                <input type="text" name="alias" class="form-control" placeholder="Plan Actual Price"
                                       id="planAlias">
                                <span class="error alias"></span>
                            </div>
                        </div>
                    </div>
                    <div class="md-form mb-1">
                        <label for="default-input" class="form-control-label">Plan Description</label>
                        <textarea name="description" class="form-control" placeholder="Plan Description"
                                  id="planDes"
                                  rows="5"></textarea>
                        <span class="error  description"></span>
                    </div>
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Plan Duration</label>
                                <input type="text" name="duration" class="form-control" id="planDuration"
                                       placeholder="Duration in days">
                                <span class="error duration"></span>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Tax %</label>
                                <input type="text" name="tax" class="form-control" id="planTax">
                            </div>
                        </div>
                        <div class="col-sm-3 col-auto my-1">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tax_inclusive"
                                       id="taxInclusiveEdit">
                                <label class="form-check-label" for="taxInclusiveEdit">
                                    Tax Inclusive
                                </label>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Country</label>
                                <input type="text" name="country_id" readonly class="form-control" id="planCountry">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 my-1">
                            <label for="default-input" class="form-control-label"> Plan amount</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <div class="input-group-text inr"></div>
                                </div>
                                <input type="text" name="amount" class="form-control" placeholder="Plan amount"
                                       id="planAmount">
                                <span class="error amount"></span></div>
                        </div>
                    </div>
                    <div class="md-form mb-1">
                        <label data-error="wrong" data-success="right" for="orangeForm-pass">Services</label>
                        <div class="row col-md-12">
                            <div id="service-container">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="" id="plan_id">
                        <button class="btn btn-deep-orange demo-btn">Update</button>
                    </div>
                </div>
            </div>
    </form>
</div>
