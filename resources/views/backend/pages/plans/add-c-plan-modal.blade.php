<div class="modal fade" id="ks-izi-modal-large3" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true">
    <form id="customPlanAdd" enctype="multipart/form-data">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h4 class="modal-title w-100 font-weight-bold g-clr">Add Plan</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body mx-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Name</label>
                                <input type="text" name="name" class="form-control" placeholder="Plan name">
                                <span class="error c-name"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Actual Price</label>
                                <input type="text" name="alias" class="form-control" placeholder="Plan Actual Price">
                                <span class="val-error c-alias"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 my-1">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Duration</label>
                                <input type="text" name="duration" class="form-control"
                                       placeholder="In days">
                                <span class="error c-duration"></span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="md-form mb-1">
                                <label class="form-control-label">Country</label>
                                <select class="form-control" name="country_id">
                                    <option value="">
                                        Select Country
                                    </option>
                                    @foreach(\App\Common\Variables::getCountryList() as $country)
                                        <option value="{{$country->id}}">{{$country->name}}</option>
                                    @endforeach
                                </select>

                                <span class="error c-country_id"></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-row align-items-center row ">
                        <div class="col-md-3">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Amount for single sms</label>
                                <input type="text" name="amount" class="form-control" placeholder="amount">
                                <span class="error c-amount"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Currency Type</label>
                                <select class="form-control" style="width: 100%;"
                                        name="currency_type">
                                    <option value="INR">INR</option>
                                    <option value="USD">USD</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3 col-auto my-1">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tax_inclusive"
                                       id="autoSizingCheck2">
                                <label class="form-check-label" for="autoSizingCheck2">
                                    Tax Inclusive
                                </label>
                            </div>
                        </div>
                        <div class="col-sm-3 my-1">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Tax %</label>
                                <input type="number" name="tax" class="form-control"
                                       placeholder="Percentage">

                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">User </label>
                                <select class="form-control" style="width: 100%;"
                                        name="vendor_id">
                                    <option value="0">Select User</option>
                                    @foreach(\App\User::getUsers() as $user)
                                        <option value="{{$user->id}}">{{$user->name}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        @foreach($smsRoutes as $route_index=>$smsRoute)
                            <div class="col-sm-3" class="test">
                                <div class="form-group">
                                    <label for="serviceCount_{{1}}">{{$smsRoute->vchr_sms_route}}</label>
                                    <input type="hidden" class="form-control"
                                           name="service[{{1}}][sms_route][{{$route_index}}][id]"
                                           value="{{$smsRoute->pk_int_sms_route_id}}"
                                           placeholder="Count" multiple=""/>
                                    <input type="hidden" class="form-control"
                                           name="service[{{1}}][sms_route][{{$route_index}}][name]"
                                           value="{{$smsRoute->vchr_sms_route}}"
                                           placeholder="Count" multiple=""/>
                                    <input type="number" class="form-control text-field"
                                           name="service[{{1}}][sms_route][{{$route_index}}][count]"
                                           placeholder="Count" multiple=""/>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="md-form mb-1">
                        <label for="default-input" class="form-control-label">Description</label>
                        <textarea name="description" class="form-control" placeholder="Plan Description"
                                  rows="5"></textarea>
                        <span class="error  c-description"></span>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    @csrf
                    <button class="btn btn-deep-orange demo-btn">Add</button>
                </div>
            </div>
        </div>
    </form>
</div>