<div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true">
    <form id="planAdd" enctype="multipart/form-data">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h4 class="modal-title w-100 font-weight-bold g-clr">Plan</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body mx-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Select User</label>
                                <select type="text" name="vendor_id" class="form-control mselect2-1" placeholder="Select User"
                                        required style="width: 100%">
                                    <option value="">Select User</option>
                                    <option value="0">All Users</option>
                                    @foreach(\App\User::getUsers() as $user)
                                        <option value="{{ $user->id }}">{{ $user->user_name_with_phone }}</option>
                                    @endforeach
                                </select>

                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Name</label>
                                <input type="text" name="name" class="form-control" placeholder="Plan name">
                                <span class="error name"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label">Actual Price</label>
                                <input type="text" name="alias" class="form-control" placeholder="Plan Actual Price">
                                <span class="val-error alias"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 my-1">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Duration</label>
                                <input type="text" name="duration" class="form-control"
                                       placeholder="In days">
                                <span class="error duration"></span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="md-form mb-1">
                                <label class="form-control-label">Country</label>
                                <select class="form-control" name="country_id">
                                    <option value="">
                                        Select Country
                                    </option>
                                    @foreach(\App\Common\Variables::getCountryList() as $country)
                                        <option value="{{$country->id}}">{{$country->name}}</option>
                                    @endforeach
                                </select>

                                <span class="error country_id"></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-row align-items-center row ">
                        <div class="col-md-4">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Amount</label>
                                <input type="text" name="amount" class="form-control" placeholder="Plan amount">
                                <span class="error amount"></span>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Currency Type</label>
                                <select class="form-control" style="width: 100%;"
                                        name="currency_type">
                                    <option value="INR">INR</option>
                                    <option value="USD">USD</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-sm-4 my-1">
                            <div class="md-form mb-1">
                                <label for="default-input" class="form-control-label"> Tax %</label>
                                <input type="number" name="tax" class="form-control"
                                       placeholder="Percentage">

                            </div>
                        </div>
                        <div class="col-sm-2 col-auto my-1">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tax_inclusive"
                                       id="taxInclusive">
                                <label class="form-check-label" for="taxInclusive">
                                    Tax Inclusive
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="md-form mb-1">
                        <label for="default-input" class="form-control-label">Description</label>
                        <textarea name="description" class="form-control" placeholder="Plan Description"
                                  rows="5"></textarea>
                        <span class="error  description"></span>
                    </div>
                    <div class="md-form mb-1">
                        <label data-error="wrong" data-success="right" for="orangeForm-pass">Services</label>
                        @foreach(\App\Common\Variables::GETLEAD_SERVICES as $index=>$service)
                            <div class="row">
                                <div class="col-sm-3 form-group">
                                    <!-- Material unchecked -->
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="form-check-input"
                                               id="checkbox_{{$index}}" name="service[{{$index}}][name]"
                                               value="{{$service}}">
                                        <label class="form-check-label"
                                               for="checkbox_{{$index}}">{{$service}}</label>
                                    </div>
                                </div>
                                @if($service=="CRM" || $service=='Missed Call'|| $service==\App\Common\Variables::SERVICE_IVR)
                                    <div class="col-sm-6 form-group">
                                        <label for="serviceCount_{{$index}}"> @if($service=="CRM")
                                                Leads  Count</label>
                                        <input type="number" class="form-control"
                                               name="usercount"
                                               placeholder="Count" multiple=""/>
                                    </div>
                                    <div class="col-sm-4 form-group">

                                    </div>
                                    <div class="col-sm-6 form-group" style="margin-left: -64px;">
                                        <label for="serviceCount_{{$index}}">
                                            Agent
                                            @elseif($service=="Missed Call" || $service==\App\Common\Variables::SERVICE_IVR)
                                                Virtual Number

                                            @endif
                                            Count</label>
                                        <input type="number" class="form-control"
                                               name="service[{{$index}}][count]"
                                               placeholder="Count" multiple=""/>
                                    </div>
                                    @elseif($service=="GL Scratch")
        
                                    <div class="col-sm-6 form-group">
                                        <label for="{{$index}}">Scratch Card Count
                                            </label>
                                        <input type="number" class="form-control"
                                               name="service[{{$index}}][count]"
                                               placeholder="Scratch Card Count" multiple=""/>
                                    </div>
                                @elseif($service=="SMS")
                                    <div class="col-sm-6 form-group">
                                        <label for="serviceCount_{{$index}}"> @if($service==\App\Common\Variables::SERVICE_CRM)
                                                Agent
                                            @elseif($service==\App\Common\Variables::SERVICE_MISSEDCALL)
                                                Virtual Number
                                                {{-- @elseif($service==\App\Common\Variables::SERVICE_SMS)
                                                     Total--}}
                                            @endif
                                            Count</label>
                                        {{--<input type="number" class="form-control" readonly id="smsTotalCount"--}}
                                        {{--name="service[{{$index}}][count]"--}}
                                        {{--placeholder="Count" multiple=""/>--}}

                                        <div class="row">
                                            @foreach($smsRoutes as $route_index=>$smsRoute)
                                                <div class="col-sm-12" class="test">
                                                    <div class="form-group">
                                                        <label for="serviceCount_{{$index}}">{{$smsRoute->vchr_sms_route}}</label>
                                                        <input type="hidden" class="form-control"
                                                               name="service[{{$index}}][sms_route][{{$route_index}}][id]"
                                                               value="{{$smsRoute->pk_int_sms_route_id}}"
                                                               placeholder="Count" multiple=""/>
                                                        <input type="hidden" class="form-control"
                                                               name="service[{{$index}}][sms_route][{{$route_index}}][name]"
                                                               value="{{$smsRoute->vchr_sms_route}}"
                                                               placeholder="Count" multiple=""/>
                                                        <input type="number" class="form-control text-field"
                                                               name="service[{{$index}}][sms_route][{{$route_index}}][count]"
                                                               placeholder="Count" multiple=""/>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endforeach

                        {{--<div class="input_fields_wrap col-md-12">--}}
                        {{--<div class="row">--}}
                        {{--<div class="col-md-6">--}}
                        {{--<label data-error="wrong" data-success="right"--}}
                        {{--for="orangeForm-pass">Name</label>--}}
                        {{--<select class="form-control" name="service[0][name]">--}}
                        {{--<option value="0">--}}
                        {{--Select Service--}}
                        {{--</option>--}}
                        {{--@foreach(\App\Common\Variables::GETLEAD_SERVICES as $service)--}}
                        {{--<option value="{{$service}}">--}}
                        {{--{{$service}}--}}
                        {{--</option>--}}
                        {{--@endforeach--}}
                        {{--</select>--}}

                        {{--<input type="text" class="form-control" name="service[0][name]" required--}}
                        {{--placeholder="Service Name"--}}
                        {{--multiple=""--}}
                        {{--value=""/>--}}
                        {{--</div>--}}
                        {{--<div class="col-md-3">--}}
                        {{--<label data-error="wrong" data-success="right"--}}
                        {{--for="orangeForm-pass">Duration </label>--}}
                        {{--<input type="number" class="form-control" name="service[0][duration]" required--}}
                        {{--placeholder="Duration"--}}
                        {{--multiple=""--}}
                        {{--value=""/>--}}
                        {{--</div>--}}
                        {{--<div class="col-md-2">--}}
                        {{--<label data-error="wrong" data-success="right"--}}
                        {{--for="orangeForm-pass">Count</label>--}}
                        {{--<input type="number" class="form-control" name="service[0][count]"--}}
                        {{--placeholder="Count"--}}
                        {{--multiple=""--}}
                        {{--value=""/>--}}
                        {{--</div>--}}
                        {{--<div class="col-md-1 mg-tp-40">--}}
                        {{--<a href="javascript:void(0);" class="add_field_button" title="Add field"><i--}}
                        {{--class="fa fa-plus-circle"></i></a>--}}
                        {{--</div>--}}
                        {{--</div>--}}
                        {{--</div>--}}
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    @csrf
                    <button class="btn btn-deep-orange demo-btn">Add</button>
                </div>
            </div>
        </div>
    </form>
</div>