@extends('backend.layout.master')
@section('page-header')
    <style type="text/css">
        .val-error {
            color: red;
        }
    </style>
@endsection
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Plan</h5>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class="">
                                    <a class="main-round-btn ks-izi-modal-trigger"
                                       data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry"
                                       href="#">Add Plan</a>
                                </li>
                                {{-- <li class="">
                                    <a class="main-round-btn ks-izi-modal-trigger"
                                       data-toggle="modal" data-target="#ks-izi-modal-large3" id="addEnquiry"
                                       href="#">Add SMS Plan</a>
                                </li> --}}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.adminsidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table" style="overflow: auto">
                    <table id="plan-info-table" class="table table-striped table-bordered nowrap table-custom"
                           cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Position</th>
                            <th>User</th>
                            <th>Plan name</th>
                            <th>Description</th>
                            <th>Actual Price</th>
                            <th>Offer Price</th>
                            <th>Duration</th>
                            <th width="10%">Actions</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </main>
    <!------------ /add custom  modal/  -------------->
    @include('backend.pages.plans.add-c-plan-modal')
    <!------------ /add modal/  -------------->
    @include('backend.pages.plans.add-plan-modal')
    <!------------ /update modal/  -------------->
    @include('backend.pages.plans.edit-plan-modal')
@endsection
@push('footer.script')
    <style type="text/css">
        
        .select2-container{
            width:100% !important;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function () {
            $('.mselect2-1').select2({
                dropdownParent: $('#ks-izi-modal-large')
            });
            $('.mselect2-2').select2({
                dropdownParent: $('#ks-izi-modal-large1')
            });
            BASE_URL = window.location.origin
            $('.error').hide();
            $('#plan-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "150px",
                    "targets": [0, 1, 2, 3, 4]
                },
                ],
                ajax: BASE_URL + '/admin/get-plan',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'position', name: 'position'},
                    {data: 'user', name: 'user'},
                    {data: 'name', name: 'name'},
                    {data: 'description', name: 'description'},
                    {data: 'alias', name: 'alias'},
                    {data: 'amount', name: 'amount'},
                    {data: 'duration', name: 'duration'},
                    {data: 'show', name: 'show', orderable: false, searchable: false}
                ],
            });
            $(document).on('submit', '#planAdd', function (event) {
                event.preventDefault();
                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                var checkedOne = Array.prototype.slice.call(checkboxes).some(x => x.checked);
                if (checkedOne === false) {
                    $.alert({
                        title: 'Validation Error',
                        type: 'red',
                        content: 'Please select atleast one service.',
                    });
                }

                $("#testimonialEdit")[0].reset();
                // $('').on('click', function (e) {
                $('.error').html('');
                $('.error').hide();
                $.ajax({
                    url: BASE_URL + '/admin/plans',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status === true) {
                        $("#planAdd")[0].reset();
                        $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else if (res.status === false) {
                        $.alert({
                            title: 'Failed',
                            type: 'red',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            console.log(val);
                            console.log(index);
                            $('.c' + index).html(val);
                            $('.c' + index).show();
                        });
                    }
                })
                    .fail(function () {
                    })
                    .always(function (com) {
                        $('#plan-info-table').DataTable().ajax.reload(null, false);
                    });
            });
            $(document).on('submit', '#customPlanAdd', function (event) {
                event.preventDefault();

                $("#testimonialEdit")[0].reset();
                // $('').on('click', function (e) {
                $('.error').html('');
                $('.error').hide();
                $.ajax({
                    url: BASE_URL + '/admin/add-custom-plans',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status === true) {
                        $("#planAdd")[0].reset();
                        $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else if (res.status === false) {
                        $.alert({
                            title: 'Failed',
                            type: 'red',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            console.log(val);
                            console.log(index);
                            $('.c' + index).html(val);
                            $('.c' + index).show();
                        });
                    }
                })
                    .fail(function () {
                    })
                    .always(function (com) {
                        $('#plan-info-table').DataTable().ajax.reload(null, false);
                    });
            });

            $(document).on('submit', '#testimonialEdit', function (event) {
                event.preventDefault();
                $("#planAdd")[0].reset();
                $('.error').html('');
                $('.error').hide();
                var id = $('#plan_id').val();
                $.ajax({
                    url: BASE_URL + '/admin/plans/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                })
                    .done(function (res) {
                        if (res.status === 'success') {
                            $('#ks-izi-modal-large1').modal('toggle');
                            $.alert({
                                title: 'Success',
                                type: 'green',
                                content: res.msg,
                            });
                            $("#planAdd")[0].reset();
                        } else {
                            $.each(res.msg, function (index, val) {
                                //iterate through array or object
                                console.log(val);
                                console.log(index);
                                $('.' + index).html(val);
                                $('.' + index).show();
                            });
                        }
                    })
                    .fail(function () {
                    })
                    .always(function (com) {
                        $('#plan-info-table').DataTable().ajax.reload(null, false);

                    });
            });
            $('#plan-info-table').on('click', '.plan-act', function (event) {
                event.preventDefault();
                if ($(this).hasClass('btn-activate')) {
                    var url = BASE_URL + '/admin/plans-deactivate/';
                    action = 'Deactivate';
                } else {
                    var url = BASE_URL + '/admin/plans-activate/';
                    action = 'Activate';
                }
                var feedbackId = $(this).attr('plan-id');
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                    .done(function (res) {
                                        if (res.status === true) {
                                            $.alert({
                                                title: 'Success',
                                                type: 'green',
                                                content: res.msg,
                                            });
                                        } else {
                                            $.alert({
                                                title: 'Failed',
                                                type: 'red',
                                                content: res.msg,
                                            });
                                        }
                                    })
                                    .fail(function (err) {
                                    })
                                    .always(function (com) {
                                        $('#plan-info-table').DataTable().ajax.reload(null, false);
                                    });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $('#plan-info-table').on('click', '.plan-delete', function (event) {
                event.preventDefault();
                var planId = $(this).attr('plan-id');
                var destinationPath = BASE_URL + '/admin/plans/' + planId;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                }).done(function (res) {
                                    if (res.status === true) {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                }).fail(function (err) {
                                }).always(function (com) {
                                    $('#plan-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $('#plan-info-table').on('click', '.ks-izi-modal-trigger1', function (event) {
                event.preventDefault();
                $("#planAdd")[0].reset();
                $('#service-container').html('');
                $('.error').html('');
                $('.error').hide();
                var planId = $(this).attr('plan-id');
                $.ajax({
                    url: BASE_URL + '/admin/plans/' + planId,
                    type: 'GET',
                    dataType: 'JSON',
                }).done(function (res) {
                    if (res.status === true) {
                        data = res.data;
                        country = data.country? data.country.name : '';
                        $("#planName").val(data.name);
                        $("#planAlias").val(data.alias);
                        $("#planDes").val(data.description);
                        $("#planAmount").val(data.amount);
                        $("#planDuration").val(data.duration);
                        $('#plan_id').val(data.id);
                        $("#planCountry").val(country);
                        $("#planCurrency").val(data.currency_type);
                        $("#planTax").val(data.tax);
                        $(".inr").text(data.currency_type);
                        $("#vendor_id").val(data.vendor_id).change();
                        if (data.tax_inclusive == 1) {
                            $('#taxInclusiveEdit').attr('checked', true);
                        } else {
                            $('#taxInclusiveEdit').attr('checked', false);
                        }
                        $('#ks-izi-modal-large1').modal('toggle');
                        var services = data.services;
                        $.each(services, function (key, value) {
                            var sms_count = value['sms_count'];
                            var content = '<div class="row">' +
                                '<div class="col-md-5">' +
                                '<input type="text" class="form-control"  name="service[' + key + '][name]" required multiple="" value="' + value['service'] + '" placeholder="Service Name" readonly/>' +
                                '</div>';
                            /* '<div class="col-md-3">' +
                             '<input type="number" class="form-control"  name="service[' + key + '][duration]" required multiple="" value="' + value['duration'] + '" placeholder="Duration"/>' +
                             '</div>' +*/
                             if(value['service'] == 'GL Scratch')
                             {
                                content += '<div class="col-md-3">' +
                                '<input type="number" class="form-control" name="service[' + key + '][count]"  multiple="" value="' + value['scratch_count'] + '" placeholder="Count"';
                             }  
                             else{
                                content += '<div class="col-md-3">' +
                                '<input type="number" class="form-control" name="service[' + key + '][count]"  multiple="" value="' + value['count'] + '" placeholder="Count"';
                             }
                            
                            if (value['service'] === '{{\App\Common\Variables::SERVICE_SMS}}') {
                                content += "readonly";
                            }
                            content += '/>';
                            if (value['service'] === '{{\App\Common\Variables::SERVICE_CRM}}') {
                                content += '<span class="pull-right">* Staff users allowed</span>';
                            } else if (value['service'] === '{{\App\Common\Variables::SERVICE_SMS}}') {
                                content += '<span class="pull-right">* SMS Available</span>';
                            } else if (value['service'] === '{{\App\Common\Variables::SERVICE_MISSEDCALL}}') {
                                content += '<span class="pull-right">* Virtual Numbers</span>';
                            }
                            content += '<input type="hidden" class="form-control" name="service[' + key + '][id]"  multiple="" value="' + value['id'] + '" placeholder="Id"/>' +
                                '</div>';

                            if (value['service'] === '{{\App\Common\Variables::SERVICE_SMS}}') {
                                content += '<div class="col-md-4">';
                                $.each(sms_count, function (index, val) {
                                    console.log(val);
                                    content +=
                                        '<div class="row">' +
                                        '<div class="form-group">' +
                                        '<label for="default-input" class="form-control-label">' + val['name'] + ' </label>' +
                                        '<input type="hidden" class="form-control" name="service[' + key + '][sms_route][' + index + '][id]" value="' + val['id'] + '" multiple=""/>' +
                                        '<input type="hidden" class="form-control" name="service[' + key + '][sms_route][' + index + '][name]" value="' + val['name'] + '" multiple=""/>' +
                                        '<input type="number" class="form-control" name="service[' + key + '][sms_route][' + index + '][count]" value="' + val['count'] + '"  placeholder="Count"/>' +
                                        '</div>' +
                                        '</div>';
                                });
                            }
                            content += '</div>' +
                                '</div>';
                            $('#service-container').append(content);
                        });
                    }
                })
                    .fail(function (err) {
                    })
                    .always(function (com) {
                        $('#plan-info-table').DataTable().ajax.reload(null, false);
                    });
            });

            $('#plan-info-table').on('click', '.plan-position', function (event) {
                event.preventDefault();
                var planId = $(this).attr('data-plan-id');
                var url = BASE_URL + '/admin/set-plan-as-first-position/' + planId;
                $.confirm({
                    title: 'Set Position',
                    content: 'Are you sure you want to  set this plan in first positions ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                    .done(function (res) {
                                        if (res.status === true) {
                                            $.alert({
                                                title: 'Success',
                                                type: 'green',
                                                content: res.msg,
                                            });
                                        } else {
                                            $.alert({
                                                title: 'Failed',
                                                type: 'red',
                                                content: res.msg,
                                            });
                                        }
                                    })
                                    .fail(function (err) {
                                    })
                                    .always(function (com) {
                                        $('#plan-info-table').DataTable().ajax.reload(null, false);
                                    });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            /** Add Additional rows to add services**/
            var max_fields = 10; //maximum input boxes allowed
            var x = 1; //initlal text box count
            $('.add_field_button').click(function (e) { //on add input button click
                e.preventDefault();
                if (x < max_fields) { //max input box allowed
                    x++; //text box increment
                    var content = '<div class="row">' +
                        '<div class="col-md-6">' +
                        '<label data-error="wrong" data-success="right" for="orangeForm-pass">Name</label>';
                    content +=
                        '<select class="form-control" name="service[' + x + '][name]">' +
                        '<option value="0">Select Service </option>';
                    @foreach(\App\Common\Variables::GETLEAD_SERVICES as $service)
                        content += '<option value="{{$service}}">{{$service}}</option>'
                    @endforeach
                        content +=
                        '</select>' +
                        '</div>' +
                        '<div class="col-md-3">' +
                        '<label data-error="wrong" data-success="right" for="orangeForm-pass">Duration </label>' +
                        '<input type="number" class="form-control"  name="service[' + x + '][duration]" required multiple="" value="" placeholder="Duration"/>' +
                        '</div>' +
                        '<div class="col-md-2">' +
                        '<label data-error="wrong" data-success="right" for="Count">Count</label>' +
                        '<input type="number" class="form-control" name="service[' + x + '][count]" required multiple="" value="" placeholder="Count"/>' +
                        '</div>' +
                        '<div class="col-md-1  mg-tp-40">' +
                        '<a href="#" class="remove_field text-danger">' +
                        '<i class="fa fa-minus-circle"></i></a>' +
                        '</div>' + '</div>';
                    $('.input_fields_wrap').append(content);
                }
            });
            $('.input_fields_wrap').on("click", ".remove_field", function () {
                $(this).closest(".row").remove();
            });
        });
    </script>
@endpush
