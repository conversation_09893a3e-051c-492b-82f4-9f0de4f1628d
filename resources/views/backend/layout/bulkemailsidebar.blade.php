<aside class="sidebar">
    <div class="aside-menu">
        <h5>Bulk Email</h5>
        <ul>
            <li class="{{ request()->is('user/email-dashboard') ? 'active' : '' }}">
                <a href="{{url('user/email-dashboard')}}"><i class="fa fa-tachometer" aria-hidden="true"></i>Dashboard</a>
            </li>
            <li>
                <a href="#"><i class="fa  fa-envelope-o" aria-hidden="true"></i>Bulk Email</a>
                <ul>
                    <li class="{{ request()->is('user/bulk-email') ? 'active' : '' }}">
                        <a href="{{url('/user/bulk-email')}}">
                            <i class="fa fa-angle-right" aria-hidden="true"></i>
                            Compose Email
                        </a>
                    </li>

                   {{--  <li class="{{ request()->is('user/dynamic-messaging') ? 'active' : '' }}">
                        <a href="{{url('/user/dynamic-messaging')}}">
                            <i class="fa fa-angle-right" aria-hidden="true"></i>
                            Dynamic Messaging
                        </a>
                    </li> --}}
                    <li class="{{ request()->is('user/email-senderid') ? 'active' : '' }}">
                        <a href="{{url('/user/email-senderid')}}">
                            <i class="fa fa-angle-right" aria-hidden="true"></i>
                            Senderid
                        </a>
                    </li>

                  {{--   <li class="{{ request()->is('user/apitemplate') ? 'active' : '' }}">
                        <a href="{{url('/user/apitemplate')}}">
                            <i class="fa fa-angle-right" aria-hidden="true"></i>
                            Api Template
                        </a>
                    </li>  --}}
                </ul>
            </li>

            {{--  <li class="{{ request()->is('user/smstemplate') ? 'active' : '' }}">
                 <a href="{{url('/user/smstemplate')}}"><i class="fa fa-envelope-o" aria-hidden="true"></i>SMS Template</a>
             </li>  --}}

            {{-- <li class="{{ request()->is('user/subscription-message') ? 'active' : '' }}">
                <a href="{{url('/user/subscription-message')}}"><i class="fa fa-address-book-o" aria-hidden="true"></i>Subscription Message</a>
            </li>  --}}
            {{-- <li class="{{ request()->is('user/user-credit') ? 'active' : '' }}">
                <a href="{{url('/user/user-credit')}}"><i class="fa fa-address-book-o" aria-hidden="true"></i>Request Credit </a>
            </li> --}}
           {{--  <li>
                <a href="#"><i class="fa  fa-group" aria-hidden="true"></i>Groups</a>
                <ul>
                    <li class="{{ request()->is('user/group-sms-users') ? 'active' : '' }}">
                        <a href="{{url('/user/group-sms-users')}}">
                            <i class="fa  fa-group" aria-hidden="true"></i>Group</a>
                    </li>
                    <li class="{{ request()->is('user/sms-groups') ? 'active' : '' }}">
                        <a href="{{url('/user/sms-groups')}}">
                            <i class="fa fa-street-view" aria-hidden="true"></i>
                            View Groups
                        </a>
                    </li>
                </ul>
            </li> --}}


            <li>
                <a href="#"><i class="fa  fa-envelope-o" aria-hidden="true"></i>Sent Email</a>
                <ul>
                    <li class="{{ request()->is('user/email-history') ? 'active' : '' }}">
                        <a href="{{url('/user/email-history')}}">
                            <i class="fa fa-street-view" aria-hidden="true"></i>Email
                        </a>
                    </li>
                   {{--  <li class="{{ request()->is('user/dynamic-sms-history') ? 'active' : '' }}">
                        <a href="{{url('/user/dynamic-sms-history')}}">
                            <i class="fa fa-street-view" aria-hidden="true"></i>Dynamic SMS
                        </a>
                    </li> --}}
                </ul>
            </li>
            <li>
                <a href="#"><i class="fa fa-sticky-note" aria-hidden="true"></i>Developer API's</a>
                <ul>
                    <li class="{{ request()->is('user/email-api') ? 'active' : '' }}">
                        <a href="{{url('/user/email-api')}}">
                            <i class="fa fa-street-view" aria-hidden="true"></i>Email API
                        </a>
                    </li>

                </ul>
            </li>
            <li class="{{ request()->is('user/email-report') ? 'active' : '' }}">
                <a href="{{url('user/email-report')}}"><i class="fa fa-bars" aria-hidden="true"></i>Report</a>
            </li>

        </ul>
        <br>
        <h5>My Account Credit Balance</h5>
        <div class="table">
            <table>
                <thead>
                <tr>
                    <th class="text-left">#</th>
                    <th class="text-left">Bal</th>
                </tr>
                </thead>
                <tbody class="table-hover">
                @foreach(\App\Subscription\SmsCount::smsCreditBalance() as $balance)
                    <tr>
                        <td class="text-left">{{$balance->vchr_sms_route}}</td>
                        <td class="text-left">
                            <div id="h_c">{{$balance->total_count + $balance->credit- $balance->used_sms_count}}</div>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    </div>
</aside>