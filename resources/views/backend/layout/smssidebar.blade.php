<nav class="sidebar">
   <div class="aside-menu">
   <!-- <h5>Leads</h5> -->
   <div>
      <i class='bx bx-chevron-right toggle' id="togglemenu"></i>
   </div>
   <div class="menu-bar">
      <div class="menu">
         <!-- <h5>SMS</h5> -->
         <ul class="menu-links">
            <li class="nav-link new-logo">
               <a href="{{url('user/crm-dashboard')}}">
                  <div class="logo-new" >
                     <img  src="{{url('backend/images/GETLEAD.svg')}}" alt="GetLead"> 
                  </div>
                  <div class="icon-logo">
                     <img  src="{{url('backend/images/getlead-logo.svg')}}" alt="GetLead"> 
                  </div>
               </a>
            </li>
            <li class="{{ request()->is('user/sms-dashboard') ? 'active' : '' }} nav-link">
               <a href="{{url('user/sms-dashboard')}}"><i class="fa fa-tachometer" aria-hidden="true"></i>
               <span class="text nav-text">Dashboard</span>
               </a>
            </li>
            <li class="{{ request()->is('user/smstemplates') ? 'active' : '' }} nav-link">
               <a href="{{url('/user/smstemplates')}}"><i  class="fa fa-commenting-o"></i>
               <span class="text nav-text">SMS
               Template</span>
               </a>
            </li>
            <li>
               <a href="#"><i class="fa  fa-envelope-o" aria-hidden="true"></i>
               <span class="text nav-text">Messaging</span>
               </a>
               <ul class="sub-menu">
                  <li class="{{ request()->is('user/bulk-push') ? 'active' : '' }} nav-link">
                     <a href="{{url('/user/bulk-push')}}">
                     <i class="fa fa-angle-right" aria-hidden="true"></i>
                     <span class="text nav-text">Compose SMS</span>
                     </a>
                  </li>
                  <li class="{{ request()->is('user/dynamic-messaging') ? 'active' : '' }} nav-link">
                     <a href="{{url('/user/dynamic-messaging')}}">
                     <i class="fa fa-angle-right" aria-hidden="true"></i>
                     <span class="text nav-text">Dynamic Messaging</span>
                     </a>
                  </li>
                  <li class="{{ request()->is('user/senderid') ? 'active' : '' }} nav-link">
                     <a href="{{url('/user/senderid')}}">
                     <i class="fa fa-angle-right" aria-hidden="true"></i>
                     <span class="text nav-text">Senderid</span>
                     </a>
                  </li>
                  <li class="{{ request()->is('user/apitemplate') ? 'active' : '' }} nav-link">
                     <a href="{{url('/user/apitemplate')}}">
                     <i class="fa fa-angle-right" aria-hidden="true"></i>
                     <span class="text nav-text">Api Template</span>
                     </a>
                  </li>
               </ul>
            </li>
            {{--  
            <li class="{{ request()->is('user/smstemplate') ? 'active' : '' }} nav-link">
               <a href="{{url('/user/smstemplate')}}"><i class="fa fa-envelope-o" aria-hidden="true"></i>
               <span class="text nav-text">SMS Template</span>
               </a>
            </li>
            --}}
            {{-- 
            <li class="{{ request()->is('user/subscription-message') ? 'active' : '' }} nav-link">
               <a href="{{url('/user/subscription-message')}}"><i class="fa fa-address-book-o" aria-hidden="true"></i>
               <span class="text nav-text">Subscription Message</span>
               </a>
            </li>
            --}}
            {{-- 
            <li class="{{ request()->is('user/user-credit') ? 'active' : '' }} nav-link">
               <a href="{{url('/user/user-credit')}}"><i class="fa fa-address-book-o" aria-hidden="true"></i>
               <span class="text nav-text">Request Credit </span>
               </a>
            </li>
            --}}
            <li>
               <a href="#"><i class="fa fa-paper-plane" aria-hidden="true"></i>
               <span class="text nav-text">Sent History</span>
               </a>
               <ul class="sub-menu">
                  <li class="{{ request()->is('user/sms-history') ? 'active' : '' }} nav-link">
                     <a href="{{url('/user/sms-history')}}">
                     <i class="fa fa-street-view" aria-hidden="true"></i>
                     <span class="text nav-text">SMS</span>
                     </a>
                  </li>
                  <li class="{{ request()->is('user/dynamic-sms-history') ? 'active' : '' }} nav-link">
                     <a href="{{url('/user/dynamic-sms-history')}}">
                     <i class="fa fa-street-view" aria-hidden="true"></i>
                     <span class="text nav-text">Dynamic SMS</span>
                     </a>
                  </li>
                  <li class="{{ request()->is('user/sms-report') ? 'active' : '' }} nav-link">
                     <a href="{{url('user/sms-report')}}">
                     <i class="fa fa-street-view" aria-hidden="true"></i>
                     <span class="text nav-text">Download Report</span>
                     </a>
                  </li>
               </ul>
            </li>
            @if(Auth::user()->int_role_id==\App\User::USERS || Auth::user()->is_co_admin==1) 
               <li>
                  <a href="#"><i class="fa  fa-group" aria-hidden="true"></i>
                  <span class="text nav-text">Groups</span></a>
                  <ul class="sub-menu">
                     <li class="{{ request()->is('user/group-sms-users') ? 'active' : '' }} nav-link">
                        <a href="{{url('/user/group-sms-users')}}">
                        <i class="fa  fa-group" aria-hidden="true"></i>
                        <span class="text nav-text">Group</span>
                        </a>
                     </li>
                     <li class="{{ request()->is('user/sms-groups') ? 'active' : '' }}">
                        <a href="{{url('/user/sms-groups')}}">
                        <i class="fa fa-street-view" aria-hidden="true"></i>
                        <span class="text nav-text">View Groups</span>
                        </a>
                     </li>
                  </ul>
               </li>
            @endif
            <li>
               <a href="#">
                    <i class="fa fa-sticky-note" aria-hidden="true"></i>
                    <span class="text nav-text">Developer API's</span>
               </a>
               <ul class="sub-menu">
                  <li class="{{ request()->is('user/sms-api') ? 'active' : '' }} nav-link">
                     <a href="{{url('/user/sms-api')}}">
                     <i class="fa fa-street-view" aria-hidden="true"></i>
                     <span class="text nav-text">SMS</span>
                     </a>
                  </li>
                  <li class="{{ request()->is('user/gl-otp') ? 'active' : '' }} nav-link">
                     <a href="{{url('/user/gl-otp')}}">
                     <i class="fa fa-street-view" aria-hidden="true"></i>
                     <span class="text nav-text">OTP</span>
                     </a>
                  </li>
               </ul>
            </li>
            @if(Auth::user()->int_role_id==\App\User::USERS || Auth::user()->is_co_admin==1) 
            <li class="nav-link">
               <a href="#"><i class="fa fa-cogs" aria-hidden="true"></i>
               <span class="text nav-text">DLT Settings</span>
               </a>
               <ul class="sub-menu">
                  <li class="{{ request()->is('user/sms-dlt-entities') ? 'active' : '' }}">
                     <a href="{{url('/user/sms-dlt-entities')}}">
                     <i class="fa fa-street-view" aria-hidden="true"></i>
                     <span class="text nav-text">Entities</span>
                     </a>
                  </li>
                  <li class="{{ request()->is('user/sms-dlt-headers') ? 'active' : '' }}">
                     <a href="{{url('/user/sms-dlt-headers')}}">
                     <i class="fa fa-street-view" aria-hidden="true"></i>
                     <span class="text nav-text">Headers</span>
                     </a>
                  </li>
                  <li class="{{ request()->is('user/sms-dlt-templates') ? 'active' : '' }}">
                     <a href="{{url('/user/sms-dlt-templates')}}">
                     <i class="fa fa-street-view" aria-hidden="true"></i>
                     <span class="text nav-text">Templates</span>
                     </a>
                  </li>
               </ul>
            </li>
            @endif
         </ul>
         <br>
         <h5>My Account Credit Balance</h5>
         <div class="otp-table table text nav-text">
            <table>
               <thead>
                  <tr>
                     <th class="text-left">#</th>
                     <th class="text-left">Bal</th>
                  </tr>
               </thead>
               <tbody class="table-hover">
                  @foreach(\App\Subscription\SmsCount::smsCreditBalance() as $balance)
                  <tr>
                     <td class="text-left">{{$balance->vchr_sms_route}}</td>
                     <td class="text-left">
                        <div id="h_c">{{$balance->total_count + $balance->credit- $balance->used_sms_count}}</div>
                     </td>
                  </tr>
                  @endforeach
               </tbody>
            </table>
         </div>
      </div>
   </div>
</nav>