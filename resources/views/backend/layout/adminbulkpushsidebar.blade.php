<aside class="sidebar">
    <div class="aside-menu">
       <h5>SMS</h5>
       <ul>
          <li>
             <a href="#"><i class="fa fa-cogs" aria-hidden="true"></i>Settings</a>
             <ul>
                <li class="{{ request()->is('admin/sms-domain') ? 'active' : '' }}">
                   <a href="{{url('admin/sms-domain')}}"><i class="fa fa-credit-card" aria-hidden="true"></i>Domain</a>
                </li>
                <li class="{{ request()->is('admin/sms-api-credentials') ? 'active' : '' }}">
                   <a href="#">
                   <i class="fa fa-credit-card" aria-hidden="true"></i>Credentials</a>
                   <ul>
                      <li class="{{ request()->is('admin/show-sms-api-routes') ? 'active' : '' }}">
                         <a href="{{url('admin/show-sms-api-routes')}}"><i class="fa fa-credit-card" aria-hidden="true"></i>Manage Route</a>
                      </li>
                      @foreach(\App\Common\Variables::getSmsRoutes($otp=1) as $route)
                      <li class="{{ request()->is('admin/sms-api-credentials') ? 'active' : '' }}">
                         <a href="{{url('admin/sms-api-credentials?priority='.$route->priority)}}">
                         <i class="fa fa-credit-card" aria-hidden="true"></i>{{$route->vchr_sms_route}}
                         </a>
                      </li>
                      @endforeach
                   </ul>
                </li>
                <li class="{{ request()->is('admin/bulk-sms-credentials') ? 'active' : '' }}" hidden>
                   <a href="#">
                   <i class="fa fa-credit-card" aria-hidden="true"></i>Bulk SMS Credentials</a>
                   <ul>
                      <li class="{{ request()->is('admin/show-bulksms-api-routes') ? 'active' : '' }}">
                         <a href="{{url('admin/show-bulksms-api-routes')}}"><i class="fa fa-credit-card" aria-hidden="true"></i>Manage Route</a>
                      </li>
                      @foreach(\App\Common\Variables::getSmsRoutes($otp=0) as $route)
                      <li class="{{ request()->is('admin/bulk-sms-credentials') ? 'active' : '' }}">
                         <a href="{{url('admin/bulk-sms-credentials?priority='.$route->priority)}}">
                         <i class="fa fa-credit-card" aria-hidden="true"></i>{{$route->vchr_sms_route}}
                         </a>
                      </li>
                      @endforeach
                   </ul>
                </li>
             </ul>
          </li>
          <li>
             <a href="#"><i class="fa  fa-envelope-o" aria-hidden="true"></i>SMS History</a>
             <ul>
                <li class="{{ request()->is('admin/sms-history') ? 'active' : '' }}">
                   <a href="{{url('admin/sms-history')}}">
                   <i class="fa fa-credit-card" aria-hidden="true"></i>SMS</a>
                </li>
                <li class="{{ request()->is('admin/dynamic-sms-history') ? 'active' : '' }}">
                   <a href="{{url('admin/dynamic-sms-history')}}">
                   <i class="fa fa-credit-card" aria-hidden="true"></i>Dynamic SMS</a>
                </li>
                {{-- 
                <li class="{{ request()->is('admin/user-sms-credit') ? 'active' : '' }}">
                   <a href="{{url('admin/user-sms-credit')}}">
                   <i class="fa fa-credit-card" aria-hidden="true"></i>User SMS Credits</a>
                </li>
                --}}
             </ul>
          </li>
          <li class="{{ request()->is('admin/sms-report') ? 'active' : '' }}">
             <a href="{{url('admin/sms-report')}}">
             <i class="fa fa-list" aria-hidden="true"></i>
             Report</a>
          </li>
          <li>
             <a href="#"><i class="fa  fa-envelope-o" aria-hidden="true"></i>SMS Panel</a>
             <ul>
                <li class="{{ request()->is('admin/alert-box-info') ? 'active' : '' }}">
                   <a href="{{url('admin/alert-box-info')}}">
                   <i class="fa fa-credit-card" aria-hidden="true"></i>AlertBox</a>
                </li>
                <li class="{{ request()->is('admin/text-local-info') ? 'active' : '' }}">
                   <a href="{{url('admin/text-local-info')}}">
                   <i class="fa fa-credit-card" aria-hidden="true"></i>TextLocal</a>
                </li>
                {{-- 
                <li class="{{ request()->is('admin/user-sms-credit') ? 'active' : '' }}">
                   <a href="{{url('admin/user-sms-credit')}}">
                   <i class="fa fa-credit-card" aria-hidden="true"></i>User SMS Credits</a>
                </li>
                --}}
             </ul>
          </li>
          {{-- 
          <li class="{{ request()->is('admin/sms-pricing') ? 'active' : '' }}"><a href="{{url('/admin/sms-pricing')}}"><i class="fa fa-tachometer" aria-hidden="true"></i>Sms Pricing</a></li>
          <li class="{{ request()->is('admin/payment-status') ? 'active' : '' }}"><a href="{{url('/admin/payment-status')}}"><i class="fa fa-tachometer" aria-hidden="true"></i>Payment Status</a></li>
          <li class="{{ request()->is('admin/payment-history') ? 'active' : '' }}"><a href="{{url('/admin/payment-history')}}"><i class="fa fa-tachometer" aria-hidden="true"></i>Payment History</a></li>
          --}}
       </ul>
    </div>
 </aside>