<nav class="sidebar single-sidebar">
   <div class="aside-menu">
      <div class="menu-bar menu-bar-2">
         <div class="menu">
            <ul class="menu-links single-col">
               <div class="home-icon">
                  <a class="home-nav">
                     <img style="width:40px; object-fit: contain;" src="{{url('backend/images/images-V2/gl-logo.svg')}}" alt="GetLead"> 
                  </a>

               </div>
               <li class="nav-link pb-3" data-toggle="tooltip" data-placement="right" title="Dashboard">
                  <a href="{{route('crm.dashboard')}}" >
                     <img   src="{{url('backend/images/images-V2/home-icon.svg')}}" alt="GetLead"> 
                  </a>
               </li>
               <li class="{{ request()->is('user/sms-dashboard') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="Dashboard">
                  <a href="{{url('user/sms-dashboard')}}"><i class="fa fa-tachometer" aria-hidden="true"></i>
                  <span class="text nav-text">Dashboard</span>
                  </a>
               </li>
               <li class="{{ request()->is('user/smstemplates') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="SMS Template">
                  <a href="{{url('/user/smstemplates')}}"><i  class="fa fa-commenting-o"></i>
                  <span class="text nav-text">SMS Template</span>
                  </a>
               </li>
               <li class="{{ request()->is('user/bulk-push') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="Messaging">
                  <a href="{{url('/user/bulk-push')}}"><i class="fa  fa-envelope-o" aria-hidden="true"></i>
                  <span class="text nav-text">Messaging</span>
                  </a>
                  {{-- <ul class="sub-menu">
                     <li >
                        <a href="{{url('/user/bulk-push')}}">
                        <i class="fa fa-angle-right" aria-hidden="true"></i>
                        <span class="text nav-text">Compose SMS</span>
                        </a>
                     </li>
                     <li class="{{ request()->is('user/dynamic-messaging') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="Dashboard">
                        <a href="{{url('/user/dynamic-messaging')}}">
                        <i class="fa fa-angle-right" aria-hidden="true"></i>
                        <span class="text nav-text">Dynamic Messaging</span>
                        </a>
                     </li>
                     <li class="{{ request()->is('user/senderid') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="Dashboard">
                        <a href="{{url('/user/senderid')}}">
                        <i class="fa fa-angle-right" aria-hidden="true"></i>
                        <span class="text nav-text">Senderid</span>
                        </a>
                     </li>
                     <li class="{{ request()->is('user/apitemplate') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="Dashboard">
                        <a href="{{url('/user/apitemplate')}}">
                        <i class="fa fa-angle-right" aria-hidden="true"></i>
                        <span class="text nav-text">Api Template</span>
                        </a>
                     </li>
                  </ul> --}}
               </li>
               {{--  
               <li class="{{ request()->is('user/smstemplate') ? 'active' : '' }} nav-link">
                  <a href="{{url('/user/smstemplate')}}"><i class="fa fa-envelope-o" aria-hidden="true"></i>
                  <span class="text nav-text">SMS Template</span>
                  </a>
               </li>
               --}}
               {{-- 
               <li class="{{ request()->is('user/subscription-message') ? 'active' : '' }} nav-link">
                  <a href="{{url('/user/subscription-message')}}"><i class="fa fa-address-book-o" aria-hidden="true"></i>
                  <span class="text nav-text">Subscription Message</span>
                  </a>
               </li>
               --}}
               {{-- 
               <li class="{{ request()->is('user/user-credit') ? 'active' : '' }} nav-link">
                  <a href="{{url('/user/user-credit')}}"><i class="fa fa-address-book-o" aria-hidden="true"></i>
                  <span class="text nav-text">Request Credit </span>
                  </a>
               </li>
               --}}
               <li class="{{ request()->is('user/sms-history') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="Sent History">
                  <a href="{{url('/user/sms-history')}}"><i class="fa fa-paper-plane" aria-hidden="true"></i>
                  <span class="text nav-text">Sent History</span>
                  </a>
                  {{-- <ul class="sub-menu">
                     <li >
                        <a href="{{url('/user/sms-history')}}">
                        <i class="fa fa-street-view" aria-hidden="true"></i>
                        <span class="text nav-text">SMS</span>
                        </a>
                     </li>
                     <li class="{{ request()->is('user/dynamic-sms-history') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="Dashboard">
                        <a href="{{url('/user/dynamic-sms-history')}}">
                        <i class="fa fa-street-view" aria-hidden="true"></i>
                        <span class="text nav-text">Dynamic SMS</span>
                        </a>
                     </li>
                     <li class="{{ request()->is('user/sms-report') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="Dashboard">
                        <a href="{{url('user/sms-report')}}">
                        <i class="fa fa-street-view" aria-hidden="true"></i>
                        <span class="text nav-text">Download Report</span>
                        </a>
                     </li>
                  </ul> --}}
               </li>
               @if(Auth::user()->int_role_id==\App\User::USERS || Auth::user()->is_co_admin==1) 
                  <li class="{{ request()->is('user/group-sms-users') ? 'active' : '' }} nav-link"  data-toggle="tooltip" data-placement="right" title="Groups">
                     <a href="{{url('/user/group-sms-users')}}"><i class="fa  fa-group" aria-hidden="true"></i>
                     <span class="text nav-text">Groups</span></a>
                     {{-- <ul class="sub-menu">
                        <li class="{{ request()->is('user/group-sms-users') ? 'active' : '' }} nav-link"> 
                           <a href="{{url('/user/group-sms-users')}}">
                           <i class="fa  fa-group" aria-hidden="true"></i>
                           <span class="text nav-text">Group</span>
                           </a>
                        </li>
                        <li class="{{ request()->is('user/sms-groups') ? 'active' : '' }}">
                           <a href="{{url('/user/sms-groups')}}">
                           <i class="fa fa-street-view" aria-hidden="true"></i>
                           <span class="text nav-text">View Groups</span>
                           </a>
                        </li>
                     </ul> --}}
                  </li>
               @endif
               <li class="{{ request()->is('user/sms-api') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="Developer API's">
                  <a href="{{url('/user/sms-api')}}">
                       <i class="fa fa-sticky-note" aria-hidden="true"></i>
                       <span class="text nav-text">Developer API's</span>
                  </a>
                  {{-- <ul class="sub-menu">
                     <li class="{{ request()->is('user/sms-api') ? 'active' : '' }} nav-link">
                        <a href="{{url('/user/sms-api')}}">
                        <i class="fa fa-street-view" aria-hidden="true"></i>
                        <span class="text nav-text">SMS</span>
                        </a>
                     </li>
                     <li class="{{ request()->is('user/gl-otp') ? 'active' : '' }} nav-link">
                        <a href="{{url('/user/gl-otp')}}">
                        <i class="fa fa-street-view" aria-hidden="true"></i>
                        <span class="text nav-text">OTP</span>
                        </a>
                     </li>
                  </ul> --}}
               </li>
               @if(Auth::user()->int_role_id==\App\User::USERS || Auth::user()->is_co_admin==1) 
               <li class="{{ request()->is('user/sms-dlt-entities') ? 'active' : '' }} nav-link" data-toggle="tooltip" data-placement="right" title="DLT Settings">
                  <a href="{{url('/user/sms-dlt-entities')}}"><i class="fa fa-cogs" aria-hidden="true"></i>
                  <span class="text nav-text">DLT Settings</span>
                  </a>
               </li>
               @endif



            </ul>
         </div>
      </div>
   </div>
</nav>