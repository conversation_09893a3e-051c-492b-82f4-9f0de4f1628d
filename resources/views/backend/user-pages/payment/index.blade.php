@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Buy Sms</h5>
                </div>
                <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                {{-- <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3 ks-izi-modal-trigger"  data-target="#ks-izi-modal-large1" id="addEnquiry" name="button" title="Click here to edit subscription messages">Add Enquiry</button> --}}
                                
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.smssidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <section class="pricing-check-sec">
                    <div class="container">
                        <div class="row d-flex align-items-center justify-content-center">
                            <div class="col-lg-7 col-md-11">
                              <form action="{{url('/user/confirm-pay')}}" method="POST">
                                <input type="hidden" name="_token" id="token" value="{{ csrf_token() }}">
                                <div class="price-select">
                                    <div class="row">
                                        <div class="col-sm-6 country">
                                        <div class="form-control">India
                                        </div>
                                        </div>
                                        <div class="col-sm-6 sms-item-cont">
                                            <select class="sms-items" id="route" name="route" required onchange="price()">
                                             <option value="">Select Route</option>
                                             @foreach($route as $key => $route1)
                                             <option value="{{ $route1->pk_int_sms_route_id }}">{{ $route1->vchr_sms_route }}</option>
                                             @endforeach
                                           </select>
                                            <div class="select-arrow"></div>
                                        </div>
                                    </div>
                                    <div  class="row">
                                        <div class="col-sm-6 volume">                               
                                        <div class="form-group">
        <!--                                    <label>Volume</label>   -->
                                            <input class="form-control" id="volume" type="number" name="smsvolume" placeholder="Volume" required="required" onkeyup="v_volume()">
                                            <input class="form-control" id="rate_sms" type="hidden" name="smsrate" placeholder="Rate/sms">
                                        </div>
                                        </div>
                                        
                                        <div class="col-sm-6 sms-item-price">
                                            <div>
                                                <span class="cost">0.00/</span>
                                                <span class="sms">SMS</span>
                                                <select class="sms-rate">
                                                    <option>USD</option>
                                                    <option>AED</option>
                                                    <option selected="selected">INR</option>
                                                    <option>AUD</option>
                                                    <option>NZD</option>
                                                </select>
                                                <div class="select-arrow"></div>
                                                <br>
                                                <p class="clearfix col-xs-12 text-left">*Include of taxes</p>
                                            </div>
                                            
                                        </div>
                                    </div>
                                    <div  class="row">
                                        <div class="col-sm-6">                                  
                                        <div class="form-group">
                                            <label>Total Amount</label> 
                                            <input class="form-control box-shadow-n " type="text-area" id="total_amount" name="smsamount" readonly="">
                                        </div>
                                        </div>
                                        <div class="col-lg-4 col-md-6 col-sm-6">
                                          <input type="hidden" value="{{ $userid }}" name="uid" id="uid">
                                            <button type="submit" id="buy" class="demo-btn sms-btn transclass" onclick="smsbuy()">Buy Now </button>
                                      <a href="tel:+************" id="call" class=" demo-btn sms-btn transclass">Call Us</a>
                                        </div>
                                      </div>
                                        
                                    </div>
                                </div>
                              </form>
                            </div>
                    </div>
                </section>
            </div>
        </div>
    </main>
 <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.1.1/jquery.min.js"></script> 
<script type="text/javascript">
  $("#call").hide();
                function getRatesms(route,volume)
                {
                    jQuery.ajax({
                        type: "POST",
                        dataType: "json",
                        url: 'select-sms-route',
                        //data: {states:states},
                        data: {'route': route,'volume':volume, "_token": $('#token').val()},
                        success: function(data) 
                        {
                            rate_sms=(data['rate']);
                            $(".cost").html(rate_sms+'/');
                            $("#rate_sms").val(rate_sms);
                            var amt=volume*rate_sms;
                            $("#total_amount").val(amt);
                            //alert(amt);
                            if(volume>1000000)
                            {
                            $("#buy").hide();
                            $("#call").show();
                            }
                            else
                            {
                            $("#buy").show();
                            $("#call").hide();
                            }
                        }
                    });
                }
                // $('#route').change(function()
                function price()
                {
                    // alert("hello");
                    
                    var route=$('#route').val();
                    var volume1= $("#volume").val();
                    //alert(volume1);
                    if(volume1=="")
                    {
                     var  volume=0;
                 }
                 else
                 {
                     var volume= parseFloat($("#volume").val());  
                 }

                 var rate_sms=$('#rate_sms').val();
                 getRatesms(route,volume);
                }

             // });

                // $("#volume").keyup(function()
                function v_volume()
                {
                    // alert("hello");
                    var route=$('#route').val();
                    var volume1= $("#volume").val();
                 //alert(volume1);
                 if(volume1=="")
                 {
                   var  volume=0;
               }
               else
               {
                   var volume= parseFloat($("#volume").val());  
               }
               var rate_sms=$('#rate_sms').val();
               getRatesms(route,volume);
           }
           // });
        function olume()
        {
             var amount = document.getElementById('total_amount').value;
        alert(amount);
           // var amount = document.getElementById('total_amount').value;
           // document.getElementsByClassName('amount-figure').innerHTML = amount;
           // alert($amount);
           // alert("hello");
          // window.location = "http://getleadcrm.local/login";
        }
       </script>
@endsection 
@push('footer.script')
   
@endpush
