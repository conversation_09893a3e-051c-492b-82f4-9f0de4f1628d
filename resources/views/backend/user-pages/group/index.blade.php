@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>

    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Sms</h5>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                {{-- <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3 ks-izi-modal-trigger"  data-target="#ks-izi-modal-large1" id="addEnquiry" name="button" title="Click here to edit subscription messages">Add Enquiry</button> --}}
                                
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.group-sidebar')
            <div class="content-section ">
                <!--  /Your content goes here/ -->
                <section class="bulk-push-sec">
                    <div class="row">
                        <div class="col-lg-12">
                            <section class="panel">
                                <header class="panel-heading">
                                   CREATE NEW GROUP WITH CONTACTS                       
                                </header>
                                <div class="panel-body">
                                    <div class="flash-message alert alert-success alert-block">
                                        <button type="button" class="close" data-dismiss="alert">×</button> 
                                        <strong></strong>
                                    </div>
                                    <div class=" form">
                                            <form class="cmxform form-horizontal" id="groupAdd" method="post" enctype="multipart/form-data">
                                                <div class="form-group ">
                                                    <label for="sender" class="control-label col-lg-3">Group Name (required)</label>
                                                    <div class="col-lg-6">
                                                        <input type="text" name="groupname" id="groupname" class="form-control">
                                                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                                    </div>
                                                    
                                                </div>
                                                <div class="form-group">
                                                    <label for="sender" class="control-label col-lg-3">Group Description</label>
                                                    <div class="col-lg-6">
                                                        <textarea name="groupdescription" id="groupdescription" class="form-control"></textarea>
                                                    </div>
                                                    
                                                </div>
                                                <div class="form-group ">
                                                    <label for="contacts" class="control-label col-lg-3">Contact Type (required)</label>
                                                    <div class="col-lg-6">
                                                       <select name="type" id="type" class="form-control m-bot15"  required>
                                                            <option value="1">Manually Enter Number(s) & Name(s)</option>
                                                            <option  value="2">Using a CSV file upload</option>
                                                        </select><br/>
                                                    </div>                                               
                                                </div>
                                                 <div class="form-group" id="contactsLabel">
                                                    <label for="sender" class="control-label col-lg-3">Contacts (required)1/line</label>
                                                    <div class="col-lg-6">
                                                        <textarea name="contacts" id="contacts" class="form-control"></textarea>
                                                    </div>
                                                    
                                                </div>
                                                <div class="form-group" id="nameLabel">
                                                    <label for="sender" class="control-label col-lg-3">Name(optional)1/line</label>
                                                    <div class="col-lg-6">
                                                        <textarea name="name" id="name" class="form-control"></textarea>
                                                    </div>
                                                    
                                                </div>
                                                      <div id="csvfile" class="form-group ">
                                                    <label for="contacts" class="control-label col-lg-3">CSV File</label>
                                                    <div class="col-lg-6">
                                                       <button type="button" id="import_num" class="btn btn-secondary popovers upload-btn" data-placement="top" data-trigger="hover"><i class="fa fa-upload"></i><input type="file" id="myfile" name="myfile" /></button>
                                                       <a  id="download" href="{{url('files/group.csv')}}"  class="btn btn-info popovers upload-btn" data-original-title="Import numbers"
                                                              data-content="Download  sample .csv file for import numbers" data-placement="top" data-trigger="hover"><i class="fa fa-download"></i></a>
                                                    </div>                                               
                                                </div>
                                               <div class="form-group">
                                                    <div class="col-lg-6 mrgn-lft-25">
                                                        <button class="main-round-btn btn-primary" id="btn_send_sms">Create Group</button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>            
                        <!-- page end-->
                    </section>
            </div>
        </div>
    </main>
@endsection 
@push('footer.script')
<script type="text/javascript">
    $('.popovers').popover();
</script>
 <script type="text/javascript">
     $(document).ready(function() {
         $("#csvfile").hide();
         $(".flash-message").hide();
            $("#type").change(function()
                { 
                    var type=$("#type").val();
                    if(type==1)
                    {
                        $("#csvfile").hide();
                        $("#nameLabel").show();
                        $("#contactsLabel").show();
                    }
                    else
                    {
                        $("#nameLabel").hide();
                        $("#contactsLabel").hide();
                        $("#csvfile").show();
                    }
                });
     });

     $(document).on('submit', '#groupAdd', function(event) {
        event.preventDefault();
        var type=$("#type").val();
        var contacts=$("#contacts").val();
        var contacts=$("#contacts").val();
        var file=$("#myfile").val();
        var groupname=$("#groupname").val();
        if(type==1)
        {
            $("#csvfile").hide();
            $("#nameLabel").show();
            $("#contactsLabel").show();
            if(groupname=="")
            {
                $(".flash-message").show();
                $('div.flash-message').html('Please Enter Group Name');
                $('div.flash-message').delay(6000).fadeOut();
                return false;
            }
            else if(contacts=="")
            {
                $(".flash-message").show();
                $('div.flash-message').html('Please Enter Contacts');
                $('div.flash-message').delay(6000).fadeOut();
                return false;
            }
            else
            {
                $.ajax({
                        url:  '{{ url('user/assign-group-manually') }}',
                        type: 'POST',
                        dataType: 'JSON',
                        data:  new FormData(this),
                        contentType: false,
                        processData:false,
                    })
                .done(function(res) {
                    if(res.status == 'success'){
                         $("#groupAdd")[0].reset();
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
            }
            
        }
        else
        {
            $("#nameLabel").hide();
            $("#contactsLabel").hide();
            $("#csvfile").show();
            var ext = $('#myfile').val().split('.').pop().toLowerCase();
            if(groupname=="")
            {
                $(".flash-message").show();
                $('div.flash-message').html('Please Enter Group Name');
                $('div.flash-message').delay(6000).fadeOut();
                return false;
            }
            else if(file=="")
            {
                $(".flash-message").show();
                $('div.flash-message').html('Please Upload CSV Files');
                $('div.flash-message').delay(6000).fadeOut();
                return false;
            }
            else if($.inArray(ext, ['csv']) == -1) {
                $(".flash-message").show();
                $('div.flash-message').html('Only CSV Files are Allowed to Upload');
                $('div.flash-message').delay(6000).fadeOut();
                return false;
            }
            else
            {
                  $.ajax({
                        url:  '{{ url('user/assign-group-upload') }}',
                        type: 'POST',
                        dataType: 'JSON',
                        data:  new FormData(this),
                        contentType: false,
                        processData:false,
                    })
                .done(function(res) {
                    if(res.status == 'success'){
                         $("#groupAdd")[0].reset();
                         $("#csvfile").hide();
                        $("#nameLabel").show();
                        $("#contactsLabel").show();
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
            }
        }
     });
 </script>   
@endpush
