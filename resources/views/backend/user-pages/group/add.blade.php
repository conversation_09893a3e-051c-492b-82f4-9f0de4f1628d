@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>

    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="d-flex row-wrap align-items-center">
                    <div class="add-y-team">
                        <div class="dashboard-row">
                              <div class="y-team-header y-team-header-V2">
                                 <a href="{{ url()->previous() }}">
                                    
                                       <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                          <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z" fill="#4D5459"/>
                                       </svg>
                                       SMS
                                   
                                 </a>
                              </div>
                        </div>
                    </div>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                {{-- <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3 ks-izi-modal-trigger"  data-target="#ks-izi-modal-large1" id="addEnquiry" name="button" title="Click here to edit subscription messages">Add Enquiry</button> --}}
                                
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.group-sidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <section class="bulk-push-sec">
                    <div class="row">
                        <div class="col-lg-12">
                            <section class="panel">
                                <header class="panel-heading">
                                   ADD MORE CONTACTS ( GROUP NAME : {{ $group->vchr_name }} )                      
                                </header>
                                <div class="panel-body">
                                    <div class="flash-message alert alert-success alert-block">
                                        <button type="button" class="close" data-dismiss="alert">×</button> 
                                        <strong></strong>
                                    </div>
                                    <div class=" form">
                                            <form class="cmxform form-horizontal" id="groupAdd" method="post" enctype="multipart/form-data">
                                                <div class="form-group ">
                                                    <label for="sender" class="control-label col-lg-3">Group Name (required)</label>
                                                    <div class="col-lg-6">
                                                        <input type="text" name="groupname" id="groupname" value="{{ $group->vchr_name }}" class="form-control" readonly="">
                                                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                                         <input type="hidden" name="groupid" value="{{ $id }}">
                                                    </div>
                                                    
                                                </div>
                                                
                                                <div class="form-group ">
                                                    <label for="contacts" class="control-label col-lg-3">Contact Type (required)</label>
                                                    <div class="col-lg-6">
                                                       <select name="type" id="type" class="form-control m-bot15"  required>
                                                            <option value="1">Manually Enter Number(s) & Name(s)</option>
                                                            <option  value="2">Using a CSV file upload</option>
                                                        </select><br/>
                                                    </div>                                               
                                                </div>
                                                 <div class="form-group" id="contactsLabel">
                                                    <label for="sender" class="control-label col-lg-3">Contacts (required)1/line</label>
                                                    <div class="col-lg-6">
                                                        <textarea name="contacts" id="contacts" class="form-control"></textarea>
                                                    </div>
                                                    
                                                </div>
                                                <div class="form-group" id="nameLabel">
                                                    <label for="sender" class="control-label col-lg-3">Name(optional)1/line</label>
                                                    <div class="col-lg-6">
                                                        <textarea name="name" id="name" class="form-control"></textarea>
                                                    </div>
                                                    
                                                </div>
                                                      <div id="csvfile" class="form-group ">
                                                    <label for="contacts" class="control-label col-lg-3">CSV File</label>
                                                    <div class="col-lg-6">
                                                       <button type="button" id="import_num" class="btn btn-secondary popovers upload-btn" data-placement="top" data-trigger="hover"><i class="fa fa-upload"></i><input type="file" id="myfile" name="myfile" /></button>
                                                    </div>                                               
                                                </div>
                                               <div class="form-group">
                                                    <div class="col-lg-6 mrgn-lft-25">
                                                        <button class="btn btn-primary" id="btn_send_sms">Add Contacts</button>
                                                         <a href="{{ url('user/sms-groups') }}" class="btn btn-success" id="btn_send_sms">Back</a>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>            
                        <!-- page end-->
                    </section>
            </div>
        </div>
    </main>
@endsection 
@push('footer.script')
<script type="text/javascript">
    $('.popovers').popover();
</script>
 <script type="text/javascript">
     $(document).ready(function() {
         $("#csvfile").hide();
         $(".flash-message").hide();
            $("#type").change(function()
                { 
                    var type=$("#type").val();
                    if(type==1)
                    {
                        $("#csvfile").hide();
                        $("#nameLabel").show();
                        $("#contactsLabel").show();
                    }
                    else
                    {
                        $("#nameLabel").hide();
                        $("#contactsLabel").hide();
                        $("#csvfile").show();
                    }
                });
     });

     $(document).on('submit', '#groupAdd', function(event) {
        event.preventDefault();
        var type=$("#type").val();
        var contacts=$("#contacts").val();
        var contacts=$("#contacts").val();
        var file=$("#myfile").val();
        var groupname=$("#groupname").val();
        if(type==1)
        {
            $("#csvfile").hide();
            $("#nameLabel").show();
            $("#contactsLabel").show();
            if(groupname=="")
            {
                $(".flash-message").show();
                $('div.flash-message').html('Please Enter Group Name');
                $('div.flash-message').delay(6000).fadeOut();
                return false;
            }
            else if(contacts=="")
            {
                $(".flash-message").show();
                $('div.flash-message').html('Please Enter Contacts');
                $('div.flash-message').delay(6000).fadeOut();
                return false;
            }
            else
            {
                $.ajax({
                        url:  '{{ url('user/assign-group-manually-add') }}',
                        type: 'POST',
                        dataType: 'JSON',
                        data:  new FormData(this),
                        contentType: false,
                        processData:false,
                    })
                .done(function(res) {
                    if(res.status == 'success'){
                         $("#groupAdd")[0].reset();
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
            }
            
        }
        else
        {
            $("#nameLabel").hide();
            $("#contactsLabel").hide();
            $("#csvfile").show();
            var ext = $('#myfile').val().split('.').pop().toLowerCase();
            if(groupname=="")
            {
                $(".flash-message").show();
                $('div.flash-message').html('Please Enter Group Name');
                $('div.flash-message').delay(6000).fadeOut();
                return false;
            }
            else if(file=="")
            {
                $(".flash-message").show();
                $('div.flash-message').html('Please Upload CSV Files');
                $('div.flash-message').delay(6000).fadeOut();
                return false;
            }
            else if($.inArray(ext, ['csv']) == -1) {
                $(".flash-message").show();
                $('div.flash-message').html('Only CSV Files are Allowed to Upload');
                $('div.flash-message').delay(6000).fadeOut();
                return false;
            }
            else
            {
                  $.ajax({
                        url:  '{{ url('user/assign-group-upload-add') }}',
                        type: 'POST',
                        dataType: 'JSON',
                        data:  new FormData(this),
                        contentType: false,
                        processData:false,
                    })
                .done(function(res) {
                    if(res.status == 'success'){
                         $("#groupAdd")[0].reset();
                         $("#csvfile").hide();
                        $("#nameLabel").show();
                        $("#contactsLabel").show();
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
            }
        }
     });
 </script>   
@endpush
