@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="d-flex row-wrap align-items-center">


                    <div class="add-y-team">
                        <div class="dashboard-row">
                              <div class="y-team-header y-team-header-V2">
                                 <a href="{{ url()->previous() }}">
                                    
                                       <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                          <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z" fill="#4D5459"/>
                                       </svg>
                                       View Groups
                                   
                                 </a>
                              </div>
                        </div>
                    </div>

                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                               
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.group-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
               <h6 class="m-2"> Group Name:   {{ $group->vchr_name }}</h6>
                
<div class="gl-table">
                <table id="enquiry-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Name</th>
                            <th>Mobile No</th>
                           <th width="10%">Actions</th>
                    </thead>
                   
                </table>
            </div>

            </div>
        </div>
    </main>
    <!------------ /add modal/  -------------->

    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="enquirySubmit">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Enquiry Purpose</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Purpose Title</label>
                            <input type="text" name="vchr_purpose" id="orangeForm-pass" class="form-control validate"> 
                            <div class="error vchr_purpose ">
                                <span class="muted"></span>
                            </div>             
                        </div>
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Purpose Description</label>
                            <input type="text" name="vchr_purpose_description" id="orangeForm-pass" class="form-control validate"> 
                            <div class="error vchr_purpose_description">
                                <span class="muted"></span>
                            </div>             
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button class="btn btn-deep-orange demo-btn">Submit</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="enquiryEdit">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Edit Contact</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        
                       
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Name</label>
                            <input type="hidden" name="pk_int_group_user_id" id="pk_int_group_user_id" class="form-control validate"> 
                            <input type="hidden" name="_method" value="PUT">
                            <input type="text" name="vchr_name" id="orangeForm-pass" class="form-control validate"> 
                            <div class="error vchr_name ">
                                <span class="muted"></span>
                            </div>             
                        </div>
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Mobile No</label>
                            <textarea name="int_mobile" required id="orangeForm-pass" class="form-control validate"></textarea> 
                            <div class="error int_mobile">
                                <span class="muted"></span>
                            </div>             
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button class="btn btn-deep-orange demo-btn">Update</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection 
@push('footer.script')
<script type="text/javascript">
    $(document).ready(function() 
    {
        BASE_URL ={!! json_encode(url('/')) !!}
        var id={{$id}};
        $('#enquiry-table').DataTable({
           scrollX: true,
           paging: true,
            language: {
                searchPlaceholder: 'Search',
                sSearch: '',
                lengthMenu: '_MENU_ page',
            },
            columnDefs: [{
                    "width": "170px",
                    "targets": [0, 1]
                },
                {
                    "width": "40px",
                    "targets": [3]
                }
            ],
            ajax: BASE_URL + '/user/get-contacts/'+id,
            columns: [
            {
                data: 'slno',
                name: 'slno'
            },
            
            {
                data: 'name',
                name: 'name'
            },
            {
                data: 'mobile',
                name: 'mobile'
            },
            {
                data: 'show',
                name: 'show',
                orderable: false,
                searchable: false
            }
            ],
        });

       

        $('#enquiry-table').on('click', '.ks-izi-modal-trigger1', function(event) 
        {
            $('.error').html('');
            $('.error').hide();
            enquiryId = $(this).attr('feedback-id');
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/show-group-users/' + enquiryId,
                    type: 'GET',
                    dataType: 'JSON',
                })
                .done(function(res) {

                    if(res.status == "success") {
                        data = res.data;
                        $("[name='pk_int_group_user_id']").val(data.pk_int_group_user_id);

                        enquiryId = data.pk_int_group_id;
                        $("[name='vchr_name']").val(data.vchr_name);
                        $("[name='int_mobile']").val(data.int_mobile);
                        $('.error').html('');
                        $('.error').hide();
                    }
                })
                .fail(function() {
                })
                .always(function() {
                });

        });

        $(document).on('submit', '#enquiryEdit', function(event) {
            event.preventDefault();
            $('.val-error').html('');
            $('.val-error').hide();
            var id=$('#pk_int_group_user_id').val();
            //alert(id);
                $.ajax({
                    url: BASE_URL + '/user/edit-group-users/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) 
                {
                    if(res.status == 'success'){
                       $('#ks-izi-modal-large1').modal('toggle');
                       $.alert({
                        title: 'Success',
                        type: 'green',
                        content: res.msg,
                    });
                   }else
                   {
                        $.each(res.msg, function(index, val) 
                        {
                        //iterate through array or object
                            console.log(val);
                            console.log(index);
                            $('.'+index).html(val);
                            $('.'+index).show();
                        });
                    }
                })
                .fail(function() {
                })
                .always(function(com) {
                    $('#enquiry-table').DataTable().ajax.reload(null, false);

                });

        });
        $('#enquiry-table').on('click', '.feedback-delete', function (event) 
        {
            event.preventDefault();
            var feedbackId = $(this).attr('feedback-id');
                //alert(feedbackId);
            var destinationPath = BASE_URL + '/user/delete-group-users/' + feedbackId;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
        });


        



    });





</script>
@endpush
