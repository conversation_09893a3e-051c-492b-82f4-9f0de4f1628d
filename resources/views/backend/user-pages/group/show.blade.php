@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>View Groups</h5>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                               
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.group-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table">
                    <table id="enquiry-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th>Sl No</th>
                                <th>Contacts</th>
                                <th>Group Name</th>
                                <th>Group Description</th>
                                <th width="10%">Actions</th>
                        </thead>
                    
                    </table>
                </div>
            </div>
        </div>
    </main>
    <!------------ /add modal/  -------------->

    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="enquirySubmit">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Enquiry Purpose</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Purpose Title</label>
                            <input type="text" name="vchr_purpose" id="orangeForm-pass" class="form-control validate"> 
                            <div class="error vchr_purpose ">
                                <span class="muted"></span>
                            </div>             
                        </div>
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Purpose Description</label>
                            <input type="text" name="vchr_purpose_description" id="orangeForm-pass" class="form-control validate"> 
                            <div class="error vchr_purpose_description">
                                <span class="muted"></span>
                            </div>             
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button class="btn btn-deep-orange demo-btn btn-info">Submit</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="enquiryEdit">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update Group</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        
                       
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Group Name</label>
                            <input type="hidden" name="pk_int_group_id" id="pk_int_group_id" class="form-control validate"> 
                            <input type="hidden" name="_method" value="PUT">
                            <input type="text" name="vchr_name" id="orangeForm-pass" class="form-control validate" required> 
                            <div class="error vchr_name ">
                                <span class="muted"></span>
                            </div>             
                        </div>
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Group Description</label>
                            <textarea name="vchr_description" required id="orangeForm-pass" class="form-control validate"></textarea> 
                            <div class="error vchr_description">
                                <span class="muted"></span>
                            </div>             
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button class="btn btn-deep-orange demo-btn btn-info">Update</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection 
@push('footer.script')
<script type="text/javascript">
    $(document).ready(function() 
    {
        BASE_URL ={!! json_encode(url('/')) !!}
        $('#enquiry-table').DataTable({
           scrollX: true,
           paging: true,
            language: {
                searchPlaceholder: 'Search',
                sSearch: '',
                lengthMenu: '_MENU_ page',
            },
            columnDefs: [{
                    "width": "170px",
                    "targets": [0, 1]
                },
                {
                    "width": "40px",
                    "targets": [3]
                }
            ],
            ajax: BASE_URL + '/user/get-sms-groups',
            columns: [
            {
                data: 'slno',
                name: 'slno'
            },
            {
                data: 'contacts',
                name: 'contacts'
            },
            {
                data: 'name',
                name: 'name'
            },
            {
                data: 'description',
                name: 'description'
            },
            {
                data: 'show',
                name: 'show',
                orderable: false,
                searchable: false
            }
            ],
        });

       

        $('#enquiry-table').on('click', '.ks-izi-modal-trigger1', function(event) 
        {
            $('.error').html('');
            $('.error').hide();
            enquiryId = $(this).attr('feedback-id');
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/show-group/' + enquiryId,
                    type: 'GET',
                    dataType: 'JSON',
                })
                .done(function(res) {

                    if(res.status == "success") {
                        data = res.data;
                        $("[name='pk_int_group_id']").val(data.pk_int_group_id);

                        enquiryId = data.pk_int_group_id;
                        $("[name='vchr_name']").val(data.vchr_name);
                        $("[name='vchr_description']").val(data.vchr_description);
                        $('.error').html('');
                        $('.error').hide();
                    }
                })
                .fail(function() {
                })
                .always(function() {
                });

        });

        $(document).on('submit', '#enquiryEdit', function(event) {
            event.preventDefault();
            $('.val-error').html('');
            $('.val-error').hide();
            var id=$('#pk_int_group_id').val();
            //alert(id);
                $.ajax({
                    url: BASE_URL + '/user/edit-group/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) 
                {
                    if(res.status == 'success'){
                       $('#ks-izi-modal-large1').modal('toggle');
                       $.alert({
                        title: 'Success',
                        type: 'green',
                        content: res.msg,
                    });
                   }else
                   {
                        $.each(res.msg, function(index, val) 
                        {
                        //iterate through array or object
                            console.log(val);
                            console.log(index);
                            $('.'+index).html(val);
                            $('.'+index).show();
                        });
                    }
                })
                .fail(function() {
                })
                .always(function(com) {
                    $('#enquiry-table').DataTable().ajax.reload(null, false);

                });

        });
        $('#enquiry-table').on('click', '.feedback-delete', function (event) 
        {
            event.preventDefault();
            var feedbackId = $(this).attr('feedback-id');
                //alert(feedbackId);
            var destinationPath = BASE_URL + '/user/delete-group/' + feedbackId;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
        });


        



    });





</script>
@endpush
