@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
<main class="main-wrapper">
    <div class="task-panel">
        <div class="row justify-content-between ">
            <div class="col-lg-6 d-flex row-wrap align-items-center">
                <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                <h5>Developer Email API's</h5>
            </div>
        </div>
    </div>
    <div class="layout-wrapper">
        <!--   / Side menu included /  -->
        @include ('backend.layout.bulkemailsidebar')
        <div class="content-section p-3 bg-white">
            <!--  /Your content goes here/ -->
            <section class="smsapi-sec">
                <div class="row">
                   <div class="col-lg-12 col-md-12">
                        <div class="px-lg-3">
                            <div class="heading">
                                <h5>PARAMETERS</h5>
                                <p>The parameters to be used are:</p>
                            </div>
                            <hr>
                            <input type="hidden" name="apitoken" id="apitoken" value="{{ $apitoken }}">
                                <div class="row">
                                    <div class="col-lg-2 col-md-4">
                                        <p class="medium">username :</p>
                                    </div>
                                    <div class="col-lg-10 col-md-8">
                                        <p>{{ $user->vchr_user_mobile }} / {{ $user->email }}</p>
                                    </div>
                                </div>
                                <hr class="mt-lg-0 mt-md-0">
                                <div class="row">
                                    <div class="col-lg-2 col-md-4">
                                        <p class="medium">token :</p>
                                    </div>
                                    <div class="col-lg-10 col-md-8">
                                         <p class="medium" id="show-token"><a href="#" class="token">(Show/generate token)</a></p>
                                    </div>
                                </div>
                                <hr class="mt-lg-0 mt-md-0">
                                <div class="row">
                                    <div class="col-lg-2 col-md-4">
                                        <p class="medium">sender :</p>
                                    </div>
                                    <div class="col-lg-10 col-md-8">
                                        <p>Full Email Approved SenderID</p>
                                    </div>
                                </div>
                                <hr class="mt-lg-0 mt-md-0">
                                <div class="row">
                                    <div class="col-lg-2 col-md-4">
                                        <p class="medium">to :</p>
                                    </div>
                                    <div class="col-lg-10 col-md-8">
                                        <p>Recipient Emails (Separate by a comaa[maximum of 10 at a time is allowed])</p>
                                    </div>
                                </div>
                                <hr class="mt-lg-0 mt-md-0">
                                <div class="row">
                                    <div class="col-lg-2 col-md-4">
                                        <p class="medium">subject :</p>
                                    </div>
                                    <div class="col-lg-10 col-md-8">
                                        <p>Subject Content(Minimum of 3 words should be present)</p>
                                    </div>
                                </div>
                                <hr class="mt-lg-0 mt-md-0">
                                <div class="row">
                                    <div class="col-lg-2 col-md-4">
                                        <p class="medium">replyto :</p>
                                    </div>
                                    <div class="col-lg-10 col-md-8">
                                        <p>replyto emailid</p>
                                    </div>
                                </div>
                                {{-- <hr class="mt-lg-0 mt-md-0">
                                <div class="row">
                                    <div class="col-lg-2 col-md-4">
                                        <p class="medium">display :</p>
                                    </div>
                                    <div class="col-lg-10 col-md-8">
                                        <p>Display Name</p>
                                    </div>
                                </div> --}}
                                <hr class="mt-lg-0 mt-md-0">
                                <div class="row">
                                    <div class="col-lg-2 col-md-4">
                                        <p class="medium">cright :</p>
                                    </div>
                                    <div class="col-lg-10 col-md-8">
                                        <p>Copyright name(Your Company name or Your name)</p>
                                    </div>
                                </div>
                                <hr class="mt-lg-0 mt-md-0">
                                <div class="row">
                                    <div class="col-lg-2 col-md-4">
                                        <p class="medium">message :</p>
                                    </div>
                                    <div class="col-lg-10 col-md-8">
                                        <p>Email Content(Both html and/or text supported)</p>
                                    </div>
                                </div>
                                
                                <hr class="mt-lg-0 mt-md-0">
                                <div class="row mt-4">
                                    <div class="col-lg-12">
                                        {{-- <p class="medium">* In the password field either account password or
                                            api_password can be used</p> --}}
                                        <p class="medium">* The Email API works on GET and POST METHOD</p>
                                        <p class="medium">* Balance check and DLR API works on GET and POST METHOD </p>
                                    </div>
                                </div>
                        </div>
                    </div>
                </div>
                <hr>


                <div class="row p-2">
                    <div class="col-lg-12">
                        <ul class="nav nav-pills mb-lg-3 flex-nowrap" id="pills-tab" role="tablist">
                            <li class="nav-item">
                                <a id="generate" class="main-round-btn" id="pills-home-tab" data-toggle="pill" href="#pills-home"
                                    role="tab" aria-controls="pills-home" aria-selected="true"><i class="fa fa-gear"></i>GENERATE API</a>
                            </li>
                            <li class="nav-item">
                                <a class="main-round-btn" id="pills-profile-tab" data-toggle="pill"
                                    href="#pills-profile" role="tab" aria-controls="pills-profile"
                                    aria-selected="false"><i class="fa fa-bars"></i>VIEW ALL API'S</a>
                            </li>
                        </ul>
                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                                <div class="row px-lg-2">
                                    <div class="col-lg-4 col-md-6">
                                        <div class="">
                                            <label class="medium">Select API</label>
                                            <select class=" form-control select2" id="api" style="width: 100%; height: 100%">
                                                <option value="">--Select API--</option>
                                                <option value="1">Message API</option>
                                                <option value="2">Balance API</option>
                                                <option value="3">DLR API</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6" id="sms_route">
                                        <div class="">
                                            <label class="medium">Select Route</label>
                                            <select class=" form-control" id="route" style="width: 100%; height: 100%">
                                                @foreach($routes as $route)
                                                <option value="{{ $route->int_sms_route_code }}">{{ $route->vchr_sms_route }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6" id="message_id">
                                        <div class="">
                                            <label class="medium">Message ID</label>
                                            <input type="text" class="form-control" placeholder="" id="messageid">
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6" id="message_type">
                                        <div class="">
                                            <label class="medium">Message Type</label>
                                            <select class=" form-control select2" style="width: 100%; height: 100%" id="messagetype">
                                                <option value="">--Select Message Type--</option>
                                                <option value="0">Text</option>
                                                <option value="1">Unicode</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6" id="sender_id">
                                        <div class="">
                                            <label class="medium">SenderID</label>
                                            <input type="text" id="senderid" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6" id="mobile_no">
                                        <div class="">
                                            <label class="medium">Mobile</label>
                                            <input type="text" id="mobileno" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                     
                                    <div class="col-lg-12" id="messages">
                                        <div class="">
                                            <label class="medium">Message</label>
                                            <textarea class="message form-control" id="exampleFormControlTextarea1 message"
                                                rows="6"></textarea>
                                        </div>
                                    </div>
                                    <div class="main-round-btn green-btn ml-lg-3" id="generates">
                                        <a href="#">Generate </a>
                                    </div>

                                    <div class="col-lg-12" id="show-api">
                                        <div class="">
                                            <textarea class="form-control show_api" id="exampleFormControlTextarea1 show_api"
                                                rows="6" placeholder=""></textarea>
                                        </div>
                                    </div>
                                    <div class="copy main-round-btn ml-lg-3" id="copy-api">
                                        <a href="#">Copy to clipboard</a>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="pills-profile" role="tabpanel"
                                aria-labelledby="pills-profile-tab">
                                <div class="row px-lg-2">
                                    <div class="col-lg-12 sample-api-block">
                                        <p>Messaging API's</p>
                                        <h6>Format of SMS API Call for Single Recipient would be:</h6>
                                        <p>{{ url('/api/pushsms') }}?username=your_username&token=your_token&sender=your_senderid
                                            &to=your_recipient&message=your_message&priority=route</p>
                                        <p>Example:{{ url('/api/pushsms') }}?username={{ $user->vchr_user_mobile }}&
                                            token=your_token&sender=testtt&to=9895******&message=Hello&priority=11</p>
                                        <h6>Format of SMS API Call for Multiple Recipient would be:
                                        </h6>
                                        <p>{{ url('/api/pushsms') }}?username=your_username&token=your_token&sender=your_senderid
                                            &to=your_recipient1,your_recipient2&message=your_message&priority=route</p>
                                        <p>Example:{{ url('/api/pushsms') }}?username={{ $user->vchr_user_mobile }}&token=your_token&sender=testtt&
                                            to=9895******,9895******&message=Hello&priorit=11
                                        </p>
                                        
                                       <p>Other API's</p>
                                        <h6>To fetch DLR </h6>
                                        <p>{{ url('/api/mail-fetchdlr') }}?username=your_username&token=your_token&gltrackid=message_id</p>
                                       {{--  <h6>To fetch Multiple DLR's
                                        </h6>
                                        <p>{{ url('/api/fetchdlr') }}?username=your_username&token=your_token&messageid=message_id1,message_id2</p> --}}
                                        <h6>A sample Email API Call for Balance Check:
                                        </h6>
                                        <p>{{ url('/api/balancecheck') }}?username=your_username&token=your_token&priority=16 </p>

                                        <h6>A sample Email API Call to get your Senderids: </h6>
                                        <p>{{ url('/api/getemailsenderids') }}?username=your_username&token=your_token</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    </div>
    </div>
</main>


@endsection
@push('footer.script')
<script type="text/javascript">
    $(".token").on("click", function(e) 
    {
        e.preventDefault();
        if (confirm("Do you want to show/generate token!")) 
        {
            jQuery.ajax
            ({
                url: "{{url('/generate-apitoken')}}", //php          
                data: "", //the data "caller=name1&&callee=name2"
                dataType: 'json', //data format   
                success: function (data) 
                {

                    $('p#show-token').empty();
                    $('p#show-token').text(data.token);
                   

                }
            });
        }
        else
        {
            return false;
        }
    });
    $("#generate").on("click", function(e){
        e.preventDefault();
        var api=$("#api").val();
        if(api==1)
        {
            $("#message_id").hide();
            $("#sms_route").show();
            $("#messages").show();
            $("#sender_id").show();
            $("#show-api").hide();
            $("#copy-api").hide();
            $("#message_type").show();
            $("#mobile_no").show();
            $("#generates").show();
            $("#show-api").show();
            $("#copy-api").show();
        }
        else if(api==2)
        {
            $("#message_id").hide();
            $("#sms_route").show();
            $("#messages").hide();
            $("#sender_id").hide();
            $("#show-api").hide();
            $("#copy-api").hide();
            $("#message_type").hide();
            $("#mobile_no").hide();
            $("#generates").show();
            $("#show-api").show();
            $("#copy-api").show();
        }
        else if(api==3)
        {
            $("#message_id").show();
            $("#sms_route").hide();
            $("#messages").hide();
            $("#sender_id").hide();
            $("#show-api").hide();
            $("#copy-api").hide();
            $("#message_type").hide();
            $("#mobile_no").hide();
            $("#generates").show();
            $("#show-api").show();
            $("#copy-api").show();
        }
        else
        {
           $("#message_id").hide();
           $("#sms_route").hide();
           $("#messages").hide();
           $("#sender_id").hide();
           $("#show-api").hide();
           $("#copy-api").hide();
           $("#message_type").hide();
           $("#mobile_no").hide();
           $("#generates").hide();
        }
    });

    $("#api").change(function(e) {
        e.preventDefault();
        var api=$("#api").val();
        if(api==1)
        {
            $("#message_id").hide();
            $("#sms_route").show();
            $("#messages").show();
            $("#sender_id").show();
            $("#show-api").hide();
            $("#copy-api").hide();
            $("#message_type").show();
            $("#mobile_no").show();
            $("#generates").show();
        }
        else if(api==2)
        {
            $("#message_id").hide();
            $("#sms_route").show();
            $("#messages").hide();
            $("#sender_id").hide();
            $("#show-api").hide();
            $("#copy-api").hide();
            $("#message_type").hide();
            $("#mobile_no").hide();
            $("#generates").show();
        }
        else if(api==3)
        {
            $("#message_id").show();
            $("#sms_route").hide();
            $("#messages").hide();
            $("#sender_id").hide();
            $("#show-api").hide();
            $("#copy-api").hide();
            $("#message_type").hide();
            $("#mobile_no").hide();
            $("#generates").show();
        }
    });


    $("#generates").on("click", function(e){
        e.preventDefault();
        $("#show-api").show();
        $("#copy-api").show();
        BASE_URL ={!! json_encode(url('/')) !!};
        NAME ={!! $user->vchr_user_mobile !!};
        APITOKEN =$("#apitoken").val();
        var api_type=$("#api").val();
        ROUTE=$("#route").val();
        MESSAGETYPE=$("#messagetype").val();
        SENDERID=$("#senderid").val();
        MOBILENO=$("#mobileno").val();
        MESSAGE=$(".message").val();
        GLTRACKID=$("#messageid").val();
            if(api_type==1)
            {
                if(MESSAGETYPE==0 ||MESSAGETYPE=='')
                {
                    var api=BASE_URL+'/'+'api/pushsms?username='+NAME+'&token='+APITOKEN+'&sender='+SENDERID+'&to='+MOBILENO+'&message='+MESSAGE+'&priority='+ROUTE;
                }
                else 
                {
                    var api=BASE_URL+'/'+'api/pushsms?username='+NAME+'&token='+APITOKEN+'&sender='+SENDERID+'&to='+MOBILENO+'&message='+MESSAGE+'&priority='+ROUTE+'&message_type='+MESSAGETYPE;  
                } 
            }
            else if(api_type==2)
            {
                    var api=BASE_URL+'/'+'api/balancecheck?username='+NAME+'&token='+APITOKEN+'&priority='+ROUTE;
            }
            else
            {
                var api=BASE_URL+'/'+'api/mail-fetchdlr?username='+NAME+'&token='+APITOKEN+'&gltrackid='+GLTRACKID;
            }

        $(".show_api").text(api);
    });


    $(".copy").click(function(e) {
        e.preventDefault();
        $("textarea").select();
        document.execCommand('copy');
    });
</script>
@endpush