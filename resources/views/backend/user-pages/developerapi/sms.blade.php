@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Developer SMS API's</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.api-sidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <section class="smsapi-sec">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-lg-12 col-md-12">
                                <div class="px-lg-3">
                                    <div class="heading">
                                        <h5>PARAMETERS</h5>
                                        <p>The parameters to be used are:</p>
                                    </div>
                                    <hr>
                                    <input type="hidden" name="apitoken" id="apitoken" value="{{ $apitoken }}">
                                    <div class="row">
                                        <div class="col-lg-2 col-md-4">
                                            <p class="medium">username :</p>
                                        </div>
                                        <div class="col-lg-10 col-md-8">
                                            <p>{{ $user->vchr_user_mobile }} / {{ $user->email }}</p>
                                        </div>
                                    </div>
                                    <hr class="mt-lg-0 mt-md-0">
                                    <div class="row">
                                        <div class="col-lg-2 col-md-4">
                                            <p class="medium">token :</p>
                                        </div>
                                        <div class="col-lg-10 col-md-8">
                                            <p class="medium" id="show-token"><a href="#" class="token">(Show/generate
                                                    token)</a></p>
                                        </div>
                                    </div>
                                    <hr class="mt-lg-0 mt-md-0">
                                    <div class="row">
                                        <div class="col-lg-2 col-md-4">
                                            <p class="medium">sender :</p>
                                        </div>
                                        <div class="col-lg-10 col-md-8">
                                            <p>6 Alphabetic Characters Only</p>
                                        </div>
                                    </div>
                                    <hr class="mt-lg-0 mt-md-0">
                                    <div class="row">
                                        <div class="col-lg-2 col-md-4">
                                            <p class="medium">to :</p>
                                        </div>
                                        <div class="col-lg-10 col-md-8">
                                            <p>Recipient Number{{--  (one at a time) --}}</p>
                                        </div>
                                    </div>
                                    <hr class="mt-lg-0 mt-md-0">
                                    <div class="row">
                                        <div class="col-lg-2 col-md-4">
                                            <p class="medium">message :</p>
                                        </div>
                                        <div class="col-lg-10 col-md-8">
                                            <p>Actual Message to be sent</p>
                                        </div>
                                    </div>
                                    <hr class="mt-lg-0 mt-md-0">
                                    {{--  <div class="row">
                                        <div class="col-lg-2 col-md-4">
                                            <p class="medium">result_type :</p>
                                        </div>
                                        <div class="col-lg-10 col-md-8">
                                            <p>result_type=2 to get both MessageId and mobile number as output</p>
                                        </div>
                                    </div> --}}
                                    {{-- <hr class="mt-lg-0 mt-md-0"> --}}
                                    <div class="row">
                                        <div class="col-lg-2 col-md-4">
                                            <p class="medium">priority :</p>
                                        </div>
                                        <div class="col-lg-10 col-md-8">
                                            <p>Route id for the selected route(4 - OTP , 8 - Promo , 11 - Enterprise , 12 -
                                                Scrub , 18 - Intrl)</p>
                                        </div>
                                    </div>
                                    <hr class="mt-lg-0 mt-md-0">
                                    <div class="row mt-4">
                                        <div class="col-lg-12">
                                            {{-- <p class="medium">* In the password field either account password or
                                                api_password can be used</p> --}}
                                            <p class="medium">* The API works on GET and POST METHOD</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row p-2">
                            <div class="col-lg-12">
                                <ul class="nav nav-pills mb-lg-3 flex-nowrap" id="pills-tab" role="tablist">
                                    <li class="nav-item">
                                        <a id="generate" class="main-round-btn" id="pills-home-tab" data-toggle="pill"
                                        href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true"><i
                                                    class="fa fa-gear"></i>GENERATE API</a>
                                    </li>
                                    <li class="nav-item ml-1">
                                        <a class="main-round-btn" id="pills-profile-tab" data-toggle="pill"
                                        href="#pills-profile" role="tab" aria-controls="pills-profile"
                                        aria-selected="false"><i class="fa fa-bars"></i>VIEW ALL API'S</a>
                                    </li>
                                </ul>
                                <div class="tab-content" id="pills-tabContent">
                                    <div class="tab-pane fade" id="pills-home" role="tabpanel"
                                        aria-labelledby="pills-home-tab">
                                        <div class="row px-lg-2">
                                            <div class="col-lg-4 col-md-6">
                                                <div class="">
                                                    <label class="medium">Select API</label>
                                                    <select class=" form-control select2" id="api"
                                                            style="width: 100%; height: 100%">
                                                        <option value="">--Select API--</option>
                                                        <option value="{{\App\Common\Variables::MESSAGE_API}}">Message API
                                                        </option>
                                                        <option value="{{\App\Common\Variables::BALANCE_API}}">Balance API
                                                        </option>
                                                        <option value="{{\App\Common\Variables::STATUS_API}}">DLR API
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-4 col-md-6" id="sms_route">
                                                <div class="">
                                                    <label class="medium">Select Route</label>
                                                    <select class=" form-control select2" id="route"
                                                            style="width: 100%; height: 100%">
                                                        <option value="">--Select Route--</option>
                                                        @foreach($routes as $route)
                                                            <option value="{{ $route->int_sms_route_code }}">{{ $route->vchr_sms_route }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-4 col-md-6" id="message_id">
                                                <div class="">
                                                    <label class="medium">Message ID</label>
                                                    <input type="text" class="form-control" placeholder="" id="messageid">
                                                </div>
                                            </div>
                                            <div class="col-lg-4 col-md-6" id="message_type">
                                                <div class="">
                                                    <label class="medium">Message Type</label>
                                                    <select class=" form-control select2" style="width: 100%; height: 100%"
                                                            id="messagetype">
                                                        <option value="">--Select Message Type--</option>
                                                        <option value="0">Text</option>
                                                        <option value="1">Unicode</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-4 col-md-6" id="sender_id">
                                                <div class="">
                                                    <label class="medium">SenderID</label>
                                                    <input type="text" id="senderid" class="form-control" placeholder="">
                                                </div>
                                            </div>
                                            <div class="col-lg-4 col-md-6" id="mobile_no">
                                                <div class="">
                                                    <label class="medium">Mobile</label>
                                                    <input type="text" id="mobileno" class="form-control" placeholder="">
                                                </div>
                                            </div>

                                            <div class="col-lg-12" id="messages">
                                                <div class="">
                                                    <label class="medium">Message</label>
                                                    <textarea class="message form-control"
                                                            id="exampleFormControlTextarea1 message"
                                                            rows="6"></textarea>
                                                </div>
                                            </div>
                                            <div class="main-round-btn green-btn ml-lg-3" id="generates">
                                                <a href="#">Generate </a>
                                            </div>

                                            <div class="col-lg-12" id="show-api">
                                                <div class="">
                                                <textarea class="form-control show_api"
                                                        id="exampleFormControlTextarea1 show_api"
                                                        rows="6" placeholder=""></textarea>
                                                </div>
                                            </div>
                                            <div class="copy main-round-btn ml-lg-3" id="copy-api">
                                                <a href="#">Copy to clipboard</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="pills-profile" role="tabpanel"
                                        aria-labelledby="pills-profile-tab">
                                        <div class="row px-lg-2">
                                            <div class="col-lg-12 sample-api-block">
                                                <p>Messaging API's</p>
                                                <h6>Format of SMS API Call for Single Recipient would be:</h6>
                                                <p>{{ url('/api/pushsms') }}
                                                    ?username=your_username&token=your_token&sender=your_senderid
                                                    &to=your_recipient&message=your_message&priority=route</p>
                                                <p>Example:{{ url('/api/pushsms') }}?username={{ $user->vchr_user_mobile }}&
                                                    token=your_token&sender=testtt&to=9895******&message=Hello&priority=11</p>
                                                <h6>Format of SMS API Call for Multiple Recipient would be:
                                                </h6>
                                                <p>{{ url('/api/pushsms') }}
                                                    ?username=your_username&token=your_token&sender=your_senderid
                                                    &to=your_recipient1,your_recipient2&message=your_message&priority=route</p>
                                                <p>Example:{{ url('/api/pushsms') }}?username={{ $user->vchr_user_mobile }}
                                                    &token=your_token&sender=testtt&
                                                    to=9895******,9895******&message=Hello&priority=11
                                                </p>
                                                <h6>A sample SMS API Call for Single Recipient with Scheduling (4th January
                                                    2020 at 3:30 PM) would be:
                                                </h6>
                                                <p>{{ url('/api/pushsms') }}
                                                    ?username=your_username&token=your_token&sender=your_senderid
                                                    &to=your_recipient&message=your_message&priority=route&shtime=04-01-2020-15-30</p>
                                                <h6>A sample SMS API Call for Sending UNICODE message would be:
                                                </h6>
                                                <p>{{ url('/api/pushsms') }}
                                                    ?username=your_username&token=your_token&sender=your_senderid
                                                    &to=your_recipient1&message=your_message&priority=route&message_type=1</p>
                                                <p>Example:{{ url('/api/pushsms') }}?username={{ $user->vchr_user_mobile }}
                                                    &token=your_token&sender=testtt&
                                                    to=9895******&message=Hello&priority=11&message_type=1

                                                {{--  <h6>A sample SMS API Call for Sending FLASH message would be:
                                                </h6>
                                                <p>{{ url('/api/pushsms') }}?username=your_username&token=your_token&sender=your_senderid
                                                    &to=your_recipient1&message=your_message&priority=route&message_type=2</p>
                                                <p>Example:{{ url('/api/pushsms') }}?username={{ $user->vchr_user_mobile }}&token=your_token&sender=testtt&
                                                    to=9895******&message=Hello&priority=11&message_type=2 --}}

                                                {{-- <h6>A sample SMS API Call for Sending PICTURE message would be:
                                                </h6>
                                                <p>{{ url('/api/pushsms') }}?username=your_username&token=your_token&sender=your_senderid
                                                    &to=your_recipient1&message=your_message&priority=route&message_type=3</p>
                                                    <p>Message would the hexcode of otb file (You should convert normal image file to otb file).</p>
                                                <p>Example:{{ url('/api/pushsms') }}?username={{ $user->vchr_user_mobile }}&token=your_token&sender=testtt&to=9895******&message=
                                                    3000000002010000481c010000000000000000000000000000 00000000000000000000000000000000000000000000000000 00000000000000000000000000000000000000000000000000 00000000000000000000000000000000000000000000000000 000002200e0000000000008ee0060007e00000018660460001 300010018440c40001300030038441e4c30320c678028440cd c583c168300484408ec902224c2007c4408c9e02378e2008cc c18d980236066010cee1c9d90666527039ecc1998e0fc38c60 00000000000000000000000000000000000000000000000000 00000000000000000000000000000000000000000000000000 00000000000000000000000000
                                                    &priority=11&message_type=3 --}}


                                                <p>Other API's</p>
                                                <h6>To fetch DLR </h6>
                                                <p>{{ url('/api/fetchdlr') }}
                                                    ?username=your_username&token=your_token&gltrackid=message_id</p>
                                                {{--  <h6>To fetch Multiple DLR's
                                                </h6>
                                                <p>{{ url('/api/fetchdlr') }}?username=your_username&token=your_token&messageid=message_id1,message_id2</p> --}}
                                                <h6>A sample SMS API Call for Balance Check:
                                                </h6>
                                                <p>{{ url('/api/balancecheck') }}
                                                    ?username=your_username&token=your_token&priority=route </p>

                                                <h6>A sample SMS API Call to get your Senderids: </h6>
                                                <p>{{ url('/api/getsenderids') }}
                                                    ?username=your_username&token=your_token</p>


                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>
@endsection
@push('footer.script')
    <script type="text/javascript">
        $(".token").on("click", function (e) {
            e.preventDefault();
            if (confirm("Do you want to show/generate token!")) {
                jQuery.ajax
                ({
                    url: "{{url('/generate-apitoken')}}", //php
                    data: "", //the data "caller=name1&&callee=name2"
                    dataType: 'json', //data format
                    success: function (data) {
                        $('p#show-token').empty();
                        $('p#show-token').text(data.token);
                    }
                });
            } else {
                return false;
            }
        });
        $("#generate").on("click", function (e) {
            e.preventDefault();
            var api = $("#api").val();
            if (api == '{{\App\Common\Variables::MESSAGE_API}}') {
                $("#message_id").hide();
                $("#sms_route").show();
                $("#messages").show();
                $("#sender_id").show();
                $("#show-api").hide();
                $("#copy-api").hide();
                $("#message_type").show();
                $("#mobile_no").show();
                $("#generates").show();
                $("#show-api").show();
                $("#copy-api").show();
            } else if (api == '{{\App\Common\Variables::BALANCE_API}}') {
                $("#message_id").hide();
                $("#sms_route").show();
                $("#messages").hide();
                $("#sender_id").hide();
                $("#show-api").hide();
                $("#copy-api").hide();
                $("#message_type").hide();
                $("#mobile_no").hide();
                $("#generates").show();
                $("#show-api").show();
                $("#copy-api").show();
            } else if (api == '{{\App\Common\Variables::STATUS_API}}') {
                $("#message_id").show();
                $("#sms_route").hide();
                $("#messages").hide();
                $("#sender_id").hide();
                $("#show-api").hide();
                $("#copy-api").hide();
                $("#message_type").hide();
                $("#mobile_no").hide();
                $("#generates").show();
                $("#show-api").show();
                $("#copy-api").show();
            } else {
                $("#message_id").hide();
                $("#sms_route").hide();
                $("#messages").hide();
                $("#sender_id").hide();
                $("#show-api").hide();
                $("#copy-api").hide();
                $("#message_type").hide();
                $("#mobile_no").hide();
                $("#generates").hide();
            }
        });

        $("#api").change(function (e) {
            e.preventDefault();
            var api = $("#api").val();
            if (api === '{{\App\Common\Variables::MESSAGE_API}}') {
                $("#message_id").hide();
                $("#sms_route").show();
                $("#messages").show();
                $("#sender_id").show();
                $("#show-api").hide();
                $("#copy-api").hide();
                $("#message_type").show();
                $("#mobile_no").show();
                $("#generates").show();
            } else if (api === '{{\App\Common\Variables::BALANCE_API}}') {
                $("#message_id").hide();
                $("#sms_route").show();
                $("#messages").hide();
                $("#sender_id").hide();
                $("#show-api").hide();
                $("#copy-api").hide();
                $("#message_type").hide();
                $("#mobile_no").hide();
                $("#generates").show();
            } else if (api === '{{\App\Common\Variables::STATUS_API}}') {
                $("#message_id").show();
                $("#sms_route").hide();
                $("#messages").hide();
                $("#sender_id").hide();
                $("#show-api").hide();
                $("#copy-api").hide();
                $("#message_type").hide();
                $("#mobile_no").hide();
                $("#generates").show();
            }
        });


        $("#generates").on("click", function (e) {
            e.preventDefault();
            $("#show-api").show();
            $("#copy-api").show();
            BASE_URL ={!! json_encode(url('/')) !!};
            NAME = '{{$user->vchr_user_mobile }}';
            APITOKEN = $("#apitoken").val();
            var api_type = $("#api").val();
            ROUTE = $("#route").val();
            MESSAGETYPE = $("#messagetype").val();
            SENDERID = $("#senderid").val();
            MOBILENO = $("#mobileno").val();
            MESSAGE = $(".message").val();
            GLTRACKID = $("#messageid").val();
            if (api_type === '{{\App\Common\Variables::MESSAGE_API}}') {
                if (MESSAGETYPE === 0 || MESSAGETYPE === '') {
                    var api = BASE_URL + '/' + 'api/pushsms?username=' + NAME + '&token=' + APITOKEN + '&sender=' + SENDERID + '&to=' + MOBILENO + '&message=' + MESSAGE + '&priority=' + ROUTE;
                } else {
                    var api = BASE_URL + '/' + 'api/pushsms?username=' + NAME + '&token=' + APITOKEN + '&sender=' + SENDERID + '&to=' + MOBILENO + '&message=' + MESSAGE + '&priority=' + ROUTE + '&message_type=' + MESSAGETYPE;
                }
            } else if (api_type === '{{\App\Common\Variables::BALANCE_API}}') {
                var api = BASE_URL + '/' + 'api/balancecheck?username=' + NAME + '&token=' + APITOKEN + '&priority=' + ROUTE;
            } else {
                var api = BASE_URL + '/' + 'api/fetchdlr?username=' + NAME + '&token=' + APITOKEN + '&gltrackid=' + GLTRACKID;
            }
            $(".show_api").text(api);
        });

        $(".copy").click(function (e) {
            e.preventDefault();
            $("textarea").select();
            document.execCommand('copy');
        });
    </script>
@endpush