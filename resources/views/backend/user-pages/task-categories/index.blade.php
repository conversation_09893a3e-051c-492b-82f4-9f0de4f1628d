@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <div class="current-page">
                        <p><a href="{{('/user/crm-settings')}}">Settings </a>>  Task Categories</p> 
                    </div>
                    <h5>Task Categories</h5>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <!-- <li class=""><a class="main-round-btn mt-2 ks-izi-modal-trigger" -->

                                <li class="">
                                    <a class="main-round-btn bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#task-category-create-modal"
                                            id="addTCategory"
                                            href="#">
                                            <i class="fa fa-plus"></i> 
                                            Task Categories
                                        </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.task-settings-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table gl-table-v2">
                    <table id="task_category_d_table" class="table table-striped table-bordered nowrap table-custom"
                        cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Task Category</th>
                            <th width="10%">Actions</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
            -
        </div>
    </main>
    <!------------ /update modal/  -------------->
    <div class="modal fade" id="task-category-create-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="taskCategoryAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Task Category</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Task Category</label>
                            <input type="text" name="name" class="form-control" placeholder="Enter Task Category"
                                   autocomplete="off"
                                   required>
                            <span class="error name"></span>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <button class="main-round-btn"><i class="fa fa-plus"></i> Add</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal fade" id="task-category-edit-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="taskCategoryEdit" enctype="multipart/form-data">
            {{method_field('PUT')}}
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Task Category</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Source</label>
                            <input type="text" name="name" class="form-control" id="task_category_name" placeholder="">
                            <span class="error name"></span>
                            <input type="hidden" name="_method" value="PUT" class="form-control" placeholder="">
                            <input type="hidden" name="id" class="form-control" placeholder=""
                                   id="task_category_id">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button class="main-round-btn">Update</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection
@push('footer.script')
    <script type="text/javascript">


        $(document).ready(function () {
            BASE_URL = {!! json_encode(url('/')) !!}
            $('.error').hide();
            /*-----Get Data------*/
            $('#task_category_d_table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                ajax: BASE_URL + '/user/task-categories-data',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'name', name: 'name'},
                    {data: 'show', name: 'show', orderable: false, searchable: false}
                ],
            });
            /*-----Add------*/
            $(document).on('submit', '#taskCategoryAdd', function (event) {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/task-categories',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                })
                    .done(function (res) {
                        if (res.status == 'success') {
                            // $($('.ks-izi-modal-trigger').data('target')).iziModal('close');
                            //  $("#testimonialAdd")[0].reset();
                            $('#task-category-create-modal').modal('toggle');
                            $.alert({
                                title: 'Success',
                                type: 'green',
                                content: res.msg,
                            });
                        } 
                        else if (res.status == 'error') {
                            $.alert({title: 'Failed', type: 'red', content: res.msg,});
                        }else {
                            $.each(res.msg, function (index, val) {
                                //iterate through array or object
                                console.log(val);
                                console.log(index);
                                $('.' + index).html(val);
                                $('.' + index).show();
                            });
                        }
                    }).fail(function () {
                }).always(function (com) {
                    $('#task_category_d_table').DataTable().ajax.reload(null, false);
                });
            });
            /*-----Edit------*/
            $(document).on('submit', '#taskCategoryEdit', function (event) {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                var id = $('#task_category_id').val();
                $.ajax({
                    url: BASE_URL + '/user/task-categories/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        //  $("#testimonialAdd")[0].reset();
                        $('#task-category-edit-modal').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                }).always(function (com) {
                    $('#task_category_d_table').DataTable().ajax.reload(null, false);
                });
            });

            $('#task_category_d_table').on('click', '.tc-delete', function (event) {
                event.preventDefault();
                var id = $(this).attr('task-c-id');
                var destinationPath = BASE_URL + '/user/task-categories/' + id;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                }).done(function (res) {
                                    if (res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                    .fail(function (err) {
                                    })
                                    .always(function (com) {
                                        $('#task_category_d_table').DataTable().ajax.reload(null, false);
                                    });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
            /*-----View------*/
            $('#task_category_d_table').on('click', '.tc-view', function (event) {
                var tcId = $(this).attr('data-t-c-id');
                $.ajax({
                    url: BASE_URL + '/user/task-categories/' + tcId,
                    type: 'GET',
                    dataType: 'JSON',
                }).done(function (res) {
                    if (res.status == "success") {
                        var data = res.data;
                        $("#task_category_name").val(data.name);
                        $('#task_category_id').val(data.id);
                    }
                }).fail(function (err) {
                }).always(function (com) {
                    $('#task_category_d_table').DataTable().ajax.reload(null, false);
                });
            });
        });

    </script>
@endpush
