@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>SENDER ID APPROVAL</h5>
                </div>
                <div class="mb-2">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class="">
                                    <a class="main-round-btn bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger"
                                       data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add
                                        Senderid </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.message-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table">
                    <table id="enquiry-table" class="table table-striped table-bordered nowrap table-custom"
                           cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Senderid Name</th>
                            <th>Senderid</th>
                            <th width="10%">Actions</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </main>
    <!------------ /add modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="Add">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Senderid</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Senderid Name</label>
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <input type="text" name="sendername" id="orangeForm-pass" class="form-control validate">
                            <div class="error sendername"><span class="muted"></span></div>
                        </div>
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Senderid</label>
                            <input type="text" name="senderid" id="orangeForm-pass" class="form-control validate">
                            <div class="error senderid"><span class="muted"></span></div>
                        </div>

                        <div class="modal-footer d-flex justify-content-center">
                            <button id="add-senderid" class="btn btn-deep-orange demo-btn">Submit</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="Edit">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update Senderid</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Senderid Name</label>
                            <input type="text" name="sendername" id="orangeForm-pass" class="form-control validate">
                            <div class="error sendername"><span class="muted"></span></div>
                        </div>
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Senderid</label>
                            <input type="text" name="senderid" id="orangeForm-pass" class="form-control validate">
                            <input type="hidden" name="_method" value="PUT" class="form-control" placeholder="">
                            <input type="hidden" name="pk_int_sender_id" id="pk_int_sender_id" class="form-control"
                                   placeholder="" id="pk_int_sender_id">
                            <div class="error senderid"><span class="muted"></span></div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button id="update-senderid" class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection
@push('footer.script')
    <script type="text/javascript">
        $(document).ready(function () {
            BASE_URL = {!! json_encode(url('/')) !!}
            $('#enquiry-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [
                    {"width": "140px", "targets": [1, 2, 3]},
                    {"width": "40px", "targets": [0]}
                ],
                ajax: BASE_URL + '/user/get-user-senderid',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'vchr_sender_id_name', name: 'vchr_sender_id_name'},
                    {data: 'vchr_sender_id', name: 'vchr_designation'},
                    {data: 'show', name: 'show', orderable: false, searchable: false}
                ],
            });

            $(document).on('submit', '#Add', function (event) {
                $('#add-senderid').html("Processing");
                $('#add-senderid').prop('disabled', true);
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                $.ajax({
                    url: BASE_URL + '/user/senderid',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        $('#add-senderid').html("Add");
                        $('#add-senderid').prop('disabled', false);
                        $("#Add")[0].reset();
                        $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else {
                        $('#add-senderid').html("Add");
                        $('#add-senderid').prop('disabled', false);
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                        // swal(res.msg.vchr_enquiry_type[0]);
                    }
                }).fail(function () {
                }).always(function (com) {
                    $('#enquiry-table').DataTable().ajax.reload(null, false);
                });
            });
            $('.error').hide();
            $('.ks-izi-modal-trigger').on('click', function (e) {
                $('.error').html('');
                $('.error').hide();
            });

            $('#enquiry-table').on('click', '.ks-izi-modal-trigger1', function (event) {
                $('.error').html('');
                $('.error').hide();
                enquiryId = $(this).attr('senderid-id');
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/senderid/' + enquiryId,
                    type: 'GET',
                    dataType: 'JSON',
                }).done(function (res) {
                    if (res.status == "success") {
                        data = res.data;
                        $("[name='sendername']").val(data.vchr_sender_id_name);
                        $("[name='senderid']").val(data.vchr_sender_id);
                        $('#pk_int_sender_id').val(data.pk_int_sender_id);
                        $("#Add")[0].reset();
                        $('.error').html('');
                        $('.error').hide();
                    }
                }).fail(function () {

                }).always(function () {
                });
            });

            $(document).on('submit', '#Edit', function (event) {
                $('#update-senderid').html("Processing");
                $('#update-senderid').prop('disabled', true);
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id = $('#pk_int_sender_id').val();
                //alert(id);
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/senderid/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        $('#update-senderid').html("Update");
                        $('#update-senderid').prop('disabled', false);
                        $('#ks-izi-modal-large1').modal('toggle');
                        $.alert({title: 'Success', type: 'green', content: res.msg,});
                    } else {
                        $('#update-senderid').html("Update");
                        $('#update-senderid').prop('disabled', false);
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {

                }).always(function (com) {
                    $('#enquiry-table').DataTable().ajax.reload(null, false);
                });
            });

            $('#enquiry-table').on('click', '.senderid-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('senderid-id');
                //alert(feedbackId);
                var destinationPath = BASE_URL + '/user/senderid/' + feedbackId;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                }).done(function (res) {
                                    if (res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                }).fail(function (err) {
                                }).always(function (com) {
                                    $('#enquiry-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $('#enquiry-table').on('click', '.default-senderid', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('senderid-id');
                var destinationPath = BASE_URL + '/user/make-default-senderid/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Dafault sender id',
                    content: 'Are you sure want to make sender id as default ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'GET',
                                }).done(function (res) {
                                    if (res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                }).fail(function (err) {
                                }).always(function (com) {
                                    $('#enquiry-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });
    </script>
@endpush
