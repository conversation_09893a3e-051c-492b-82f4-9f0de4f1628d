@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Subscription Message</h5>
                </div>
                <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3 "  id="editData" name="button" title="Click here to edit subscription messages">Edit Subscription SMS</button>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.smssidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <div class="col-sm-12 py-3">
                    <form id="subscriptionEdit" method="POST">
                    <table id="enquiry-info-table1" class="table table-striped table-bordered" width="100%">
                        <thead>
                            <tr>
                               <th width="10px" ><center>Field</center> </th>
                               <th width="10px">  <center>  Data</center></th>
                                
                            </tr>
                            <tr>
                                <td>
                                    <b>SUBSCRIPTION MESSAGE</b>
                                </td>
                                <td>
                                    <input type="hidden" name="id" class="form-control" placeholder="" value="{{$user->pk_int_subscription_message_id}}" id="id">
                                    {{csrf_field()}}
                                    <input type="hidden" name="_method" class="form-control" placeholder="" value="PUT" id="">

                                      <div class="col-sm-12">
                                    <textarea name="text_subscription_message" class="form-control" placeholder="" rows="5"  id="sub">{{$user->text_subscription_message}}</textarea>
                                    <span class="val-error  text_subscription_message"></span>
                                    {{-- <input type="text" name="short_code_description" class="form-control" placeholder=""> --}}
                                </div>
                                </td>
                            </tr>
                                 <tr>
                                <td>
                                    <b>UNSUBSCRIPTION MESSAGE</b>
                                </td>
                                <td>
                                    
                                    <div class="col-sm-12">
                                    <textarea name="text_unsubscription_message" class="form-control" placeholder="" rows="5" id="unsub">{{$user->text_unsubscription_message}}</textarea>
                                    <span class="val-error  text_unsubscription_message"></span>
                                    {{-- <input type="text" name="short_code_description" class="form-control" placeholder=""> --}}
                                </div>
                                </td>
                            </tr>
                                <tr>
                                <td>
                                    <b>MESSAGE STATUS</b>
                                </td>
                                <td>
                                    
                                 <div class="col-sm-12">
                                  @if($user->int_subscription_message_status)
                                   <button class="btn btn-success ks-split" title="SMS Subscription message is active">
                                        <span class="ks-text">ACTIVE</span>
                                        <span class="la la-check ks-icon"></span>
                                    </button>
                                  @else
                                   <button class="btn btn-danger ks-split" title="Waiting for admin approval">
                                        <span class="ks-text">PENDING</span>
                                        <span class="la la-check ks-icon"></span>
                                    </button>
                                  @endif
                                </div>
                                </td>
                            </tr>
                        </thead>
                    </table>
                    <div class="col-sm-12">
                       <div class="row"> 
                         <button type="submit" class="btn  btn-primary btn-md"  id="submitApi1" name="button" style="margin-left: 40%;">UPDATE </button>
                       </div>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </main>


@endsection 
@push('footer.script')
<script type="text/javascript">
    
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!}

            $('.error').hide();

            
            $('input').prop('disabled',true);
            $('textarea').prop('disabled',true);
            $('#submitApi1').hide();
    


            $('.ks-izi-modal-trigger').on('click', function (e) {
                $($(this).data('target')).iziModal('open');

                $(".test").on('click', function (e) {
                    //alert('hi');
                }); 

            });

             $(document).on('click', '#editData', function(event) {
                $('input').prop('disabled',false);
                $('textarea').prop('disabled',false);
                $('#submitApi1').show();
            });
           
            $(document).on('submit', '#subscriptionEdit', function(event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id=$('#id').val();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/subscription-message/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    swal(res.msg);
                     setTimeout(function(){ location.reload(); }, 1000);
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
               

            });

            });
            $('#enquiry-info-table').on('click', '.smstemplate-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('smstemplate-id');
                var destinationPath = BASE_URL + '/user/smstemplate/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });   
    </script>
@endpush
