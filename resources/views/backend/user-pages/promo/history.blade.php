@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Gl Promo History</h5>
                </div>
                {{-- <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add SMS Template </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.promosidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <div class="col-md-12" style="overflow: auto">
                <table id="enquiry-info-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Date & Time</th>
                            <th>User Name</th>
                            <th>Mobile No</th>
                            <th>Package</th>
                            <th>Remarks</th>
                        </tr>
                    </thead>
                </table>
            </div>
            </div>
        </div>
    </main>

@endsection 
@push('footer.script')
<script type="text/javascript">

  

     function readURL1(input) {
      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function(e) {
          $('#profile-img-tag').attr('src', e.target.result);

          $('#profile-img-tag').hide();
          $('#profile-img-tag').fadeIn(650);

        }

        reader.readAsDataURL(input.files[0]);
      }
    }

    function readURL(input) {

      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function(e) {
          $('#blah').attr('src', e.target.result);

          $('#blah').hide();
          $('#blah').fadeIn(650);

        }
        reader.readAsDataURL(input.files[0]);
      }
    }

        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!}

            $('.error').hide();
            $('.ks-izi-modal-trigger').on('click', function (e) {
                $($(this).data('target')).iziModal('open');

                $(".test").on('click', function (e) {
                    //alert('hi');
                });
               $("#fileInput").change(function() {
                    readURL(this);
                   // alert('hi');
                });

               $("#fileInput1").change(function() {
                    readURL1(this);
                   alert('hi');
                });
            });

        function gl_history(userid)
        {
            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1,2,3]
                },
                
                ],
                ajax: BASE_URL + '/user/getglpromo-history',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data: 'date',
                        name: 'date'
                    },
                    {
                        data: 'username',
                        name: 'username'
                    },
                    {

                        data: 'mobno',
                        name: 'mobno'
                    },
                    {
                        data: 'package',
                        name: 'package'
                    },
                    {
                        data: 'remarks',
                        name: 'remarks'
                    }
                   
                    
                ],
                "bDestroy": true,
                
            });
        }
        
        gl_history();

            $(document).on('submit', '#testimonialAdd', function(event) {
                event.preventDefault();
                // $('').on('click', function (e) {
               $('.val-error').html('');
              $('.val-error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/glpromo',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    $($('.ks-izi-modal-trigger').data('target')).iziModal('close');
                    swal(res.msg);
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });

            $(document).on('submit', '#testimonialEdit', function(event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id=$('#pk_int_sms_pricing_id').val();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/admin/edit-smspricing/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                    $($('.ks-izi-modal-trigger1').data('target')).iziModal('close');
                    swal(res.msg);
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });


            $('#enquiry-info-table').on('click', '.testimonial-act', function(event) {
                event.preventDefault();
                if($(this).hasClass('btn-activate')) {
                    var url = BASE_URL + '/admin/glpromo-deactivate/';
                    action = 'Deactivate';
                } else {
                    var url = BASE_URL + '/admin/glpromo-activate/';
                    action = 'Activate';
                }
                var feedbackId = $(this).attr('testimonial-id');
                url = url + feedbackId;
                // console.log(url);
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $('#enquiry-info-table').on('click', '.testimonial-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('testimonial-id');
                var destinationPath = BASE_URL + '/admin/smspricing-delete/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });

        

   
     $('#enquiry-info-table').on('click', '.ks-izi-modal-trigger1', function(event) {

            var testimonialId = $(this).attr('testimonial-id'); 
            $.ajax({
                    url: BASE_URL + '/admin/show-smspricing/' + testimonialId,
                    type: 'GET',
                    dataType: 'JSON',
                })
            .done(function(res) {
                if(res.status == "success") {
                        data = res.data;

                        $("[name='vchr_sms_pricing_route']").val(data.fk_int_sms_route);
                        $("[name='vchr_sms_pricing_rangefrom']").val(data.vchr_sms_pricing_rangefrom);
                        $("[name='vchr_sms_pricing_rangeto']").val(data.vchr_sms_pricing_rangeto);
                        $("[name='vchr_sms_pricing_rate_sms']").val(data.vchr_sms_pricing_rate_sms);
                        $('#pk_int_sms_pricing_id').val(data.pk_int_sms_pricing_id);

                        
                        $($('.ks-izi-modal-trigger1').data('target')).iziModal('open');
                        
                    }
            })
            .fail(function(err) {

            })
            .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });
     });
            
        
    </script>
@endpush
