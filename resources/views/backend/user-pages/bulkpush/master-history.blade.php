@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <style type="text/css">
        .val-error {
            color: red;
        }

    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Sms History</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.history-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table">
                    <table id="enquiry-info-table" class="table table-striped table-bordered" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Count(Credits)</th>

                            <th>Date and Time</th>
                            <th>Message</th>
                            <th></th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </main>


@endsection
@push('footer.script')
    <script type="text/javascript">

        $(document).ready(function () {
            BASE_URL = window.location.origin;

            $('.error').hide();
            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                autoWidth:true,
                processing:true,
                serverSide: true,
                pageLength: 25,
                lengthChange: true,
                lengthMenu: [25, 50, 75, 100],
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [
                    {
                    "width": "70px",
                    "targets": [0]
                    },
                    {
                        "width": "50px",
                        "targets": [0]
                    }, {
                        "width": "50px",
                        "targets": [1]
                    }, {
                        "width": "150px",
                        "targets": [2]
                    },
                ],
                "ajax": {
                    "url": BASE_URL + '/user/sms-history-data',
                    "dataSrc": "data.data"
                },
                // ajax: BASE_URL + '/user/sms-history-data',
                columns: [
                    // {data: 'slno', name: 'slno'},
                    // {data: 'credit', name: 'credit', orderable: false, searchable: false},
                    // {data: 'date', name: 'date'},
                    // {data: 'message', name: 'message'},

                    // {data: 'show', name: 'show'},
                    {
                        data: 'slno', name: 'slno', orderable: false, searchable: false
                    },
                    {
                        data: 'message_count', name: 'credit', orderable: false, searchable: false, 
                        fnCreatedCell: function (nTd, sData, oData, iRow, iCol) {
                            $(nTd).html(oData.message_count + ' [ ' + oData.credit_count + ' ]');
                        }
                    },
                    {
                        data: 'message_date', name: 'date', orderable: false, searchable: false
                    },
                    {
                        data: 'message_encoded', name: 'message', orderable: false, searchable: false, 
                        fnCreatedCell: function (nTd, sData, oData, iRow, iCol) {
                            $(nTd).html(`<div>
                                <div class="row">
                                    <div class="col-sm-12"><p>` + oData.message_encoded + `<p/></div>
                                </div> 
                                <div class="row">
                                    <div class="col-sm-3">SenderId:</div>
                                    <div class="col-sm-8">` + oData.sender_id + ` </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-3">Route:</div>
                                    <div class="col-sm-8">` + oData.routedata.vchr_sms_route + ` </div>
                                </div>
                                 <div class="row">
                                    <div class="col-sm-3">Message Via:</div>
                                    <div class="col-sm-8">` + oData.message_ids + ` </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-3">GLTrackId:</div>
                                    <div class="col-sm-8">` + oData.id + ` </div>
                                </div>
                           </div>`);
                        }
                    },
                    {
                        data: 'id', name: 'show', orderable: false, searchable: false, 
                        fnCreatedCell: function (nTd, sData, oData, iRow, iCol) {
                            $(nTd).html(`<div class="btn-group btn-collection btn-icon-group">
                            <a class="btn btn-sm btn-default view-enquiry-type" href="{{ url('user/sms-history')}}/`+oData.id+`" title="View" ><i class="fa fa-send-o r-5"></i></a>
                            
                        </div>`);
                        }
                    },

                ],

            });


        });
    </script>
@endpush
