<div class="modal fade" id="myModal-3" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true">
    <form id="Add">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h4 class="modal-title w-100 g-clr">Add Message Template</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body mx-3">
                    <div class="md-form mb-1">
                        <input type="hidden" name="pk_int_short_code_id">
                        <label for="default-input" class="form-control-label">SMS Template Name</label>
                        <input type="text" name="smstemplatetitle" class="form-control" placeholder="">
                        <span class="val-error smstemplatetitle" style="color: red"></span>
                    </div>
                    <div class="md-form mb-1">
                        <input type="hidden" name="pk_int_short_code_id">
                        <label for="default-input" class="form-control-label">SMS Template</label>
                        <textarea name="smstemplatebody" class="form-control" placeholder="" rows="4"></textarea>
                        <span class="val-error text_sms_template_description" style="color: red"></span>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                    <button class="btn btn-deep-orange demo-btn">Add</button>
                </div>
            </div>
        </div>
    </form>
</div>
