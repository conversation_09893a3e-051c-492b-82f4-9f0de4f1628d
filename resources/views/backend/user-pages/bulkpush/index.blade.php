@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <style type="text/css">
        .val-error {
            color: red;
        }

    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>BULK PUSH SMS</h5>
                </div>
                {{-- <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3 ks-izi-modal-trigger"  data-target="#ks-izi-modal-large1" id="addEnquiry" name="button" title="Click here to edit subscription messages">Add Enquiry</button>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add Enquiry </a>
                            </ul>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.message-sidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <section class="bulk-push-sec">
                    <div class="row">
                        <div class="col-lg-12">
                            <section class="panel">
                                <header class="panel-heading">
                                    Write & send / schedule new SMS
                                </header>
                                <div class="panel-body">
                                    <div class=" form">
                                        <form id="sendSms" enctype="multipart/form-data">
                                            <div class="cmxform form-horizontal ">
                                                <div class="form-group ">
                                                    <label for="sender" class="control-label col-lg-3">Sender ID
                                                        (required)</label>
                                                    <div class="col-lg-3">
                                                        <select class="form-control m-bot15" name="senderid"
                                                                id="senderid" required>
                                                            {{--  <option value="SMSSMS">SMSSMS</option>
                                                             <option selected value="Church">Church</option>
                                                             <option value="twewet">twewet</option> --}}
                                                        </select>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <a id="reload_sender" class="btn btn-success popovers"
                                                           data-original-title="Reload Sender ID"
                                                           data-content="Click this icon to reload all the sender IDs"
                                                           data-placement="top" data-trigger="hover"><i
                                                                    class="fa fa-refresh"></i></a>
                                                        <a href="#modal-senderid" data-toggle="modal"
                                                           class="btn btn-warning popovers"
                                                           data-original-title="Create Sender ID"
                                                           data-content="Click this icon to create a new sender ID"
                                                           data-placement="top" data-trigger="hover"><i
                                                                    class="fa fa-plus"></i></a>
                                                    </div>
                                                </div>
                                                <div class="form-group ">
                                                    <label for="contacts" class="control-label col-lg-3">Contacts
                                                        (required) 1/line</label>
                                                    <div class="col-lg-6 btn-gap">
                                                        <textarea rows="4" class="form-control " id="vchr_mobile"
                                                                  name="vchr_mobile"></textarea><br/>

                                                        <a id="download" href="{{url('files/phone-numbers.csv')}}"
                                                           class="btn btn-info popovers upload-btn"
                                                           data-original-title="Export numbers"
                                                           data-content="Download  sample .csv file for import numbers"
                                                           data-placement="top" data-trigger="hover"><i
                                                                    class="fa fa-download"></i></a>

                                                        <button type="button" id="import_num"
                                                                class="btn btn-secondary popovers upload-btn"
                                                                data-original-title="Import numbers"
                                                                data-content="Select .csv file for import numbers from first column (column A)."
                                                                data-placement="top" data-trigger="hover"><i
                                                                    class="fa fa-upload"></i>
                                                            <input type="file" id="csvFile" name="myfile" >
                                                        </button>
                                                        {{--  <a  id="import_num" href="#myModal-6"  data-toggle="modal" class="btn btn-secondary popovers upload-btn" data-original-title="Import numbers" data-content="Select .csv file for import numbers from first column (column A). Multiple files can be uploaded" data-placement="top" data-trigger="hover"><i class="fa fa-upload"></i></a> --}}
                                                        <a id="count_num" class="btn btn-success popovers"
                                                           data-original-title="Count numbers"
                                                           data-content="Click this icon to count all the numbers in 'Contacts'"
                                                           data-placement="top" data-trigger="hover"><i
                                                                    class="fa fa-tags"></i></a>
                                                        <a id="clear_contacts" class="btn btn-info popovers"
                                                           data-original-title="Clear numbers"
                                                           data-content="Click this icon to clear all the numbers from 'Contacts'"
                                                           data-placement="top" data-trigger="hover"><i
                                                                    class="fa fa-eraser"></i></a>
                                                        {{-- <button type="button" id="remove_dnd" class="btn btn-warning popovers" data-original-title="Remove DND" data-content="Click this icon to remove all the DND numbers from 'Contacts'" data-placement="top" data-trigger="hover"><i class="fa fa-mobile"></i></button> --}}
                                                        <a id="remove_dups" class="btn btn-primary popovers"
                                                           data-original-title="Remove duplicates & invalid"
                                                           data-content="Click this icon to remove all the invalid & duplicate numbers from 'Contacts'"
                                                           data-placement="top" data-trigger="hover"><i
                                                                    class="fa fa-minus"></i></a>
                                                    </div>
                                                </div>
                                                <div class="form-group ">
                                                    <label for="groups" class="control-label col-lg-3">Group(s)</label>
                                                    <div class="col-lg-6">
                                                        <textarea class="form-control " id="groups" name="groups"
                                                                  readonly required></textarea><br/>
                                                        <input type="hidden" name="int_mobile" id="pk_int_group_id"
                                                               class="form-control" value="">
                                                        <a href="#myModal-2" data-toggle="modal"
                                                           class="btn btn-success popovers"
                                                           data-original-title="Select group(s) from list"
                                                           data-content="Click this icon to select groups"
                                                           data-placement="top" data-trigger="hover"><i
                                                                    class="fa fa-group" id="selectGroup"></i></a>&nbsp;&nbsp;
                                                    </div>
                                                </div>
                                                <div class="form-group ">
                                                    <label for="route" class="control-label col-lg-3">Route</label>

                                              

                                                    <div class="col-lg-6">
                                                        @foreach($routes as $key => $route)
                                                            <input @if($key==0) checked="" @endif type="radio"
                                                                   name="route" id="rd_to_{{ $key }}"
                                                                   value="{{ $route->pk_int_sms_route_id }}"
                                                                   autocomplete="off"><label for="rd_to_{{ $key }}"
                                                                                             class="input-label"> {{ $route->vchr_sms_route }} </label>
                                                        @endforeach
                                                        {{-- <input tabindex="3" type="radio"  id="route" name="route" value="P" ><label class="input-label">PROMOTION</label>
                                                        <input tabindex="3" type="radio"  id="route" name="route" value="T" ><label class="input-label">TRANSACTION</label>                                      
                                                        <input tabindex="3" type="radio"  id="route" name="route" value="I" ><label class="input-label">ISD</label>    --}}
                                                    </div>
                                                </div>
                                                <div class="form-group ">
                                                    <label for="msgtype" class="control-label col-lg-3">Message
                                                        type</label>
                                                    <div class="col-lg-6">
                                                        {{-- <input type="radio" id="msgtype" name="msgtype" value="1" checked="checked" /> --}}
                                                        <input type="radio" class="msgtype" name="msgtype" value="0"
                                                               id="msgtype" checked="checked"
                                                               autocomplete="off">
                                                        <label class="input-label">Text</label>
                                                        {{-- <input type="radio" id="msgtype" name="msgtype" value="2" />
                                                        <label class="input-label">Flash</label> --}}
                                                        {{-- <input type="radio" id="msgtype" name="msgtype" value="3" /> --}}
                                                        <input type="radio" class="msgtype" name="msgtype" value="1"
                                                               id="msgtype" autocomplete="off">
                                                        <label class="input-label"> Unicode</label>
                                                        <div style="margin-top: -24px;margin-left: 170px;display: none;"
                                                             id='translControl'></div>
                                                        <div class="col-lg-6" id="language">

                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group ">
                                                    <label for="message" class="control-label col-lg-3">Content
                                                        (required)</label>
                                                    <div class="col-lg-6 btn-gap">

                                                        <div id="textarea-id">
                                                        </div>


                                                        <div>


                                                            <a href="#myModal-4" data-toggle="modal"
                                                               class="btn btn-success popovers"
                                                               data-original-title="Select template"
                                                               data-content="Click this icon to select message template"
                                                               data-placement="top" data-trigger="hover"><i
                                                                        class="fa fa-file"></i></a>&nbsp;&nbsp;
                                                            {{--   <a href="#myModal-3" data-toggle="modal"
                                                                 class="btn btn-success popovers"
                                                                 data-original-title="Create template"
                                                                 data-content="Click this icon to create new message template"
                                                                 data-placement="top" data-trigger="hover"><i
                                                                          class="fa fa-plus-circle"></i></a> --}}&nbsp;&nbsp;
                                                            <span id="chr_cnt"><strong><span class="ks-icon"
                                                                                             id="remaining">0</span></strong></span>
                                                            Character(s)&nbsp;&nbsp;<span id="sms_cnt"><strong><span
                                                                            class="ks-icon"
                                                                            id="messages">0</span></strong></span>
                                                            SMS(s)
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="form-group ">
                                                    <label for="signature"
                                                           class="control-label col-lg-3">Signature</label>
                                                    <div class="col-lg-6">
                                                        <input name="signaturename" type="checkbox" id="signaturename"
                                                               value=""/> Use Signature&nbsp;&nbsp;
                                                        <input name="signaturename1" type="hidden" id="signaturename1"
                                                               value="0"/>
                                                        <a id="btnsign" href="#myModal-5" data-toggle="modal"
                                                           class="btn btn-success popovers"
                                                           data-original-title="Add / Edit signature"
                                                           data-content="Click this icon to add / edit message signature"
                                                           data-placement="top" data-trigger="hover"
                                                           user-id="{{ Auth::user()->pk_int_user_id }}"><i
                                                                    class="fa fa-pencil"></i></a>
                                                    </div>
                                                </div>

                                                <div class="form-group ">
                                                    <label for="schedule_box"
                                                           class="control-label col-lg-3">Schedule</label>
                                                    <div class="col-lg-6">
                                                        <input name="schedule_box" type="checkbox" id="schedule_box"
                                                               value="0"/>
                                                        <input name="schedule_box1" type="hidden" id="schedule_box1"
                                                               value="0"/>
                                                        <span> Set Schedule time & Send later</span>
                                                        <div class="col-md-8">
                                                            <div id="schedule_area">
                                                                {{-- <input id="schdatetime" size="16" type="text" value="10 Jan 2019 - 15:54" readonly class="form_datetime form-control" style="cursor:pointer"> --}}

                                                                <input name="vchr_schedule" id="schdatetime"
                                                                       class="datetime form_datetime form-control"
                                                                       type="text" value=""
                                                                       placeholder="Select Date and Time" readonly>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <div class="col-lg-6 mrgn-lft-25">
                                                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                                        {{-- <button class="btn btn-second" id="btn_sch_sms" type="button">Schedule</button> --}}
                                                        {{-- <button class="btn btn-secondary" id="reset_values" type="button">Reset</button> --}}
                                                        <button class="main-round-btn btn-primary" id="btn_send_sms"
                                                                type="submit">
                                                            Send SMS
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>
    <!-- MODAL Forms -->
    <!------------ /Create Sender ID/  -------------->
    @include('backend.user-pages.bulkpush.create-sender-id-modal')
    <!-- Add group -->
    @include('backend.user-pages.bulkpush.add-group-modal')
    <!-- Add message template -->
    @include('backend.user-pages.bulkpush.add-group-modal')
    {{-- select the sms template --}}
    @include('backend.user-pages.bulkpush.sms-template-modal')
    <!-- Add / edit signature -->
    @include('backend.user-pages.bulkpush.add-signature-modal')
    <!-- Add / edit signature -->
    @include('backend.user-pages.bulkpush.edit-signature-modal')
@endsection
@push('footer.script')
    <script type="text/javascript">
        $('.popovers').popover();
    </script>
    <script type="text/javascript">

        // $('#btn_send_sms').attr('disable');

        function getSenderId() {
            BASE_URL = window.location.origin;


            $("#senderid").empty();
            //get user senderid
            $.ajax({
                url: BASE_URL + '/user/get-senderid',
                type: 'GET',
                dataType: 'JSON',
            }).done(function (res) {
                if (res.status === 'success') {
                    // console.log(res.data);
                    $("#senderid").append("<option value=''>Select SenderId</option>");
                    $.each(res.data, function (key, value) {
                        $("#senderid").append('<option value=' + value.id + '>' + value.name + '</option>');
                    });
                } else {
                    $("#senderid").append("<option value=''>No SENDERID found</option>");
                }
            }).fail(function () {
            }).always(function (com) {
            });
        }

        function new_change_to_radio(id) {
            // alert(id);
            if (id == 0) {
                $('#excelupload').hide();
                $('#textbox').show();
            } else if (id == 2) {
                $('#excelupload').show();
                $('#textbox').hide();
            } else {
                $('#excelupload').hide();
                $('#textbox').hide();
            }
        }

        $(document).ready(function () {


            BASE_URL ={!! json_encode(url('/')) !!};
            $('#textarea-id').html(`<textarea class="form-control" placeholder="Enter message here"
                                                                  rows="7" name="message" id="message"
                                                                  required></textarea>`);
            getSenderId();

           
            $(document).on('change', '#selectLanguage', function (event) {
                var languageType = document.getElementById("selectLanguage").value;
                alert(languageType);
                if (languageType == 'arabi') {
                    $('#textarea-id').html(`<textarea class="form-control" placeholder="Enter message here" onkeyup="arabicValue(message)"
                                                                  rows="7" name="message" id="message"
                                                                  required></textarea>`);

                }
                if (languageType == 'hindi') {
                    $('#textarea-id').html(`<textarea class="form-control"style="font-family: Arial, Helvetica, sans-serif;" placeholder="Enter message here"
                                                                  rows="7" name="message" id="message"
                                                                  required></textarea>`);
                }
                if (languageType == 'malayalam') {
                    $('#textarea-id').html(`<textarea class="form-control" placeholder="Enter message here"
                                                                  rows="7" name="message" id="message"
                                                                  required></textarea>`);
                }


            });


            $('#schdatetime').html('');
            $('#schdatetime').hide('');

            $('#flash-notis').html('');
            $('#flash-notis').hide('');

            $('#enquiry-info-table').DataTable({
                lengthChange: 1,
                // buttons: ["print", "excel", "pdf"],
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                // scrollX: true,
                searching: true,
                orderable: false,
                ajax: BASE_URL + '/user/getgroupname',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'show', name: 'show', orderable: false, searchable: false},
                    {data: 'vchr_name', name: 'vchr_name'},
                ],
            });
            var yas;

            function arabicValue(txt) {
                yas = txt.value;
                yas = yas.replace(/`/g, "ذ");
                yas = yas.replace(/0/g, "۰");
                yas = yas.replace(/1/g, "۱");
                yas = yas.replace(/2/g, "۲");
                yas = yas.replace(/3/g, "۳");
                yas = yas.replace(/4/g, "٤");
                yas = yas.replace(/5/g, "۵");
                yas = yas.replace(/6/g, "٦");
                yas = yas.replace(/7/g, "۷");
                yas = yas.replace(/8/g, "۸");
                yas = yas.replace(/9/g, "۹");
                yas = yas.replace(/0/g, "۰");
                yas = yas.replace(/q/g, "ض");
                yas = yas.replace(/w/g, "ص");
                yas = yas.replace(/e/g, "ث");
                yas = yas.replace(/r/g, "ق");
                yas = yas.replace(/t/g, "ف");
                yas = yas.replace(/y/g, "غ");
                yas = yas.replace(/u/g, "ع");
                yas = yas.replace(/i/g, "ه");
                yas = yas.replace(/o/g, "خ");
                yas = yas.replace(/p/g, "ح");
                yas = yas.replace(/\[/g, "ج");
                yas = yas.replace(/\]/g, "د");
                yas = yas.replace(/a/g, "ش");
                yas = yas.replace(/s/g, "س");
                yas = yas.replace(/d/g, "ي");
                yas = yas.replace(/f/g, "ب");
                yas = yas.replace(/g/g, "ل");
                yas = yas.replace(/h/g, "ا");
                yas = yas.replace(/j/g, "ت");
                yas = yas.replace(/k/g, "ن");
                yas = yas.replace(/l/g, "م");
                yas = yas.replace(/\;/g, "ك");
                yas = yas.replace(/\'/g, "ط");
                yas = yas.replace(/z/g, "ئ");
                yas = yas.replace(/x/g, "ء");
                yas = yas.replace(/c/g, "ؤ");
                yas = yas.replace(/v/g, "ر");
                yas = yas.replace(/b/g, "لا");
                yas = yas.replace(/n/g, "ى");
                yas = yas.replace(/m/g, "ة");
                yas = yas.replace(/\,/g, "و");
                yas = yas.replace(/\./g, "ز");
                yas = yas.replace(/\//g, "ظ");
                yas = yas.replace(/~/g, " ّ");
                yas = yas.replace(/Q/g, "َ");
                yas = yas.replace(/W/g, "ً");
                yas = yas.replace(/E/g, "ُ");
                yas = yas.replace(/R/g, "ٌ");
                yas = yas.replace(/T/g, "لإ");
                yas = yas.replace(/Y/g, "إ");
                yas = yas.replace(/U/g, "‘");
                yas = yas.replace(/I/g, "÷");
                yas = yas.replace(/O/g, "×");
                yas = yas.replace(/P/g, "؛");
                yas = yas.replace(/A/g, "ِ");
                yas = yas.replace(/S/g, "ٍ");
                yas = yas.replace(/G/g, "لأ");
                yas = yas.replace(/H/g, "أ");
                yas = yas.replace(/J/g, "ـ");
                yas = yas.replace(/K/g, "،");
                yas = yas.replace(/L/g, "/");
                yas = yas.replace(/Z/g, "~");
                yas = yas.replace(/X/g, "ْ");
                yas = yas.replace(/B/g, "لآ");
                yas = yas.replace(/N/g, "آ");
                yas = yas.replace(/M/g, "’");
                yas = yas.replace(/\?/g, "؟");
                txt.value = yas;
            }

            /*Get User message template*/
            $('#enquiry-table').DataTable({
                scrollX: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                ajax: BASE_URL + '/user/get-usersmstemplate',
                columns: [
                    {data: 'vchr_sms_template_title', name: 'vchr_sms_template_title'},
                    {data: 'sms_template_code', name: 'sms_template_code'},
                    {data: 'show', name: 'show', orderable: false, searchable: false}
                ],
            });

            $(document).on('click', '#reload_sender', function (event) {
                getSenderId();
            });
            $('.error').hide();

            $(document).on('change', '#excelFile', function (event) {
                //   alert("hello");

            });
            var charcount;
            var smscount;
            var msgtype = $("input[name='msgtype']:checked").val();
            if (msgtype === "0") {
                var limit = 160;
            } else {
                var limit = 70;
            }

            $(document).on('change', '.msgtype', function (event) {
                var msgtype = $("input[name='msgtype']:checked").val();
                //alert(msgtype);
                var chars = $('#message').val().length;
                //alert(chars);
                count(msgtype, chars)
            });

            function count(msgtype, chars) {
                if (msgtype == "0") {
                    if (chars > 306) {
                        limit = 153;
                    } else {
                        limit = 160;
                    }
                } else {
                    if (chars > 134) {
                        limit = 67;
                    } else {
                        limit = 70;
                    }
                }

                var messages = Math.ceil(chars / limit),
                    remaining = messages * limit - (chars % (messages * limit) || messages * limit);

                $('#remaining').text(chars);
                $('#messages').text(messages);
            }

            $('#message').keyup(function () {
                var msgtype = $("input[name='msgtype']:checked").val();
                //alert(msgtype);
                var chars = $('#message').val().length;
                count(msgtype, chars);
            });


            $('#message').keyup(function () {
                var str = $('#message').val();
                hasUnicode(str);
            });
            $(document).on('change', '.msgtype', function (event) {
                var str = $('#message').val();
                hasUnicode(str);
            });


            $(document).on('submit', '#Add', function (event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                $.ajax({
                    url: BASE_URL + '/user/smstemplate',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status === 'success') {
                        $("#Add")[0].reset();
                        $('#myModal-3').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                }).always(function (com) {
                    $('#enquiry-table').DataTable().ajax.reload(null, false);
                });
            });

            $(document).on('submit', '#AddSignature', function (event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                $.ajax({
                    url: BASE_URL + '/user/add-signature',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status === 'success') {
                        $("#Add")[0].reset();
                        $('#myModal-5').modal('toggle');
                        // alertify.success(res.msg);
                        iziToast.success({
                            message: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                })
            });

            $(document).on('submit', '#Addfile', function (event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                $.ajax({
                    url: BASE_URL + '/user/add-file',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status === 'success') {
                        $("#Add")[0].reset();
                        $('#myModal-5').modal('toggle');
                        // alertify.success(res.msg);
                        iziToast.success({
                            message: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                })
            });

            $(document).on('click', '#btnsign', function (event) {
                userId = $(this).attr('user-id');
                $.ajax({
                    url: BASE_URL + '/user/viewsignature/' + userId,
                    type: 'GET',
                    dataType: 'JSON',
                }).done(function (res) {
                    if (res.status === "success") {
                        data = res.datas[0];
                        $("[name='signaturename']").val(data.vchr_name);
                        $("[name='signature_name']").val(data.vchr_name);
                    }
                }).fail(function () {
                }).always(function () {
                });

            });

            $('#enquiry-info-table').on('click', '.default-senderid', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('senderid-id');
                var destinationPath = BASE_URL + '/user/default-senderid/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Dafault sender id',
                    content: 'Are you sure want to make sender id as default ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'GET',
                                }).done(function (res) {
                                    if (res.status === 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                }).fail(function (err) {
                                }).always(function (com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $(document).on('submit', '#testimonialEdit', function (event) {
                event.preventDefault();
                $('.val-error').html('');
                $('.val-error').hide();
                // $('').on('click', function (e) {
                var id = $('#pk_int_sender_id').val();
                $.ajax({
                    url: BASE_URL + '/user/senderid/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status === 'success') {
                        $($('.ks-izi-modal-trigger1').data('target')).iziModal('close');
                        swal(res.msg);
                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                }).always(function (com) {
                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                });
            });

            $('#enquiry-info-table').on('click', '.senderid-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('senderid-id');
                var destinationPath = BASE_URL + '/user/senderid/' + feedbackId;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                }).done(function (res) {
                                    if (res.status === 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                }).fail(function (err) {
                                }).always(function (com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });
        $('#enquiry-info-table').on('click', '.ks-izi-modal-trigger1', function (event) {

            var testimonialId = $(this).attr('senderid-id');
            $.ajax({
                url: BASE_URL + '/user/senderid/' + testimonialId,
                type: 'GET',
                dataType: 'JSON',
            }).done(function (res) {
                if (res.status === "success") {
                    data = res.data;
                    $("[name='sendername']").val(data.vchr_sender_id_name);
                    $("[name='senderid']").val(data.vchr_sender_id);
                    $('#pk_int_sender_id').val(data.pk_int_sender_id);
                    $($('.ks-izi-modal-trigger1').data('target')).iziModal('open');
                }
            }).fail(function (err) {
            }).always(function (com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);
            });
        });

        $(document).on('submit', '#sendSms', function (event) {
            event.preventDefault();
            $("#btn_send_sms").attr("disabled", 'disabled');

            var msgtype = $("input[name='msgtype']:checked").val();
            var str = $('#message').val();
            for (var i = 0; i < str.length; i++) {
                if (str.charCodeAt(i) > 127) {

                    //return(msgtype);
                    if (msgtype == "0") {
                        alert("Unicode or unsupported character detected, Please select unicode to send unicode messages.");

                        return false;
                    } else {
                        //return "unicode";
                    }
                } else {
                    //return (msgtype);
                    //return true;
                }
            }
            $('.val-error').html('');
            $('.val-error').hide();
            $.ajax({
                url: BASE_URL + '/user/send-sms',
                type: 'POST',
                dataType: 'JSON',
                data: new FormData(this),
                contentType: false,
                processData: false,
            }).done(function (res) {
                if (res.status === 'success') {
                    $("#sendSms")[0].reset();
                    $('#schdatetime').hide('');
                    $("[name='schedule_box1']").val('0');
                    $("[name='signaturename1']").val('0');
                    $.alert({
                        title: 'Success',
                        type: 'green',
                        content: res.msg,
                    });
                } else {
                    $.alert({
                        title: 'Failed',
                        //    type: 'red',
                        content: res.msg,
                    });
                    //$("#sendSms")[0].reset();
                    // iziToast.error({
                    //     message: res.msg,
                    // });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
            }).fail(function () {
            }).always(function (com) {
            });
        });

        function hasUnicode(str) {
            var msgtype = $("input[name='msgtype']:checked").val();
            for (var i = 0; i < str.length; i++) {
                if (str.charCodeAt(i) > 127) {

                    //return(msgtype);
                    if (msgtype == "0") {
                        alert("Unicode or unsupported character detected, Please select unicode to send unicode messages.");

                        return false;
                    } else {
                        //return "unicode";
                    }
                } else {
                    //return (msgtype);
                    //return true;
                }
            }
        }

        function groupDisplay() {
            var groupName = [];
            var groupId = [];
            $.each($("input[name='contact_detail[]']:checked"), function () {
                groupName.push($(this).attr('data-name'));
            });
            $.each($("input[name='contact_detail[]']:checked"), function () {
                groupId.push($(this).val());
            });
            document.getElementById("groups").value = groupName.join(",");
            document.getElementById("pk_int_group_id").value = groupId.join(",");
        }

        function tempDisplay() {
            var TempName = [];
            $.each($("input[name='temp_detail[]']:checked"), function () {
                TempName.push($(this).attr('data-name'));
            });
            document.getElementById("message").value = TempName.join(",");
            var message = $('#message').val();
        }

        $(document).on('click', '#schedule_box', function (event) {
            $('#schdatetime').toggle();
        });

        $('#schedule_box').click(function () {
            if ($('input[name="schedule_box"]').is(':checked')) {
                $("[name='schedule_box1']").val('1');
            } else {
                $("[name='schedule_box1']").val('0');
            }
        });

        $('#signaturename').click(function () {
            if ($('input[name="signaturename"]').is(':checked')) {
                $("[name='signaturename1']").val('1');

            } else {
                $("[name='signaturename1']").val('0');
            }
        });

        $(document).on('click', '#count_num', function (event) {
            var number = [];
            number = document.getElementById('vchr_mobile').value;
            event.preventDefault();
            $.ajax({
                url: BASE_URL + '/user/check-count',
                type: 'get',
                dataType: 'JSON',
                data: {data: number},
            }).done(function (res) {
                if (res.status === 'success') {
                    iziToast.success({
                        message: "TOTAL NUMBER CONTACTS : " + res.datas,
                    });
                } else {
                    $.each(res.msg, function (index, val) {
                        //iterate through array or object
                        $('.' + index).html(val);
                        $('.' + index).show();
                    });
                }
            })
        });

        $(document).on('click', '#clear_contacts', function (event) {
            // $('#contacts').empty();
            document.getElementById("vchr_mobile").value = "";
        });

        $(document).on('click', '#remove_dups', function (event) {
            var number = [];
            number = document.getElementById('vchr_mobile').value;
            event.preventDefault();
            $.ajax({
                url: BASE_URL + '/user/remove-same',
                type: 'get',
                dataType: 'JSON',
                data: {data: number},
            }).done(function (res) {
                document.getElementById("vchr_mobile").value = "";
                if (res.status === 'success') {
                    document.getElementById("vchr_mobile").value = res.datas;
                    // alertify.success("TOTAL NUMBER CONTACTS : "+res.datas);
                } else {
                    $.each(res.msg, function (index, val) {
                        //iterate through array or object
                        $('.' + index).html(val);
                        $('.' + index).show();
                    });
                }
            })
        });
    </script>

    <script type="text/javascript">
        $(function () {
            $('.datetime').datetimepicker();
        });
        $(document).on('change', '#csvFile', function (e) {
            var ext = $("input#csvFile").val().split(".").pop().toLowerCase();
            if ($.inArray(ext, ["csv"]) === -1) {
                iziToast.error({
                    message: "Upload CSV",
                    position: 'topRight',
                });
                return false;
            }
            if (e.target.files !== undefined) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    var csvval = e.target.result;
                    $('#vchr_mobile').val(csvval);
                };
                reader.readAsText(e.target.files.item(0));
            }
            return false;
        });


        $(document).on('submit', '#testimonialAdd', function (event) {
            $('#add-senderid').html("Processing");
            $('#add-senderid').prop('disabled', true);
            event.preventDefault();
            $('.error').html('');
            $('.error').hide();
            $.ajax({
                url: BASE_URL + '/user/senderid',
                type: 'POST',
                dataType: 'JSON',
                data: new FormData(this),
                contentType: false,
                processData: false,
            })
                .done(function (res) {
                    if (res.status == 'success') {
                        $('#add-senderid').html("Add");
                        $('#add-senderid').prop('disabled', false);
                        $("#testimonialAdd")[0].reset();
                        $('#modal-senderid').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else {
                        $('#add-senderid').html("Add");
                        $('#add-senderid').prop('disabled', false);
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            console.log(val);
                            console.log(index);
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                        // swal(res.msg.vchr_enquiry_type[0]);
                    }
                })
                .fail(function () {
                })

        });

    </script>
@endpush
