@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <style type="text/css">
        .val-error {
            color: red;
        }

    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Sms History</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.smssidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <table id="enquiry-info-table" class="table table-striped table-bordered" width="100%">
                    <thead>
                    <tr>
                        <th>Sl No</th>
                        <th>Date & Time</th>
                        <th>SenderId</th>
                        <th>Route</th>
                        <th>Mobile No</th>
                        <th>Message</th>
                        <th>Status</th>
                        <th>API Response</th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
    </main>


@endsection
@push('footer.script')
    <script type="text/javascript">

        $(document).ready(function () {
            BASE_URL ={!! json_encode(url('/')) !!};

            $('.error').hide();
            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [
                    {"width": "70px", "targets": [0]},
                    {"width": "150px", "targets": [1]},
                    {"width": "40px", "targets": [3]}
                ],
                ajax: BASE_URL + '/user/push-history',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'date', name: 'date'},
                    {data: 'senderid', name: 'senderid'},
                    {data: 'route', name: 'route'},
                    {data: 'mobno', name: 'mobno'},
                    {data: 'message', name: 'message'},
                    {data: 'status', name: 'status', orderable: false, searchable: false},
                    {data: 'api_response', name: 'api_response', orderable: false, searchable: false}
                ],

            });
        });
    </script>
@endpush
