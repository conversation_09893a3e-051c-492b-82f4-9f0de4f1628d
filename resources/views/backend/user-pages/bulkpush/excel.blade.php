<?php
header("Content-type: application/octet-stream");
header("Content-Disposition: attachment; filename=SMS_REPORT.xls");
header("Pragma: no-cache");
header("Expires: 0");
?>

<h3>SMS Report</h3>

From Date:{{ $from_date }}<br>
To Date:{{ $to_date }}<br>

<table border='1' width="100%">
  <tr>
    <td><b>Sl.No<b></td>
    <td><b>Date<b></td>
    <td><b>GL Track Id<b></td>
    <td><b>Mobile No</b></th>
    <td><b>SenderId<b></td>
    <td><b>Route</b></th>
    <td><b>Message Via</b></th>
    <td><b>Credit Deducted</b></td>
    <td><b>AlertId</b></td>
    <td><b>TrackId</b></td>
    <td><b>Status</b></td>
   
  </tr>
    <?php
      foreach ($sms as $key => $value)
    {
    ?>
  <tr>
    <td>{{ ++$key}}</td>
    <td>{{ $value->created_at }}</td>
    <td>{{ $value->gl_track_id }}</td>
    <td>{{ $value->vchr_mobile }}</td>
    <td>{{ $value->vchr_sender_id }}</td>
    <td>{{ $value->vchr_route }}</td>
    <td>{{ $value->message_via }}</td>
    <td>{{ $value->message_count }}</td>
    <td>{{ $value->vchr_alertid }}</td>
    <td>{{ $value->vchr_trackid }}</td>
    <td>{{ $value->vchr_status }}</td>
   
    
  </tr>
  <?php 
  } 

?>
</table>




