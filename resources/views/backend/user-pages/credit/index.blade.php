@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Credit Request</h5>
                </div>
                <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">REQUEST CREDIT</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.smssidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <div class="col-md-12" style="overflow: auto">
                <table id="enquiry-info-table" class="table table-striped table-bordered" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Request Sms count</th>
                            <th>Requested On</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
            </div>
        </div>
    </main>

    <!------------ /add modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="testimonialAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Credit</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                             <label for="default-input" class="form-control-label">SMS COUNT</label>
                               <input type="text" name="vchr_sms_request_count" class="form-control" placeholder="Enter SMS count" >
                                <span class="val-error vchr_sms_request_count"></span>         
                        </div>
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <div class="modal-footer d-flex justify-content-center" id="short_code_save">
                            <button class="btn btn-primary" type="submit" >
                              <span class="la la-check ks-icon"></span>
                              <span class="ks-text">Add</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection
@push('footer.script')
    <script type="text/javascript">
    
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!};

            $('.error').hide();

            // $('.ks-izi-modal-trigger').on('click', function (e) {
            //     $($(this).data('target')).iziModal('open');

            //     $(".test").on('click', function (e) {
            //         //alert('hi');
            //     });
               
                // $('#ks-izi-modal-large').modal('toggle');

            // });

            $('#enquiry-info-table').DataTable({
                scrollX: true,
               paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                        "width": "170px",
                        "targets": [0, 1]
                    },
                    {
                        "width": "40px",
                        "targets": [3]
                    }
                ],
                ajax: BASE_URL + '/user/get-user-credit',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },

                    {
                        data: 'vchr_sms_request_count',
                        name: 'vchr_sms_request_count'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at'
                    },
                    
                   
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
                
            });

            $(document).on('submit', '#testimonialAdd', function(event) {
                event.preventDefault();
                // $('').on('click', function (e) {
               $('.val-error').html('');
              $('.val-error').hide();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/user-credit',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                      $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });

             $('#enquiry-info-table').on('click', '.senderid-delete', function (event) {
                event.preventDefault();
                var feedbackId = $(this).attr('senderid-id');
                var destinationPath = BASE_URL + '/user/senderid/' + feedbackId;
                // console.log(destinationPath);
                // $("#delete-confirm-modal").modal('show');
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            });

    </script>
@endpush