@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <main class="main-wrapper">

        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Dashboard</h5>
                </div>
                <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.sms-sidebar')
            <div class="content-section p-3 bg-white">
                <div class="container-fluid">
                <!--  /Your content goes here/ -->
                <section class="dashboard-sec main-dash">
                    <div class="row justify-content-center">
                        @if(count(\App\Subscription\SmsCount::smsCreditBalance()) > 0)
                            @foreach(\App\Subscription\SmsCount::smsCreditBalance() as $key=> $balance)
                                <div class="col-lg-4 col-md-4 pl-2 pr-2">
                                    <div class="@if($key==0)top-block g-bg-blue-drk @elseif($key==1) top-block g-bg-blue-drk @elseif($key==2) top-block g-bg-blue-drk @else top-block g-bg-purpule @endif ">
                                        <p>{{ $balance->vchr_sms_route}}</p>
                                        <h5>{{ $balance->total_count + $balance->credit- $balance->used_sms_count}}</h5>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            @foreach([1=>'OTP',2=>'Transactional SMS',3=>'Promotional SMS',4 =>'International SMS',5=>'Scrub'] as $key=> $value)
                                <div class="col-lg-4 col-md-4 pl-2 pr-2">
                                    <div class="@if($key==0)top-block g-bg-blue-drk @elseif($key==1) top-block g-bg-blue-drk @elseif($key==2) top-block g-bg-blue-drk @else top-block g-bg-purpule @endif ">
                                        <p>{{ $value}}</p>
                                        <h5>0</h5>
                                    </div>
                                </div>
                            @endforeach
                        @endif

                    </div>

                    <!-- <div class="row">
                        <div class="col-lg-12">
                            <h6>Your Plan Details</h6>
                        </div>
                        <div class="col-lg-3 col-md-3 col-6 pl-2 pr-2">
                            <div class="card dash-stats">
                                <h5 class="dash-text-green-w ">ACTIVE</h5>
                                <p>Account Status</p>
                                <div class="dash-stats-btm-bar"></div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-6 pl-2 pr-2">
                            <div class="card dash-stats">
                                <h5 class="dash-text-red">PREMIUM</h5>
                                <p>Active Plans</p>
                                <div class="dash-stats-btm-bar"></div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-6 pl-2 pr-2">
                            <div class="card dash-stats">
                                <h5 class="dash-text-violet">2019 dec 31</h5>
                                <p>Plan Validity</p>
                                <div class="dash-stats-btm-bar"></div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-3 col-6 pl-2 pr-2">
                            <div class="card dash-stats">
                                <h5 class="dash-text-drk-violet">5999</h5>
                                <p>Plan Amount</p>
                                <div class="dash-stats-btm-bar"></div>
                            </div>
                        </div>
                    </div> -->
            </div>
        </div>
        </div>
    </main>

@endsection
@push('footer.script')
    <script type="text/javascript">


    </script>

@endpush

