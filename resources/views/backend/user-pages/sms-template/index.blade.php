@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
<style type="text/css">
    .val-error
    {
        color:red;
    }
 </style>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>SMS Template</h5>
                </div>
                <div class="">
                    <div class="task-nav mb-2">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="main-round-btn bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add SMS Template </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.sms-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table">
                <table id="enquiry-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Created by</th>
                            <th>SMS Template Title</th>
                            <th>SMS Template</th>
                            <th width="10%">Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
            </div>
        </div>
    </main>

    <!------------ /add modal/  -------------->

    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="Add">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">SMS Template</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">SMS Template Name</label>
                           <input type="text" name="vchr_sms_template_title" class="form-control" placeholder="" >
                            <span class="error vchr_sms_template_title" style="color: red"></span>          
                        </div>
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">SMS Template</label>
                            <textarea name="sms_template_code" class="form-control" placeholder="" rows="4"></textarea>
                            <span class="error sms_template_code" style="color: red"></span>
                        </div>             
                    </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button id="add-template" class="btn btn-deep-orange demo-btn">Add</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="Edit">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update SMS Template</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">SMS Template title</label>
                                <input type="text" name="vchr_sms_template_title" class="form-control" placeholder="">
                                 <span class="error vchr_sms_template_title"></span>        
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">SMS Template </label>
                                <textarea rows="4" type="text" name="sms_template_code" class="form-control" placeholder="" id="sms_template_code">
                                </textarea>
                                <span class="error sms_template_code"></span>
                                <input type="hidden" name="_method"  value="PUT" class="form-control" placeholder="" >
                                <input type="hidden" name="fk_int_sms_template_id" class="form-control" placeholder=""  id="fk_int_sms_template_id">
                            </div>             
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button id="update-template" class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection 
@push('footer.script')
<script type="text/javascript">
    $(document).ready(function() 
    {
        BASE_URL ={!! json_encode(url('/')) !!}
        $('#enquiry-table').DataTable({
           scrollX: true,
           paging: true,
            language: {
                searchPlaceholder: 'Search',
                sSearch: '',
                lengthMenu: '_MENU_ page',
            },
            columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1]
                },
                // {
                //     "width": "40px",
                //     "targets": [3]
                // }
            ],
            ajax: BASE_URL + '/user/get-user-smstemplates',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },
                    {
                        data:'vchr_user_name',
                        name:'vchr_user_name'
                    },

                    {
                        data: 'vchr_sms_template_title',
                        name: 'vchr_sms_template_title'
                    },
                    {

                        data: 'sms_template_code',
                        name: 'sms_template_code'
                    },
                    
                   
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
        });

        $(document).on('submit', '#Add', function(event) {
            $('#add-template').html("Processing");
            $('#add-template').prop('disabled', true);
            event.preventDefault();
            $('.val-error').html('');
            $('.val-error').hide();
                $.ajax({
                    url: BASE_URL + '/user/smstemplates',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                        $('#add-template').html("Add");
                        $('#add-template').prop('disabled', false);
                    // $($('.ks-izi-modal-trigger').data('target')).iziModal('close');
                    // swal(res.msg);
                    $("#Add")[0].reset();
                        $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $('#add-template').html("Add");
                    $('#add-template').prop('disabled', false);
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-table').DataTable().ajax.reload(null, false);
            });
            });

        $('.error').hide();

        

        $('.ks-izi-modal-trigger').on('click', function (e) {
           $('.error').html('');
            $('.error').hide();

        });

        $('#enquiry-table').on('click', '.ks-izi-modal-trigger1', function(event) {
            $('.error').html('');
            $('.error').hide();
          var  id = $(this).attr('smstemplate-id');
                        //  alert(id);
                editId=id;
                $.ajax({
                    url: BASE_URL + '/user/smstemplates/' + id,
                    type: 'GET',
                    dataType: 'JSON',
                })
                .done(function(res) {

                    if(res.status == "success") {
                        data = res.data;
                        $("[name='vchr_sms_template_title']").val(data.vchr_sms_template_title);
                        $("[name='sms_template_code']").val(data.sms_template_code);
                        $('#fk_int_sms_template_id').val(data.fk_int_sms_template_id);
                        $("#Add")[0].reset();
                        $('.error').html('');
                        $('.error').hide();
                    }
                })
                .fail(function() {
                })
                .always(function() {
                });

        });


        $(document).on('submit', '#Edit', function(event) {
            $('#update-template').html("Processing");
            $('#update-template').prop('disabled', true);
            event.preventDefault();
            $('.error').html('');
            $('.error').hide();
            // var  fffid = $(this).attr('smstemplate-id');
                        //  alert(editId);
            // var id=$('fk_int_sms_template_id').val();
            //     alert(id);
                $.ajax({
                    url: BASE_URL + '/user/Proposal/'+editId,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                        $('#update-template').html("Update");
                        $('#update-template').prop('disabled', false);
                         $('#ks-izi-modal-large1').modal('toggle');
                        $.alert({
                             title: 'Success',
                             type: 'green',
                             content: res.msg,
                         });
                    }else{
                        $('#update-template').html("Update");
                        $('#update-template').prop('disabled', false);
                        $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    }
                })
                .fail(function() {
                })
                .always(function(com) {
                    $('#enquiry-table').DataTable().ajax.reload(null, false);

                });

        });


        $('#enquiry-table').on('click', '.smstemplate-delete', function (event) 
        {
          event.preventDefault();
            var feedbackId = $(this).attr('smstemplate-id');
            var destinationPath = BASE_URL + '/user/smstemplates/' + feedbackId;
            // alert(destinationPath);
            $.confirm({
                title: 'Deletion',
                content: 'Are you sure you want to delete ?',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                   'confirm': {
                       text: 'Proceed',
                       btnClass: 'btn-info',
                       action: function () {
                            $.ajax({
                                url: destinationPath,
                                type: 'DELETE',
                            })
                            .done(function(res) {
                                if(res.status == 'success') {
                                    $.alert({
                                        title: 'Success',
                                        type: 'green',
                                        content: res.msg,
                                    });
                                } else {
                                    $.alert({
                                        title: 'Failed',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                }
                            })
                            .fail(function(err) {
                            })
                            .always(function(com) {
                                $('#enquiry-table').DataTable().ajax.reload(null, false);
                            });
                        }
                    },
                    cancel: function () {
                        $.alert('Operation <strong>canceled</strong>');
                    }
                }
            });
        });

       
    });
</script>
@endpush
