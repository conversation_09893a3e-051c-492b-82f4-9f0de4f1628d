@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Api template</h5>
                </div>
                <div class="mb-2">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="main-round-btn bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add Api template </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.message-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table">
                <table id="enquiry-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Api template Name</th>
                            <th width="30%">Api template</th>
                            <th>Default Api template</th>
                            <th width="10%">Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
            </div>
        </div>
    </main>

    <!------------ /add modal/  -------------->

    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="Add">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Api Template</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Api Template Name</label>
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <input type="text" name="vchr_api_template_title" id="orangeForm-pass" class="form-control validate"> 
                            <div class="error vchr_api_template_title ">
                                <span class="muted"></span>
                            </div>             
                        </div>
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Api template</label>
                            <textarea id="orangeForm-pass" name="text_api_template_description" class="form-control validate"></textarea>
                            <div class="error text_api_template_description">
                                <span class="muted"></span>
                            </div>             
                        </div>
                       
                            <div class="modal-footer d-flex justify-content-center">
                                <button class="btn btn-deep-orange demo-btn">Submit</button>
                            </div>
                    </div>
                </div>
            </div>
        </form>
    </div>



    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="Edit">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update Api Template</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Api template Name</label>
                            <input type="text" name="apitemplatetitle" id="orangeForm-pass" class="form-control validate"> 
                            <div class="error apitemplatetitle ">
                                <span class="muted"></span>
                            </div>             
                        </div>
                        <div class="md-form mb-1">
                            <label data-error="wrong" data-success="right" for="orangeForm-pass">Api template</label>
                            <textarea name="apitemplatebody" id="orangeForm-pass" class="form-control validate"></textarea> 
                            <input type="hidden" name="_method"  value="PUT" class="form-control" placeholder="" >
                            <input type="hidden" name="pk_int_api_template_id" id="pk_int_api_template_id" class="form-control" placeholder="">
                            <div class="error apitemplatebody">
                                <span class="muted"></span>
                            </div>             
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>






@endsection 
@push('footer.script')
<script type="text/javascript">
    $(document).ready(function() 
    {
        BASE_URL ={!! json_encode(url('/')) !!}
        $('#enquiry-table').DataTable({
           scrollX: true,
           paging: true,
            language: {
                searchPlaceholder: 'Search',
                sSearch: '',
                lengthMenu: '_MENU_ page',
            },
            columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1]
                },
                {
                    "width": "40px",
                    "targets": [4]
                }
            ],
            ajax: BASE_URL + '/user/get-user-apitemplate',
            columns: [
            {
                data: 'slno',
                name: 'slno'
            },



            {
                data: 'vchr_api_template_title',
                name: 'vchr_api_template_title'
            },
            {

                data: 'text_api_template_description',
                name: 'text_api_template_description'
            },
            {
                data: 'default',
                name: 'default'
            },
            
            {
                data: 'show',
                name: 'show',
                orderable: false,
                searchable: false
            }
            ],
        });
      
        

        $(document).on('submit', '#Add', function(event) 
        {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                $.ajax({
                    url: BASE_URL + '/user/apitemplate',
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                        $("#Add")[0].reset();
                        $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                }else{
                    $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    // swal(res.msg.vchr_enquiry_type[0]);
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-table').DataTable().ajax.reload(null, false);

            });
        });

        $('.error').hide();

        

        $('.ks-izi-modal-trigger').on('click', function (e) {
           $('.error').html('');
            $('.error').hide();

        });

        $('#enquiry-table').on('click', '.ks-izi-modal-trigger1', function(event) {
            $('.error').html('');
            $('.error').hide();
            id = $(this).attr('apitemplate-id');
               
                $.ajax({
                    url: BASE_URL + '/user/apitemplate/' + id,
                    type: 'GET',
                    dataType: 'JSON',
                })
                .done(function(res) {

                    if(res.status == "success") {
                        data = res.data;
                        $("[name='apitemplatetitle']").val(data.vchr_api_template_title);
                        $("[name='apitemplatebody']").val(data.text_api_template_description);
                        $('#pk_int_api_template_id').val(data.pk_int_api_template_id);
                        $("#Add")[0].reset();
                        $('.error').html('');
                        $('.error').hide();
                    }
                })
                .fail(function() {
                })
                .always(function() {
                });

        });


        $(document).on('submit', '#Edit', function(event) {
            event.preventDefault();
            $('.val-error').html('');
            $('.val-error').hide();
            var id=$('#pk_int_api_template_id').val();
                $.ajax({
                    url: BASE_URL + '/user/apitemplate/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                         $('#ks-izi-modal-large1').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    }else{
                        $.each(res.msg, function(index, val) {
                        //iterate through array or object
                        console.log(val);
                        console.log(index);
                        $('.'+index).html(val);
                        $('.'+index).show();
                    });
                    }
                })
                .fail(function() {
                })
                .always(function(com) {
                    $('#enquiry-table').DataTable().ajax.reload(null, false);

                });

        });


        $('#enquiry-table').on('click', '.apitemplate-delete', function (event) 
        {
            event.preventDefault();
            var feedbackId = $(this).attr('apitemplate-id');
                //alert(feedbackId);
            var destinationPath = BASE_URL + '/user/apitemplate/' + feedbackId;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                       'confirm': {
                           text: 'Proceed',
                           btnClass: 'btn-info',
                           action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                .done(function(res) {
                                    if(res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                .fail(function(err) {
                                })
                                .always(function(com) {
                                    $('#enquiry-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
        });

        $('#enquiry-table').on('click', '.default-apitemplate', function (event) {
            event.preventDefault();
            var feedbackId = $(this).attr('apitemplate-id');
            var destinationPath = BASE_URL + '/user/default-apitemplate/' + feedbackId;
            // console.log(destinationPath);
            // $("#delete-confirm-modal").modal('show');
            $.confirm({
                title: 'Dafault api template',
                content: 'Are you sure want to make api template as default ?',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                   'confirm': {
                       text: 'Proceed',
                       btnClass: 'btn-info',
                       action: function () {
                            $.ajax({
                                url: destinationPath,
                                type: 'GET',
                            })
                            .done(function(res) {
                                if(res.status == 'success') {
                                    $.alert({
                                        title: 'Success',
                                        type: 'green',
                                        content: res.msg,
                                    });
                                } else {
                                    $.alert({
                                        title: 'Failed',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                }
                            })
                            .fail(function(err) {
                            })
                            .always(function(com) {
                                $('#enquiry-table').DataTable().ajax.reload(null, false);
                            });
                        }
                    },
                    cancel: function () {
                        $.alert('Operation <strong>canceled</strong>');
                    }
                }
            });
        });

       
    });





</script>
@endpush
