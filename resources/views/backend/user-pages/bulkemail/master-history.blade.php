@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <style type="text/css">
        .val-error {
            color: red;
        }

    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://unpkg.com/popper.js/dist/umd/popper.min.js"></script>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Email History</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.bulkemailsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <table id="enquiry-info-table" class="table table-striped table-bordered" width="100%">
                    <thead>
                    <tr>
                        <th>Sl No</th>
                        <th>Count(Credits)</th>

                        <th>Date and Time</th>
                        <th>Details</th>
                        <th></th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
    </main>


@endsection
@push('footer.script')
    <script type="text/javascript">

        $(document).ready(function () {
            BASE_URL = window.location.origin;

            $('.error').hide();
            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "70px",
                    "targets": [0]
                },
                    {
                        "width": "50px",
                        "targets": [0]
                    }, {
                        "width": "50px",
                        "targets": [1]
                    }, {
                        "width": "150px",
                        "targets": [2]
                    },
                ],
                ajax: BASE_URL + '/user/email-history-data',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'credit', name: 'credit', orderable: false, searchable: false},
                    {data: 'date', name: 'date'},
                    {data: 'message', name: 'message'},

                    {data: 'show', name: 'show'},
                ],

            });


        });
    </script>
@endpush
