@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
<main class="main-wrapper">
    <div class="task-panel">
        <div class="row justify-content-between ">
            <div class="col-lg-6 d-flex row-wrap align-items-center">
                <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                <h5>Bulk Email</h5>
            </div>
             @if(session('flash_notification'))
                    <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
                @endif
            <div class="col-lg-6 text-right">
                <div class="task-nav">
                    <div class="dropdown-navigation">
                        <ul>
                            {{-- <button type="button" class="btn btn-sm btn-primary ml-auto mr-3 my-3 ks-izi-modal-trigger"  data-target="#ks-izi-modal-large1" id="addEnquiry" name="button" title="Click here to edit subscription messages">Add Enquiry</button> --}}
                            {{-- <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger"
                                    data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add
                                    Enquiry </a> --}}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layout-wrapper">
        <!--   / Side menu included /  -->
        @include ('backend.layout.bulkemailsidebar')
        <div class="content-section p-3 bg-white">
            <!--  /Your content goes here/ -->
            <div class="row m-lg-0">
                <div class="col-lg-8">
                    <div class="row px-lg-2">
                        <!-- <div class="col-lg-12mb-3 mt-2 ">
                            <div class="d-lg-flex justify-content-between align-items-center">
                                <label class="medium col-md-3">Sender ID*</label>
                                <select class=" form-control select2 col-md-9" style="width: 100%; height: 100%">
                                    <option>--Select--</option>
                                    <option>Message API</option>
                                    <option>Balance API</option>
                                    <option>DLR API</option>
                                </select>
                            </div>
                        </div> -->
                        <form id="sendSms" method="post" action="{{ url('user/send-bulk-email') }}" enctype="multipart/form-data">
                        <div class="col-lg-12 ">
                            <h6 class="font-17"> From</h6>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <div class="input-group-text font-13">Sender ID*</div>
                                </div>
                                <select name="sender_id" class="country form-control" required id="fieldId" required>
                                    @foreach($senderids as $senderid)
                                    <option value="{{ $senderid->senderid }}">{{ $senderid->senderid }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-12">
                            <h6 class="font-17"> To </h6>
                            <div class=" row">
                               {{--  <label class="medium col-md-2">Send to*</label>
                                <div class="col-md-9 d-lg-flex align-items-center">
                                    <div class="radio pr-3">
                                        <label><input type="radio" value="0" class="type" name="type" checked>Email(s)</label>
                                    </div>
                                    <div class="radio">
                                        <label><input type="radio" value="1" class="type" name="type">Quick Push</label>
                                    </div>
                                </div> --}}
                                <div class="col-lg-12 emails">
                                    <div class="mb-3">
                                        <label class="">Email/Emails*(Only Emails are allowed)</label>
                                        <textarea name="email_address" class="form-control email" id="exampleFormControlTextarea1"
                                            rows="6" required></textarea>
                                      <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    </div>
                                </div>
                                <div class="col-lg-9 mb-3 bulkemails">
                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input" id="validatedCustomFile">
                                        <label class="custom-file-label" for="validatedCustomFile">Choose
                                            file...</label>
                                        <div class="invalid-feedback">Example invalid custom file feedback</div>
                                    </div>
                                </div>
                                <div class="col-lg-3 mb-3 bulkemails">
                                  <div class="custom-file">
                                    <a id="download" href="{{url('files/email-address.csv')}}" class="btn btn-info popovers upload-btn" data-original-title="Export numbers"
                                    data-content="Download  sample .csv file for import numbers"
                                    data-placement="top" data-trigger="hover"><i
                                    class="fa fa-download"></i></a>
                                  </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-12 ">
                            <h6 class="font-17"> Reply To</h6>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend" style="height: 40px;">
                                    <div class="input-group-text font-13">Reply To*</div>
                                </div>
                                <input name="replyto" type="email" class=" country form-control" required id="fieldId"></input>
                            </div>
                        </div>

                        <div class="col-lg-12 ">
                            <h6 class="font-17"> Subject </h6>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend" style="height: 40px;">
                                    <div class="input-group-text font-13"> Subject</div>
                                </div>
                                <input name="subject" pattern="\b\w+\b(?:.*?\b\w+\b){2}" placeholder="Minimum of 3 words should be present" class=" country form-control" required id="fieldId"></input>
                            </div>
                        </div>

                        <div class="col-md-12 mt-3">
                          {{--   <div class="summernote-1"></div> --}}
                          <textarea name="message" class="summernote-1" id="exampleFormControlTextarea1"
                                rows="6"></textarea>
                        </div>

                        <div class="col-lg-12 bdr-btm mb-3">
                            <div class="input-group mb-3">
                                <div class="input-group-prepend" style="height: 40px;">
                                    <div class="input-group-text font-13">Copyright ©{{ date('Y') }}</div>
                                </div>
                                <input name="copyright" class="country form-control" required id="fieldId"></input>
                                <div class="input-group-append">
                                        <div class="input-group-text font-13">*.All Rights Reserved.</div>
                                    </div>
                            </div>
                            <p>*You should enter your domain name/Company name</p>
                        </div>

                        <div >
                                {{-- <a href="#"><i class="fa fa-paper-plane mr-1"></i> Send Email </a> --}}
                                <button class="main-round-btn m-auto" type="submit"><i class="fa fa-paper-plane mr-1"></i>Send Email</button>
                            </div>
                        </form>

                    </div>
                </div>
                <div class="col-lg-4 card p-3 " style="border:1px solid #efefef; border-radius:10px;">
                    <h5 class="mt-1 mb-3 font-18">Instructions</h5>
                    <div>
                        <p>*For multiple Emails, use "ENTER" to seperate emails. Don't use comma, semicolon etc.<br>
                            Example:<br>
                            <EMAIL><br><EMAIL><br><EMAIL>
                        </p>
                        <p>*In Quick Push,uploaded file should only contain Emails.<br>
                            Example:<br>
                            <EMAIL><br><EMAIL><br><EMAIL>
                        </p>
                        <p>*Please do not copy and paste any content from any pages.</p>
                        <p>*Subjects should Be Minimum 3 Words.</p>
                       {{--  <p>*Please note: For Sending BULK EMAIL, please contact support team.</p>
                        <p>*Please note: Default SenderID is for testing purpose only</p>
                        <p>*Please note: Minimum of 2 emailids is need to send email vis quick push</p> --}}
                    </div>
                </div>

                
            </div>
        </div>
    </div>
</main>


@endsection
@push('footer.script')
<script>
    $('.summernote-1').summernote({
        height: '215',

    });

    // $('.bulkemails').hide();
    // $(document).on('change', '.type', function (event) {
    //     var type =  $("input[name='type']:checked").val();
    //     if(type==0)
    //     {
    //       $('.bulkemails').hide();
    //       $('.emails').show();
    //     }
    //     else
    //     {
    //       $('.bulkemails').show();
    //       $('.emails').hide();
    //     }
       
    // });

    $(document).on('change', '#validatedCustomFile', function (e) {
        var ext = $("input#validatedCustomFile").val().split(".").pop().toLowerCase();
        if ($.inArray(ext, ["csv"]) === -1) {
            iziToast.error({
                message: "Upload CSV",
                position: 'topRight',
            });
            return false;
        }
        if (e.target.files !== undefined) {
            var reader = new FileReader();
            reader.onload = function (e) {
                var csvval = e.target.result;
                $('.email').val(csvval);
            };
            reader.readAsText(e.target.files.item(0));
        }
        return false;
    });
</script>
@endpush