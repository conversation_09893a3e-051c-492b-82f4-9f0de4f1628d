@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')

    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>View Email</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.bulkemailsidebar')


            <div class="content-section p-3 bg-white">
                <div class="row ">
                    <div class="col-sm-12">
                        <div class="alert bg-light-grey">
                            <div class="row">
                                <div class="col-sm-2">
                                    <strong> Subject : </strong>
                                </div>
                                <div class="col-sm-8">
                                    {{$master_sms_data->subject}}
                                </div>
                            </div>
                              <div class="row">
                                <div class="col-sm-2">
                                    <strong> Reply To : </strong>
                                </div>
                                <div class="col-sm-8">
                                    {{$master_sms_data->replyto}}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-2">
                                    <strong> Sender Id : </strong>
                                </div>
                                <div class="col-sm-8">
                                    {{$master_sms_data->sender_id}}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-2">
                                    <strong> GL Track ID : </strong>
                                </div>
                                <div class="col-sm-8">
                                    {{$master_sms_data->id}}
                                </div>
                            </div>
                           
                            <div class="row">
                                <div class="col-sm-2">
                                    <strong> Date And Time : </strong>
                                </div>
                                <div class="col-sm-8">
                                    {{$master_sms_data->date}}
                                </div>
                            </div>
                           {{--  <div class="row">
                                <div class="col-sm-2">
                                    <strong> Message Count : </strong>
                                </div>
                                <div class="col-sm-8">
                                    {{$master_sms_data->email_count}}
                                </div>
                            </div> --}}
                            <div class="row">
                                <div class="col-sm-2">
                                    <strong> Count : </strong>
                                </div>
                                <div class="col-sm-8">
                                    {{$master_sms_data->email_count}}
                                </div>
                            </div>
                         
                         {{--    <div class="row">
                                <div class="col-sm-2">
                                    <strong>Sent Count : </strong>
                                </div>
                                <div class="col-sm-10">
                                    <p id="sentCount"></p>
                                </div>
                            </div> --}}
                           {{--  <div class="row">
                                <div class="col-sm-2">
                                    <strong>Delivered Count : </strong>
                                </div>
                                <div class="col-sm-10">
                                    <p id="deliveredCount"></p>
                                </div>
                            </div> --}}
                        </div>
                    </div>

                </div>

                {!! $master_sms_data->message !!}
                <p><center>©{{ $year }} {{ $master_sms_data->copyright }}. All Rights Reserved.</center></p>
               
            </div>
        </div>
    </main>


@endsection
@push('footer.script')
    <script type="text/javascript">


        $(document).ready(function () {
            BASE_URL = window.location.origin;
            loadSentCount();

            $('.error').hide();
            {{--$('#sms-history-table').DataTable({--}}
            {{--    scrollX: true,--}}
            {{--    paging: true,--}}
            {{--    language: {--}}
            {{--        searchPlaceholder: 'Search',--}}
            {{--        sSearch: '',--}}
            {{--        lengthMenu: '_MENU_ page',--}}
            {{--    },--}}
            {{--    columnDefs: [{--}}
            {{--        "width": "70px",--}}
            {{--        "targets": [0]--}}
            {{--    },--}}
            {{--    ],--}}
            {{--    // ajax: BASE_URL + '/user/push-history',--}}

            {{--    ajax: BASE_URL + '/user/push-history-data/' + '{{$master_sms_data->id}}',--}}
            {{--    columns: [--}}
            {{--        {data: 'slno', name: 'slno'},--}}
            {{--        {data: 'mobno', name: 'mobno'},--}}
            {{--        {data: 'message_via', name: 'message_via'},--}}
            {{--        {data: 'status', name: 'status', orderable: false, searchable: false},--}}
            {{--        {data: 'send_sms', name: 'send_sms', orderable: false, searchable: false},--}}
            {{--        //   {data: 'api_response', name: 'api_response', orderable: false, searchable: false}--}}
            {{--    ],--}}

            {{--});--}}

            $('#sms-history-table').on('click', '.send-sms', function (event) {
                event.preventDefault();
                var smsId = $(this).attr('data-sms-id');
                var destinationPath = BASE_URL + '/user/send-single-sms/' + smsId;
                $.confirm({
                    title: 'Send SMS',
                    content: 'Are you sure want to send sms  ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'GET',
                                }).done(function (res) {
                                    if (res.status === true) {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                }).fail(function (err) {
                                }).always(function (com) {
                                    $('#sms-history-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>Cancelled</strong>');
                        }
                    }
                });
            });
        });

        window.setInterval(function () {
            loadSentCount();
        }, 5000);

        function loadSentCount() {
            $.ajax({
                url: BASE_URL + '/user/get-sms-send-count/' + '{{$master_sms_data->id}}',
                type: 'get',
                dataType: 'JSON',
            }).done(function (res) {
                if (res.status === true) {
                    $('#deliveredCount').text(res.delivered_count);
                    $('#sentCount').text(res.sent_count);
                }
            });
        }
    </script>
@endpush
