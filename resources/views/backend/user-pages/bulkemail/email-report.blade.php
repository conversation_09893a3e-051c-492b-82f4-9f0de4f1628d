@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
 <style type="text/css">
    .val-error
    {
        color:red;
    }
 </style>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Reports-SMS</h5>
                </div>
                {{-- <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" data-toggle="modal" data-target="#ks-izi-modal-large" id="addEnquiry" href="#">Add SMS Template </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.bulkemailsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <div class="row">

                <div class="col-sm-12 py-3">
                    <form method="post" action="{{ url('user/bulkemail-reports') }}"  id="Add" enctype="multipart/form-data">

                       {{--  <div class="form-group row">
                            <input type="hidden" name="pk_int_short_code_id">
                              <label for="default-input" class="col-sm-2 form-control-label">Select User</label>
                           <div class="col-sm-9">
                                <select name="users" class="form-control select2" data-placeholder="Select" id="code" required>
                                    <option value="">Select User</option>
                                    @foreach($users as $key => $user)
                                    <option value="{{ $user->pk_int_user_id }}">{{ $user->vchr_user_name }}</option>
                                    @endforeach 
                                </select> 
                             <span class="val-error vchr_promocode"></span>
                         </div>
                        </div> --}}
                        <div class="form-group row">
                              <label for="default-input" class="col-sm-2 form-control-label">From Date</label>
                            <div class="col-sm-9">
                                <input type="text" name="fromdate" class="form-control date" value="" placeholder="From Date" required>
                                <input type="hidden" name="users" class="form-control" value="{{ $users }}" placeholder="From Date" required>
                                <span class="val-error vchr_promocode"></span>
                            </div>
                        </div>

                        <div class="form-group row">
                              <label for="default-input" class="col-sm-2 form-control-label">To Date</label>
                            <div class="col-sm-9">
                               <input type="text" name="todate" class="form-control date1" value="" placeholder="To Date" required>
                                <span class="val-error vchr_promocode"></span>
                            </div>
                        </div>
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                     <div class="form-group row" id="short_code_save">
                        <div class="col-sm-4 offset-sm-4 center">
                            <button class="btn btn-primary" type="submit" >
                                <span class="la la-check ks-icon"></span>
                                <span class="ks-text">Submit</span>
                            </button>
                        </div>
                    </div>
                </form> 
            </div>
        </div>
            </div>
        </div>
    </main>

@endsection 
@push('footer.script')
<script type="text/javascript">
    $(function ()
    {
        $('.date').datepicker({
                    dateFormat: 'yy-mm-dd',
                    autoclose: true,
                });
    });

    $(function ()
    {
        $('.date1').datepicker({
                    dateFormat: 'yy-mm-dd',
                    autoclose: true,
                });
    });
              
</script>
@endpush
