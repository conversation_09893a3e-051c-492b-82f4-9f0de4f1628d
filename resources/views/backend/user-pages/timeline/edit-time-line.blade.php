@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <!-- <link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.11/summernote-bs4.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.11/summernote-bs4.js"></script> -->
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.0/jquery.min.js"></script>

    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Timeline</h5>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            <input type="hidden" value="{{$enquiry->pk_int_enquiry_id}}" id="enqId">
            @include ('backend.layout.crmsidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <section class="tl-new-top-sec">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                @if($enquiryFollowup)
                                 <!----------------------Note ------------------->
                                    @if($enquiryFollowup->log_type==\App\BackendModel\EnquiryFollowup::TYPE_NOTE)
                                        <form action="{{url('user/update-timeline-note/'.$enquiryFollowup->id)}}"
                                              method="POST">
                                            {{csrf_field()}}

                                            <div class="card-header text-white bg-secondary">Edit Note</div>
                                            <div class="card-body">
                                                <div class="form-group">
                                                <textarea class="form-control summernote-1" name="note">
                                                    {!! $enquiryFollowup->note !!}
                                                </textarea>
                                                </div>
                                                <div class="col-md-8">
                                                    <div class="btn-holder">
                                                        <button class="main-round-btn" id="btn_update_note"
                                                                type="submit"><i class="fa fa-save"></i>Update
                                                        </button>
                                                       {{--  <button class="main-round-btn red-btn" id="reset_values"
                                                                type="button"><i class="fa  fa-trash-o"></i>Discard
                                                        </button> --}}
                                                        <a class="main-round-btn red-btn" href="{{ url('user/enquiry-timeline'.'/'.$enquiry->pk_int_enquiry_id) }}" ><i class="fa fa-arrow-circle-left"></i>Back</a>
                                                    </div>
                                                </div>

                                            </div>
                                        </form>
                                        <!----------------------Task ------------------->
                                    @elseif($enquiryFollowup->log_type==\App\BackendModel\EnquiryFollowup::TYPE_TASK)
                                            <form action="{{url('user/update-timeline-task/'.$enquiryFollowup->id)}}"
                                                     method="POST">
                                                   {{csrf_field()}}

                                                <div class="card-header text-white bg-secondary">Edit Task</div>
                                                   <div class="card-body">
                                                        <div class="row">
                                                            <div class="col-md-4">
                                                                <label>Task</label>
                                                                <input value="{{$enquiryFollowup->name }}" class="form-control " id="task-name" name="name"
                                                                placeholder="Enter your task"></input>
                                                                <span  style="font-size: 11px;" id="task-name-error"   class="text-danger"></span>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <label>Date</label>
                                                                <div class="input-group with-addon-icon-left">
                                                                    <input type="text" value="{{$enquiryFollowup->date}}"id="datepicker-2" class="form-control datepicker"
                                                                    placeholder="Choose Date" name="date">
                                                                        <span class="input-group-append">
                                                                            <span class="input-group-text">
                                                                            <i class="fa fa-calendar"></i>
                                                                            </span>
                                                                        </span>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                 <label>Time</label>
                                                                <div class="input-group with-addon-icon-left">
                                                                    <input type="text" name="time" id="time-2" class="form-control time" value="{{$enquiryFollowup->time}}"
                                                                           placeholder="Time">
                                                                    <span class="input-group-append">
                                                                              <span class="input-group-text">
                                                                                <i class="fa fa-clock-o"></i>
                                                                              </span>
                                                                            </span>
                                                                </div>
                                                                <span  style="font-size: 11px;" id="time-2-error" class="text-danger"></span>
                                                            </div>
                                                        </div>
                                                        <div class="form-group">
                                                            <textarea name="description" class="form-control" rows="8">{!! $enquiryFollowup->note !!}</textarea>
                                                        </div>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                             <label>Type</label>
                                                            <select class=" form-control select2"
                                                                    style="width: 100%; height: 100%" id="task-type" name="task">
                                                                    @foreach (\App\TaskCategory::whereNull('vendor_id')->orWhere('vendor_id',0)->orWhere('vendor_id',\App\User::getVendorId())->get() as $category)
                                                                        <option value="{{$category->id}}">{{$category->name}}</option>
                                                                    @endforeach
                                                            </select>
                                                            <span  style="font-size: 11px;" id="task-type-error" class="text-danger"></span>
                                                        </div>
                                                        <div class="col-md-4">
                                                             <label>Assigned To</label>
                                                            <select class=" form-control select2"
                                                                    style="width: 100%; height: 100%" id="assigned-to" name="assigned_to">
                                                                <option value="">Assigned To</option>
                                                                @foreach($staffId as $staff)
                                                                <option value="{{ $staff->pk_int_user_id }}" @if($staff->pk_int_user_id==$enquiryFollowup->assigned_to) selected @endif>{{ $staff->vchr_user_name }}</option>
                                                                @endforeach
                                                            </select>
                                                            <span style="font-size: 11px;" id="assigned-to-error" class="text-danger"></span>
                                                        </div>
                                                        <div class="col-md-4">
                                                             <label>Email Reminder</label>
                                                            <select class=" form-control select2"
                                                                    style="width: 100%; height: 100%" id="reminder" name="reminder">
                                                                <option value="">Select Email Reminder</option>
                                                                <option value="0" @if($enquiryFollowup->reminder==-1) selected @endif>No reminder</option>
                                                                <option value="1"
                                                                        @if($enquiryFollowup->reminder==0) selected @endif>
                                                                    The day of
                                                                </option>
                                                                <option value="2"
                                                                        @if($enquiryFollowup->reminder==1) selected @endif>
                                                                    The day before
                                                                </option>
                                                                <option value="8"
                                                                        @if($enquiryFollowup->reminder==7) selected @endif>
                                                                    The week before
                                                                </option>
                                                            </select>
                                                            <span  style="font-size: 11px;" id="reminder-error" class="text-danger"></span>
                                                    </div>
                                                        <div class="col-md-8">
                                                           <div class="btn-holder">
                                                               <button class="main-round-btn" id="btn_update_task"
                                                                       type="submit"><i class="fa fa-save"></i>Update
                                                               </button>
                                                              {{--  <button class="main-round-btn red-btn" id="reset_values"
                                                                       type="button"><i class="fa  fa-trash-o"></i>Discard
                                                               </button> --}}
                                                                <a class="main-round-btn red-btn" href="{{ url('user/enquiry-timeline'.'/'.$enquiry->pk_int_enquiry_id) }}" ><i class="fa fa-arrow-circle-left"></i>Back</a>
                                                            </div>
                                                        </div>

                                                    </div>
                                            </form>

                                                <!----------------------Schedule ------------------->
                                            @elseif($enquiryFollowup->log_type==\App\BackendModel\EnquiryFollowup::TYPE_SCHEDULE)
                                                @if($enquiry->next_follow_up)
                                              
                                                <form action="{{url('user/next-follow-up/update-timeline-schedule/'.$enquiryFollowup->id)}}"
                                                             method="POST">
                                                @else
                                                <form action="{{url('user/update-timeline-schedule/'.$enquiryFollowup->id)}}"
                                                             method="POST">
                                                @endif
                                                     {{csrf_field()}}
                                                     @if($enquiry->next_follow_up)
                                                     <div class="card-header text-white bg-secondary">Edit next follow up schedule</div>
                                                     @else
                                                     <div class="card-header text-white bg-secondary">Edit Schedule</div>
                                                     @endif
                                                           <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-md-4">
                                                                        <label>Task</label>
                                                                        <input value="{{$enquiryFollowup->name }}" class="form-control " id="task-name" name="name"
                                                                        placeholder="Enter your task"></input>
                                                                        <span  style="font-size: 11px;" id="task-name-error"   class="text-danger"></span>
                                                                    </div>
                                                                    @if($enquiry->next_follow_up)
                                                                    <div class="col-md-4">
                                                                        <label>Date</label>
                                                                        <div class="input-group with-addon-icon-left">
                                                                            <input type="text" value="{{$enquiryFollowup->date}}"id="datepicker-2" class="form-control datepicker"
                                                                            placeholder="Choose Date" name="date">
                                                                                <span class="input-group-append">
                                                                                    <span class="input-group-text">
                                                                                    <i class="fa fa-calendar"></i>
                                                                                    </span>
                                                                                </span>
                                                                        </div>
                                                                    </div>
                                                                    @else
                                                                    <div class="col-md-3">
                                                                        <label>Date</label>
                                                                        <div class="input-group with-addon-icon-left">
                                                                            <input type="text" value="{{$enquiryFollowup->date}}"id="datepicker-2" class="form-control datepicker"
                                                                            placeholder="Choose Date" name="date">
                                                                                <span class="input-group-append">
                                                                                    <span class="input-group-text">
                                                                                    <i class="fa fa-calendar"></i>
                                                                                    </span>
                                                                                </span>
                                                                        </div>
                                                                    </div>
                                                                    @endif
                                                                    @if($enquiry->next_follow_up)
                                                                    <div class="col-md-4">
                                                                         <label>Time</label>
                                                                        <div class="input-group with-addon-icon-left">
                                                                         @if($enquiryFollowup->time == '12:00 AM')
                                                                         <input type="text" name="time" id="time-2" class="form-control time" value=""
                                                                                   placeholder="Time">
                                                                         @else
                                                                         <input type="text" name="time" id="time-2" class="form-control time" value="{{$enquiryFollowup->time}}"
                                                                                   placeholder="Time">
                                                                         @endif
                                                                            
                                                                            <span class="input-group-append">
                                                                                      <span class="input-group-text">
                                                                                        <i class="fa fa-clock-o"></i>
                                                                                      </span>
                                                                                    </span>
                                                                        </div>
                                                                        <span  style="font-size: 11px;" id="time-2-error" class="text-danger"></span>
                                                                    </div>
                                                                    @else
                                                                    <div class="col-md-2">
                                                                         <label>Time</label>
                                                                        <div class="input-group with-addon-icon-left">
                                                                            <input type="text" name="time" id="time-2" class="form-control time" value="{{$enquiryFollowup->time}}"
                                                                                   placeholder="Time">
                                                                            <span class="input-group-append">
                                                                                      <span class="input-group-text">
                                                                                        <i class="fa fa-clock-o"></i>
                                                                                      </span>
                                                                                    </span>
                                                                        </div>
                                                                        <span  style="font-size: 11px;" id="time-2-error" class="text-danger"></span>
                                                                    </div>
                                                                    @endif
                                                                    @if($enquiry->next_follow_up == NULL)
                                                                    <div class="col-md-3">
                                                                        <label>Duration</label>
                                                                        <select name="duration" class=" form-control select2" style="width: 100%; height: 100%" id="duration">
                                                                            <option value="15 Minutes"
                                                                                @if($enquiryFollowup->duration=="15 Minutes") selected @endif>
                                                                            15 Minutes
                                                                        </option>
                                                                        <option value="30 Minutes"
                                                                                @if($enquiryFollowup->duration=="30 Minutes") selected @endif>
                                                                            30 Minutes
                                                                        </option>
                                                                        <option value="45 Minutes"
                                                                                @if($enquiryFollowup->duration=="45 Minutes") selected @endif>
                                                                            45 Minutes
                                                                        </option>
                                                                        <option value="1 Hour"
                                                                                @if($enquiryFollowup->duration=="1 Hour") selected @endif>
                                                                            1 Hour
                                                                        </option>
                                                                        </select>
                                                                        <span  style="font-size: 11px;" id="task-name-error"   class="text-danger"></span>
                                                                    </div>
                                                                    @endif
                                                                </div>
                                                                <div class="form-group">
                                                                    <label>Remarks</label>
                                                                    <textarea name="note" class="form-control summernote-1" name="note">
                                                                   {!! $enquiryFollowup->note !!}
                                                                    </textarea>
                                                                </div>
                                                            
                                                                <div class="col-md-8">
                                                                   <div class="btn-holder">
                                                                       <button class="main-round-btn" id="btn_update_task"
                                                                               type="submit"><i class="fa fa-save"></i>Update
                                                                       </button>
                                                                      {{--  <button class="main-round-btn red-btn" id="reset_values"
                                                                               type="button"><i class="fa  fa-trash-o"></i>Discard
                                                                       </button> --}}
                                                                       <a class="main-round-btn red-btn" href="{{ url('user/enquiry-timeline'.'/'.$enquiry->pk_int_enquiry_id) }}" ><i class="fa fa-arrow-circle-left"></i>Back</a>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                    </form>

                                                <!----------------------Logged Call------------------->
                                            @elseif($enquiryFollowup->log_type==\App\BackendModel\EnquiryFollowup:: TYPE_LOG_CALL)
                                                    <form action="{{url('user/update-timeline-log-call/'.$enquiryFollowup->id)}}"
                                                             method="POST">
                                                           {{csrf_field()}}

                                                        <div class="card-header text-white bg-secondary">Edit Log a Call</div>
                                                           <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-md-4">
                                                                        <label>Outcome</label>
                                                                        <select class="form-control select2" name="response" id="log-outcome"
                                                                                style="width: 100%; height: 100%">
                                                                        <option value="No Answer" @if($enquiryFollowup->response=="No Answer")
                                                                        selected @endif>No Answer
                                                                        </option>
                                                                        <option value="Busy" @if($enquiryFollowup->response=="Busy")
                                                                        selected @endif>Busy
                                                                        </option>
                                                                        <option value="Wrong Number"
                                                                                @if($enquiryFollowup->response=="Wrong Number") selected
                                                                                @endif>Wrong Number
                                                                        </option>
                                                                        <option value="Left Live Message"
                                                                                @if($enquiryFollowup->response=="Left Live Message") selected
                                                                                @endif>Left Live Message
                                                                        </option>
                                                                        <option value="Left Voicecall"
                                                                                @if($enquiryFollowup->response=="Left Voicecall") selected
                                                                                @endif>Left Voicecall
                                                                        </option>
                                                                        <option value="Connected"
                                                                                @if($enquiryFollowup->response=="Connected") selected
                                                                                @endif>Connected
                                                                        </option>
                                                                        </select>
                                                                        <span  style="font-size: 11px;" id="task-name-error"   class="text-danger"></span>
                                                                    </div>
                                                                    <div class="col-md-3">
                                                                        <label>Date</label>
                                                                        <div class="input-group with-addon-icon-left">
                                                                            <input type="text" value="{{$enquiryFollowup->date}}"id="datepicker-2" class="form-control datepicker"
                                                                            placeholder="Choose Date" name="date">
                                                                                <span class="input-group-append">
                                                                                    <span class="input-group-text">
                                                                                    <i class="fa fa-calendar"></i>
                                                                                    </span>
                                                                                </span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-2">
                                                                         <label>Time</label>
                                                                        <div class="input-group with-addon-icon-left">
                                                                            <input type="text" name="time" id="time-2" class="form-control time" value="{{$enquiryFollowup->time}}"
                                                                                   placeholder="Time">
                                                                            <span class="input-group-append">
                                                                                      <span class="input-group-text">
                                                                                        <i class="fa fa-clock-o"></i>
                                                                                      </span>
                                                                                    </span>
                                                                        </div>
                                                                        <span  style="font-size: 11px;" id="time-2-error" class="text-danger"></span>
                                                                    </div>
                                                                    
                                                                </div>
                                                                <div class="form-group">
                                                                    <textarea name="note" class="form-control summernote-1" name="note">
                                                                   {!! $enquiryFollowup->note !!}
                                                                    </textarea>
                                                                </div>
                                                            
                                                                <div class="col-md-8">
                                                                   <div class="btn-holder">
                                                                       <button class="main-round-btn" id="btn_update_task"
                                                                               type="submit"><i class="fa fa-save"></i>Update
                                                                       </button>
                                                                      {{--  <button class="main-round-btn red-btn" id="reset_values"
                                                                               type="button"><i class="fa  fa-trash-o"></i>Discard
                                                                       </button> --}}
                                                                       <a class="main-round-btn red-btn" href="{{ url('user/enquiry-timeline'.'/'.$enquiry->pk_int_enquiry_id) }}" ><i class="fa fa-arrow-circle-left"></i>Back</a>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                    </form>

                                            <!----------------------Logged Email------------------->
                                                    @elseif($enquiryFollowup->log_type==\App\BackendModel\EnquiryFollowup:: TYPE_LOG_EMAIL)
                                                            <form action="{{url('user/update-timeline-log-email/'.$enquiryFollowup->id)}}"
                                                                     method="POST">
                                                                   {{csrf_field()}}

                                                                <div class="card-header text-white bg-secondary">Edit Log a Email</div>
                                                                   <div class="card-body">
                                                                        <div class="row">
                                                                            
                                                                            <div class="col-md-3 mb-3">
                                                                                <label>Date</label>
                                                                                <div class="input-group with-addon-icon-left">
                                                                                    <input type="text" value="{{$enquiryFollowup->date}}"id="datepicker-2" class="form-control datepicker"
                                                                                    placeholder="Choose Date" name="date">
                                                                                        <span class="input-group-append">
                                                                                            <span class="input-group-text">
                                                                                            <i class="fa fa-calendar"></i>
                                                                                            </span>
                                                                                        </span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-2">
                                                                                 <label>Time</label>
                                                                                <div class="input-group with-addon-icon-left">
                                                                                    <input type="text" name="time" id="time-2" class="form-control time" value="{{$enquiryFollowup->time}}"
                                                                                           placeholder="Time">
                                                                                    <span class="input-group-append">
                                                                                              <span class="input-group-text">
                                                                                                <i class="fa fa-clock-o"></i>
                                                                                              </span>
                                                                                            </span>
                                                                                </div>
                                                                                <span  style="font-size: 11px;" id="time-2-error" class="text-danger"></span>
                                                                            </div>
                                                                            
                                                                        </div>
                                                                        <div class="form-group">
                                                                            <textarea name="note" class="form-control summernote-1" name="note">
                                                                           {!! $enquiryFollowup->note !!}
                                                                            </textarea>
                                                                        </div>
                                                                    
                                                                        <div class="col-md-8">
                                                                           <div class="btn-holder">
                                                                               <button class="main-round-btn" id="btn_update_task"
                                                                                       type="submit"><i class="fa fa-save"></i>Update
                                                                               </button>
                                                                              {{--  <button class="main-round-btn red-btn" id="reset_values"
                                                                                       type="button"><i class="fa  fa-trash-o"></i>Discard
                                                                               </button> --}}
                                                                               <a class="main-round-btn red-btn" href="{{ url('user/enquiry-timeline'.'/'.$enquiry->pk_int_enquiry_id) }}" ><i class="fa fa-arrow-circle-left"></i>Back</a>
                                                                            </div>
                                                                        </div>

                                                                    </div>
                                                            </form>

                                            <!----------------------Logged Meeting------------------->
                                                    @elseif($enquiryFollowup->log_type==\App\BackendModel\EnquiryFollowup:: TYPE_LOG_MEETING)
                                                            <form action="{{url('user/update-timeline-log-meeting/'.$enquiryFollowup->id)}}"
                                                                     method="POST">
                                                                   {{csrf_field()}}

                                                                <div class="card-header text-white bg-secondary">Edit Log a Meeting</div>
                                                                   <div class="card-body">
                                                                        <div class="row">
                                                                            
                                                                            <div class="col-md-3 mb-3">
                                                                                <label>Date</label>
                                                                                <div class="input-group with-addon-icon-left">
                                                                                    <input type="text" value="{{$enquiryFollowup->date}}"id="datepicker-2" class="form-control datepicker"
                                                                                    placeholder="Choose Date" name="date">
                                                                                        <span class="input-group-append">
                                                                                            <span class="input-group-text">
                                                                                            <i class="fa fa-calendar"></i>
                                                                                            </span>
                                                                                        </span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-2">
                                                                                 <label>Time</label>
                                                                                <div class="input-group with-addon-icon-left">
                                                                                    <input type="text" name="time" id="time-2" class="form-control time" value="{{$enquiryFollowup->time}}"
                                                                                           placeholder="Time">
                                                                                    <span class="input-group-append">
                                                                                              <span class="input-group-text">
                                                                                                <i class="fa fa-clock-o"></i>
                                                                                              </span>
                                                                                            </span>
                                                                                </div>
                                                                                <span  style="font-size: 11px;" id="time-2-error" class="text-danger"></span>
                                                                            </div>
                                                                            
                                                                        </div>
                                                                        <div class="form-group">
                                                                            <textarea name="note" class="form-control summernote-1" name="note">
                                                                           {!! $enquiryFollowup->note !!}
                                                                            </textarea>
                                                                        </div>
                                                                    
                                                                        <div class="col-md-8">
                                                                           <div class="btn-holder">
                                                                               <button class="main-round-btn" id="btn_update_task"
                                                                                       type="submit"><i class="fa fa-save"></i>Update
                                                                               </button>
                                                                              {{--  <button class="main-round-btn red-btn" id="reset_values"
                                                                                       type="button"><i class="fa  fa-trash-o"></i>Discard
                                                                               </button> --}}
                                                                               <a class="main-round-btn red-btn" href="{{ url('user/enquiry-timeline'.'/'.$enquiry->pk_int_enquiry_id) }}" ><i class="fa fa-arrow-circle-left"></i>Back</a>
                                                                            </div>
                                                                        </div>

                                                                    </div>
                                                            </form>







                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>

    </main>

@endsection
@push('footer.script')

    <script type="text/javascript">

        $(document).ready(function () {

            /*----------- Front End Scripts ----------*/


            $('.select2').select2();
            width: 'resolve'
            $('.summernote-1').summernote({
                height: '215',
                toolbar: [
                    ['style', ['bold', 'italic', 'underline', 'clear']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['fontsize', ['fontsize']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                ]
            });
            $('.summernote-2').summernote({
                height: '170',
                toolbar: [
                    ['style', ['bold', 'italic', 'underline', 'clear']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['fontsize', ['fontsize']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                ]
            });
            $('.summernote-3').summernote({
                height: '125',
                toolbar: [
                    ['style', ['bold', 'italic', 'underline', 'clear']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['fontsize', ['fontsize']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                ]
            });

            /*----------- Front End Scripts ----------*/

            /*--------------------------------Tab notes------------------------------------*/

            /* Add Note */


        });


    </script>

@endpush