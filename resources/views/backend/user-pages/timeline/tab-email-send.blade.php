<style>
.content {
            display: none;
        }
        .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}
.lh{line-height:24px}
.lh label{padding-left: 6px; }

input[type='radio']:checked:after {
   width: 7px;
    height: 7px;
    border-radius: 15px;
    top: 5px;
    left: 5px;
    position: relative;
    background-color: #fff;
    content: '';
    display: inline-block;
    visibility: visible;
    border: 2px solid #fff;
    right: 0;
    margin: 0 auto;
}

.form-check-input {
    width: 1em;
    height: 1em;
    margin-top: 0.25em;
    vertical-align: top;
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 1px solid rgba(0,0,0,.25);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
}

.form-check .form-check-input {
    float: left;
    margin-left: -1.5em;
}

.form-check-input[type=radio] {
    border-radius: 50%;
}
</style>
<div class="tab-pane fade" id="emailsend" role="tabpanel" aria-labelledby="task-tab">
  <div class="row m-0">
    <div class="card w-100" style="border:none !important">
      <div class="" style="padding-top: 0px">
        <div class="log-header" style="margin-top: 15px">
          {{-- <a class="log-add-task" href="#" data-toggle="modal" data-target="#lead-email-modal">Add Email
						<i class="fa fa-plus"></i>
					</a> --}}
        </div>
      </div>
    </div>
  </div>
  <!-- <form  id="sendMailTemplete" enctype="multipart/form-data"><div class="timeline-tab-inner"><div class="row"><div class="col-md-1 sm-mar-btm "><input type="checkbox" class="form-control mail-templete" id="mail_templete" name="mail_templete" style="margin-bottom: 0px"></div><div class="col-md-4 sm-mar-btm "><label style="font-size: 1rem;">Use Template</label></div></div><div class="col-md-12 pad-rght sm-mar-btm" id="templete-view" style="display:none;"><a class="text-danger small">Use these tags : For name - <label class="btn btn-sm btn-info text-light btn-label">[gl_name]</label>,
             For agent name - <label class="btn btn-sm btn-info text-light btn-label">[gl_agent]</label>,
             For company name - <label class="btn btn-sm btn-info text-light btn-label">[gl_company]</label>
             ,For mobile - <label class="btn btn-sm btn-info text-light btn-label">[gl_mobile]</label>,  
             ,For email - <label class="btn btn-sm btn-info text-light btn-label">[gl_email]</label>,  
             ,For source - <label class="btn btn-sm btn-info text-light btn-label">[gl_source]</label>,  
             ,For lead type - <label class="btn btn-sm btn-info text-light btn-label">[gl_lead_type]</label>,  
             ,For lead status - <label class="btn btn-sm btn-info text-light btn-label">[gl_lead_status]</label>,  
             for content - <label class="btn btn-sm btn-info text-light btn-label">[gl_content]</label></a><textarea  rows="13" class="form-control" name="email_template_code"  id="email_template_codes"></textarea><span class="error email_template_code"></span><input type="hidden" name="templete_id" id="templete-id"></div><div class="col-md-8" id="templete_button" style="display:none;"><div class="btn-holder"><input type="hidden" name="_token" value="{{ csrf_token() }}"><button class="main-round-btn" id="addmail" type="submit"><i class="fa fa-paper-plane"></i>Send
                </button></div></div></div></form>-->
    @if($mailSettings)            
    <form id="sendMail" enctype="multipart/form-data" class="row">
      <div class="row mg-bt-15 col-sm-12" id="withouttemplete">
        <div class="col-sm-12"><p class="h-label">Draft your mail</p></div>
        <div class="col-md-4 pad-rght sm-mar-btm sm-pad-rl-0">
          <label>From Name</label>
          <input type="text" class="form-control" id="#" name="from_name" id="from_name" placeholder="From Name (Else taking default name)" value="{{ $enquiry->vchr_user_name ?? 'No Name' }}"/>
          <div class="error from_name ">
            <span class="muted"></span>
          </div>
        </div>
        <div class="col-md-4 pad-rght sm-mar-btm sm-pad-rl-0">
          <label>Cc</label>
          <input type="email" class="form-control" id="#" name="vchr_cc_email" id="vchr_cc_email" placeholder="CC" />
          <div class="error vchr_cc_email ">
            <span class="muted"></span>
          </div>
        </div>
        <div class="col-md-4 pad-rght sm-mar-btm sm-pad-rl-0">
          <label>Bcc</label>
          <input type="email" class="form-control" id="#" name="vchr_bcc_email" id="vchr_bcc_email" placeholder="BCC" />
          <div class="error vchr_bcc_email ">
            <span class="muted"></span>
          </div>
        </div>
      <div class="col-md-12 pad-rght sm-mar-btm sm-pad-rl-0">
        <input class="form-control" name="vchr_subject" id="vchr_subject" placeholder="Enter your Subject" /required>
        <div class="error vchr_subject ">
          <span class="muted"></span>
        </div>
      </div>
      <div class="col-md-4 pad-rght sm-mar-btm sm-pad-rl-0">
        <span class="error">
          {{-- * Maximum upload  size  : 2 MB --}}
        </span>
        <input type="file" class="form-control" name="vchr_attachment" id="vchr_attachment" placeholder="" />
        <div class="error vchr_attachment ">
          <span class="muted"></span>
        </div>
      </div>
      <div class="col-md-12 sm-pad-0 mt-2">
        <div class="row col-sm-12">
          <div class="col-sm-3 lh"><input type="radio" value="custome" name="toggle" id="toggle1" class="toggle-radio form-check-input" data-target="content1" checked>
            <label for="toggle1">Custome message</label></div>

          <div class="col-sm-3 lh"><input type="radio" value="template" name="toggle" id="toggle2" class="toggle-radio form-check-input" data-target="content2">
            <label for="toggle2">Default Template</label></div>
        </div>
        <div id="content1" class="content">
           <textarea name="text_content" id="text_content" rows="5" class="form-control summernote-1" placeholder="content" ></textarea>
        </div>
        <div id="content2" class="content">
          <select class="form-control" name="templete_id">
            <option value="" selected disabled>--Select a template--</option>
            @foreach($mailTemplates as $template)
              <option value="{{$template->id}}">{{$template->template_title}}</option>
            @endforeach
              {{-- <option value="template1">Template 1</option>
              <option value="template2">Template 2</option>
              <option value="template3">Template 3</option> --}}
          </select>
        </div>
      </div>
     
      <div class="col-md-8">
        <div class="btn-holder">
          <input type="hidden" name="_token" value="{{ csrf_token() }}">
          <button class="main-round-btn" id="addmail" type="submit">
            <i class="fa fa-paper-plane"></i>Send </button>
          {{-- <button class="main-round-btn red-btn" id="reset_values"
                   type="button">
												<i class="fa  fa-trash-o"></i>Discard
                
											</button> --}}
        </div>
      </div>
    </div>
   
  </form>
  @else
     Please Setup Mail Configration to send mail 
     <a href="{{('/user/mail-configuration')}}" style="color:#5387d4 !important" target="_blank">Click </a>
  @endif
</div>


<script>
   $(document).ready(function () {
        $('.summernote-1').summernote({
        height: '215',
    });
  });
        $(document).ready(function () {
            $('#content1').show();
            $('.toggle-radio').on('change', function () {
                // Get the target content ID
                var targetContent = $(this).data('target');

                // Hide all content divs
                $('.content').hide();

                // Show the target content div
                $('#' + targetContent).show();
            });
        });
    </script>