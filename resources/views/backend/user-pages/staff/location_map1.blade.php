<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
  <head>
    <title>{{$user->vchr_user_name}}</title>
    <style>html, body, #map {width:100%;height: 100%; margin: 0; padding: 0;}</style>
    <link type="text/css" rel="stylesheet" href="https://cdn.rawgit.com/markkr125/jsmaps/v0.0.15/library/native/core.native.css" />
    <script src="https://cdn.rawgit.com/markkr125/jsmaps/v0.0.15/library/core.abstract.js" type="text/javascript">
    </script>
    <script src="https://cdn.rawgit.com/markkr125/jsmaps/v0.0.15/library/core.abstract-helper.js" type="text/javascript">
    </script>
    <script src="https://cdn.rawgit.com/markkr125/jsmaps/v0.0.15/library/core.js" type="text/javascript">
    </script>
    <script src="https://cdn.rawgit.com/markkr125/jsmaps/v0.0.15/library/native/core.native.js" type="text/javascript">
    </script>
    <script src="https://cdn.rawgit.com/markkr125/jsmaps/v0.0.15/library/native/core.native-ui.js" type="text/javascript">
    </script>
    <script src="https://cdn.rawgit.com/markkr125/jsmaps/v0.0.15/library/native/core.native-helper.js" type="text/javascript">
    </script>
    <script src="https://cdn.rawgit.com/markkr125/jsmaps/v0.0.15/library/native/core.native-marker.js" type="text/javascript">
    </script>
    <script src="https://cdn.rawgit.com/markkr125/jsmaps/v0.0.15/library/native/core.native-infoWindow.js" type="text/javascript">
    </script>
    <script src="https://cdn.rawgit.com/markkr125/jsmaps/v0.0.15/library/native/core.native-vector.js" type="text/javascript">
    </script>
    <script
  src="https://code.jquery.com/jquery-3.5.1.min.js"
  integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="
  crossorigin="anonymous"></script>
  </head>
  <body>
  @if($history->count()>0)
    <script type='text/javascript'>
    function distance(lat1, lon1, lat2, lon2, unit) {
	if ((lat1 == lat2) && (lon1 == lon2)) {
		return 0;
	}
	else {
		var radlat1 = Math.PI * lat1/180;
		var radlat2 = Math.PI * lat2/180;
		var theta = lon1-lon2;
		var radtheta = Math.PI * theta/180;
		var dist = Math.sin(radlat1) * Math.sin(radlat2) + Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
		if (dist > 1) {
			dist = 1;
		}
		dist = Math.acos(dist);
		dist = dist * 180/Math.PI;
		dist = dist * 60 * 1.1515;
		if (unit=="K") { dist = dist * 1.609344 }
		if (unit=="N") { dist = dist * 0.8684 }
		return dist;
	}
}
var total_distance = 0;
var dist =0;
    jsMaps.loader(function (){
        var tiles = new jsMaps.Native.Tiles();
        
tiles.addTileLayer("http://{s}.tile.osm.org/{z}/{x}/{y}.png",['a','b','c'],'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap contributors<span id="distance"></span></a>.','OpenStreetMap');
tiles.addTileLayer("http://{s}.mqcdn.com/tiles/1.0.0/map/{z}/{x}/{y}.png",['otile1','otile2','otile3','otile4'],'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap contributors</a>. Tiles courtesy of <a href="http://www.mapquest.com/" target="_blank">MapQuest</a> <img src="https://developer.mapquest.com/content/osm/mq_logo.png">','Map Quest');
tiles.addTileLayer("http://{s}.mqcdn.com/tiles/1.0.0/sat/{z}/{x}/{y}.jpg",['oatile1','oatile2','oatile3','oatile4'],'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap contributors</a>. Tiles courtesy of <a href="http://www.mapquest.com/" target="_blank">MapQuest</a> <img src="https://developer.mapquest.com/content/osm/mq_logo.png">','Map Quest Satellite',19);
//Filter by Date:<input type="date" id="date">
        var map = jsMaps.api.init(
                '#map',
                'native',
                {
                    center: {
                        latitude: {{$history[0]->latitude}},
                        longitude: {{$history[0]->longitude}}
                    },
                    zoom: 10,
                    mouse_scroll: true,
                    zoom_control: true,
                    map_type: true
                },tiles
        );

        var polyLine1 = [
            @foreach($history as $key=>$histo)  
            {lat: {{$histo->latitude}},lng:  {{$histo->longitude}}}@if($history->count()!=$key+1){{','}}@endif
            @endforeach
        ];
        jsMaps.api.polyLine(map,{
            path: polyLine1,
            strokeColor: '#FF0000',
            strokeOpacity: 1.0,
            strokeWeight: 6,
            draggable: true,
            editable: false
        });
        @foreach($history as $key=>$histo)  
        @if($key!=0)
        dist = distance({{$history[$key-1]->latitude}}, {{$history[$key-1]->longitude}}, {{$history[$key]->latitude}}, {{$history[$key]->longitude}}, "K");
        total_distance += dist;
        @endif
        @if(!request('all_points'))
        @if($key==0 )
        var contentString = '<div id="content">'+
                '<div id="siteNotice">'+
                '</div>'+
                '<h1 id="firstHeading" class="firstHeading">{{$user->vchr_user_name}}</h1>'+
                '<div id="bodyContent">'+
                '<h2>{{$histo->created_at->format("d M Y h:i A")}} (Distance:'+ Math.round(dist,2) +')</h2>'+
                '</div>'+
                '</div>';
                var infoWindow = jsMaps.api.infoWindow({content: contentString});
               var marker1= jsMaps.api.marker(map,{position: {lat: {{$histo->latitude}},lng:  {{$histo->longitude}}}, title: '{{$user->vchr_user_name}},{{$histo->created_at->format("d M Y h:i A")}}',draggable: false});
        jsMaps.api.attach_event(marker1,'click',function() {
            infoWindow.open(map,marker1);
        });

        jsMaps.api.attach_event(marker1,'mouseover',function() {
            infoWindow.open(map,marker1);
        });

        jsMaps.api.attach_event(marker1,'mouseout',function() {
            infoWindow.close();
        });

        
        @endif
        @if($key==$history->count()-1 )
        var contentString2 = '<div id="content">'+
                '<div id="siteNotice">'+
                '</div>'+
                '<h1 id="firstHeading" class="firstHeading">{{$user->vchr_user_name}}</h1>'+
                '<div id="bodyContent">'+
                '<h2>{{$histo->created_at->format("d M Y h:i A")}} (Distance:'+ Math.round(dist,2) +')</h2>'+
                '</div>'+
                '</div>';
                var infoWindow2 = jsMaps.api.infoWindow({content: contentString2});
            var marker2 = jsMaps.api.marker(map,{position: {lat: {{$histo->latitude}},lng:  {{$histo->longitude}}}, title: '{{$user->vchr_user_name}},{{$histo->created_at->format("d M Y h:i A")}}',draggable: false});
        jsMaps.api.attach_event(marker2,'click',function() {
            infoWindow2.open(map,marker2);
        });

        jsMaps.api.attach_event(marker2,'mouseover',function() {
            infoWindow2.open(map,marker2);
        });

        jsMaps.api.attach_event(marker2,'mouseout',function() {
            infoWindow2.close();
        });
        @endif
        @else
        var contentString{{$key}} = '<div id="content">'+
                '<div id="siteNotice">'+
                '</div>'+
                '<h1 id="firstHeading" class="firstHeading">{{$user->vchr_user_name}}</h1>'+
                '<div id="bodyContent">'+
                '<h2>{{$histo->created_at->format("d M Y h:i A")}} (Distance:'+ Math.round(dist,2) +')</h2>'+
                '</div>'+
                '</div>';;
                var infoWindow{{$key}} = jsMaps.api.infoWindow({content: contentString{{$key}} });
               var marker{{$key}}= jsMaps.api.marker(map,{position: {lat: {{$histo->latitude}},lng:  {{$histo->longitude}}}, title: '{{$user->vchr_user_name}},{{$histo->created_at->format("d M Y h:i A")}}',draggable: false});
        jsMaps.api.attach_event(marker{{$key}},'click',function() {
            infoWindow{{$key}}.open(map,marker{{$key}});
        });

        jsMaps.api.attach_event(marker{{$key}},'mouseover',function() {
            infoWindow{{$key}}.open(map,marker{{$key}});
        });

        jsMaps.api.attach_event(marker{{$key}},'mouseout',function() {
            infoWindow{{$key}}.close();
        });
        @endif
        @endforeach
        document.getElementById('distance').innerHTML = ', Total Distance='+Math.round(total_distance,2)+"";
        
       
    });
    $(document).ready(function(){
        $('#date').on('change',function(){
            window.location.href = window.location.href+'?date='+$(this).val();
        })
    })
</script>
<div id='map'>
  </div>
  @else
  No Location History
  @endif
</body>
</html>