@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="add-y-team">
                    <div class="dashboard-row">
                          <div class="y-team-header y-team-header-V2">
                             <a href="{{ url()->previous() }}">
                                
                                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                      <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z" fill="#4D5459"/>
                                   </svg>
                                   Location History of {{$user->vchr_user_name}}
                             </a>
                          </div>
                    </div>
                </div>

                @if(session('flash_notification'))
                    <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
                @endif
                
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.account-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table" style="overflow: auto">
                    <table id="staff-login-info-table" class="table table-striped table-bordered nowrap table-custom"
                           cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Date & Time</th>
                            <th>Name</th>
                            <th>Latitude</th>
                            <th>Longitude</th>
                            <th>Status</th>
                            <th>Note</th>
                            <th>Poster Pasted</th>
                            <th>Products Properly Displayed</th>
                            <th width="10%">Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($history as $histo)
                        <tr>
                            <td>{{$histo->created_at}}</td>
                            <td>{{($histo->enquiry)?$histo->enquiry->vchr_customer_name:''}}</td>
                            <td>{{$histo->latitude}}</td>
                            <td>{{$histo->longitude}}</td>
                            <td>{{($histo->log_type==1)?'checkin':'checkout'}}</td>
                            <td>{{$histo->note}}</td>
                            <td>{{$histo->poster_pasted ? 'Yes' : 'No'}}</td>
                            <td>{{$histo->products_properly_displayed ? 'Yes' : 'No'}}</td>
                            <td>  <a href="http://maps.google.com/maps?q={{$histo->latitude}},{{$histo->longitude}}" target="_blank" class="btn btn-xs btn-primary edit"><i class="fa fa-map"></i></a></td>
                        </tr>
                        @endforeach
                        </tbody>
                    </table>
                    {{$history->links()}}
                </div>
            </div>
        </div>
    </main>
    <!------------ /add modal/  -------------->
    
@endsection
@push('footer.script')
    <script type="text/javascript">
        $(document).ready(function () {
            BASE_URL = window.location.origin;
            $('#staff-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "170px",
                    "targets": [0, 1, 2]
                },

                ],
                ajax: BASE_URL + '/user/get-staff-data',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'vchr_user_name', name: 'vchr_user_name'},
                    {data: 'vchr_user_mobile', name: 'vchr_user_mobile'},
                    {data: 'email', name: 'email'},
                    {data: 'designation', name: 'designation'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],

            });


            $('#staff-info-table').on('click', '.staff-delete', function (event) {
                event.preventDefault();
                var staffId = $(this).attr('data-staff-id');
                var destinationPath = BASE_URL + '/user/staff/' + staffId;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                })
                                    .done(function (res) {
                                        console.log("success");
                                        console.log(res);
                                        if (res.status === 'success') {
                                            $.alert({
                                                title: 'Success',
                                                type: 'green',
                                                content: res.msg,
                                            });
                                        } else {
                                            $.alert({
                                                title: 'Failed',
                                                type: 'red',
                                                content: res.msg,
                                            });
                                        }
                                    })
                                    .fail(function (err) {
                                        console.log("error");
                                    })
                                    .always(function (com) {
                                        console.log("complete");
                                        $('#staff-info-table').DataTable().ajax.reload(null, false);
                                    });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });
        });
    </script>
@endpush