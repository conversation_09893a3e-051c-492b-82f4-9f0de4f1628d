@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper main-wrapper-2">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="">
                    <div class="current-page">
                        <p><a href="{{('/user/crm-settings')}}">Settings </a>>  Senderid</p> 
                    </div>
                    <h5>Senderid</h5>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                              
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.account-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table gl-table-v2">
                    <table id="enquiry-info-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th>Sl No</th>
                                <th>Type</th>
                                <th>Senderid </th>
                                <th width="10%">Actions</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </main>
    <!------------ /add modal/  -------------->

    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="testimonialAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Autorespond code</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">Title</label>
                            <input type="text" name="title" class="form-control" placeholder="" >
                             <span class="error title"></span>          
                        </div>
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">Autorespond Message</label>
                            <textarea  name="message" class="form-control" placeholder="" rows="4" ></textarea>
                            <span class="error message"></span>
                        </div>             
                    </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button  id="add-senderid" class="btn btn-deep-orange demo-btn">Add</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="testimonialEdit" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update Senderid</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Senderid</label>
                            <input type="text" name="senderid" class="form-control" placeholder="" id="senderid">
                            {{csrf_field()}}
                            <input type="hidden" name="_method"  value="PUT" class="form-control" placeholder="" >
                            <input type="hidden" name="id" class="form-control" placeholder=""  id="id">
                            <span class="error senderid"></span>        
                        </div>
                                  
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button id="update-senderid" class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection 
@push('footer.script')
<script type="text/javascript">
    
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!}
            $('.error').hide();

            $('.ks-izi-modal-trigger').on('click', function (e) {
                $($(this).data('target')).iziModal('open');

                $(".test").on('click', function (e) {
                    //alert('hi');
                });
               
               

            });


            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1,2,3]
                },

                ],
                ajax: BASE_URL + '/user/get-default-senderid/',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },

                    {
                        data: 'type',
                        name: 'type'
                    },
                    {

                        data: 'senderid',
                        name: 'senderid'
                    },
                   
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
                
            });

           

            $(document).on('submit', '#testimonialEdit', function(event) {
                $('#update-senderid').html("Processing");
                $('#update-senderid').prop('disabled', true);
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                // $('').on('click', function (e) {
                var id=$('#id').val();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/default-senderid/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                        $('#update-senderid').html("Update");
                        $('#update-senderid').prop('disabled', false);
                   $('#ks-izi-modal-large1').modal('toggle');
                    $.alert({
                        title: 'Success',
                        type: 'green',
                        content: res.msg,
                    });
                }else{
                        $('#update-senderid').html("Update");
                        $('#update-senderid').prop('disabled', false);
                        $.each(res.msg, function(index, val) {
                                console.log(val);
                                console.log(index);
                                $('.'+index).html(val);
                                $('.'+index).show();
                            });
                       
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });

           
        });

        


   
     $('#enquiry-info-table').on('click', '.ks-izi-modal-trigger1', function(event) {

            var testimonialId = $(this).attr('sender-id'); 
            $.ajax({
                    url: BASE_URL + '/user/default-senderid/' + testimonialId,
                    type: 'GET',
                    dataType: 'JSON',
                })
            .done(function(res) {
                if(res.status == "success") {
                        data = res.data;

                        $("[name='senderid']").val(data.senderid);
                       
                        $('#id').val(data.id);

                        $('#ks-izi-modal-large1').modal('show');
                        //$($('.ks-izi-modal-trigger1').data('target')).iziModal('open');
                        
                    }
            })
            .fail(function(err) {

            })
            .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });
     });
            
        
    </script>
@endpush
