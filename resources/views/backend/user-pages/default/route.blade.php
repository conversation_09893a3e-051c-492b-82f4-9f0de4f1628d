@extends('backend.layout.master') 
@section('page-header')
@endsection 
@section('content')
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Senderid</h5>
                </div>
                <div class="col-lg-6 text-right">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                              
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar')
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <table id="enquiry-info-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Type</th>
                            <th>Route</th>
                            <th width="10%">Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </main>
    <!------------ /add modal/  -------------->

    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="testimonialAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Autorespond code</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">Title</label>
                            <input type="text" name="title" class="form-control" placeholder="" >
                             <span class="error title"></span>          
                        </div>
                        <div class="md-form mb-1">
                            <input type="hidden" name="pk_int_short_code_id">
                            <label for="default-input" class="form-control-label">Autorespond Message</label>
                            <textarea  name="message" class="form-control" placeholder="" rows="4" ></textarea>
                            <span class="error message"></span>
                        </div>             
                    </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button class="btn btn-deep-orange demo-btn">Add</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


    <!------------ /update modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
      aria-hidden="true">
        <form  id="testimonialEdit" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update Route</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Select Route</label>
                           <select class="form-control" data-placeholder="Select" name="route" required>
                               <option value="">Select Route</option>
                                @foreach($route as $key => $route1)
                               <option value="{{ $route1->pk_int_sms_route_id }}">{{ $route1->vchr_sms_route }}</option>
                               @endforeach
                           </select>
                            {{csrf_field()}}
                            <input type="hidden" name="_method"  value="PUT" class="form-control" placeholder="" >
                            <input type="hidden" name="id" class="form-control" placeholder=""  id="id">
                            <span class="error route"></span>        
                        </div>
                                  
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button class="btn btn-deep-orange demo-btn">Update</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection 
@push('footer.script')
<script type="text/javascript">
    
        $(document).ready(function() {
            BASE_URL ={!! json_encode(url('/')) !!}
            $('.error').hide();

            $('.ks-izi-modal-trigger').on('click', function (e) {
                $($(this).data('target')).iziModal('open');

                $(".test").on('click', function (e) {
                    //alert('hi');
                });
               
               

            });


            $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                columnDefs: [{
                    "width": "140px",
                    "targets": [0, 1,2,3]
                },

                ],
                ajax: BASE_URL + '/user/get-default-route/',
                columns: [
                    {
                        data: 'slno',
                        name: 'slno'
                    },

                    {
                        data: 'type',
                        name: 'type'
                    },
                    {

                        data: 'route',
                        name: 'route'
                    },
                   
                    {
                        data: 'show',
                        name: 'show',
                        orderable: false,
                        searchable: false
                    }
                ],
                
            });

           

            $(document).on('submit', '#testimonialEdit', function(event) {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                // $('').on('click', function (e) {
                var id=$('#id').val();
                // console.log(enquiryId);
                $.ajax({
                    url: BASE_URL + '/user/default-route/'+id,
                    type: 'POST',
                    dataType: 'JSON',
                    data:  new FormData(this),
                    contentType: false,
                    processData:false,
                })
                .done(function(res) {
                    if(res.status == 'success'){
                   $('#ks-izi-modal-large1').modal('toggle');
                    $.alert({
                        title: 'Success',
                        type: 'green',
                        content: res.msg,
                    });
                }else{
                        $.each(res.msg, function(index, val) {
                                console.log(val);
                                console.log(index);
                                $('.'+index).html(val);
                                $('.'+index).show();
                            });
                       
                }
                })
                .fail(function() {
                })
                 .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });

            });

           
        });

        


   
     $('#enquiry-info-table').on('click', '.ks-izi-modal-trigger1', function(event) {

            var testimonialId = $(this).attr('route-id'); 
            $.ajax({
                    url: BASE_URL + '/user/default-route/' + testimonialId,
                    type: 'GET',
                    dataType: 'JSON',
                })
            .done(function(res) {
                if(res.status == "success") {
                        data = res.data;

                        $("[name='route']").val(data.route);
                        //alert(data.route);
                       
                        $('#id').val(data.id);

                        $('#ks-izi-modal-large1').modal('show');
                        //$($('.ks-izi-modal-trigger1').data('target')).iziModal('open');
                        
                    }
            })
            .fail(function(err) {

            })
            .always(function(com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);

            });
     });
            
        
    </script>
@endpush
