@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
    <main class="main-wrapper">

        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="d-flex row-wrap align-items-center">
                    <div class="add-y-team">
                        <div class="dashboard-row">
                              <div class="y-team-header y-team-header-V2">
                                 <a href="{{ url()->previous() }}">
                                    
                                       <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                          <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z" fill="#4D5459"/>
                                       </svg>
                                       IVR Extensions
                                   
                                 </a>
                              </div>
                        </div>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger"
                                                onclick="add()" 
                                                href="#">Add Extensions</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.ivr-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table" style="overflow: auto">
                    <table id="ivr-numbers-table" class="table table-striped table-bordered nowrap table-custom"
                           cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Extension Number</th>
                            <th>Agent Name</th>
                            <th>Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($extensions as $extension)
                        <tr>
                            <td>{{$loop->iteration}}</td>
                            <td>{{$extension->extension}}</td>
                            <td>{{$extension->agent ? $extension->agent->vchr_user_name : 'UnAssigned'}}</td>
                            <td><a href="#" onclick="edit({{$extension->id}},{{$extension->extension}},{{$extension->staff_id}})">Edit</a><br />
                            <a href="#" onclick="deleteEntry({{$extension->id}})" >Delete</a></td>
                        </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
             
           
            </div>
        </div>
  
</main>
<div class="modal fade" id="extension-add-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Add Extension</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                </button>
            </div>
            <div class="modal-body">
                <form id="submitExtension" method="POST" action="#">
                    {{csrf_field()}}
                    <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Extension Number</label>
                            <input type="text" name="extension" id="extension" class="form-control" placeholder="">
                            <span class="error extension"></span>
                    </div>
                    <div class="md-form mb-1">
                        <label for="recipient-name" class="form-control-label">Agent:</label>

                        <select class="form-control" name="agent_id" id="agent_id" required>
                            <option value="">---- Select Agent ----</option>
                            <option value="{{\App\User::getVendorId()}}">{{\App\User::getUserName(\App\User::getVendorId())}}</option>
                            @foreach(\App\User::getAgentList() as $agent)
                                <option value="{{$agent->id}}">{{$agent->name}}</option>

                            @endforeach
                        </select>
                    </div>
                    <input class="form-control col-md-8" type="hidden" name="ivr_id" value="{{$id}}" id='ivr-id'>
                    <input class="form-control col-md-8" type="hidden" name="edit_id" id='ivr-extension-id'>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary" id="button-edit">Submit</button>
                    </div>
                    <span class="error"></span>
                </form>
            </div>
        </div>
    </div>
</div>      
@endsection
@push('footer.script')
   <script type="text/javascript">
   function add(){
       $('#exampleModalLabel').text('Add Extension')
       $('#ivr-extension-id').val('');
       $('#extension').val('');
       $('#agent_id').val('');
       $('#extension-add-modal').modal('show');
   }
   function edit(id,ext,staff){
        $('#exampleModalLabel').text('Update Extension')

       $('#ivr-extension-id').val(id);
       $('#extension').val(ext);
       $('#agent_id').val(staff);
       $('#extension-add-modal').modal('show');
   }
   $(document).ready(function(){

    $('#ivr-numbers-table').DataTable({
                    scrollX: true,
                    paging: true,
    });
    $(document).on('submit', '#submitExtension', function (event) {
                event.preventDefault();
                $.ajax({
                    url: '{{url('user/ivr-extension')}}',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if(res.status == 'success'){
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                        location.reload();
                    }else{
                        $.alert({
                            title: 'Failed',
                            type: 'red',
                            content: res.msg,
                        });
                    }
                }).fail(function () {
                    $.alert({
                        title: 'Failed',
                        type: 'red',
                        content: 'Whoops, Something went wrong',
                    });
                }).always(function (com) {
                   
                });

            });
   })
   function deleteEntry(id){
    $.confirm({
                title: 'Deletion',
                content: 'Are you sure you want to delete ?',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                    'confirm': {
                        text: 'Proceed',
                        btnClass: 'btn-info',
                        action: function () {
                            
                            $.ajax({
                                url: '{{url('user/ivr-delete-extension')}}'+'/'+id,
                                type: 'POST',
                                dataType: 'JSON',
                                data: {},
                                contentType: false,
                                processData: false,
                            }).done(function (res) {
                                if(res.status == 'success'){
                                    $.alert({
                                        title: 'Success',
                                        type: 'green',
                                        content: res.msg,
                                    });
                                    location.reload();
                                }else{
                                    $.alert({
                                        title: 'Failed',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                }
                            }).fail(function () {
                                $.alert({
                                    title: 'Failed',
                                    type: 'red',
                                    content: 'Whoops, Something went wrong',
                                });
                            }).always(function (com) {
                                
                            });
                        }
                      },
                      cancel: function () {
                        $.alert('Operation <strong>Canceled</strong>');
                     }
                   }
               });
   }
   </script>
@endpush
