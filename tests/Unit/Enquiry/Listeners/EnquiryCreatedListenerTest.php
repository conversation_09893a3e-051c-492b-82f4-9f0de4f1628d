<?php

declare(strict_types=1);

namespace Tests\Unit\Enquiry\Listeners;

use App\Enquiry\Events\EnquiryCreated;
use App\Enquiry\Jobs\ProcessNewEnquiry;
use App\Enquiry\Listeners\EnquiryCreatedListener;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

final class EnquiryCreatedListenerTest extends TestCase
{
    /**
     * @test
     */
    public function it_dispatches_process_new_enquiry_job_when_enquiry_is_created(): void
    {
        Bus::fake(ProcessNewEnquiry::class);
        $event = new EnquiryCreated(enquiryId: 123, vendorId: 456, enquiryTypeId: 789, source: 'website');

        Log::shouldReceive('withContext')
            ->once()
            ->with([
                'enquiry_id' => $event->enquiryId,
                'vendor_id' => $event->vendorId,
                'enquiry_type_id' => $event->enquiryTypeId,
                'source' => $event->source,
            ]);

        $listener = new EnquiryCreatedListener();

        $listener->handle($event);

        Bus::assertDispatched(
            ProcessNewEnquiry::class,
            static fn($job) => $job->enquiryId === $event->enquiryId &&
                $job->vendorId === $event->vendorId
        );
    }

    /**
     * @test
     */
    public function it_sets_delay_to_one_second(): void
    {
        $listener = new EnquiryCreatedListener();

        $this->assertEquals(1, $listener->delay);
    }
}
