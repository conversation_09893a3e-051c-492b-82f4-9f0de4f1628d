<?php

declare(strict_types=1);

namespace Tests\Unit\Enquiry\Jobs;

use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\Enquiry\Jobs\ProcessNewEnquiry;
use App\Events\ApiHistoryPost;
use App\Events\CreateFollowup;
use App\Ivr\IVRWebhook\Jobs\DispatchWebhooks;
use App\Modules\Facebook\Jobs\RecordEnquiry\ProcessNewEnquiryAutomations;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

final class ProcessNewEnquiryTest extends TestCase
{
    use DatabaseTransactions;

    private ProcessNewEnquiryAutomations&MockInterface $processNewEnquiryAutomations;

    protected function setUp(): void
    {
        parent::setUp();

        Bus::fake([DispatchWebhooks::class]);
        Event::fake();

        $this->processNewEnquiryAutomations = Mockery::mock(ProcessNewEnquiryAutomations::class);
    }

    /**
     * @test
     */
    public function it_processes_new_enquiry_correctly(): void
    {
        $enquiryId = 123;
        $vendorId = 456;
        $enquiryTypeId = 789;
        $source = 'website';

        Log::shouldReceive('withContext')
            ->once()
            ->with([
                'enquiry_id' => $enquiryId,
                'vendor_id' => $vendorId,
            ])
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('Processing new enquiry');

        $enquiry = $this->getEnquiry($enquiryId, $vendorId, $enquiryTypeId);

        $this->processNewEnquiryAutomations->shouldReceive('for')
            ->once()
            ->withArgs(function (Enquiry $argEnquiry) use ($enquiry): bool {
                $this->assertEquals($enquiry->pk_int_enquiry_id, $argEnquiry->pk_int_enquiry_id);
                return true;
            })
            ->andReturn(null);

        $job = new ProcessNewEnquiry(enquiryId: $enquiryId, vendorId: $vendorId);

        $job->handle($this->processNewEnquiryAutomations);

        Event::assertDispatched(ApiHistoryPost::class, static fn ($event) => $event->enquiry_id === $enquiryId
            && $event->vendor_id === $vendorId
            && $event->source_id === $enquiry->fk_int_enquiry_type_id
            && $event->type === 1
            && $event->duplicate === false
            && $event->page === 2);

        Bus::assertDispatched(DispatchWebhooks::class, static fn ($job) => $job->enquiryId === $enquiryId
            && $job->vendorId === $vendorId
            && $job->typeId === $enquiryTypeId);
    }

    /**
     * @test
     */
    public function it_should_skip_processing_when_enquiry_not_found(): void
    {
        $enquiryId = 999;
        $vendorId = 456;

        $this->processNewEnquiryAutomations->shouldNotReceive('for');

        $job = new ProcessNewEnquiry(enquiryId: $enquiryId, vendorId: $vendorId);

        Log::shouldReceive('withContext')
            ->once()
            ->with([
                'enquiry_id' => $enquiryId,
                'vendor_id' => $vendorId,
            ])
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('Processing new enquiry');

        Log::shouldReceive('info')
            ->once()
            ->with('Enquiry not found, skip processing');

        $job->handle($this->processNewEnquiryAutomations);

        Event::assertNotDispatched(ApiHistoryPost::class);
        Bus::assertNotDispatched(DispatchWebhooks::class);
    }

    /**
     * @test
     */
    public function it_assigns_enquiry_to_admin_when_staff_id_is_null(): void
    {
        $enquiryId = 123;
        $vendorId = 456;
        $enquiryTypeId = 789;

        $this->getEnquiry($enquiryId, $vendorId, $enquiryTypeId);

        $this->processNewEnquiryAutomations->shouldReceive('for')
            ->once()
            ->andReturn(null);

        $job = new ProcessNewEnquiry(enquiryId: $enquiryId, vendorId: $vendorId);

        $job->handle($this->processNewEnquiryAutomations);

        Event::assertDispatched(CreateFollowup::class, static fn ($event) => $event->enquiry_id === $enquiryId
            && $event->created_by === $vendorId
            && $event->note === 'Admin has been designated as the lead via api.'
            && $event->log_type === EnquiryFollowup::TYPE_ACTIVITY);

        $this->assertDatabaseHas(Enquiry::class, [
            'pk_int_enquiry_id' => $enquiryId,
            'staff_id' => $vendorId,
        ]);
    }

    /**
     * @test
     */
    public function it_does_not_assign_enquiry_to_admin_when_staff_id_already_exists(): void
    {
        $enquiryId = 123;
        $vendorId = 456;
        $enquiryTypeId = 789;
        $source = 'website';
        $existingStaffId = 789;

        $this->getEnquiry($enquiryId, $vendorId, $enquiryTypeId, $existingStaffId);

        $this->processNewEnquiryAutomations->shouldReceive('for')
            ->once()
            ->andReturnNull();

        $job = new ProcessNewEnquiry(enquiryId: $enquiryId, vendorId: $vendorId);

        $job->handle($this->processNewEnquiryAutomations);

        Event::assertNotDispatched(CreateFollowup::class);
        $this->assertDatabaseHas(Enquiry::class, [
            'pk_int_enquiry_id' => $enquiryId,
            'staff_id' => $existingStaffId,
        ]);
    }

    /**
     * @test
     */
    public function it_sets_queue_to_general(): void
    {
        $job = new ProcessNewEnquiry(enquiryId: 123, vendorId: 456);

        $this->assertEquals('general', $job->queue);
    }

    private function getEnquiry(int $enquiryId, int $vendorId, int $enquiryTypeId, ?int $staffId = null): Enquiry
    {
        return Enquiry::query()
            ->create([
                'pk_int_enquiry_id' => $enquiryId,
                'fk_int_user_id' => $vendorId,
                'fk_int_enquiry_type_id' => $enquiryTypeId,
                'vchr_customer_name' => 'John Doe',
                'vchr_customer_email' => '<EMAIL>',
                'vchr_customer_mobile' => '+971551234567',
                'mobile_no' => '+971551234567',
                'vchr_customer_company_name' => 'Company Name',
                'staff_id' => $staffId ?? null,
            ]);
    }
}
