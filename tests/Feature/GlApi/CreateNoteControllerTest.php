<?php

declare(strict_types=1);

namespace Tests\Feature\GlApi;

use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\GlApiTokens;
use App\Events\CreateFollowup;
use App\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class CreateNoteControllerTest extends TestCase
{
    use DatabaseTransactions;

    private GlApiTokens $apiToken;
    private User $vendor;
    private Enquiry $enquiry;

    protected function setUp(): void
    {
        parent::setUp();
        
        Event::fake([CreateFollowup::class]);
        
        // Create vendor user
        $this->vendor = User::factory()->create([
            'pk_int_user_id' => 100,
            'parent_user_id' => 2,
        ]);
        
        // Create API token
        $this->apiToken = GlApiTokens::create([
            'vchr_token' => 'test-api-token-123',
            'fk_int_user_id' => $this->vendor->pk_int_user_id,
        ]);
        
        // Create enquiry
        $this->enquiry = Enquiry::create([
            'fk_int_user_id' => $this->vendor->pk_int_user_id,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '919876543210',
            'mobile_no' => '9876543210',
            'vchr_customer_email' => '<EMAIL>',
            'fk_int_enquiry_type_id' => 1,
            'feedback_status' => 1,
        ]);
    }

    /** @test */
    public function it_creates_note_for_existing_enquiry_using_mobile_number()
    {
        $response = $this->postJson('/api/gl-note-creation-api', [
            'token' => 'test-api-token-123',
            'note' => 'This is a test note',
            'mobile_no' => '9876543210',
            'countrycode' => '91',
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Note added successfully',
            ]);
        
        Event::assertDispatched(CreateFollowup::class, function ($event) {
            return $event->note === 'This is a test note'
                && $event->logType === EnquiryFollowup::TYPE_NOTE
                && $event->enquiryId === $this->enquiry->pk_int_enquiry_id
                && $event->userId === $this->vendor->pk_int_user_id;
        });
    }

    /** @test */
    public function it_creates_note_for_existing_enquiry_using_email()
    {
        $response = $this->postJson('/api/gl-note-creation-api', [
            'token' => 'test-api-token-123',
            'note' => 'This is a test note via email',
            'email' => '<EMAIL>',
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Note added successfully',
            ]);
        
        Event::assertDispatched(CreateFollowup::class);
    }

    /** @test */
    public function it_returns_error_for_invalid_token()
    {
        $response = $this->postJson('/api/gl-note-creation-api', [
            'token' => 'invalid-token',
            'note' => 'This is a test note',
            'mobile_no' => '9876543210',
        ]);
        
        $response->assertStatus(401)
            ->assertJson([
                'status' => 'fail',
                'message' => 'Invalid API token',
            ]);
    }

    /** @test */
    public function it_returns_error_when_enquiry_not_found()
    {
        $response = $this->postJson('/api/gl-note-creation-api', [
            'token' => 'test-api-token-123',
            'note' => 'This is a test note',
            'mobile_no' => '1234567890', // Non-existent number
        ]);
        
        $response->assertStatus(404)
            ->assertJson([
                'status' => 'fail',
                'message' => 'Enquiry not found',
            ]);
    }

    /** @test */
    public function it_validates_required_fields()
    {
        $response = $this->postJson('/api/gl-note-creation-api', [
            'token' => 'test-api-token-123',
            // Missing note field
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['note']);
    }

    /** @test */
    public function it_validates_token_in_middleware()
    {
        $response = $this->postJson('/api/gl-note-creation-api', [
            // Missing token
            'note' => 'Test note',
            'mobile_no' => '9876543210',
        ]);
        
        $response->assertStatus(401)
            ->assertJson([
                'status' => 'fail',
                'message' => 'API token is required',
            ]);
    }

    /** @test */
    public function it_validates_note_max_length()
    {
        $longNote = str_repeat('a', 2001); // Exceeds 2000 character limit
        
        $response = $this->postJson('/api/gl-note-creation-api', [
            'token' => 'test-api-token-123',
            'note' => $longNote,
            'mobile_no' => '9876543210',
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['note']);
    }

    /** @test */
    public function it_validates_mobile_number_format()
    {
        $response = $this->postJson('/api/gl-note-creation-api', [
            'token' => 'test-api-token-123',
            'note' => 'Test note',
            'mobile_no' => 'abc123', // Invalid format
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['mobile_no']);
    }

    /** @test */
    public function it_validates_email_format()
    {
        $response = $this->postJson('/api/gl-note-creation-api', [
            'token' => 'test-api-token-123',
            'note' => 'Test note',
            'email' => 'invalid-email', // Invalid format
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function it_sets_default_country_code_when_not_provided()
    {
        $response = $this->postJson('/api/gl-note-creation-api', [
            'token' => 'test-api-token-123',
            'note' => 'Test note with default country code',
            'mobile_no' => '9876543210',
            // countrycode not provided, should default to 91
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Note added successfully',
            ]);
    }

    /** @test */
    public function it_cleans_mobile_number_from_non_numeric_characters()
    {
        $response = $this->postJson('/api/gl-note-creation-api', [
            'token' => 'test-api-token-123',
            'note' => 'Test note with cleaned mobile',
            'mobile_no' => '+98-765-43210', // Contains non-numeric characters
            'countrycode' => '91',
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Note added successfully',
            ]);
    }
}