<?php

declare(strict_types=1);

namespace Tests\Feature\Jobs;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\LeadType;
use App\FrontendModel\LeadAdditionalDetails;
use App\Ivr\IVRWebhook\Jobs\AutomationApiCall;
use App\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

final class AutomationApiCallIntegrationTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * @test
     */
    public function it_can_be_dispatched_to_queue(): void
    {
        // Arrange
        Queue::fake();

        $enquiryId = 123;
        $vendorId = 456;
        $automationId = 789;

        // Act
        AutomationApiCall::dispatch($enquiryId, $vendorId, $automationId);

        // Assert
        Queue::assertPushed(AutomationApiCall::class, static fn ($job) => $job->enquiryId === $enquiryId &&
                $job->vendorId === $vendorId &&
                $job->automationId === $automationId);
    }

    /**
     * @test
     */
    public function it_processes_real_database_data_correctly(): void
    {
        // Arrange
        Http::fake([
            'https://webhook.example.com/api' => Http::response([
                'received' => true,
            ], 200),
        ]);

        $user = User::factory()->create();

        $enquiryType = EnquiryType::create([
            'vchr_enquiry_type' => 'Website Form',
            'fk_int_user_id' => $user->pk_int_user_id,
            'vendor_id' => $user->pk_int_user_id,
            'int_status' => 1,
            'created_by' => $user->pk_int_user_id,
        ]);

        $enquiryPurpose = EnquiryPurpose::create([
            'vchr_purpose' => 'Product Inquiry',
            'fk_int_user_id' => $user->pk_int_user_id,
        ]);

        $feedbackStatus = FeedbackStatus::create([
            'vchr_status' => 'New Lead',
            'fk_int_user_id' => $user->pk_int_user_id,
        ]);

        $leadType = LeadType::create([
            'name' => 'Hot Lead',
            'vendor_id' => $user->pk_int_user_id,
        ]);

        $enquiry = Enquiry::create([
            'fk_int_user_id' => $user->pk_int_user_id,
            'fk_int_enquiry_type_id' => $enquiryType->pk_int_enquiry_type_id,
            'fk_int_purpose_id' => $enquiryPurpose->pk_int_purpose_id,
            'feedback_status' => $feedbackStatus->pk_int_feedback_status_id,
            'lead_type_id' => $leadType->id,
            'vchr_customer_name' => 'Jane Smith',
            'vchr_customer_email' => '<EMAIL>',
            'vchr_customer_mobile' => '+971501234567',
            'mobile_no' => '+971501234567',
            'vchr_customer_company_name' => 'Smith Industries',
            'country_code' => '+971',
            'address' => '456 Business Bay, Dubai',
        ]);

        // Add custom fields
        LeadAdditionalDetails::create([
            'enquiry_id' => $enquiry->pk_int_enquiry_id,
            'field_name' => 'Industry',
            'value' => 'Technology',
        ]);

        $automation = AutomationRule::factory()->create([
            'vendor_id' => $user->pk_int_user_id,
            'api' => 'https://webhook.example.com/api',
            'trigger' => 'new_lead',
            'action' => 'api',
        ]);

        // Act
        (new AutomationApiCall($enquiry->pk_int_enquiry_id, $user->pk_int_user_id, $automation->id))->handle();

        // Assert
        Http::assertSent(static function ($request) use ($enquiry) {
            $data = $request->data();

            return $request->url() === 'https://webhook.example.com/api' &&
                $data['enquiry_id'] === $enquiry->pk_int_enquiry_id &&
                $data['customer_name'] === 'Jane Smith' &&
                $data['email'] === '<EMAIL>' &&
                $data['phone'] === '+971501234567' &&
                $data['company_name'] === 'Smith Industries' &&
                $data['status'] === 'New Lead' &&
                $data['purpose'] === 'Product Inquiry' &&
                $data['source'] === 'Website Form' &&
                $data['type'] === 'Hot Lead' &&
                count($data['custom_fields']) === 1 &&
                $data['custom_fields'][0]['field_name'] === 'Industry' &&
                $data['custom_fields'][0]['value'] === 'Technology';
        });
    }

    /**
     * @test
     */
    public function it_handles_missing_related_data_gracefully(): void
    {
        // Arrange
        Http::fake([
            'https://webhook.example.com/api' => Http::response([
                'received' => true,
            ], 200),
        ]);

        $user = User::factory()->create();

        // Create enquiry without some related data
        $enquiry = Enquiry::create([
            'fk_int_user_id' => $user->pk_int_user_id,
            'vchr_customer_name' => 'John Minimal',
            'vchr_customer_email' => '<EMAIL>',
            // Missing many optional fields
        ]);

        $automation = AutomationRule::factory()->create([
            'vendor_id' => $user->pk_int_user_id,
            'api' => 'https://webhook.example.com/api',
        ]);

        // Act
        $job = new AutomationApiCall($enquiry->pk_int_enquiry_id, $user->pk_int_user_id, $automation->id);

        $job->handle();

        // Assert - Should not throw exception and should send data with null values
        Http::assertSent(static function ($request) use ($enquiry) {
            $data = $request->data();

            return $request->url() === 'https://webhook.example.com/api' &&
                $data['enquiry_id'] === $enquiry->pk_int_enquiry_id &&
                $data['customer_name'] === 'John Minimal' &&
                $data['email'] === '<EMAIL>' &&
                $data['status'] === null && // Missing status should be null
                $data['purpose'] === null && // Missing purpose should be null
                is_array($data['custom_fields']) &&
                count($data['custom_fields']) === 0; // No custom fields
        });
    }
}
