@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
@include('woocommerce::create')
@include('woocommerce::edit')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <main class="main-wrapper">
        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="add-y-team">
                    <div class="dashboard-row">
                          <div class="y-team-header y-team-header-V2">
                             <a href="{{ url()->previous() }}">
                                
                                   <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                      <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z" fill="#4D5459"/>
                                   </svg>
                                   Woocommerce Settings
                               
                             </a>
                          </div>
                       </div>
                </div>
                <div class="">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li class=""><a style="cursor: pointer;" class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white" onclick="getCreateModal()">Add credentials</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            {{-- @include('backend.layout.connectsidebar') --}}
            @include ('backend.layout.sidebar-v2.glconnect-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table" style="overflow: auto">
                    <table id="designation-table" class="table table-striped table-bordered nowrap table-custom"
                           cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Url</th>
                            <th>Api key</th>
                            <!-- <th>Username</th> -->
                            <th>Secret Key</th>
                            <th width="10%">Actions</th>
                        </tr>


                        </thead>
                        <tbody> 
                            @foreach($data as $key => $datas)
                            <tr>
                                <td>{{ $key+1 }}</td>
                                <td>{{$datas->woo_commerce_url }}</td>
                                <td>{{$datas->woo_commerce_apikey }}</td>
                                <!-- <td>{{$datas->woo_commerce_username }}</td> -->
                                <td>{{$datas->woo_commerce_pwd }}</td>
                                <td><a class="btn  btn-sm btn-success editBtn"  data-id="{{ $datas->id }}" data-url="{{$datas->woo_commerce_url}}" data-apikey="{{$datas->woo_commerce_apikey}}" data-username="{{ $datas->woo_commerce_username }}" data-pwd="{{ $datas->woo_commerce_pwd }}" data-toggle="tooltip" title="Edit" onclick="getEditModal()"> <i class="fa fa-edit"></i></a>
                                <a style="cursor: pointer;" title="Delete" class="btn  btn-sm btn-danger btnDelete" data-id="{{ $datas->id }}"> <i style="color:white;cursor: pointer;" class="fa fa-trash"></i></a>
                                </td>
                            </tr>
                            @endforeach
                    </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>
@endsection
<script src="https://ajax.aspnetcdn.com/ajax/jQuery/jquery-3.4.1.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
@push('footer.script')
    <script type="text/javascript">

    // load modal
    function getCreateModal(){
        $('#create_details').modal('show');
    }
    
    function getEditModal(){
     
        $('#username').val($(this).data('username'));
        $('#url').val($(this).data('url'));
        $('#apikey').val($(this).data('apikey'));
        $('#pwd').val($(this).data('pwd'));
        $('#id').val($(this).data('id'));
        $('#edit_details').modal('show');
    }

    // saving data from modal
    function saveCredientails(){
        $('#spin').show();
            $.ajax({
                url:'/packages/woocommerce/settings/create',
                type: "POST",
                data: $('#createdetails').serialize(),
                cache: false,
                beforeSend: function () {

                },
                success: function (data) {
                    if(data.message == 'Only one credentials needed'){
                        $('#create_details').modal('hide');
                        toastr.error('You can add only one credentials');
                        $('#spin').hide();
                        $("input[type=text]").val('')
                    }else{
                        toastr.success('Added sucessfully..');
                        $('#create_details').modal('hide');
                        window.location.href = '/packages/woocommerce/settings';
                    }
                }
            });
        }

        // saving data from modal
    function editCredientails(){
        $('#editspin').show();
            $.ajax({
                url:'/packages/woocommerce/settings/update',
                type: "POST",
                data: $('#editdetails').serialize(),
                cache: false,
                beforeSend: function () {

                },
                success: function (data) {
                if(data){
                    toastr.success('Updated sucessfully..');
                    $('#edit_details').modal('hide');
                    window.location.href = '/packages/woocommerce/settings';
                    } 
                }
            });
        }
        
        $(document).on('click', '.editBtn', function () {
            $('#id').val($(this).data('id'));
            $('#username').val($(this).data('username'));
            $('#url').val($(this).data('url'));
            $('#apikey').val($(this).data('apikey'));
            $('#pwd').val($(this).data('pwd'));
           
            // $('#email').val($(this).data('email'));
            // $('#pwd').val($(this).data('pwd'));
        });

        $(document).ready(function () {
            $('#spin').hide();
            $('#editspin').hide();
            var BASE_URL = window.location.origin;
            $('#designation-table').DataTable({});
        });
        
        // for delete an credientails
        $(document).on('click', '.btnDelete', function () {
            
            var id = $(this).data('id'); 
            var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
            
            $.confirm({
                title: 'Deletion',
                content: 'Are you sure you want to delete ?',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                    'confirm': {
                        text: 'Proceed',
                        btnClass: 'btn-info',
                        action: function () {
                            
                        $.ajax({
                            url:'/packages/woocommerce/settings/delete',
                            type: "POST",
                            data: { id: id, _token: CSRF_TOKEN },
                            cache: false,
                            beforeSend: function () {

                            },
                            success: function (data) {
                            if(data){
                                window.location.href = '/packages/woocommerce/settings';
                            } 
                           }
                          });
                        }
                      },
                      cancel: function () {
                        $.alert('Operation <strong>Canceled</strong>');
                     }
                   }
               });
           });
        // ends

    </script>
@endpush