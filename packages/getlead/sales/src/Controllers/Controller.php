<?php

namespace Getlead\Sales\Controllers;

use Illuminate\Routing\Controller as BaseController;
use Carbon\Carbon;
use Validator;

class Controller extends BaseController
{
    public function response($status,$error,$message,$data=[]){
        if (!$message && $error) {
            switch ($status) {
              case 201:
              {
                $message = 'Failed';
                break;
              }
              case 400:
              {
                $message = 'Missing required fields';
                break;
              }
              case 401:
              {
                $message = 'Unauthorized';
                break;
              }
              case 500:
              {
                $message = 'Internal server error';
              }
            }
          }elseif(!$message && !$error){
            switch ($status) {
                case 200:
                {
                  $message = 'Success';
                  break;
                }
            }
          }
        return response()->json([
            'status'=> $error ? 0 : 1,
            'message'=> $message,
            'data'=> $data
        ]);
    }
    public function requestValidate($request,$fields,$messages){
        $validation= Validator::make($request->all(),$fields,$messages);
        if($validation->fails()){
          \Log::error($validation->errors());
            return $this->response(400,true,$validation->errors()->first(),$validation->errors());
        }
        return null;
    }
}
