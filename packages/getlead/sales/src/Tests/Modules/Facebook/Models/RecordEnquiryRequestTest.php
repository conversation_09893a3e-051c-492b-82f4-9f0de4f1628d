<?php

declare(strict_types=1);

namespace Getlead\Sales\Tests\Modules\Facebook\Models;

use App\Modules\Facebook\Models\FbWorkFlow;
use App\Modules\Facebook\Models\LeadStatus;
use App\Modules\Facebook\Models\RecordEnquiryRequest;
use App\Modules\Facebook\Models\RecordEnquiryRequestStatus;
use App\Modules\Facebook\ValueObjects\LeadData;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

final class RecordEnquiryRequestTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * @test
     */
    public function it_should_include_workflow_name_using_scope(): void
    {
        // Create a workflow
        $workflow = FbWorkFlow::create([
            'workflow_name' => 'Test Workflow',
            'vendor_id' => 1234,
            'fb_vendor_id' => 1,
            'fb_page_id' => 123456,
            'fb_ad_id' => 789012,
            'mapped_keys' => [
                ['key' => 'mobileno', 'label' => 'mobileno']
            ],
            'page_access_token' => 'test_token',
            'active' => 1,
        ]);

        // Create a record enquiry request that matches the workflow
        $recordEnquiryRequest = RecordEnquiryRequest::create([
            'page_id' => (string) $workflow->fb_page_id,
            'form_id' => (string) $workflow->fb_ad_id,
            'lead_gen_id' => '12345',
            'vendor_id' => 1234,
            'received_at' => now(),
            'status' => RecordEnquiryRequestStatus::Success,
            'failure_reason' => '',
            'lead_data' => new LeadData(
                source: 'Facebook',
                name: 'John Doe',
                phoneNumber: '+971551234567',
                email: '<EMAIL>',
                metadata: []
            ),
            'lead_status' => LeadStatus::New,
        ]);

        // Create another record enquiry request without matching workflow
        $recordEnquiryRequestWithoutWorkflow = RecordEnquiryRequest::create([
            'page_id' => '999999',
            'form_id' => '888888',
            'lead_gen_id' => '54321',
            'vendor_id' => 1234,
            'received_at' => now(),
            'status' => RecordEnquiryRequestStatus::Success,
            'failure_reason' => '',
            'lead_data' => new LeadData(
                source: 'Facebook',
                name: 'Jane Doe',
                phoneNumber: '+971551234568',
                email: '<EMAIL>',
                metadata: []
            ),
            'lead_status' => LeadStatus::New,
        ]);

        // Test the scope
        $results = RecordEnquiryRequest::withWorkflowName()
            ->where('vendor_id', 1234)
            ->get();

        $this->assertCount(2, $results);

        // Find the record with workflow
        $recordWithWorkflow = $results->firstWhere('id', $recordEnquiryRequest->id);
        $this->assertNotNull($recordWithWorkflow);
        $this->assertEquals('Test Workflow', $recordWithWorkflow->workflow_name);

        // Find the record without workflow
        $recordWithoutWorkflow = $results->firstWhere('id', $recordEnquiryRequestWithoutWorkflow->id);
        $this->assertNotNull($recordWithoutWorkflow);
        $this->assertNull($recordWithoutWorkflow->workflow_name);
    }

    /**
     * @test
     */
    public function it_should_only_match_active_workflows(): void
    {
        // Create an inactive workflow
        $inactiveWorkflow = FbWorkFlow::create([
            'workflow_name' => 'Inactive Workflow',
            'vendor_id' => 1234,
            'fb_vendor_id' => 1,
            'fb_page_id' => 123456,
            'fb_ad_id' => 789012,
            'mapped_keys' => [
                ['key' => 'mobileno', 'label' => 'mobileno']
            ],
            'page_access_token' => 'test_token',
            'active' => 0, // Inactive
        ]);

        // Create a record enquiry request that matches the inactive workflow
        $recordEnquiryRequest = RecordEnquiryRequest::create([
            'page_id' => (string) $inactiveWorkflow->fb_page_id,
            'form_id' => (string) $inactiveWorkflow->fb_ad_id,
            'lead_gen_id' => '12345',
            'vendor_id' => 1234,
            'received_at' => now(),
            'status' => RecordEnquiryRequestStatus::Success,
            'failure_reason' => '',
            'lead_data' => new LeadData(
                source: 'Facebook',
                name: 'John Doe',
                phoneNumber: '+971551234567',
                email: '<EMAIL>',
                metadata: []
            ),
            'lead_status' => LeadStatus::New,
        ]);

        // Test the scope
        $results = RecordEnquiryRequest::withWorkflowName()
            ->where('vendor_id', 1234)
            ->get();

        $this->assertCount(1, $results);

        // Find the record
        $record = $results->first();
        $this->assertNotNull($record);
        $this->assertEquals($recordEnquiryRequest->id, $record->id);
        
        // Should be null since the workflow is inactive
        $this->assertNull($record->workflow_name);
    }
}
