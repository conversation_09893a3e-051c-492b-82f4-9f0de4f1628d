<?php

declare(strict_types=1);

namespace Getlead\Sales\Tests\Modules\Facebook\Jobs\RecordEnquiry\Automations;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\Modules\Facebook\Jobs\RecordEnquiry\Automations\AssignAgent;
use App\Modules\Facebook\Jobs\RecordEnquiry\Automations\AutomationRuleHandler;
use App\Modules\Facebook\Jobs\RecordEnquiry\Automations\PurposeChangeRuleHandler;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

final class PurposeChangeRuleHandlerTest extends TestCase
{
    use DatabaseTransactions;
    use AutomationTraits;

    private AssignAgent&MockInterface $assignAgent;

    private PurposeChangeRuleHandler $purposeChangeRuleHandler;

    protected function setUp(): void
    {
        parent::setUp();

        $this->assignAgent = Mockery::mock(AssignAgent::class);
        $this->purposeChangeRuleHandler = new PurposeChangeRuleHandler(assignAgent: $this->assignAgent);
    }

    /**
     * @test
     */
    public function it_should_process_purpose_change_rules(): void
    {
        $enquiry = $this->getEnquiry();
        $rules = Collection::make([
            AutomationRule::factory()->create([
                'trigger' => 'purpose_change',
                'action' => 'assign',
                'enquiry_purpose_id' => $enquiry->fk_int_purpose_id,
            ]),
        ]);

        $this->assignAgent->shouldReceive('byRule')
            ->once()
            ->withArgs(function (AutomationRule $argRule, Enquiry $argEnquiry) use ($rules, $enquiry): bool {
                $this->assertEquals($argRule->id, $rules->first()->id);
                $this->assertEquals($argEnquiry->pk_int_enquiry_id, $enquiry->pk_int_enquiry_id);
                return true;
            });

        $this->purposeChangeRuleHandler->handle(enquiry: $enquiry, rules: $rules);
    }

    /**
     * @test
     */
    public function it_should_skip_when_no_purpose_change_rules_found(): void
    {
        $enquiry = $this->getEnquiry();
        $rules = Collection::make([
            AutomationRule::factory()->create([
                'trigger' => 'source_change',
                'action' => 'assign',
                'enquiry_purpose_id' => $enquiry->fk_int_purpose_id,
            ]),
        ]);

        $this->assignAgent->shouldReceive('byRule')
            ->never();

        $this->purposeChangeRuleHandler->handle(enquiry: $enquiry, rules: $rules);
    }

    /**
     * @test
     */
    public function it_should_skip_when_purpose_id_does_not_match(): void
    {
        $enquiry = $this->getEnquiry([
            'fk_int_purpose_id' => 1,
        ]);
        $rules = Collection::make([
            AutomationRule::factory()->create([
                'trigger' => 'purpose_change',
                'action' => 'assign',
                'enquiry_purpose_id' => 2, // Different purpose ID
            ]),
        ]);

        $this->assignAgent->shouldReceive('byRule')
            ->never();

        $this->purposeChangeRuleHandler->handle(enquiry: $enquiry, rules: $rules);
    }

    /**
     * @test
     */
    public function it_should_call_parent_handler_when_no_assignment_rule_found(): void
    {
        $enquiry = $this->getEnquiry();
        $rules = Collection::make([
            AutomationRule::factory()->create([
                'trigger' => 'status_change', // Different trigger
                'action' => 'assign',
                'enquiry_purpose_id' => $enquiry->fk_int_purpose_id,
            ]),
        ]);

        $nextHandler = Mockery::mock(AutomationRuleHandler::class);
        $nextHandler->shouldReceive('handle')
            ->once()
            ->with($enquiry, $rules);

        $this->purposeChangeRuleHandler->setNext($nextHandler);

        $this->assignAgent->shouldReceive('byRule')
            ->never();

        $this->purposeChangeRuleHandler->handle(enquiry: $enquiry, rules: $rules);
    }
}
