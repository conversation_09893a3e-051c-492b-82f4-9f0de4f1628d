<?php

namespace Getlead\Sales\Tests\Modules\Facebook\Jobs\RecordLead;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\BackendModel\WhatsappTemplate;
use App\Common\Common;
use App\Common\Variables;
use App\FeatureRelease\Facade\FeatureReleaseChecker;
use App\IvrExtension;
use App\Ivr\IVRWebhook\Jobs\AutomationApiCall;
use App\Modules\Facebook\Jobs\RecordEnquiry\Automations\AutomationProcessor;
use App\Modules\Facebook\Jobs\RecordEnquiry\CutisInternationalCustomAutomation;
use App\Modules\Facebook\Jobs\RecordEnquiry\ProcessNewEnquiryAutomations;
use App\User;
use Getlead\Campaign\Models\LeadCampaign;
use Getlead\Messagebird\Common\GupShup;
use Getlead\Messagebird\Models\WatsappCredential;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

final class ProcessNewEnquiryAutomationsTest extends TestCase
{
    use DatabaseTransactions;

    private ProcessNewEnquiryAutomations $automation;

    private GupShup&MockInterface $gupshup;

    private Common&MockInterface $common;

    private CutisInternationalCustomAutomation&MockInterface $cutisInternationalCustomAutomation;

    private AutomationProcessor&MockInterface $automationProcessor;

    protected function setUp(): void
    {
        parent::setUp();
        $this->gupshup = Mockery::mock(GupShup::class);
        $this->common = Mockery::mock(Common::class);
        $this->cutisInternationalCustomAutomation = Mockery::mock(CutisInternationalCustomAutomation::class);
        $this->automationProcessor = Mockery::mock(AutomationProcessor::class);

        $this->automation = new ProcessNewEnquiryAutomations(
            gupshup: $this->gupshup,
            common: $this->common,
            cutisInternationalCustomAutomation: $this->cutisInternationalCustomAutomation,
            automationProcessor: $this->automationProcessor,
        );

        Log::spy();
        Bus::fake();
        FeatureReleaseChecker::spy();
    }

    /**
     * @test
     */
    public function it_should_void_when_no_automations_were_found(): void
    {
        $enquiry = $this->getEnquiry();

        $this->cutisInternationalCustomAutomation->shouldReceive('for')
            ->once()
            ->with($enquiry);

        Log::shouldReceive('withContext')
            ->twice()
            ->with([
                'vendor_id' => $enquiry->fk_int_user_id,
                'source_id' => $enquiry->fk_int_enquiry_type_id,
            ]);

        Log::shouldReceive('info')
            ->once()
            ->with('No rules found for enquiry');

        $this->automation->for(enquiry: $enquiry);
    }

    /**
     * @test
     */
    public function it_should_process_whatsapp_automations(): void
    {
        $enquiry = $this->getEnquiry();
        $whatsappTemplate = $this->getWhatsappTemplate();
        $automationRule = $this->getAutomationRule([
            'whatsapp_template_id' => $whatsappTemplate->pk_int_whatsapp_template_id,
        ]);
        $whatsappCredentials = $this->getWhatsappCredentials();

        $this->cutisInternationalCustomAutomation->shouldReceive('for')
            ->once()
            ->with($enquiry);

        Log::shouldReceive('withContext')
            ->twice()
            ->with([
                'vendor_id' => $enquiry->fk_int_user_id,
                'source_id' => $enquiry->fk_int_enquiry_type_id,
            ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Whatsapp message sending attempt');

        $this->gupshup->shouldReceive('sendWatsappMessageIndividal')
            ->once()
            ->withArgs(function (string $countryId, string $customerMobileNumber, string $message, array $data) use ($enquiry, $whatsappCredentials): bool {
                $this->assertEquals($enquiry->vchr_customer_mobile, $customerMobileNumber);
                $this->assertEquals('Hello Test, your enquiry is received!', $message);
                $this->assertEquals([
                    'api_key' => $whatsappCredentials->access_key,
                    'from_number' => $whatsappCredentials->source_mobile_num,
                    'app_name' => $whatsappCredentials->template_name
                ], $data);

                return true;
            });

        $this->automationProcessor->shouldReceive('process')
            ->once()
            ->with($enquiry, Mockery::on(function ($collection) use ($automationRule) {
                return $collection instanceof \Illuminate\Database\Eloquent\Collection &&
                       $collection->contains('id', $automationRule->id);
            }));

        $this->automation->for(enquiry: $enquiry);
    }

    /**
     * @test
     */
    public function it_should_process_api_automations_with_legacy_flow(): void
    {
        $enquiry = $this->getEnquiry([
            'fk_int_user_id' => Variables::NIKSHAN_USER_ID, // vendor id
        ]);

        $user = new User([
            'pk_int_user_id' => Variables::NIKSHAN_USER_ID,
            'vchr_user_name' => 'Nikshan',
            'email' => '<EMAIL>',
            'vchr_user_mobile' => '+919876543210',
        ]);
        $user->save();

        $ivrExtension = new IvrExtension([
            'staff_id' => $enquiry->fk_int_user_id,
            'virtual_number_id' => 1,
            'extension' => '123456789',
        ]);
        $ivrExtension->save();

        $user->setRelation('userExtension', $ivrExtension);
        $enquiry->setRelation('assigned_user', $user);

        $automationRule = $this->getAutomationRule([
            'vendor_id' => $enquiry->fk_int_user_id,
            'action' => 'api',
            'api' => 'https://api.nikshan.com/api/v1/enquiry/create',
        ]);

        $this->cutisInternationalCustomAutomation->shouldReceive('for')
            ->once()
            ->with($enquiry);

        Log::shouldReceive('withContext')
            ->twice()
            ->with([
                'vendor_id' => $enquiry->fk_int_user_id,
                'source_id' => $enquiry->fk_int_enquiry_type_id,
            ]);

        // Mock feature flag to return false (legacy flow)
        FeatureReleaseChecker::shouldReceive('isEnabled')
            ->once()
            ->with('new-lead-automation-api-call', Mockery::type('App\FeatureRelease\TargetContext'))
            ->andReturn(false);

        $this->common->shouldReceive('postToIvrAutoCall')
            ->once()
            ->withArgs(function (string $argUrl, string $argExtension, array $argData) use ($enquiry, $ivrExtension, $automationRule): bool {
                $this->assertEquals($ivrExtension->extension, $argExtension);
                $this->assertEquals($automationRule->api, $argUrl);
                $this->assertEquals([
                    'phone' => $enquiry->vchr_customer_mobile,
                ], $argData);
                return true;
            });

        $this->automationProcessor->shouldReceive('process')
            ->once()
            ->with($enquiry, Mockery::on(function ($collection) use ($automationRule) {
                return $collection instanceof \Illuminate\Database\Eloquent\Collection &&
                       $collection->contains('id', $automationRule->id);
            }));

        $this->automation->for(enquiry: $enquiry);
    }

    /**
     * @test
     */
    public function it_should_process_api_automations_with_new_flow(): void
    {
        $enquiry = $this->getEnquiry([
            'fk_int_user_id' => Variables::NIKSHAN_USER_ID,
            'pk_int_enquiry_id' => 123,
        ]);

        $automationRule = $this->getAutomationRule([
            'vendor_id' => $enquiry->fk_int_user_id,
            'action' => 'api',
            'api' => 'https://api.nikshan.com/api/v1/enquiry/create',
            'id' => 456,
        ]);

        $this->cutisInternationalCustomAutomation->shouldReceive('for')
            ->once()
            ->with($enquiry);

        Log::shouldReceive('withContext')
            ->twice()
            ->with([
                'vendor_id' => $enquiry->fk_int_user_id,
                'source_id' => $enquiry->fk_int_enquiry_type_id,
            ]);

        // Mock feature flag to return true (new flow)
        FeatureReleaseChecker::shouldReceive('isEnabled')
            ->once()
            ->with('new-lead-automation-api-call', Mockery::type('App\FeatureRelease\TargetContext'))
            ->andReturn(true);

        $this->automationProcessor->shouldReceive('process')
            ->once()
            ->with($enquiry, Mockery::on(function ($collection) use ($automationRule) {
                return $collection instanceof \Illuminate\Database\Eloquent\Collection &&
                       $collection->contains('id', $automationRule->id);
            }));

        $this->automation->for(enquiry: $enquiry);

        Bus::assertDispatched(AutomationApiCall::class, function ($job) use ($enquiry, $automationRule) {
            return $job->enquiryId === $enquiry->pk_int_enquiry_id &&
                   $job->vendorId === $enquiry->fk_int_user_id &&
                   $job->automationId === $automationRule->id;
        });
    }

    /**
     * @test
     */
    public function it_should_process_campaign_automation(): void
    {
        $enquiry = $this->getEnquiry();

        $campaign = new LeadCampaign();
        $campaign->type = LeadCampaign::STATIC; // Use integer type
        $campaign->save();

        $automationRule = $this->getAutomationRule([
            'action' => 'add_to_campaign',
            'campaign_id' => $campaign->id,
        ]);

        $this->cutisInternationalCustomAutomation->shouldReceive('for')
            ->once()
            ->with($enquiry);

        Log::shouldReceive('withContext')
            ->twice()
            ->with([
                'vendor_id' => $enquiry->fk_int_user_id,
                'source_id' => $enquiry->fk_int_enquiry_type_id,
            ]);

        $this->common->shouldReceive('addToCampaign')
            ->once()
            ->withArgs(function ($automation, $lead, $userId, $vendorId, $type) use ($automationRule, $enquiry, $campaign): bool {
                $this->assertEquals($automationRule->id, $automation->id);
                $this->assertEquals($enquiry->pk_int_enquiry_id, $lead->pk_int_enquiry_id);
                $this->assertEquals($enquiry->fk_int_user_id, $userId);
                $this->assertEquals($enquiry->fk_int_user_id, $vendorId);
                $this->assertEquals($campaign->type, $type);
                return true;
            });

        $this->automationProcessor->shouldReceive('process')
            ->once()
            ->with($enquiry, Mockery::on(function ($collection) use ($automationRule) {
                return $collection instanceof \Illuminate\Database\Eloquent\Collection &&
                       $collection->contains('id', $automationRule->id);
            }));

        $this->automation->for(enquiry: $enquiry);
    }

    /**
     * @test
     */
    public function it_should_skip_api_automation_for_non_nikshan_vendor(): void
    {
        $enquiry = $this->getEnquiry([
            'fk_int_user_id' => 999, // Not Nikshan vendor ID
        ]);

        $automationRule = $this->getAutomationRule([
            'vendor_id' => $enquiry->fk_int_user_id,
            'action' => 'api',
            'api' => 'https://api.example.com/webhook',
        ]);

        $this->cutisInternationalCustomAutomation->shouldReceive('for')
            ->once()
            ->with($enquiry);

        Log::shouldReceive('withContext')
            ->twice()
            ->with([
                'vendor_id' => $enquiry->fk_int_user_id,
                'source_id' => $enquiry->fk_int_enquiry_type_id,
            ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Nikshan automation not implemented');

        $this->common->shouldNotReceive('postToIvrAutoCall');
        FeatureReleaseChecker::shouldNotReceive('isEnabled');
        Bus::assertNotDispatched(AutomationApiCall::class);

        $this->automationProcessor->shouldReceive('process')
            ->once()
            ->with($enquiry, Mockery::on(function ($collection) use ($automationRule) {
                return $collection instanceof \Illuminate\Database\Eloquent\Collection &&
                       $collection->contains('id', $automationRule->id);
            }));

        $this->automation->for(enquiry: $enquiry);
    }

    /**
     * @test
     */
    public function it_should_handle_missing_whatsapp_template(): void
    {
        $enquiry = $this->getEnquiry();
        $automationRule = $this->getAutomationRule([
            'whatsapp_template_id' => 999, // Non-existent template ID
        ]);
        // Don't create WhatsApp template to simulate missing template

        $this->cutisInternationalCustomAutomation->shouldReceive('for')
            ->once()
            ->with($enquiry);

        Log::shouldReceive('withContext')
            ->twice()
            ->with([
                'vendor_id' => $enquiry->fk_int_user_id,
                'source_id' => $enquiry->fk_int_enquiry_type_id,
            ]);

        Log::shouldReceive('error')
            ->once()
            ->with('Whatsapp template not found');

        $this->gupshup->shouldNotReceive('sendWatsappMessageIndividal');

        $this->automationProcessor->shouldReceive('process')
            ->once()
            ->with($enquiry, Mockery::on(function ($collection) use ($automationRule) {
                return $collection instanceof \Illuminate\Database\Eloquent\Collection &&
                       $collection->contains('id', $automationRule->id);
            }));

        $this->automation->for(enquiry: $enquiry);
    }

    /**
     * @test
     */
    public function it_should_handle_missing_whatsapp_credentials(): void
    {
        $enquiry = $this->getEnquiry();
        $whatsappTemplate = $this->getWhatsappTemplate();
        $automationRule = $this->getAutomationRule([
            'whatsapp_template_id' => $whatsappTemplate->pk_int_whatsapp_template_id,
        ]);
        // Don't create WhatsApp credentials to simulate missing credentials

        $this->cutisInternationalCustomAutomation->shouldReceive('for')
            ->once()
            ->with($enquiry);

        Log::shouldReceive('withContext')
            ->twice()
            ->with([
                'vendor_id' => $enquiry->fk_int_user_id,
                'source_id' => $enquiry->fk_int_enquiry_type_id,
            ]);

        Log::shouldReceive('error')
            ->once()
            ->with('Whatsapp credential not found');

        $this->gupshup->shouldNotReceive('sendWatsappMessageIndividal');

        $this->automationProcessor->shouldReceive('process')
            ->once()
            ->with($enquiry, Mockery::on(function ($collection) use ($automationRule) {
                return $collection instanceof \Illuminate\Database\Eloquent\Collection &&
                       $collection->contains('id', $automationRule->id);
            }));

        $this->automation->for(enquiry: $enquiry);
    }

    private function getEnquiry(array $overrides = []): Enquiry
    {
        $attributes = array_merge([
            'fk_int_user_id' => 1,
            'fk_int_purpose_id' => 1,
            'fk_int_enquiry_type_id' => 1,
            'lead_type_id' => 1,
            'lead_source' => 'Meta Leads',
            'mobile_no' => '+914847110101',
            'vchr_customer_name' => 'Test',
            'vchr_customer_mobile' => '+914847110101',
            'vchr_customer_email' => '<EMAIL>',
            'fb_ad_id' => '1234567890',
            'fb_ad_info' => 'Test',
            'read_status' => 0,
            'staff_id' => 1,
            'next_follow_up' => '2021-01-01 00:00:00',
        ], $overrides);

        return new Enquiry($attributes);
    }

    private function getAutomationRule(array $overrides = []): AutomationRule
    {
        $attributes = array_merge([
            'vendor_id' => 1,
            'trigger' => 'new_lead',
            'action' => 'whatsapp',
            'enquiry_source_id' => 1,
            'whatsapp_template_id' => 1,
        ], $overrides);

        $automationRule = new AutomationRule($attributes);
        $automationRule->save();

        return $automationRule;
    }

    private function getWhatsappTemplate(array $overrides = []): WhatsappTemplate
    {
        $attributes = array_merge([
            'text_whatsapp_template_description' => 'Hello {{name}}, your enquiry is received!',
        ], $overrides);

        $whatsappTemplate = new WhatsappTemplate($attributes);
        $whatsappTemplate->save();

        return $whatsappTemplate;
    }

    private function getWhatsappCredentials(): WatsappCredential
    {
        $credentials = new WatsappCredential();
        $credentials->vendor_id = 1;
        $credentials->status = 1;
        $credentials->platform_id = 2;
        $credentials->save();

        return $credentials;
    }
}
