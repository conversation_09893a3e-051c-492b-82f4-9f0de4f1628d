<?php

namespace Getlead\Sales\Tests\Modules\Facebook\Jobs\RecordEnquiry\Automations;

use App\BackendModel\Enquiry;
use App\Modules\Facebook\Jobs\RecordEnquiry\Automations\AbstractRuleHandler;
use App\Modules\Facebook\Jobs\RecordEnquiry\Automations\AutomationRuleHandler;
use Illuminate\Database\Eloquent\Collection;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

final class AbstractRuleHandlerTest extends TestCase
{
    use AutomationTraits;

    private MockInterface $nextHandler;

    private AbstractRuleHandler $ruleHandler;

    protected function setUp(): void
    {
        parent::setUp();

        $this->nextHandler = Mockery::mock(AutomationRuleHandler::class);
        $this->ruleHandler = new class extends AbstractRuleHandler {
            public function handle(Enquiry $enquiry, Collection $rules): void
            {
                parent::handle($enquiry, $rules);
            }
        };
    }

    /**
     * @test
     */
    public function it_should_not_call_next_handler_when_not_set(): void
    {
        $enquiry = $this->getEnquiry();
        $rules = Collection::make();

        $this->nextHandler->shouldReceive('handle')
            ->never();

        $this->ruleHandler->handle($enquiry, $rules);
    }

    /**
     * @test
     */
    public function it_should_call_next_handler_when_available(): void
    {
        $enquiry = $this->getEnquiry();
        $rules = Collection::make();

        $this->ruleHandler->setNext($this->nextHandler);

        $this->nextHandler->shouldReceive('handle')
            ->once()
            ->with($enquiry, $rules);

        $this->ruleHandler->handle($enquiry, $rules);
    }
}
