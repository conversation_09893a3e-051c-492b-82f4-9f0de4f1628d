<?php

declare(strict_types=1);

namespace Getlead\Sales\Tests\Modules\Facebook\Http\Controllers;

use App\User;
use App\Modules\Facebook\Models\FbWorkFlow;
use App\Modules\Facebook\Models\LeadStatus;
use App\Modules\Facebook\Models\RecordEnquiryRequest;
use App\Modules\Facebook\Models\RecordEnquiryRequestStatus;
use App\Modules\Facebook\ValueObjects\LeadData;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

final class RecordEnquiryRequestUrlStateTest extends TestCase
{
    use DatabaseTransactions;

    private int $vendorId = 1234;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $user = User::factory()->create([
            'pk_int_user_id' => $this->vendorId,
        ]);

        $this->actingAs($user);
        
        // Disable middleware for testing
        $this->withoutMiddleware();
    }

    /**
     * @test
     */
    public function it_should_handle_url_parameters_for_filtering(): void
    {
        // Create test data
        $workflow = FbWorkFlow::create([
            'workflow_name' => 'Test Workflow',
            'vendor_id' => $this->vendorId,
            'fb_vendor_id' => 1,
            'fb_page_id' => 123456,
            'fb_ad_id' => 789012,
            'mapped_keys' => [
                ['key' => 'mobileno', 'label' => 'mobileno']
            ],
            'page_access_token' => 'test_token',
            'active' => 1,
        ]);

        $recordEnquiryRequest = RecordEnquiryRequest::create([
            'page_id' => (string) $workflow->fb_page_id,
            'form_id' => (string) $workflow->fb_ad_id,
            'lead_gen_id' => '12345',
            'vendor_id' => $this->vendorId,
            'received_at' => now(),
            'status' => RecordEnquiryRequestStatus::Success,
            'failure_reason' => '',
            'lead_data' => new LeadData(
                source: 'Facebook',
                name: 'John Doe',
                phoneNumber: '+971551234567',
                email: '<EMAIL>',
                metadata: []
            ),
            'lead_status' => LeadStatus::New,
        ]);

        // Test with URL parameters
        $response = $this->getJson(route('v1.facebook.lead-requests.data', [
            'from_date' => now()->format('Y-m-d'),
            'to_date' => now()->format('Y-m-d'),
            'search' => 'John'
        ]));

        $response->assertOk();
        
        $responseData = $response->json();
        
        // Verify the response structure
        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(1, $responseData['data']);

        // Find the record
        $record = collect($responseData['data'])->first();
        $this->assertNotNull($record);
        $this->assertEquals('Test Workflow', $record['workflow_name']);
        $this->assertEquals('John Doe', $record['name']);
    }

    /**
     * @test
     */
    public function it_should_handle_search_parameter_from_url(): void
    {
        // Create test data with different names
        $workflow = FbWorkFlow::create([
            'workflow_name' => 'Test Workflow',
            'vendor_id' => $this->vendorId,
            'fb_vendor_id' => 1,
            'fb_page_id' => 123456,
            'fb_ad_id' => 789012,
            'mapped_keys' => [
                ['key' => 'mobileno', 'label' => 'mobileno']
            ],
            'page_access_token' => 'test_token',
            'active' => 1,
        ]);

        // Create record that should match search
        RecordEnquiryRequest::create([
            'page_id' => (string) $workflow->fb_page_id,
            'form_id' => (string) $workflow->fb_ad_id,
            'lead_gen_id' => '12345',
            'vendor_id' => $this->vendorId,
            'received_at' => now(),
            'status' => RecordEnquiryRequestStatus::Success,
            'failure_reason' => '',
            'lead_data' => new LeadData(
                source: 'Facebook',
                name: 'John Doe',
                phoneNumber: '+971551234567',
                email: '<EMAIL>',
                metadata: []
            ),
            'lead_status' => LeadStatus::New,
        ]);

        // Create record that should NOT match search
        RecordEnquiryRequest::create([
            'page_id' => (string) $workflow->fb_page_id,
            'form_id' => (string) $workflow->fb_ad_id,
            'lead_gen_id' => '54321',
            'vendor_id' => $this->vendorId,
            'received_at' => now(),
            'status' => RecordEnquiryRequestStatus::Success,
            'failure_reason' => '',
            'lead_data' => new LeadData(
                source: 'Facebook',
                name: 'Jane Smith',
                phoneNumber: '+971551234568',
                email: '<EMAIL>',
                metadata: []
            ),
            'lead_status' => LeadStatus::New,
        ]);

        // Test search parameter
        $response = $this->getJson(route('v1.facebook.lead-requests.data', [
            'search' => 'John'
        ]));

        $response->assertOk();
        
        $responseData = $response->json();
        
        // Should only return the John Doe record
        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(1, $responseData['data']);

        $record = collect($responseData['data'])->first();
        $this->assertEquals('John Doe', $record['name']);
    }

    /**
     * @test
     */
    public function it_should_return_index_page_with_url_parameters(): void
    {
        // Test that the index page loads correctly with URL parameters
        $response = $this->get(route('v1.facebook.lead-requests', [
            'from_date' => '2023-01-01',
            'to_date' => '2023-12-31',
            'search' => 'test'
        ]));

        $response->assertOk();
        $response->assertViewIs('facebook::record-enquiry-requests.index');
        $response->assertViewHas('stats');
    }
}
