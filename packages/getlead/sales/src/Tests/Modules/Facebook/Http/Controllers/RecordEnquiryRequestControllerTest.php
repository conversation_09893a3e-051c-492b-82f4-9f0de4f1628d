<?php

declare(strict_types=1);

namespace Getlead\Sales\Tests\Modules\Facebook\Http\Controllers;

use App\User;
use App\Modules\Facebook\Models\FbWorkFlow;
use App\Modules\Facebook\Models\LeadStatus;
use App\Modules\Facebook\Models\RecordEnquiryRequest;
use App\Modules\Facebook\Models\RecordEnquiryRequestStatus;
use App\Modules\Facebook\ValueObjects\LeadData;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

final class RecordEnquiryRequestControllerTest extends TestCase
{
    use DatabaseTransactions;

    private int $vendorId = 1234;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $user = User::factory()->create([
            'pk_int_user_id' => $this->vendorId,
        ]);

        $this->actingAs($user);

        // Disable middleware for testing
        $this->withoutMiddleware();
    }

    /**
     * @test
     */
    public function it_should_include_workflow_name_in_datatable_response(): void
    {
        // Create a workflow
        $workflow = FbWorkFlow::create([
            'workflow_name' => 'Test Workflow',
            'vendor_id' => $this->vendorId,
            'fb_vendor_id' => 1,
            'fb_page_id' => 123456,
            'fb_ad_id' => 789012,
            'mapped_keys' => [
                ['key' => 'mobileno', 'label' => 'mobileno']
            ],
            'page_access_token' => 'test_token',
            'active' => 1,
        ]);

        // Create a record enquiry request that matches the workflow
        $recordEnquiryRequest = RecordEnquiryRequest::create([
            'page_id' => (string) $workflow->fb_page_id,
            'form_id' => (string) $workflow->fb_ad_id,
            'lead_gen_id' => '12345',
            'vendor_id' => $this->vendorId,
            'received_at' => now(),
            'status' => RecordEnquiryRequestStatus::Success,
            'failure_reason' => '',
            'lead_data' => new LeadData(
                source: 'Facebook',
                name: 'John Doe',
                phoneNumber: '+971551234567',
                email: '<EMAIL>',
                metadata: []
            ),
            'lead_status' => LeadStatus::New,
        ]);

        // Create another record enquiry request without matching workflow
        $recordEnquiryRequestWithoutWorkflow = RecordEnquiryRequest::create([
            'page_id' => '999999',
            'form_id' => '888888',
            'lead_gen_id' => '54321',
            'vendor_id' => $this->vendorId,
            'received_at' => now(),
            'status' => RecordEnquiryRequestStatus::Success,
            'failure_reason' => '',
            'lead_data' => new LeadData(
                source: 'Facebook',
                name: 'Jane Doe',
                phoneNumber: '+971551234568',
                email: '<EMAIL>',
                metadata: []
            ),
            'lead_status' => LeadStatus::New,
        ]);

        // Make the request to get record enquiry requests
        $response = $this->getJson(route('v1.facebook.lead-requests.data'));

        $response->assertOk();

        $responseData = $response->json();
        
        // Verify the response structure
        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(2, $responseData['data']);

        // Find the record with workflow
        $recordWithWorkflow = collect($responseData['data'])->firstWhere('id', $recordEnquiryRequest->id);
        $this->assertNotNull($recordWithWorkflow);
        $this->assertEquals('Test Workflow', $recordWithWorkflow['workflow_name']);

        // Find the record without workflow
        $recordWithoutWorkflow = collect($responseData['data'])->firstWhere('id', $recordEnquiryRequestWithoutWorkflow->id);
        $this->assertNotNull($recordWithoutWorkflow);
        $this->assertEquals('N/A', $recordWithoutWorkflow['workflow_name']);
    }

    /**
     * @test
     */
    public function it_should_only_match_active_workflows(): void
    {
        // Create an inactive workflow
        $inactiveWorkflow = FbWorkFlow::create([
            'workflow_name' => 'Inactive Workflow',
            'vendor_id' => $this->vendorId,
            'fb_vendor_id' => 1,
            'fb_page_id' => 123456,
            'fb_ad_id' => 789012,
            'mapped_keys' => [
                ['key' => 'mobileno', 'label' => 'mobileno']
            ],
            'page_access_token' => 'test_token',
            'active' => 0, // Inactive
        ]);

        // Create a record enquiry request that matches the inactive workflow
        $recordEnquiryRequest = RecordEnquiryRequest::create([
            'page_id' => (string) $inactiveWorkflow->fb_page_id,
            'form_id' => (string) $inactiveWorkflow->fb_ad_id,
            'lead_gen_id' => '12345',
            'vendor_id' => $this->vendorId,
            'received_at' => now(),
            'status' => RecordEnquiryRequestStatus::Success,
            'failure_reason' => '',
            'lead_data' => new LeadData(
                source: 'Facebook',
                name: 'John Doe',
                phoneNumber: '+971551234567',
                email: '<EMAIL>',
                metadata: []
            ),
            'lead_status' => LeadStatus::New,
        ]);

        // Make the request to get record enquiry requests
        $response = $this->getJson(route('v1.facebook.lead-requests.data'));

        $response->assertOk();
        
        $responseData = $response->json();
        
        // Find the record
        $record = collect($responseData['data'])->firstWhere('id', $recordEnquiryRequest->id);
        $this->assertNotNull($record);
        
        // Should show N/A since the workflow is inactive
        $this->assertEquals('N/A', $record['workflow_name']);
    }
}
