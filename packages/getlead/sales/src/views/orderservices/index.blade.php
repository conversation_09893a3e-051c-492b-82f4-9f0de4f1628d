@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
<main class="main-wrapper main-wrapper-2">
   @if(session('flash_notification'))
   <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
   @endif
   <div class="task-panel">
      <div class="row justify-content-between ">
         <div class="">
            <h5>Order - Service</h5>
         </div>
         <div class="mb-2">
            <div class="task-nav">
               <div class="dropdown-navigation">
                  <ul>
                     <li class=""><a class="main-round-btn bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger" href="{{url('/user/sales/orderservices/create')}}" id="addEnquiry" href="#"><i class="fa fa-plus"></i>Add New Service</a>
                     </li>
                  </ul>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div class="layout-wrapper">
      <!--   / Side menu included /  -->
      @include ('sales::layouts.order-sidebar')
      @if(session('flash_notification'))
      <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
      @endif
      <div class="content-section">
         <!--  /Your content goes here/ -->
         <div class="row">
            <div class="col-lg-5 col-md-5">
            </div>
         </div>
         <div class="gl-table">
            <table id="datatable" class="table table-striped table-bordered nowrap table-custom table-responsive" cellspacing="0" width="100%">
                <thead>
                <tr>
                    <th>Sl No</th>
                    <th>Order ID</th>
                    <th>Agent Name</th>
                    <th>Dealer Name</th>
                    <th>Products</th>
                    <th>Order Amount</th>
                    <th>Date and Time</th>
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody id="datatable_body">
                </tbody>
            </table>
        </div>
      </div>
   </div>
</main>
@endsection 
@push('footer.script')
<script type="text/javascript">
   $(document).ready(function() 
   {
       $.ajaxSetup({
           headers: {
               'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
           }
       });
       $.ajax({
           type: 'GET',
   
           url: "{{ url('ajax/sales/orders') }}",
   
           data: { },
           cache: false,
           success: function(data) {
               var tabledata = '';
               var i=1;
               $('#datatable_body').html('')
               $.each(data.data,function(index,value){
                   var products='';
                   $.each(value.items,function(j,product){
                       if(product.type==2){
                       products += '<b>Name:</b>'+product.name+' |<b>Duration:</b>'+product.unit+'|<b>Days:</b>'+product.price_duration+' |<b>Qty:</b>'+product.qty+' | <b>Rate:</b>'+product.price+' | <b>Total:</b>'+product.amount+
                       '<br>';
                       }
                   })
                   if(products!=''){
                     //   $('#datatable_body').append('<tr><td>'+i+'</td><td>'+value.id+'</td><td>'+value.agent_name+'</td><td>'+value.name+'</td><td>'+products+'</td><td>'+value.amount+'</td><td>'+value.created_at+'</td><td><a href="/user/sales/orderservices/show/'+value.id+'"> <button  data-id="'+ value.id+'"  class="btn btn-sm btn-info mg-b-10 task-show"> <i class="fa fa-eye mg-r-5"></i></button></a></td></tr>');
                       $('#datatable_body').append('<tr><td>'+i+'</td><td>'+value.id+'</td><td>'+value.agent_name+'</td><td>'+value.name+'</td><td>'+products+'</td><td>' + value.amount + '</td><td>' + value.created_at +'</td><td><div class="dropdown show"><a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="fa fa-circle" aria-hidden="true"></i><i class="fa fa-circle" aria-hidden="true"></i> <i class="fa fa-eye" aria-hidden="true"></i></a><div class="dropdown-menu" aria-labelledby="dropdownMenuLink"><a href="/user/sales/orderservices/show/'+value.id+'" id="delete_plan"  class="dropdown-item task-show"><i class="fa fa-eye" aria-hidden="true"></i>&nbsp;View</a></td></tr>');
                   }
                   i=i+1;
               });
               $('#datatable').DataTable();
           },
           errors: function (){
   
           }
       });
   
   });
</script>
@endpush