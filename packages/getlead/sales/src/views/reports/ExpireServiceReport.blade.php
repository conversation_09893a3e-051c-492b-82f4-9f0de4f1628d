@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
    <main class="main-wrapper">

        <div class="task-panel">
            <div class="row justify-content-between ">
                <div class="col-lg-6 d-flex row-wrap align-items-center">
                    <a id="sidebarMenu" class="sidebar-menu"><i class="fa fa-th" aria-hidden="true"></i></a>
                    <h5>Sales Report</h5>
                </div>
                
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('sales::layouts.sidebar')
            <div class="content-section p-3 bg-white">
            <div class="content-section p-3 bg-white">
                <!--  /Your content goes here/ -->
                <div class="row">
                        <div class="card card-body filter-container bg-light-grey">
                            <div class="row">
                                <div class="col-sm-3">
                                <label for="">Date From</label>
                                    <input name="" class="date form_datetime form-control" type="date"
                                            id="from_date"
                                            value="" placeholder="Select From Date">
                                </div>
                                <div class="col-sm-3">
                                <label for="">Date To</label>
                                    <input name="" class="date form_datetime form-control" type="date"
                                            id="to_date"
                                            value="" placeholder="Select To Date">
                                </div>
                                <div class="col-sm-3">
                                <label for="">Dealers</label>
                                        <select class="form-control select2" id="enquiry_id"
                                                style="width: 100%; height: 100%" multiple 
                                               >
                                            <!-- <option value="" selected>All Dealers</option> -->

                                            @foreach($dealers as $dealer)
                                                <option value="{{$dealer->pk_int_enquiry_id}}">{{$dealer->vchr_customer_name}} {{$dealer->district_name}} {{$dealer->vchr_customer_mobile}}
                                                </option>
                                            @endforeach
                                        </select>
                                </div>
                                <div class="col-sm-3">
                                <label for="">Agents</label>
                                        <select class="form-control select2" id="staff_id"
                                                style="width: 100%; height: 100%" multiple 
                                               >
                                            <!-- <option value="" selected>All Agents</option> -->

                                            @foreach($agents as $staff)
                                                <option value="{{$staff->pk_int_user_id}}">{{$staff->vchr_user_name}}
                                                </option>
                                            @endforeach
                                        </select>
                                </div>

                                <div class="col-sm-3">
                                <label for="">Products</label>
                                        <select class="form-control select2" id="product_id"
                                                style="width: 100%; height: 100%" multiple 
                                               >
                                            <!-- <option value="" selected>All Products</option> -->

                                            @foreach($products as $product)
                                                <option value="{{$product->id}}">{{$product->name}}
                                                </option>
                                            @endforeach
                                        </select>
                                </div>
                                <div class="col-sm-3">
                                <label for="">Brands</label>
                                        <select class="form-control select2" id="brand_id"
                                                style="width: 100%; height: 100%" multiple 
                                               >
                                            <!-- <option value="" selected>All Brands</option> -->

                                            @foreach($brands as $brand)
                                                <option value="{{$brand->id}}">{{$brand->name}}
                                                </option>
                                            @endforeach
                                        </select>
                                </div>
                                <div class="col-sm-3">
                                <label for="">Billed</label>
                                        <select class="form-control select2" id="billed"
                                                style="width: 100%; height: 100%"  
                                               >
                                            <option value="" selected></option>
                                            <option value="1" >Yes</option>
                                            <option value="0" >No</option>
                                        </select>
                                </div>
                                <div class="col-12"></div>
                                <div class="filter-round-btn ml-3">
                                    <a href="#" role="button" onclick="filterData()"
                                        aria-expanded="false">Filter</a>
                                </div>
                                <!-- <div class="filter-round-btn ml-3">
                                    <a href="#" role="button" id="export_btn" onclick="exportData()"
                                        aria-expanded="false">Export</a>
                                </div> -->
                            </div>
                        </div>
                </div>
                <div class="col-md-12 gl-table" style="overflow: auto">
                    <table id="datatable" class="table table-striped table-bordered nowrap table-custom table-responsive"
                           cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Agent Name</th>
                            <th>Dealer Name</th>
                            <th>Products</th>
                            <th>Order Amount</th>
                            <th>Date and Time</th>
                        </tr>
                        </thead>
                        <tbody id="datatable_body">

                        </tbody>
                    </table>
                </div>
             
           
            </div>
             
           
            </div>
        </div>
    
</main>
 
@endsection
@push('footer.script')
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css" />
<script type="text/javascript" src="https://cdn.datatables.net/buttons/1.7.1/js/dataTables.buttons.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js" ></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/1.7.1/js/buttons.html5.min.js" ></script>
<script type="text/javascript">
    function loadData(){
        $('#datatable').DataTable().destroy();
        $.ajax({
            type: 'POST',

            url: "{{ url('ajax/sales/report/woocommercesales') }}",

            data: { 
                from: $('#from_date').val(),
                to: $('#to_date').val(),
                agent: $('#staff_id').val(),
                dealer: $('#enquiry_id').val(),
                product: $('#product_id').val(),
                brand: $('#brand_id').val(),
                billed: $('#billed').val(),

            },
            cache: false,
            success: function(data) {
                console.log(data)
                var tabledata = '';
                var i=1;
                $('#datatable_body').html('');
                $.each(data.data,function(index,value){
                    var products='';
                    $.each(value.products,function(j,product){
                        products += '<b>Name:</b>'+product.name+' | <b>Qty:</b>'+product.qty+' '+product.unit+' | <b>Rate:</b>'+product.price+' | <b>Total:</b>'+product.amount+
                        '<br>';
                    })
                    $('#datatable_body').append('<tr><td>'+i+'</td><td>'+value.agent_name+'</td><td>'+value.dealer_name+'</td><td>'+products+'</td><td>'+value.order_amount+'</td><td>'+value.created_at+'</td></tr>');
                    i=i+1;
                });
                $('#datatable').DataTable({
                    dom: 'Bfrtip',
                    buttons: [
                        'excelHtml5',
                        'csvHtml5'
                    ]
                });
            },
            errors: function (){

            }
        });
    }
    function filterData(){
        loadData();
    }
    $(document).ready(function() 
    {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        loadData();

    });
</script>
@endpush
