@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<main class="main-wrapper">
   <div class="task-panel">
      <div class="row justify-content-between ">
         <div class="">
            <h5>Brands</h5>
         </div>
         <div class="">
            <div class="task-nav">
               <div class="dropdown-navigation">
                  <ul>
                     <li class=""><a class="main-round-btn bdr1 bdr-blue mg-lft-25 clr-white ks-izi-modal-trigger"  id="addBrand" data-toggle="modal" data-target="#add_brand"><i class="fa fa-plus"></i>Add New Brand</a>
                     </li>
                  </ul>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div class="layout-wrapper main-wrapper-2">
      <!--   / Side menu included /  -->
      @include ('sales::layouts.setting-sidebar')
      @if(session('flash_notification'))
      <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
      @endif
      <div class="content-section">
         <!--  /Your content goes here/ -->
         <div class="row">
            <div class="col-lg-5 col-md-5">
            </div>
         </div>
         <div class="gl-table">
            <table id="datatable" class="table table-striped table-bordered nowrap table-custom table-responsive" cellspacing="0" width="100%">
                <thead>
                <tr>
                    <th>Sl No</th>
                    <th>Image</th>
                    <th>Brand</th>
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody id="datatable_body">
                </tbody>
            </table>
        </div>
      </div>
   </div>
   </div>
   </div>
</main>
<div class="modal fade" id="add_brand" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabelAgent"
   aria-hidden="true">
   <div class="modal-dialog modal-md" role="document">
      <div class="modal-content">
         <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLabel">Add Brand</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            </button>
         </div>
         <div class="modal-body">
            <form id="add_brand" method="POST" action="{{url('user/sales/brands/store')}}">
               {{csrf_field()}}
               <div class="form-group">
                  <label for="recipient-name" class="form-control-label">Brand:</label>
                  <input type="text" name="name" class="form-control">
               </div>
               <div class="form-group">
                  <label for="recipient-name" class="form-control-label">Brand Image:</label>
                  <input type="file" name="image"  class="form-control"
                     placeholder=" Image">
               </div>
               <input class="form-control col-md-8" type="hidden" name="lead_ids" id='lead_ids'>
               <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                  <button type="submit" class="btn btn-primary" id="button-edit">Submit</button>
               </div>
               <span class="error"></span>
            </form>
         </div>
      </div>
   </div>
</div>
@endsection
@push('footer.script')
<script type="text/javascript">
   $(document).ready(function() 
    {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        $.ajax({
            type: 'GET',
   
            url: "{{ url('ajax/sales/brands') }}",
   
            data: { },
            cache: false,
            success: function(data) {
                // console.log('brands');console.log(data.data);
                var tabledata = '';
                var i=1;
                $.each(data.data,function(index,value){
                    
                    // imgpath='';
                    // if(value.image)
                    // {
                    //   var img="{{ asset(':imgpath') }}";
                    //   imgpath=img.replace(":imgpath",value.image);
                    // }
                    // console.log('path');console.log(imgpath);
                    $('#datatable_body').append('<tr><td>'+i+'</td><td><img width="100px" height="auto" src="'+value.image+'"></td><td>'+value.name+'</td><td><div class="dropdown show"><a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="fa fa-circle" aria-hidden="true"></i><i class="fa fa-circle" aria-hidden="true"></i><i class="fa fa-circle" aria-hidden="true"></i>   </a><div class="dropdown-menu" aria-labelledby="dropdownMenuLink"><a href="/user/sales/brands/show/'+value.id+'" id="delete_plan"  class="dropdown-item task-show"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32"><path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4l15-15zm-5-5L24 7.6l-3 3L17.4 7l3-3zM6 22v-3.6l10-10l3.6 3.6l-10 10H6z"/></svg>&nbsp;Edit</a>'+ ' ' +' <a href="/user/sales/brands/delete/'+value.id+'" id="delete_plan"class="dropdown-item task-delete"><i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete</a></div></div></td></tr>');
                    i=i+1;
                });
        
                $('#datatable').DataTable();
                console.log(data.data);
            },
            errors: function (){
   
            }
        });
    });
</script>
@endpush