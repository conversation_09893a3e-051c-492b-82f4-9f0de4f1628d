<?php

namespace Getlead\Rolespermissions;

use Illuminate\Support\ServiceProvider;

class RolespermissionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function boot()
    {

        $this->app->make('Getlead\Rolespermissions\Controllers\RoleController');
        $this->loadRoutesFrom(__DIR__ . '/routes.php');
        $this->loadViewsFrom(__DIR__.'/views', 'rolespermissions');
    }

    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
        $this->loadRoutesFrom(__DIR__ . '/routes.php');
        $this->loadMigrationsFrom(__DIR__ . '/migrations');
    }
}
