<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->unsignedBigInteger('id')->autoIncrement();
            $table->string('loggable_type');
            $table->unsignedBigInteger('loggable_id');
            $table->string('attribute_name');
            $table->text('old_value')->nullable();
            $table->text('new_value')->nullable();
            $table->string('action');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('user_type')->default('App\\User');
            $table->json('metadata')->nullable();
            $table->timestamp('created_at');
            
            $table->index(['loggable_type', 'loggable_id']);
            $table->index(['user_id', 'user_type']);
            $table->index(['attribute_name']);
            $table->index(['action']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_logs');
    }
};
