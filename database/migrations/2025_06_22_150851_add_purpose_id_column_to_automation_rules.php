<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('automation_rules', static function (Blueprint $table): void {
            $table->unsignedMediumInteger('enquiry_purpose_id')->nullable()->after('enquiry_source_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //no go back
    }
};
