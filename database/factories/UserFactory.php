<?php

declare(strict_types=1);

namespace Database\Factories;

use App\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Tests\FakePhoneNumber;

final class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition(): array
    {
        $phoneNumber = FakePhoneNumber::create('IN');

        return [
            'vchr_user_name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'countrycode' => $phoneNumber->countryCode(),
            'mobile' => $phoneNumber->nationalNumber(),
            'vchr_user_mobile' => $phoneNumber->toPhoneNumber(),
            'password' => bcrypt('password'),
            'remember_token' => Str::random(10),
            'int_role_id' => User::USERS,
            'int_status' => User::ACTIVATE,
            'is_co_admin' => false,
            'parent_user_id' => null,
            'telegram_id' => null,
            'fcm_token' => null,
            'address' => $this->faker->address(),
            'gst_number' => $this->faker->optional()->numerify('##########'),
            'enquiry_display_fields' => [],
        ];
    }

    /**
     * Configure the factory for admin users.
     */
    public function admin(): self
    {
        return $this->state(static fn (array $attributes) => [
            'int_role_id' => User::ADMIN,
        ]);
    }

    /**
     * Configure the factory for staff users.
     */
    public function staff(): self
    {
        return $this->state(static fn (array $attributes) => [
            'int_role_id' => User::STAFF,
            'parent_user_id' => static fn () => User::factory()->create()->pk_int_user_id,
        ]);
    }

    /**
     * Configure the factory for co-admin users.
     */
    public function coAdmin(): self
    {
        return $this->state(static fn (array $attributes) => [
            'is_co_admin' => true,
        ]);
    }

    /**
     * Configure the factory for inactive users.
     */
    public function inactive(): self
    {
        return $this->state(static fn (array $attributes) => [
            'int_status' => User::DEACTIVATE,
        ]);
    }

    /**
     * Configure the factory for users with telegram.
     */
    public function withTelegram(): self
    {
        return $this->state(fn (array $attributes) => [
            'telegram_id' => $this->faker->numerify('########'),
        ]);
    }

    /**
     * Configure the factory for users with FCM token.
     */
    public function withFcmToken(): self
    {
        return $this->state(fn (array $attributes) => [
            'fcm_token' => $this->faker->uuid(),
        ]);
    }

    /**
     * Configure the factory for users with specific enquiry display fields.
     */
    public function withEnquiryDisplayFields(array $fields): self
    {
        return $this->state(static fn (array $attributes) => [
            'enquiry_display_fields' => $fields,
        ]);
    }

    /**
     * Configure the factory for users with specific parent.
     */
    public function withParent(int $parentUserId): self
    {
        return $this->state(static fn (array $attributes) => [
            'parent_user_id' => $parentUserId,
        ]);
    }
}
