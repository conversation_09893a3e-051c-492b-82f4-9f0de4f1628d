<?php

use Illuminate\Database\Seeder;
use App\BackendModel\Smsroute;

class SmsrouteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('tbl_sms_route')->delete();
        DB::table('tbl_sms_route')->insert([
            'vchr_sms_route' => 'Transactional SMS',
             'int_sms_route_status' => '1',
             'short_code'=>'TL',
             'priority'=>11,
             'int_sms_route_code'=>11,   
        ]);
        DB::table('tbl_sms_route')->insert([
            'vchr_sms_route' => 'Send OTP',
             'int_sms_route_status' => '1',
             'short_code'=>'OTP',
             'priority'=>4,
             'int_sms_route_code'=>4,   
        ]);
        DB::table('tbl_sms_route')->insert([
            'vchr_sms_route' => 'Promotional SMS',
             'int_sms_route_status' => '1',
             'short_code'=>'',
             'priority'=>8,
             'int_sms_route_code'=>8,   
        ]);
        DB::table('tbl_sms_route')->insert([
            'vchr_sms_route' => 'International SMS',
             'int_sms_route_status' => '1',
             'short_code'=>'IL',
             'priority'=>18,
             'int_sms_route_code'=>18,   
        ]);
        DB::table('tbl_sms_route')->insert([
            'vchr_sms_route' => 'Scrub',
             'int_sms_route_status' => '1',
             'short_code'=>'',
             'priority'=>12,
             'int_sms_route_code'=>12,   
        ]);
    }
}
