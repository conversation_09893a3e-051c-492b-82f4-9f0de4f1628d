n<?php

use Illuminate\Database\Seeder;

class SmsPanelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $smsPanels = [
            ['name' => 'AlertBox', 'about' => '', 'status'=>1,'created_at' => \Carbon\Carbon::today()],
            ['name' => 'Merabt', 'about' => '', 'created_at' => \Carbon\Carbon::today()],
        
            ['name' => 'TextLocal', 'about' => '', 'created_at' => \Carbon\Carbon::today()],
        ];

        \App\SmsPanel::truncate();
        foreach ($smsPanels as $index => $panel) {
            $panel_obj = new \App\SmsPanel();
            $panel_obj->name = $panel['name'];
            $panel_obj->about = $panel['about'];
            $panel_obj->created_at = $panel['created_at'];
            $panel_obj->save();
            echo "New Panel Added";
        }
    }
}
