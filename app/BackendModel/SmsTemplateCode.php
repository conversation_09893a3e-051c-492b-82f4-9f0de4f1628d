<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;

class SmsTemplateCode extends Model
{
    //
    const ACTIVATE=1;
    const DEACTIVATE=0;
     protected $primaryKey='pk_int_sms_template_id';
     protected $table='sms_templates';
     
     protected $fillable=[
         'vchr_sms_template_title',
         'fk_int_user_id',
         'sms_template_code',
         'int_status',
         'created_by',
         'updated_by'
     ];
     public static $rule = [

        'vchr_sms_template_title' => 'required',
        'sms_template_code'=> 'required',
      ];
  
      public static $message = [
          'vchr_sms_template_title.required'=>'SMS Template Title is required',
          'sms_template_code.required'=>'SMS Template Code is required',
          
      ];
  
      public static $updaterule = [
  
        
        'vchr_sms_template_title' => 'required',
        'sms_template_code'=> 'required',
      ];
  
      public static $updatemessage = [
        'vchr_sms_template_title.required'=>'SMS Template Title is required',
        'sms_template_code.required'=>'SMS Template Code is required',
          
      ];
}
