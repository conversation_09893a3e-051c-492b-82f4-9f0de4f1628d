<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SmsTemplates extends Model
{
    use SoftDeletes;

	const ACTIVATE = 1;
    const DEACTIVATE = 0;

    const UNDEFAULT = 0;
    const DEFAULT = 1;


    protected $dates = ['deleted_at'];
    protected $primaryKey = 'pk_int_sms_template_id';
    protected $table = 'tbl_sms_template';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

     protected $fillable = [
        'vchar_sms_template_title','fk_int_user_id','text_sms_template_description','int_sms_template_count', 'int_default_sms_template_id','int_sms_template_status'
    ];

     public static $rule = [

      'text_sms_template_description' => 'required',
      'fk_int_user_id'=> 'required',
    ];

    public static $message = [
        'fk_int_user_id.required'=>'Userid is required',
        'text_sms_template_description.required'=>'Sms Template is required',
        
    ];

    public static $updateRule = [

      'smstemplatebody' => 'required',
      
    ];

    public static $updateMessage = [
        'smstemplatebody.required'=>'Sms template is required',
       
    ];


}
