<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class SendSmsHistory extends Model
{
    

	const ACTIVATE = 1;
  const DEACTIVATE = 0;
    protected $primaryKey = 'pk_int_send_sms_history_id';
    protected $table = 'tbl_send_sms_history';

    
    protected $fillable = [
        'fk_int_user_id','fk_int_template_id','vchr_mobile','vchr_sender_id', 'vchr_route','vchr_messagetype','text_message','vchr_alertid','vchr_trackid','int_status',
    ];

     public static $rule = [

      
      'senderid'=> 'required',
      
      
    ];

    public static $message = [
        'senderid.required'=>'Senderid is required',
        
   
    ];

    
}
