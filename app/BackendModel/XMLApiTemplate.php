<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class XMLApiTemplate extends Model
{
    use SoftDeletes;
    const ACTIVATE = 1;
    const DEACTIVATE = 0;

    protected $primaryKey = 'id';
    protected $table = 'xml_api_templates';
    protected $fillable = [
        'vendor_id',
        'domain_id',
        'route_id',
        'api_username',
        'password',
        'api_password',
        'xml_api',
        'message_api',
        'balance_api',
        'status_api',
        'status',
        'created_at'
    ];
    public function user()
    {
        return $this->hasOne('App\User', 'pk_int_user_id', 'vendor_id');
    }
    public function route()
    {
        return $this->hasOne(Smsroute::class, 'pk_int_sms_route_id', 'route_id');
    }
    public function subdomain()
    {
        return $this->hasOne('App\BackendModel\SmsDomain', 'id', 'domain_id');
    }
    public function scopeActive($query){
        return $query->where('status',1);
    }
}
