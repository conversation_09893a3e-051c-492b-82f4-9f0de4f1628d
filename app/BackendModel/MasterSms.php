<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MasterSms extends Model
{
 	use SoftDeletes;

    const ACTIVATE = 1;
    const DEACTIVATE = 0;


    protected $dates = ['deleted_at'];
    protected $primaryKey = 'pk_int_master_sms_id';
    protected $table = 'tbl_master_sms_table';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

     protected $fillable = [
        'int_master_sms_count','vchr_master_sms_name'
    ];
     public static $rule = [
        'int_master_sms_count'=>'required'
    ];

    public static $message = [
        'int_master_sms_count.required'=>'Master sms count is required',
       
    ];

     public static $creditRule = [
        'username' => 'required',
        'int_master_sms_count'=>'required|numeric',
        'days' => 'required|numeric',
    ];

    public static $creditMessage = [
        'username.required'=>'Username is required',
        'int_master_sms_count.required'=>'Sms count is required',
        'days.required'=>'Days are required',
        'int_master_sms_count.numeric'=>'Sms count should be a number',
        'days.numeric'=>'Days should be a number',
    ];
    
}
