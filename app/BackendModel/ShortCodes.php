<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ShortCodes extends Model
{
    //
    use SoftDeletes;

    protected $dates = ['deleted_at'];
    protected $primaryKey = 'pk_int_short_code_id';
    protected $table = 'tbl_short_codes';

    protected $fillable = [
        'vchr_short_code_name', 'vchr_short_code_template', 'fk_int_user_id', 'vchr_short_code_description'
    ];

    public static $rules = [
        'vchr_short_code_name' => 'required|unique:tbl_short_codes,deleted_at,null',
        'vchr_short_code_template' => 'required|unique:tbl_short_codes,deleted_at,null',
        'vchr_short_code_description' => 'required',
    ];

    public static $rulesMessage = [
        'vchr_short_code_name.required' => 'Short code name is required.',
        'vchr_short_code_name.unique' => 'Short code name is already in use.',
        'vchr_short_code_name.unique' => 'Short code name is already in use.',
        'vchr_short_code_template.required' => 'Short code template is required.',
        'vchr_short_code_description.required' => 'Short code description is required.',
    ];
}
