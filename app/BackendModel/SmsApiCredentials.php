<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;

class SmsApiCredentials extends Model
{
    // use SoftDeletes;
    const ACTIVATE = 1;
    const DEACTIVATE = 0;

    protected $primaryKey = 'id';
    protected $table = 'sms_api_credentials';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

    protected $fillable = [
        'vendor_id', 'route_id', 'sms_domain_id', 'status', 'username', 'password', 'api_password',
    ];

    public static $rule = [

        'route_id' => 'required',

    ];

    public static $message = [
        'route_id.required' => 'Route is required',

    ];

    public function user()
    {
        return $this->hasOne('App\User', 'pk_int_user_id', 'vendor_id');
    }
    public function route()
    {
        return $this->hasOne(Smsroute::class, 'pk_int_sms_route_id', 'route_id');
    }
    public function subdomain()
    {
        return $this->hasOne('App\BackendModel\SmsDomain', 'id', 'sms_domain_id');
    }
    public function scopeActive($query){
        return $query->where('status',1);
    }


}
