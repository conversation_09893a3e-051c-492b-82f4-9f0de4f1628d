<?php

namespace App\BackendModel;

use App\Common\Unguarded;
use Illuminate\Database\Eloquent\Model;

/**
 * 
 *
 * @property int $pk_int_token_id
 * @property int $fk_int_user_id
 * @property string $vchr_token
 * @property int $int_status
 * @property int|null $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|GlApiTokens newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GlApiTokens newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GlApiTokens query()
 * @method static \Illuminate\Database\Eloquent\Builder|GlApiTokens whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GlApiTokens whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GlApiTokens whereFkIntUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GlApiTokens whereIntStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GlApiTokens wherePkIntTokenId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GlApiTokens whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GlApiTokens whereVchrToken($value)
 * @mixin \Eloquent
 */
class GlApiTokens extends Model
{
    use Unguarded;

    const ACTIVATE = 1;
    const DEACTIVATE = 0;

    protected $dates = ['deleted_at'];
    protected $primaryKey = 'pk_int_token_id';
    protected $table = 'tbl_gl_api_tokens';

}
