<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SmsConfiguration extends Model
{
    use SoftDeletes;

    const ACTIVATE = 1;
    const DEACTIVATE = 0;

    const DEFAULT=1;
    const UNDEFAULT=0;

    protected $dates = ['deleted_at'];
    protected $primaryKey = 'pk_sms_configuration_id';
    protected $table = 'tbl_sms_configuration';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

     protected $fillable = [
        'vchr_sms_configuration_name','text_sms_configuration_api','text_sms_configuration_delivery_api','int_sms_configuration_default', 'int_sms_configuration_status','vchr_sms_configuration_senderid'
    ];

    public static $rule = [

      'vchr_sms_configuration_name'=> 'required',
      'text_sms_configuration_api'=> 'required|url',
      'vchr_sms_configuration_senderid'=> 'required|max:6|min:6',
      
    ];

    public static $message = [
        'vchr_sms_configuration_name.required'=>'SMS API name is required',
        'text_sms_configuration_api.required'=>'SMS API is required',
        'text_sms_configuration_api.url'=>'Valid SMS API is required',

        'vchr_sms_configuration_senderid.required'=>'SMS Senderid is required',
        'vchr_sms_configuration_senderid.max'=>'maximum 6 charecter length is allowed',
        'vchr_sms_configuration_senderid.min'=>'Senderid should have minimum 6 charecter length',

       
    ];


}
