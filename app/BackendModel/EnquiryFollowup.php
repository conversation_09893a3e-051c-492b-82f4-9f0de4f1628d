<?php

namespace App\BackendModel;

use App\Common\Notifications;
use App\Common\SingleSMS;
use App\CustomFieldValue;
use App\Ivr\Models\Ivr;
use App\Task;
use App\User;
use Auth;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Model;

class EnquiryFollowup extends Model
{
    const TYPE_NEW = 0;
    const TYPE_NOTE = 1;
    const TYPE_LOG_CALL = 2;
    const TYPE_LOG_EMAIL = 3;
    const TYPE_LOG_MEETING = 4;
    const TYPE_TASK = 5;
    const TYPE_SCHEDULE = 6;
    const TYPE_STATUS = 7;
    const TYPE_EMAIL = 8;
    const WHATSAPP_HISTORY = 9;
    const SMS_HISTORY = 10;
    const IVR = 11;
    const ENQ_PURPOSE = 12;
    const TYPE_VOICE_NOTE = 13;
    const TYPE_FILE_NOTE = 14;
    const TYPE_ORDER = 15;
    const TYPE_INDIA_MART = 16;
    const TYPE_ACTIVITY = 17;
    /** added by goutham */
    const TYPE_DOCUMENT = 18;
    const TYPE_CHECKIN = 19;
    const TYPE_CHECKOUT = 20;
    const TYPE_NEW_LEAD = 21;
    const TYPE_TASK_NEW = 22;
    const TYPE_TASK_HISTORY = 23;
    const TYPE_DEAL = 25;
    const TYPE_VISIT = 27;
    const TYPE_DELETE = 28;
    const TASK_COMPLETED = 29;
    const TYPE_AGENCY = 30;
    

    protected $table = 'tbl_enquiry_followups';
    protected $fillable = [
        'assinged_to','created_by','enquiry_id','note','name','log_type'
    ];
    protected $guarded = [];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone(auth()->user()->time_zone ?? 'Asia/Kolkata');
    }
    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone(auth()->user()->time_zone ?? 'Asia/Kolkata');
    }
    
    public function user()
    {
        return $this->belongsTo('App\User', 'assigned_to');
    }

    public function added_by()
    {
        return $this->belongsTo('App\User', 'created_by');
    }

    public function assigned()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }
    public function currentStatus()
    {
        return $this->belongsTo(FeedbackStatus::class, 'note');
    }
    public function oldStatus()
    {
        return $this->belongsTo(FeedbackStatus::class, 'old_status_id');
    }
    public function source()
    {
        return $this->belongsTo(EnquiryType::class, 'note');
    }
    public function purpose()
    {
        return $this->belongsTo(EnquiryPurpose::class, 'note');
    }
    public function ivrdata()
    {
        return $this->hasOne(Ivr::class, 'call_uuid', 'note');
    }
    public function callMaster()
    {
        return $this->hasOne('App\CallMaster', 'id', 'task_id');
    }

    public function custom_field_values()
    {
        return $this->hasMany(CustomFieldValue::class, 'related_id', 'id');
    }

    public function enquiry()
    {
        return $this->belongsTo('App\BackendModel\Enquiry', 'enquiry_id');
    }

    public function task()
    {
        return $this->belongsTo(Task::class, 'task_id');
    }
    public function location()
    {
        return $this->belongsTo(EnquiryLocation::class, 'bcc');
    }

    public static function addSchedule($input, $userid)
    {
        /*
        $enquiryfollowups = new EnquiryFollowup();
        $enquiryfollowups->enquiry_id = $input['id'];
        $enquiryfollowups->name = $input['name'];
        $enquiryfollowups->note = $input['note'];
        $enquiryfollowups->duration = $input['duration'];
        $enquiryfollowups->created_by = $userid;
        if ($input['date'] != " " && $input['time'] != "") {
            $date_time = Carbon::createFromFormat('m/d/Y', $input['date'])->toDateString() . ' ' . Carbon::createFromFormat('H:i A', $input['time'])->toTimeString();
            $enquiryfollowups->date = $date_time;
        } else if ($input['date'] != "") {
            $date_time = Carbon::createFromFormat('m/d/Y', $input['date'])->toDateString();
            $enquiryfollowups->date = $date_time;
        } else {
            $date_time = "";
        }

        $enquiryfollowups->log_type = EnquiryFollowup::TYPE_SCHEDULE;
        $enquiryfollowups->save();
        */
        //Create Task
        $enquiry = Enquiry::where('pk_int_enquiry_id',$input['id'])->first();
        $task_data = [
            'name' => $input['name'],
            'description' => $input['note'],
            'task_category_id' => 2,
            'assigned_to' => $enquiry->staff_id ?? $userid,
            'assigned_by' => $userid,
            'vendor_id' => $enquiry->fk_int_user_id,
            'enquiry_id' => $input['id'],
            'comment' => '',
            'status' => 0,
        ];
        if ($input['date'] != " " && $input['time'] != "")
            $task_data['scheduled_date'] = $input['date'];
        else
            $task_data['scheduled_date'] = now();  
              
        $task=Task::create($task_data);
        /*
        $enquiryfollowups->task_id = $task->id;
        $enquiryfollowups->save();
        */
        //
        //Get VendorId
        $enquiry = Enquiry::where('pk_int_enquiry_id', $input['id'])->first();
        $enquiry->followup_required = isset($input['followup_required']) ? $input['followup_required'] : '';
        $enquiry->lead_attension = 0;
        $enquiry->updated_at = now();
        $enquiry->save();
        $vendorId = $enquiry->fk_int_user_id;

        //Notifications
        /*
        $notifications = new Notifications();
        $from = config('appdata.from_mail_address');
        $to = User::getUserDetails($vendorId)->email;
        $subject = "Schedule";
        $name = User::getUserDetails($vendorId)->vchr_user_name;
        $logo = User::getSingleAdminDetails()->vchr_logo;
        $attachment = "";
        $telegramId = User::getUserDetails($vendorId)->telegram_id;
        $mobileNumber = User::getUserDetails($vendorId)->vchr_user_mobile;
        $defaultSenderIdAdmin = SingleSMS:: getSenderid(User::getSingleAdminDetails()->pk_int_user_id, '');
        $defaultRouteAdmin = SingleSMS:: getRoute(User::getSingleAdminDetails()->pk_int_user_id, '');

        $content1 = "New Meeting (" . $input['name'] . ") scheduled on " . Carbon::parse($date_time)->format('M d') . " at " . Carbon::parse($date_time)->format('g:i a');
        $content2 = "You have a meeting (" . $input['name'] . ") scheduled  on " . Carbon::parse($date_time)->format('M d') . " at " . Carbon::parse($date_time)->format('g:i a');
        $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $vendorId, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin);
        */
        //-----------------
    }

    /**
     * @static
     * Function to update the created schedule
     *
     * @param array $input
     * @param int $id
     * @param int $userid
     * @return void
     */
    public static function updateSchedule($input, $id, $userid)
    {
        $enquiryfollowups = EnquiryFollowup::find($id);
        $oldDate = $enquiryfollowups->date;
        $enquiryfollowups->name = $input['name'];
        $enquiryfollowups->note = $input['note'];
        $enquiryfollowups->duration = $input['duration'];
        if ($input['date'] != " " && $input['time'] != "") {
            $date_time = Carbon::createFromFormat('m/d/Y', $input['date'])->toDateString() . ' ' . Carbon::createFromFormat('H:i A', $input['time'])->toTimeString();
            $enquiryfollowups->date = $date_time;
        }
        $enquiryfollowups->updated_by = $userid;
        $enquiryfollowups->save();
        //Update Task
        $enquiry = Enquiry::where('pk_int_enquiry_id',$enquiryfollowups->enquiry_id)->first();
        $task_id= $enquiryfollowups->task_id;
        $task_data = [
            'name' => $input['name'],
            'description' => $input['note'],
            'task_category_id' => 2,
            'assigned_to' => $enquiry->staff_id ?? Auth::user()->pk_int_user_id,
            'assigned_by' => Auth::user()->pk_int_user_id,
            'vendor_id' => $enquiry->fk_int_user_id,
        ];
        if ($input['date'] != " " && $input['time'] != "")
            $task_data['scheduled_date'] = $enquiryfollowups->date;
        if($task_id){
            Task::where('id',$task_id)->update($task_data);
        }
        else{
            $task_data['enquiry_id'] =$enquiryfollowups->enquiry_id;
            $task_data['status'] = 0;
            $task=Task::create($task_data);
            $enquiryfollowups->task_id = $task->id;
            $enquiryfollowups->save();
        }
        //
        //Get VendorId
        $enquiry = Enquiry::where('pk_int_enquiry_id', $enquiryfollowups->enquiry_id)->first();
        $vendorId = $enquiry->fk_int_user_id;

        //Notifications
        if ($oldDate != $date_time) {
            $userObject = User::getUserDetails($vendorId);
            $userAdminObject = User::getSingleAdminDetails();
            $notifications = new Notifications();
            $from = env('MAIL_FROM_ADDRESS');
            $to = $userObject->email;
            $subject = "Schedule";
            $name = $userObject->vchr_user_name;
            $logo = $userObject->vchr_logo;
            $attachment = "";
            $telegramId = $userObject->telegram_id;
            $mobileNumber = $userObject->vchr_user_mobile;
            $defaultSenderIdAdmin = SingleSMS::getSenderid($userAdminObject->pk_int_user_id, '');
            $defaultRouteAdmin = SingleSMS:: getRoute($userAdminObject->pk_int_user_id, '');

            $content1 = "Meeting (" . $input['name'] . ") scheduled on " . Carbon::parse($oldDate)->format('M d') . " at " . Carbon::parse($oldDate)->format('g:i a') . " updated to " . Carbon::parse($date_time)->format('M d') . " at " . Carbon::parse($date_time)->format('g:i a');
            $content2 = "You have a meeting (" . $input['name'] . ") scheduled  on " . Carbon::parse($oldDate)->format('M d') . " at " . Carbon::parse($oldDate)->format('g:i a') . " updated to " . Carbon::parse($date_time)->format('M d') . " at " . Carbon::parse($date_time)->format('g:i a');
            $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $vendorId, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin);
        }
    }

    public function getIvrVoiceUrlAttribute()
    {
        if($this->ivrdata && $this->ivrdata->recording_url){
            $url = 'https://pbx.voxbaysolutions.com/callrecordings/' . str_replace(" ","+", $this->ivrdata->recording_url);
        }elseif($this->callMaster && $this->callMaster->recording_url){
            $url = $this->callMaster->recording_url;
        }else{
            $url = '';
        }  

        return $url;
    }

    public static function tasks($enqId, $month)
    {
        $date = date_parse($month);
        $selectColumns = [
            'tasks.id', 'tasks.name',
            'tasks.description', 'tasks.scheduled_date',
            'tasks.task_category_id', 'tasks.task_order',
            'tasks.assigned_to', 'tasks.assigned_by',
            'tasks.vendor_id', 'tasks.enquiry_id',
            'tasks.call_status_id','tasks.description',
            'tasks.comment', 'tasks.status',
            'tasks.created_at',
            'task_categories.name as task_category',
            'tbl_enquiries.vchr_customer_name as customer_name',
            'tbl_enquiries.vchr_customer_mobile',
            'tbl_users.vchr_user_name as assigned_user_name',
            't_user.vchr_user_name as assigned_by_user'
        ];
        $tasksQ = Task::where('tasks.vendor_id', User::getVendorId())
                        ->where('enquiry_id', $enqId)
                        ->whereMonth('tasks.created_at', $date['month'])
                        ->whereYear('tasks.created_at', $date['year'])
                        ->join('task_categories', 'task_category_id', '=', 'task_categories.id')
                        ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
                        ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
                        ->leftjoin('tbl_users as t_user', 'assigned_by', '=', 't_user.pk_int_user_id')
                        ->orderBy('tasks.id', 'DESC')
                        ->select($selectColumns);

        return $tasksQ->get();
    }

    public function checkin()
    {
        return $this->hasOne('App\BackendModel\Checkout_note', 'id', 'note');
    }
    public function checkOut()
    {
        return $this->hasOne('App\BackendModel\EnquiryFollowup', 'id', 'checkout_id')->select('id','checkout_id','note',
        DB::raw("DATE_FORMAT(created_at, '%d %M %Y %H:%i') as date"));
    }
    public function scopeFromDate($query,$start_date, $end_date)
    {
        return $query->whereDate('created_at', '>=', $start_date)
                     ->whereDate('created_at', '<=', $end_date)->get();
    }
}
