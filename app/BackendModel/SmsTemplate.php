<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SmsTemplate extends Model
{
    //
    use SoftDeletes;

    protected $dates = ['deleted_at'];
    protected $primaryKey = 'pk_int_sms_template_id';
    protected $table = 'tbl_sms_templates';

    protected $fillable = [
        'vchr_sms_template_name', 'vchr_sms_template_code', 'int_status'
    ];

    public static $rules = [
        'vchr_sms_template_name' => 'required|unique:tbl_sms_templates',
        'vchr_sms_template_code' => 'required'
    ];

    public static $rulesMessage = [
        'vchr_sms_template_name.required' => 'Template name is required.',
        'vchr_sms_template_code.required' => 'Template code is required.'
    ];
}
