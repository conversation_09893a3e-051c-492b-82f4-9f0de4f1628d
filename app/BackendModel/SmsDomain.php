<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;

class SmsDomain extends Model
{
    // use SoftDeletes;
    const ACTIVATE = 1;
    const DEACTIVATE = 0;

    protected $primaryKey = 'id';
    protected $table = 'sms_domain';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

    protected $fillable = [
        'title', 'domain', 'status',
    ];

    public static $rule = [

        'title' => 'required',
        'domain' => 'required|url',

    ];

    public static $message = [
        'title.required' => 'title is required',
        'domain.required' => 'domain is required',
        'domain.url' => 'domain is invalid',

    ];


}
