<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class DefaultRoute extends Model
{
   // use SoftDeletes;
    const ACTIVATE = 1;
    const DEACTIVATE = 0;
    const ROUTE = 'Transactional SMS';
    const INTERNATIONAL = 'International SMS';

	



    protected $primaryKey = 'id';
    protected $table = 'default_route';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

    protected $fillable = [
        'vendor_id','route','type','status',
    ];

     public static $rule = [

      'route' => 'required',
    
    ];

    public static $message = [
        'route.required'=>'Route is required',
        
    ];

     

    

     
}
