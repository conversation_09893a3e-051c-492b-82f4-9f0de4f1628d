<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;

class CompanyProfile extends Model
{
    //
    const ACTIVATE=1;
    const DEACTIVATE=0;
    const DEFAULT=1;
    protected $primaryKey="id";
    protected $table='company_profiles';
    protected $fillable=[
        'name',
        'address',
        'pin_no',
        'account_name',
        'account_no',
        'gst_number',
        'bank_name',
        'bank_branch',
        'ifsc_code',
        'status',
        'default',
    ];
    public static $rule=[
        'name'=>'required',
        'address'=>'required',
        'pin_no'=>'required',
        'account_name'=>'required',
        'account_no'=>'required',
     
        'bank_name'=>'required',
        'bank_branch'=>'required',
        'ifsc_code'=>'required',
        

    ];

    public static $message =[
        'name.required'=>'name is required',
        'address.required'=>'address is required',
        'pin_no.required'=>'pin_no is required',
        'account_name.required'=>'account name is required',
        'account_no.required'=>'Account number is required',

        'bank_name.required'=>'Bank name is required',
        'bank_branch.required'=>'Bank branch is required',
        'ifsc_code.required'=>'IFSC code is required',
 
    ];
}
