<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;

class TataCompetingModel extends Model
{
    protected $table = 'tbl_tata_competing_models';

    const ACTIVE = 1;

    public static $create_rule = [
        'name' => 'required',
        'model_id' => 'required'
    ];
  
    public static $create_message = [
          'name.required'=>'Name is required',
          'model_id' => 'Model is required'
    ];

    public static $update_rule = [
        'name' => 'required',
        'model_id' => 'required'
    ];
  
    public static $update_message = [
          'name.required'=>'Name is required',
          'model_id' => 'Model is required'
    ];
}
