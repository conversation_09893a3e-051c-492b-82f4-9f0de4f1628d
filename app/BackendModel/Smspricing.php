<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Smspricing extends Model
{
    

	const ACTIVATE = 1;
  const DEACTIVATE = 0;




    
    protected $primaryKey = 'pk_int_sms_pricing_id';
    protected $table = 'tbl_sms_pricing';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

     protected $fillable = [
        'fk_int_sms_route','vchr_sms_pricing_rate_sms','vchr_sms_pricing_rangefrom','vchr_sms_pricing_rangeto', 'int_sms_pricing_status',
    ];

     public static $rule = [

      
      'vchr_sms_pricing_route'=> 'required',
      'vchr_sms_pricing_rate_sms'=> 'required',
      'vchr_sms_pricing_rangefrom'=> 'required',
      'vchr_sms_pricing_rangeto'=> 'required',
      
    ];

    public static $message = [
        'vchr_sms_pricing_route.required'=>'Route is required',
        'vchr_sms_pricing_rate_sms.required'=>'Rate/sms is required',
     ];

    
}
