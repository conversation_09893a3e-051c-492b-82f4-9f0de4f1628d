<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MasterSmsHistory extends Model
{
    
 	use SoftDeletes;

    //account credited from main wallet
    const CREDIT = 1;

    //taken back from exipry user
    const RECREDIT = 0;
    //rescredited as forgot password
    const FORGOTPASSWORD= 2;
    ////admin added to wallet
    const SELFCREDIT=3;
    //otp verification
    const OTPVERIFICATION=4;



    protected $dates = ['deleted_at'];
    protected $primaryKey = 'pk_int_master_sms_history_id';
    protected $table = 'tbl_master_sms_history_table';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

     protected $fillable = [
        'vchr_master_sms_history_statement','int_master_sms_history_type'
    ];
    
}
