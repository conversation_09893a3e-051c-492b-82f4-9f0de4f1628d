<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Smsroute extends Model
{
   // use SoftDeletes;

	const ACTIVATE = 1;
    const DEACTIVATE = 0;
    const EMAIL = "Email";



    protected $primaryKey = 'pk_int_sms_route_id';
    protected $table = 'tbl_sms_route';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

     protected $fillable = [
        'vchr_sms_route','int_sms_route_status',
    ];

     
}
