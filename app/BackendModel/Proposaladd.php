<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;

class Proposaladd extends Model
{
     const ACTIVATE = 1;
    const DEACTIVATE = 0;

    protected $fillable = [
        'fk_int_user_id','proposaluserid','serviceid','quality', 'price','discount','total_price','price_discount',
    ];

    protected $primaryKey = 'id';
        
    protected $table='proposals';

    // public static $rule = [
    //     'vchr_website_enquiry_name' => 'required',
    //     'vchr_website_enquiry_email' => 'required',
    // ];

    // public static $message = [
    //     'vchr_website_enquiry_name.required' => 'Name  is required.',
    //     'vchr_website_enquiry_email.required' => 'Email is required.',
    // ];

}
