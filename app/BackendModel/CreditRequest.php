<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CreditRequest extends Model
{

	use SoftDeletes;

	const ACTIVATE = 1;
    const DEACTIVATE = 0;


    const UNDEFAULT = 0;
    const DEFAULT = 1;


    protected $dates = ['deleted_at'];
    protected $primaryKey = 'pk_int_credit_request_id';
    protected $table = 'tbl_credit_request';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

     protected $fillable = [
        'vchr_sms_request_count','fk_int_user_id', 'int_status',
    ];

     public static $rule = [

      'vchr_sms_request_count' => 'required|min:1|numeric',
      'fk_int_user_id'=> 'required',
    ];

    public static $message = [
        'fk_int_user_id.required'=>'Userid is required',
        'vchr_sms_request_count.required'=>'SENDER ID is required',
        'vchr_sms_request_count.min'=>'Minimum 1 digit  required',
        'vchr_sms_request_count.numeric'=>'SMS Request count should be a number',

        
    ];

}
