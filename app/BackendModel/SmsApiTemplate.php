<?php

namespace App\BackendModel;

use Illuminate\Database\Eloquent\Model;


class SmsApiTemplate extends Model
{
    
  

    const ACTIVATE = 1;
    const DEACTIVATE = 0;

    protected $primaryKey = 'pk_int_sms_api_template_id';
    protected $table = 'tbl_sms_api_template';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

     protected $fillable = [
        'fk_int_user_id','fk_int_sms_route_id','text_sms_api_template_url','text_sms_api_template_balance_url','text_sms_api_template_status_url', 'int_status',
    ];

     public static $rule = [

      'text_sms_api_template_url' => 'required|url',
      'fk_int_sms_route_id'=> 'required',
      'text_sms_api_template_balance_url'=>'required|url',
      'text_sms_api_template_status_url'=>'required|url',
    ];

    public static $message = [
        'fk_int_sms_route_id.required'=>'Route is required',
        'text_sms_api_template_url.required'=>'Api Template is required',
        'text_sms_api_template_balance_url.required'=>'Balance Api is required',
        'text_sms_api_template_balance_url.url'=>'Balance Api is invalid',
        'text_sms_api_template_status_url.required'=>'Status Api is required',
        'text_sms_api_template_status_url.url'=>'Status Api is invalid',

        
    ];

    public static $updateRule = [

      'text_sms_api_template_url' => 'required|url',
     'fk_int_sms_route_id'=> 'required',
      
    ];

    public static $updateMessage = [
       'fk_int_sms_route_id.required'=>'Route is required',
        'text_sms_api_template_url.required'=>'Api Template is required',
        'text_sms_api_template_url.url'=>'Api Template is invalid',
    ];
}
