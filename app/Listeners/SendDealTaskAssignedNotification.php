<?php

namespace App\Listeners;

use App\Events\TaskAssigned;
use App\Common\Notifications;
use App\User;
use App\Common\SingleSMS;
use App\Deal;
use App\DealTask;
use App\Events\DealTaskAssigned;
use App\Events\SendPusherNotification;
use App\Jobs\SendNotification;
// use App\Jobs\SendPusherNotification;
use App\PusherSetting;
use Carbon\Carbon;

class SendDealTaskAssignedNotification
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  DealTaskAssigned  $event
     * @return void
     */
    public function handle(DealTaskAssigned $event)
    {
        $task = $event->task;
        $vendorId = $task->vendor_id;
        //Notifications
        $userObject = User::getUserDetails($vendorId);
        $userAdminObject = User::getSingleAdminDetails();
        $notifications = new Notifications();
        $from = env('MAIL_FROM_ADDRESS');
        $to = $userObject->email;
        $subject = getDealName(true)." Tasks";
        $name = $userObject->vchr_user_name;
        $logo = $userAdminObject->vchr_logo;
        $attachment = "";
        $telegramId = $userObject->telegram_id;
        $mobileNumber = $userObject->vchr_user_mobile;
        $defaultSenderIdAdmin = SingleSMS:: getSenderid($userAdminObject->pk_int_user_id, '');
        $defaultRouteAdmin = SingleSMS:: getRoute($userAdminObject->pk_int_user_id, '');
        if ($task->assigned_to != NULL) {
            $assignedStaff = " assigned to " . User::getUserDetails($task->assigned_to)->vchr_user_name;
        } else {
            $assignedStaff = "";
        }
        $content1 = "New ".getDealName(true)." Task (" . $task->name . ") ".( $task->scheduled_date ? "on " . Carbon::parse($task->scheduled_date)->format('M d') . " at " . Carbon::parse($task->scheduled_date)->format('g:i a') : "") . $assignedStaff;
        $content2 = "You have new ".getDealName(true)." task (" . $task->name . ") ".( $task->scheduled_date ? "on " . Carbon::parse($task->scheduled_date)->format('M d') . " at " . Carbon::parse($task->scheduled_date)->format('g:i a') : "") . $assignedStaff;
        $dataSend['message'] = $content1;
        $dataSend['user_id'] = $task->assigned_to ?? $vendorId;
        $dataSend['page'] = 'new_task';

        try {
            $this->sendTaskNotification($task,$content2);
        } catch (\Exception $e) {
            \Log::info($e->getMessage());
            \Log::info('deal task notification issue');
        }

        $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $vendorId, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin,$dataSend);
    }

    public static function sendTaskNotification($task,$content2){
            $description = $task->description;
            $time = ( $task->scheduled_date ? "on " . Carbon::parse($task->scheduled_date)->format('M d') . " at " . Carbon::parse($task->scheduled_date)->format('g:i a') : "");
            $notification_title = $content2;
            $notification_description = $description;
            if($task->deal_id){
                $enquiry = Deal::find($task->deal_id);
                $notification_data = [
                    "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                    "sound" => "alarm.mp3",
                    "page" => "tasks",
                    "id" => (string)($enquiry)? $enquiry->lead_id : $task->deal_id,
                    "task_id" => (string)$task->id,
                    "category_id"=> (string)$task->task_category_id,
                    "task_type" => "deal"
                ];
            }else{
                $notification_data = [];
            }
            // \Log::info('dealtask assign');\Log::info($notification_data);
        /* ----------Notification---------- */
            $result = Notifications::getUserTokens($task->assigned_to);
            if($result){
                dispatch(new SendNotification($result, $notification_title, $notification_description, $notification_data))->onQueue('notification');
            }

            try{
                $existPusher = PusherSetting::where('vendor_id',$task->vendor_id)->active()->first();
                if($existPusher){
                    
                    $message = 'Hey 👋, You Have a Scheduled '.getDealName(true).' Task. '.$task->name.' @'.$time;
                    event(new SendPusherNotification($task->assigned_to, $existPusher,$message));
                    // dispatch(new SendPusherNotification($task->assigned_to, $existPusher,$message))->onQueue('pusher-notification');
                }
            }catch(\Exception $e){
                \Log::info('Pusher issue');
                \Log::info($e->getMessage());
            }
            
        /* ----------End Notification---------- */
    }	
}
