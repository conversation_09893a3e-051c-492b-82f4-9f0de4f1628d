<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;
use App\BackendModel\Enquiry;
class DealActivity extends Model
{
    const TYPE_NEW = 1;
    const TYPE_NOTE = 2;
    const TYPE_STAGE = 3;
    const TYPE_DEAL_TYPE = 4;
    const TYPE_AGENT = 5;
    const TYPE_LEAD = 6;
    const TYPE_DATE = 7;
    const TYPE_COMMENT = 8;
    const TYPE_COMPANY = 9;
    const TYPE_AMOUNT = 10; 
    const TYPE_NAME = 11;
    const TYPE_DETAILS = 12;
    const TYPE_VOICE_NOTE = 13;
    const TYPE_FILE_NOTE = 14;
    const TYPE_DOCUMENT = 15;
    const TYPE_TASK = 16;
    const TYPE_LOG_CALL = 17;
    const TYPE_LOG_EMAIL = 18;
    const TYPE_LOG_MEETING = 19;
    const TYPE_DELETE = 20;
    const TYPE_TASK_DELETE = 21;
    

    protected $fillable = [
        'note','created_by','deal_id','log_type','created_at','updated_at','email_template','date','outcome','assigned_to','duration','task_id','task_category','task_status','updated_on'
    ];

    public function getCreatedAtAttribute($value)
    {
        if(Request::is('api/*'))
            return Carbon::parse($value)->timezone(auth()->user()->time_zone ?? 'Asia/Kolkata')->format('Y-m-d H:i:s');
        else
           return Carbon::parse($value)->timezone(auth()->user()->time_zone ?? 'Asia/Kolkata');
    }
    public function getUpdatedAtAttribute($value)
    {
        if(Request::is('api/*'))
            return Carbon::parse($value)->timezone(auth()->user()->time_zone ?? 'Asia/Kolkata')->format('Y-m-d H:i:s');
        else
            return Carbon::parse($value)->timezone(auth()->user()->time_zone ?? 'Asia/Kolkata');
    }

    public function agent()
    {
        return $this->belongsTo('App\User', 'created_by');
    }
    public function deal()
    {
        return $this->belongsTo(Deal::class, 'deal_id');
    }
    public function dealAdditionalField()
    {
        return $this->belongsTo(DealAdditionalField::class, 'deal_id');
    }
    public function assignTo()
    {
        return $this->belongsTo('App\User', 'note');
    }

    public function dealStage()
    {
        return $this->belongsTo(DealStage::class,'note','pk_int_deal_stage_id');
    }
    public function dealOldStage()
    {
        return $this->belongsTo(DealStage::class,'old_status_id','pk_int_deal_stage_id');
    }
    public function dealTask()
    {
        return $this->belongsTo(DealTask::class, 'task_id');

    }
    public function dealType()
    {
        return $this->belongsTo(DealType::class, 'note');

    }
    public function enquiry()
    {
        return $this->belongsTo(Enquiry::class, 'note');
    }
    public function taskCategory()
    {
        return $this->belongsTo(TaskCategory::class, 'task_category');
    }
    public function meetingOutcome()
    {
        return $this->belongsTo(MeetingOutcome::class, 'outcome');
    }
    public function callStatus()
    {
        return $this->belongsTo(CallStatus::class, 'outcome');
    }
    public function updatedOn()
    {
        return $this->belongsTo(DealActivity::class, 'updated_on');
    }
}
