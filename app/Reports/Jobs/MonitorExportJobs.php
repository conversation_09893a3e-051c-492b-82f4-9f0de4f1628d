<?php

declare(strict_types=1);

namespace App\Reports\Jobs;

use App\ExportHistory;
use App\Reports\Enums\ExportStatus;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

final class MonitorExportJobs implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(
        public readonly int $trackingId
    ) {

    }

    public function handle(): void
    {
        $tracker = ExportHistory::query()
            ->whereStatus(ExportStatus::InProgress)
            ->whereKey($this->trackingId)
            ->first();

        if (! $tracker instanceof ExportHistory) {
            Log::info('Export already in a finite state', [
                'tracking_id' => $this->trackingId,
            ]);
            return;
        }

        $tracker->update([
            'status' => ExportStatus::Failed,
        ]);

        Log::info('Export monitor job executed and moved to failed state', [
            'tracking_id' => $this->trackingId,
        ]);
    }
}
