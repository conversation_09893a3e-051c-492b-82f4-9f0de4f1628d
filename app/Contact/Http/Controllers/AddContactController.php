<?php

namespace App\Contact\Http\Controllers;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\LeadType;
use App\BackendModel\WhatsappTemplate;
use App\Common\Common;
use App\Common\Notifications;
use App\Common\SingleSMS;
use App\Common\Variables;
use App\Contact\Http\Requests\AddContactRequest;
use App\Events\CreateFollowup;
use App\Events\LeadAssigned;
use App\Facades\AutomationFacade;
use App\FrontendModel\LeadAdditionalDetails;
use App\FrontendModel\LeadAdditionalField;
use App\Notifications\AssignLeadToStaff;
use App\Notifications\NewLead;
use App\Task;
use App\User;
use Carbon\Carbon;
use Getlead\Campaign\Models\LeadCampaign;
use Getlead\Messagebird\Common\GupShup;
use Getlead\Messagebird\Models\WatsappCredential;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Laracasts\Flash\Flash;

final class AddContactController
{
    public function __invoke(AddContactRequest $request)
    {
        /**
         * @var $users \App\User|null
         */
        $user = auth()->user();

        $input = $request->all();
        $vendorId = $user->getBusinessId();

        $request->merge([
            'country_code' => str_replace("+", "", $request->country_code)
        ]);

        $exist_mobile = Enquiry::where('fk_int_user_id', $vendorId)
            ->where(function ($where) use ($request) {
                $where->where('vchr_customer_mobile', $request->country_code . $request->vchr_customer_mobile)
                    ->orWhere('vchr_customer_mobile', "+" . $request->country_code . $request->vchr_customer_mobile)
                    ->orWhere('more_phone_numbers', 'like', '%' . $request->vchr_customer_mobile . '%');
            })
            ->exists();

        if ($exist_mobile) {
            Flash::error("Whoops! Form Validation Failed");

            return redirect()
                ->back()
                ->withErrors(['vchr_customer_mobile' => ["This mobile number or additional numbers already exists."]])
                ->withInput();
        }

        $statusId = $request->feedback_status;
        if (!$request->feedback_status) {
            $feedback_status = FeedbackStatus::where('vchr_status', 'New')
                ->where('fk_int_user_id', $vendorId)
                ->first();
            if (!$feedback_status) {
                $feedback_status = new FeedbackStatus();
                $feedback_status->vchr_status = 'New';
                $feedback_status->vchr_color = '#000000';
                $feedback_status->fk_int_user_id = $vendorId;
                $feedback_status->created_by = $vendorId;
                $feedback_status->save();
            }
            $statusId = $feedback_status->pk_int_feedback_status_id;
        }

        try {
            $enquiry = new Enquiry();
            $enquiry->fk_int_user_id = $vendorId;
            $enquiry->taluk_id = $request->taluk_id;
            $enquiry->district_id = $request->district_id;
            $enquiry->model_id = $request->model_id;
            $enquiry->purchase_plan = $request->purchase_plan;
            $enquiry->date_of_purchase = $request->date_of_purchase;
            $enquiry->live_deal = $request->live_deal;
            $enquiry->remarks = $request->remarks;
            $enquiry->competing_model = $request->competing_model;
            $enquiry->vchr_customer_name = $request->vchr_customer_name;
            $enquiry->vchr_customer_company_name = $request->vchr_customer_company_name;
            $enquiry->vchr_customer_mobile = $request->country_code . $request->vchr_customer_mobile;
            $enquiry->vchr_customer_email = $request->vchr_customer_email;
            $enquiry->mobile_no = $request->vchr_customer_mobile;
            $enquiry->vchr_enquiry_feedback = $request->vchr_enquiry_feedback;
            $enquiry->country_code = $request->country_code;
            $enquiry->lead_type_id = $request->lead_type_id;
            $enquiry->purchase_date = $request->purchase_date;
            $enquiry->exp_wt_grams = $request->exp_wt_grams;
            $enquiry->function_date = $request->function_date;
            $enquiry->agency_id = $request->agency_id;

            if ($request->staff_id) {
                $enquiry->staff_id = $request->staff_id;
            }

            $enquiry->landline_number = $request->landline_number;
            $enquiry->designation_id = $request->designation_id;
            if ($request->more_phone_numbers) {
                $phone = array();
                foreach ($request->more_phone_numbers as $key => $number) {

                    if ($number && strlen((string)$number) > 3) {
                        $phone[$key] = $request->more_country_code[$key] . $number;
                    }
                }
                $more_phone_numbers = json_encode($phone);
                $enquiry->more_phone_numbers = $more_phone_numbers;
            }
            $enquiry->created_by = Auth::user()->pk_int_user_id;
            $enquiry->date_of_birth = $request->date_of_birth;
            $enquiry->feedback_status = $statusId;
            $enquiry->read_status = 0;
            $enquiry->fk_int_enquiry_type_id = $request->fk_int_enquiry_type_id;
            $enquiry->fk_int_purpose_id = $request->fk_int_purpose_id;
            $enquiry->event_date = $request->event_date;
            $enquiry->address = $request->address;
            $flag = $enquiry->save();
        } catch (\Exception $e) {
            Log::info('Unable to save enquiry from web due to', ['reason' => $e->getMessage()]);
            Flash::error("Whoops! Form Validation Failed ");
            return redirect()
                ->back()
                ->withInput();
        }

        if ($request->additional_field) {
            foreach ($request->additional_field as $index => $value) {
                if ($value != "") {
                    $field_name = LeadAdditionalField::find($index);
                    if ($field_name->type_text == 'Image') {
                        $additionalDetails = new LeadAdditionalDetails();
                        $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                        $additionalDetails->field_id = $index;
                        $additionalDetails->field_name = $field_name->field_name;
                        $filename = $value->store('public/custom_field_image');
                        $additionalDetails->value = $filename;
                        $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                        $additionalDetails->save();
                    } elseif ($field_name->input_type == 8) {
                        // Multi Select DropDown
                        $additionalDetails = new LeadAdditionalDetails();
                        $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                        $additionalDetails->field_id = $index;
                        $additionalDetails->field_name = $field_name->field_name;
                        $additionalDetails->value = json_encode($value);
                        $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                        $additionalDetails->save();
                    } else {
                        $additionalDetails = new LeadAdditionalDetails();
                        $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                        $additionalDetails->field_id = $index;
                        $additionalDetails->field_name = $field_name->field_name;
                        $additionalDetails->value = $value;
                        $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                        $additionalDetails->save();
                    }
                }
            }
        }

        try {
            if (!$request->staff_id)
                $lead_id = AutomationFacade::newLeadFunctions($enquiry->pk_int_enquiry_id);
        } catch (\Exception $e) {
            \Log::info($e->getMessage());
        }
        $vendor = User::find($vendorId);
        $enquiryType = EnquiryType::where('pk_int_enquiry_type_id', $request->fk_int_enquiry_type_id)->first()->vchr_enquiry_type;
        if (Auth::user()->int_role_id == User::STAFF) {
            $vendor->notify(new NewLead($enquiry));
        }

        if ($request->lead_type_id) {
            $lead_type = LeadType::where('id', $request->lead_type_id)->first();
            if ($lead_type && $lead_type->name == 'Key Person') {
                $exist = EnquiryType::where('vendor_id', $vendorId)
                    ->where('fk_int_enquiry_id', $enquiry->pk_int_enquiry_id)
                    ->first();
                if (!$exist) {
                    $enquiry_source = new EnquiryType();
                    $enquiry_source->vendor_id = $vendorId;
                    $enquiry_source->vchr_enquiry_type = $enquiry->vchr_customer_name;
                    $enquiry_source->fk_int_user_id = $vendorId;
                    $enquiry_source->fk_int_enquiry_id = $enquiry->pk_int_enquiry_id;
                    $enquiry_source->int_status = Variables::ACTIVE;
                    $enquiry_source->created_by = Auth::user()->pk_int_user_id;
                    $enquiry_source->save();
                }
            }
        }
        try {
            event(new CreateFollowup($enquiry->fk_int_enquiry_type_id, EnquiryFollowup::TYPE_NEW, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));

            if ($request->feedback_status) {
                event(new CreateFollowup($request->feedback_status, EnquiryFollowup::TYPE_STATUS, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));
            }
        } catch (\Exception $e) {
            Log::info($e->getMessage());
        }

        if ($request->vchr_enquiry_feedback) {
            try {
                event(new CreateFollowup($request->vchr_enquiry_feedback, EnquiryFollowup::TYPE_NOTE, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }
        }

        // Start Add Followup
        if ($request->followup_date) {
            if ($request->followup_date != " " && $request->followup_time != "") {
                $date_time = Carbon::createFromFormat('d-m-Y', $request->followup_date)->toDateString() . ' ' . Carbon::createFromFormat('H:i A', $request->followup_time)->toTimeString();
            } elseif ($request->followup_date != " ") {
                $date_time = Carbon::createFromFormat('d-m-Y', $request->followup_date)->toDateString() . ' ' . Carbon::now()->toTimeString();
            }
            $input = [
                'name' => request('followup_title') != '' ? request('followup_title') : 'Follow Up',
                'description' => request('followup_title') != '' ? request('followup_title') : 'Follow Up created from lead creation',
                'scheduled_date' => $date_time ?? now(),
                'task_category_id' => 2,
                'assigned_to' => $enquiry->staff_id,
                'assigned_by' => Auth::user()->pk_int_user_id,
                'vendor_id' => $vendorId,
                'enquiry_id' => $enquiry->pk_int_enquiry_id,
                'status' => 0,
            ];
            Task::create($input);
        }
        // End Add Followup

        if ($flag) {
            $show = new Common();

            /**-------------------AUTOMATION_START------------------------------------**/
            /**--WEBHOOK---------------------------------------------**/
            $automation_rule_webhook = $show->getRule($vendorId, 'new_lead', 'webhook', $enquiry->fk_int_enquiry_type_id);
            $status = FeedbackStatus::where('pk_int_feedback_status_id', $request->feedback_status)->select('vchr_status')->first();
            $post_data = [
                'customer_name' => $enquiry->vchr_customer_name,
                'email' => $enquiry->vchr_customer_email,
                'status' => ($status) ? $status->vchr_status : "New Status",
                'phone' => $enquiry->vchr_customer_mobile,
                'mobile' => $enquiry->mobile_no,
                'flag' => "new_lead",
            ];
            if (count($automation_rule_webhook) > 0) {
                foreach ($automation_rule_webhook as $w_hook) {
                    if ($w_hook->webhook_id != null) {
                        try {
                            $webHook = $show->getWebHookById($w_hook->webhook_id);
                        } catch (\Exception $e) {
                            Log::info($e->getMessage());
                        }

                        if ($webHook) {
                            try {
                                $show->postToWebHook($webHook->url, $post_data);
                            } catch (\Exception $e) {
                                Log::info($e->getMessage());
                            }
                        }
                    }
                }
            }
            /**--WEBHOOK---------------------------------------------**/
            /**--TASK---------------------------------------------**/
            $automation_rule_task = $show->getRule($vendorId, 'new_lead', 'task');
            if ($automation_rule_task) {
                $input = [
                    'name' => $automation_rule_task->task_title,
                    'description' => $automation_rule_task->task_description,
                    'scheduled_date' => Carbon::tomorrow(),
                    'task_category_id' => $automation_rule_task->task_category_id,
                    'assigned_to' => $automation_rule_task->task_assigned_to,
                    'assigned_by' => $vendorId,
                    'vendor_id' => $vendorId,
                    'enquiry_id' => $enquiry->pk_int_enquiry_id,
                    'status' => 0,
                ];
                Task::create($input);
            }
            /**-------------------AUTOMATION_END------------------------------------**/

            /**--WHATSAPP---------------------------------------------**/
            $automation_whatsapp = AutomationRule::where('vendor_id', $enquiry->fk_int_user_id)
                ->where('trigger', 'new_lead')
                ->where('action', 'whatsapp')
                ->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)
                ->orderBy('id', 'DESC')
                ->first();

            if ($automation_whatsapp) {
                $whatsappTemplate = WhatsappTemplate::where('pk_int_whatsapp_template_id', $automation_whatsapp->whatsapp_template_id)
                    ->select('text_whatsapp_template_description')
                    ->first();
                if ($whatsappTemplate) {
                    $gupshupObj = new GupShup();
                    $credientails = WatsappCredential::where('vendor_id', $enquiry->fk_int_user_id)
                        ->where('status', 1)
                        ->where('platform_id', 2)
                        ->first();
                    if ($credientails) {
                        $data = [
                            "api_key" => $credientails->access_key,
                            "from_number" => $credientails->source_mobile_num,
                            "app_name" => $credientails->template_name
                        ];
                        $gupshupObj->sendWatsappMessageIndividal(
                            $enquiry->country_id ?? '',
                            $enquiry->vchr_customer_mobile,
                            str_replace("{{name}}", $enquiry->vchr_customer_name, $whatsappTemplate->text_whatsapp_template_description),
                            $data
                        );
                    }
                }
            }
            /**--WHATSAPP---------------------------------------------**/

            /**--Assigned to Campaign--------------------------------------------- */
            $automation_rule_campaign = $show->getRule($vendorId, 'new_lead', 'add_to_campaign', $enquiry->fk_int_enquiry_type_id);

            if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                if ($automation_rule_campaign->enquiry_source_id == $enquiry->fk_int_enquiry_type_id) {
                    try {
                        $leads = Enquiry::find($enquiry->pk_int_enquiry_id);
                        $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                        $show->addToCampaign($automation_rule_campaign, $leads, Auth::user()->pk_int_user_id, $vendorId, $cmp->type);

                    } catch (\Exception $e) {
                        Log::info($e->getMessage());
                    }
                }
            }

            $enquiryNotif = Enquiry::find($enquiry->pk_int_enquiry_id);
            if ($enquiryNotif->staff_id != null) {
                $staff = User::find($enquiryNotif->staff_id);
                if ($staff && $enquiryNotif->staff_id != User::getVendorId()) {
                    $vendor->notify(new AssignLeadToStaff($enquiryNotif, Auth::user()->pk_int_user_id));
                }
                if ($staff) {
                    $staff->notify(new AssignLeadToStaff($enquiryNotif, Auth::user()->pk_int_user_id));
                    event(new LeadAssigned($enquiry->pk_int_enquiry_id, $request->staff_id));
                }
            }

            /**--API---------------------------------------------**/
            $source = $enquiry->fk_int_enquiry_type_id;
            $automation_rule_api = $show->getRule($vendorId, 'new_lead', 'api', $source);
            if ($automation_rule_api && $automation_rule_api->api != null) {
                try {
                    if ($vendorId == Variables::NIKSHAN_USER_ID) {
                        // Nikshan automation
                        $usr = $enquiryNotif->assigned_user;
                        $extension = null;
                        if ($usr) {
                            $extension = $usr->userExtension ? $usr->userExtension->extension : null;
                        }
                        if ($extension)
                            $show->postToIvrAutoCall($automation_rule_api->api, $extension, $post_data);
                    } else {
                        $show->postToWebHook($automation_rule_api->api, $post_data);
                    }
                } catch (\Exception $e) {
                    Log::info($e->getMessage());
                }
            }
            /**--API---------------------------------------------**/

            $userObject = User::getUserDetails($vendorId);
            $userAdminObject = User::getSingleAdminDetails();
            //Notifications
            $notifications = new Notifications();
            $from = env('MAIL_FROM_ADDRESS');
            $to = $userObject->email;
            $subject = "New Leads Notifications";
            $name = $userObject->vchr_user_name;
            $logo = $userAdminObject->vchr_logo;
            $attachment = "";
            $telegramId = $userObject->telegram_id;
            $mobileNumber = $userObject->vchr_user_mobile;
            $defaultSenderIdAdmin = SingleSMS::getSenderid($userAdminObject->pk_int_user_id, '');
            $defaultRouteAdmin = SingleSMS::getRoute($userAdminObject->pk_int_user_id, '');
            $content1 = 'New Leads via  ' . $enquiryType . '-' . $request->vchr_customer_name . '( ' . $request->vchr_customer_mobile . ' ). Added By ' . auth()->user()->vchr_user_name;
            $content2 = 'You have new leads via ' . $enquiryType . '-' . $request->vchr_customer_name . '( ' . $request->vchr_customer_mobile . ' ). Added By ' . auth()->user()->vchr_user_name;
            $dataSend['message'] = $content1;
            $dataSend['user_id'] = $enquiryNotif->staff_id ?? $vendorId;
            $dataSend['page'] = 'lead_page';
            $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $vendorId, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin, $dataSend);
            //-----------------End Notification

            if (!$enquiryNotif->staff_id && !$automation_rule_campaign) {
                /**--Assigned to Datapool--------------------------------------------- */
                $automation_rule_campaign = $show->getRule($vendorId, 'new_lead', 'add_to_data_pool', $enquiryNotif->fk_int_enquiry_type_id);

                if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                    if ($automation_rule_campaign->enquiry_source_id == $enquiryNotif->fk_int_enquiry_type_id) {
                        try {
                            $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                            $show->addToCampaign($automation_rule_campaign, $enquiryNotif, Auth::user()->pk_int_user_id, $vendorId, $cmp->type);

                        } catch (\Exception $e) {
                            Log::info($e->getMessage());
                        }
                    }
                } else {
                    $enquiryNotif->staff_id = auth()->user()->pk_int_user_id;
                    $enquiryNotif->save();
                }

            }

            if ($vendorId == 880) {
                if (Auth::user()->int_role_id == User::ADMIN) {
                    Flash::success("New Lead Created Successfully");

                    return redirect('admin/enquiries/create');
                } else {
                    Flash::success("New Lead Created Successfully");

                    return redirect('user/enquiries/create');
                }
            } else {
                if (Auth::user()->int_role_id == User::ADMIN) {
                    Flash::success("New Lead Created Successfully");
                    return redirect('/user/enquiries');
                } else {
                    Flash::success("New Lead Created Successfully");
                    return redirect('/user/enquiries');
                }
            }
        }
    }
}