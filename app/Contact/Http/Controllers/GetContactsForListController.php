<?php

namespace App\Contact\Http\Controllers;

use App\AgentStaff;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\LeadType;
use App\BackendModel\VirtualNumber;
use App\CallMaster;
use App\CloudTelephonySetting;
use App\FeatureRelease\Facade\FeatureReleaseChecker;
use App\FeatureRelease\TargetContext;
use App\FrontendModel\LeadAdditionalDetails;
use App\FrontendModel\LeadAdditionalField;
use App\Ivr\Models\Ivr;
use App\User;
use Carbon\Carbon;
use Getlead\Campaign\Models\LeadCampaign;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Log;

final class GetContactsForListController
{
    public function __invoke(Request $request)
    {
        Log::info('Request received to get list of paginated contacts ~', ['params' => $request->all()]);

        $draw = $request->get('draw');
        $start = $request->get('start');
        $length = $request->get('length'); //How many records to show
        $searchValue = $request->search['value']; //Search value

        /**
         * @var User $user
         */
        $user = $request->user();
        $vendorId = $user->getBusinessId();

        //Sets the current page
        Paginator::currentPageResolver(function () use ($start, $length) {
            return $start == 0 ? 1 : $start / $length + 1;
        });

        $campaignType = null;

        $enquiries = Enquiry::query()
            ->select(
                'tbl_enquiries.vchr_customer_name',
                'tbl_enquiries.address',
                'tbl_enquiries.followup_required',
                'tbl_enquiries.vchr_customer_email',
                'tbl_enquiries.vchr_customer_mobile',
                'tbl_enquiries.vchr_customer_company_name',
                'tbl_enquiries.pk_int_enquiry_id',
                'tbl_enquiries.int_status',
                'tbl_enquiries.fk_int_purpose_id',
                'tbl_enquiries.fk_int_enquiry_type_id',
                'tbl_enquiries.feedback_status',
                'tbl_enquiries.created_at',
                'tbl_enquiries.updated_at',
                'tbl_enquiries.read_status',
                'tbl_enquiries.staff_id',
                'tbl_enquiries.lead_type_id',
                'tbl_enquiries.next_follow_up',
                'tbl_enquiries.assigned_date',
                'tbl_enquiries.mobile_no'
            )
            ->addSelect([
                'lead_type' => LeadType::query()
                    ->select('name')
                    ->whereColumn('lead_types.id', '=', 'tbl_enquiries.lead_type_id')
                    ->limit(1),
                'vchr_user_name' => User::query()
                    ->select('tbl_users.vchr_user_name')
                    ->whereColumn('tbl_users.pk_int_user_id', 'tbl_enquiries.created_by')
                    ->limit(1),
                'staff_name' => User::query()
                    ->select('tbl_users.vchr_user_name')
                    ->whereColumn('tbl_users.pk_int_user_id', 'tbl_enquiries.staff_id')
                    ->limit(1),
                'vchr_purpose' => EnquiryPurpose::query()
                    ->selectRaw('coalesce(`tbl_enquiry_purpose`.`vchr_purpose`, "None")')
                    ->whereColumn('tbl_enquiry_purpose.pk_int_purpose_id', '=', 'tbl_enquiries.fk_int_purpose_id')
                    ->limit(1),
                'source' => EnquiryType::query()
                    ->select('tbl_enquiry_types.vchr_enquiry_type')
                    ->whereColumn('tbl_enquiry_types.pk_int_enquiry_type_id', '=', 'tbl_enquiries.fk_int_enquiry_type_id')
                    ->limit(1),
                'vchr_status' => FeedbackStatus::query()
                    ->select('tbl_feedback_status.vchr_status')
                    ->whereColumn('tbl_feedback_status.pk_int_feedback_status_id', '=', 'tbl_enquiries.feedback_status')
                    ->limit(1),
                'vchr_color' => FeedbackStatus::query()
                    ->select('tbl_feedback_status.vchr_color')
                    ->whereColumn('tbl_feedback_status.pk_int_feedback_status_id', '=', 'tbl_enquiries.feedback_status')
                    ->limit(1),
            ])
            ->with(['lastTask:id,scheduled_date,enquiry_id,status'])
            ->where('tbl_enquiries.fk_int_user_id', $vendorId)
            ->when($user->isOpsStaff(), function ($query) use ($request, $user) {
                $query->where(function ($q) use ($request, $user) {
                    $q->where('tbl_enquiries.staff_id', $user->pk_int_user_id)
                        ->orWhereIn('tbl_enquiries.staff_id', AgentStaff::query()
                            ->where('agent_id', $user->pk_int_user_id)
                            ->when($request->filled('branch_id'), function ($q) use ($request) {
                                $q->whereHas('agentStaff', function ($where) use ($request) {
                                    $where->where('branch_id', $request->get('branch_id'));
                                });
                            })
                            ->select('staff_id'));
                });
            })
            ->when($user->isOpsUser() && $request->filled('branch_id'), function ($query) use ($request) {
                $query->whereIn('staff_id', fn($query) => $query->select('staff_id')
                    ->from('tbl_users')
                    ->where('branch_id', $request->get('branch_id'))
                );
            })
            ->when($request->filled('enquiry_type'), function ($query) use ($request) {
                $purpose = $request->integer('enquiry_type');
                $purpose == -1
                    ? $query->whereNull('tbl_enquiries.fk_int_enquiry_type_id')
                    : $query->where('tbl_enquiries.fk_int_enquiry_type_id', $purpose);
            })
            ->when($request->filled('enquiry_purpose'), function ($query) use ($request) {
                $purpose = $request->get('enquiry_purpose');
                $purpose == -1
                    ? $query->whereNull('tbl_enquiries.fk_int_purpose_id')
                    : $query->where('tbl_enquiries.fk_int_purpose_id', $purpose);
            })->when($request->filled('staff_id'), function ($query) use ($request) {
                $staff_id = $request->get('staff_id');
                $staff_id == "unassigned"
                    ? $query->whereNull('tbl_enquiries.staff_id')
                    : $query->where('tbl_enquiries.staff_id', $staff_id);
            })->when($request->filled('enquiry_status'), function ($query) use ($request) {
                $status = $request->get('enquiry_status');
                $status == -1
                    ? $query->whereNull('tbl_enquiries.feedback_status')
                    : $query->where('tbl_enquiries.feedback_status', $status);
            })->when($request->filled('created_at_from')
                && $request->filled('created_at_to')
                , function ($query) use ($request) {
                    $dateField = 'tbl_enquiries.created_at';

                    $dateBy = $request->integer('dateBy');
                    $dateField = match ($dateBy) {
                        1 => 'tbl_enquiries.created_at',
                        2 => 'tbl_enquiries.updated_at',
                        3 => 'tbl_enquiries.assigned_date',
                        default => $dateField,
                    };

                    $query->whereBetween($dateField, [$request->get('created_at_from') . ' 00:00:00', $request->get('created_at_to') . ' 23:59:59']);

                })->when($request->filled('created_by'), function ($query) use ($request) {
                $query->where('tbl_enquiries.created_by', $request->get('created_by'));
            })->when($request->filled('lead_type_id'), function ($query) use ($request) {
                $leadTypeId = $request->get('lead_type_id');
                $leadTypeId == -1
                    ? $query->whereNull('tbl_enquiries.lead_type_id')
                    : $query->where('tbl_enquiries.lead_type_id', $leadTypeId);

            })->when($request->filled('filter'), function ($query) use ($request) {
                switch ($request->get('filter')) {
                    case 'latest':
                        $query->whereBetween('tbl_enquiries.created_at', [Carbon::today()->subDays(7)->toDateString(), Carbon::today()->toDateString()]);
                        break;

                    case 'today':
                        $query->whereDate('tbl_enquiries.created_at', Carbon::today()->toDateString());
                        break;

                    case 'current-month':
                        $query->whereBetween('tbl_enquiries.created_at', [Carbon::now()->startOfMonth()->toDateString(), Carbon::now()->endOfMonth()->toDateString()]);
                        break;
                }
            })->when($request->filled('district_id'), function ($query) use ($request) {
                $query->where('tbl_enquiries.district_id', $request->get('district_id'));
            })
            ->when($request->filled('campaign_id') && !$request->boolean('unassigned'), function ($query) use ($request, $vendorId) {
                $query->whereIn('tbl_enquiries.pk_int_enquiry_id', function ($subQuery) use ($request, $vendorId) {
                    $subQuery->select('lead_id')
                        ->from('campaign_leads')
                        ->where('vendor_id', $vendorId)
                        ->where('campaign_id', $request->get('campaign_id'));
                });
            })
            ->when($request->filled('campaign_id') && $request->boolean('unassigned'), function ($query) use ($request, $vendorId) {
                $query->whereNotIn('tbl_enquiries.pk_int_enquiry_id', function ($subQuery) use ($request, $vendorId) {
                    $subQuery->select('lead_id')
                        ->from('campaign_leads')
                        ->where('vendor_id', $vendorId)
                        ->where('campaign_id', $request->get('campaign_id'));
                })->whereNull('tbl_enquiries.staff_id');
            })
            ->when($request->boolean('yet_to_contacts'), function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->whereNull('tbl_enquiries.staff_id')
                        ->whereNull('tbl_enquiries.feedback_status');
                });
            })->when($request->boolean('attention'), fn($query) => $query->where('lead_attension', 1))
            ->when($request->filled('followup_added'), function ($query) use ($request) {

                $followup = $request->get('followup_added');

                if ($followup == 1) {
                    $query->whereHas('task', function ($q) {
                        $q->where('status', 0);
                    });
                }

                if ($followup == 2) {
                    $query->whereDoesntHave('task', function ($q) {
                        $q->where('status', 0);
                    });
                }
            })->when($request->filled('follow_up_leads'), function ($query) {
                $query->whereHas('task', function ($q) {
                    $q->where('scheduled_date', '<=', Carbon::now()->startOfDay()->toDateTimeString())
                        ->where('status', 0);
                });
            })->when($request->filled('sortby') && $request->integer('sortby') == 1,
                function ($query) {
                    $query->orderBy('created_at', 'DESC');
                },
                function ($query) {
                    $query->orderBy('updated_at', 'DESC');
                }
            );

        // Retrieve all relevant LeadAdditionalField records at once
        if ($request->filled('additional_ids')) {
            $fields = LeadAdditionalField::query()
                ->whereIn('id', $request->get('additional_ids'))
                ->get()
                ->keyBy('id');

            $enquiriesQuery = $enquiries->newQuery();

            foreach ($request->get('additional_ids') as $key => $value) {
                $field = $fields->get($value);

                if (!$field) {
                    continue;
                }

                if ($field->input_type == 8) {
                    $values = $request->additional_val[$key];
                    // Use a collection to handle multiple values
                    $enquiriesQuery->whereHas('additional_details', function ($q) use ($value, $values) {
                        $q->where('field_id', $value);

                        // Combine conditions using whereIn or whereRaw if needed
                        $q->where(function ($subQuery) use ($values) {
                            foreach ($values as $v) {
                                $subQuery->orWhere('value', 'LIKE', '%' . $v . '%');
                            }
                        });
                    });
                } else {
                    $value = $request->additional_val[$key];

                    $enquiriesQuery->whereHas('additional_details', function ($q) use ($value, $field) {
                        $q->where('field_id', $field->id)
                            ->where('value', $value);
                    });
                }
            }

            // Apply the optimized query to your base query
            $enquiries = $enquiriesQuery;
        }

        if ($searchValue != '' && strlen($searchValue) >= 3 && $this->isMeilieSearchEnabled($vendorId)) {
            $searchIds = Enquiry::search($searchValue)
                ->where('fk_int_user_id', $vendorId)
                ->orderByDesc('pk_int_enquiry_id')
                ->query(fn($builder) => $builder->select('pk_int_enquiry_id'))
                ->take(60)
                ->get()
                ->pluck('pk_int_enquiry_id');

            $enquiries->whereIn('tbl_enquiries.pk_int_enquiry_id', $searchIds);

        } else {
            $enquiries = $enquiries
                ->where(function ($where) use ($searchValue, $vendorId) {
                    if ($searchValue != '' && strlen($searchValue) >= 3) {
                        $where->searchLegacy($searchValue);
                    }
                });
        }

        if (request()->has('site_page') || request()->did_filter) {
            $enquiries = $enquiries->whereHas('leadSource', function ($q) use ($vendorId) {
                $q->where('fk_int_user_id', $vendorId);
                $q->where('vchr_enquiry_type', 'IVR');
            });

            $telephony = CloudTelephonySetting::query()
                ->where('default', 1)
                ->where('vendor_id', $vendorId)
                ->first();

            if ($telephony && request()->did_filter) {
                if ($telephony->operator == 2) {
                    if (strlen(request()->did_filter) > 10)
                        $didNum = substr(request()->did_filter, 2, 10);
                    else
                        $didNum = request()->did_filter;

                    $did = CallMaster::query()
                        ->where('vendor_id', $vendorId)
                        ->distinct('caller_number')
                        ->where(function ($q) use ($didNum) {
                            if (request()->did_filter) {
                                $q->where('did', request()->did_filter)
                                    ->orWhere('called_number', request()->did_filter)
                                    ->orWhere('did', $didNum);
                            }
                        })
                        ->pluck('caller_number')
                        ->toArray();
                } else {
                    $did = Ivr::query()
                        ->where('vendor_id', $vendorId)
                        ->distinct('caller_number')
                        ->where(function ($q) use ($request) {
                            if ($request->did_filter)
                                $q->where('called_number', 'LIKE', $request->did_filter . '%');
                        })
                        ->pluck('caller_number')
                        ->toArray();
                }
            } else {
                $did = Ivr::query()
                    ->where('vendor_id', $vendorId)
                    ->distinct('caller_number')
                    ->where(function ($q) use ($request) {
                        if ($request->did_filter)
                            $q->where('called_number', 'LIKE', $request->did_filter . '%');
                    })
                    ->pluck('caller_number')
                    ->toArray();
            }

            if (count($did) > 0) {
                $enquiries = $enquiries->whereIn('vchr_customer_mobile', $did);
            }
        }

        $enquiries = $enquiries->paginate($length);

        $telephony = CloudTelephonySetting::query()
            ->where('default', 1)
            ->where('vendor_id', $vendorId)
            ->first();

        $i = 1;
        $additional_details = LeadAdditionalDetails::query()
            ->whereIn('enquiry_id', $enquiries->pluck('pk_int_enquiry_id')->toArray())->get();
        $additional_fields = LeadAdditionalField::query()->where('vendor_id', $vendorId)->get();

        foreach ($enquiries as $key => $row) {
            $row->slno = $i;
            $i++;
            //Datas
            $row->assign_agent =
                ' <div class="form-check form-check-assign" >
                    <input id="checkbox2" type="checkbox" data-enq_id="' .
                $row->pk_int_enquiry_id .
                '">
                    </div>';
            // for ivr lead report
            if (request()->has('site_page')) {
                if ($telephony) {
                    if ($telephony->operator == 2) {
                        $did = CallMaster::where('vendor_id', $vendorId)
                            ->where('caller_number', $row->vchr_customer_mobile)
                            ->select('call_uuid as did')
                            ->first();
                    } else {
                        $did = Ivr::where('vendor_id', $vendorId)
                            ->where('caller_number', $row->vchr_customer_mobile)
                            ->select('called_number as did')
                            ->first();
                    }
                } else {
                    $did = Ivr::where('vendor_id', $vendorId)
                        ->where('caller_number', $row->vchr_customer_mobile)
                        ->select('called_number as did')
                        ->first();
                }
                if ($did) {
                    $row->did = $did->did;
                    $vNumber = VirtualNumber::where('fk_int_user_id', $vendorId)
                        ->where('vchr_virtual_number', 'LIKE', $did->did . '%')
                        ->first();

                    $row->did_name = $vNumber ? $vNumber->name : '';
                } else {
                    $row->did = '';
                    $row->did_name = '';
                }
            }

            if ($row->read_status == 0) {
                if ($row->vchr_customer_name != null) {
                    $row->cust_name =
                        '<span>
                        <a class="ks-izi-modal-trigger2" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' .
                        $row->pk_int_enquiry_id .
                        '">
                        <!--<span class="d-tbl-noti">New</span>-->' .
                        '</strong><br>' .
                        $row->vchr_customer_name .
                        '</strong><br><div class="shortner">' .
                        '' .
                        $row->vchr_customer_company_name .
                        '</div></a></span>';
                } else {
                    $row->cust_name =
                        '<span>
                         <a class="ks-izi-modal-trigger2" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' .
                        $row->pk_int_enquiry_id .
                        '"><!--<span class="d-tbl-noti">New</span>-->' .
                        "No name" .
                        '</a></span>';
                }
            } else {
                if ($row->vchr_customer_name != null) {
                    $row->cust_name =
                        '<span>
                        <a class="ks-izi-modal-trigger2" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' .
                        $row->pk_int_enquiry_id .
                        '">' .
                        $row->vchr_customer_name .
                        '<br><div class="shortner">' .
                        '' .
                        $row->vchr_customer_company_name .
                        '</div></a></span>';
                } else {
                    $row->cust_name =
                        '<span>
                        <a  class="ks-izi-modal-trigger2" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' .
                        $row->pk_int_enquiry_id .
                        '">' .
                        'No name' .
                        '</a></span>';
                }
            }

            //Company Name
            $row->cust_company = '<span>' . ($row->vchr_customer_company_name ?? "No company name") . '</span>';
            //
            //Mobile Number
            $row->mobno = $row->vchr_customer_mobile ? '<a href="tel:+' . $row->vchr_customer_mobile . '">' . $row->vchr_customer_mobile . '</a>' : "No mobile no";

            $row->mobile = $row->mobile_no ? '<a href="tel:+' . $row->mobile_no . '">' . $row->mobile_no . '</a>' : "No mobile no";

            //
            //Purpose
            $row->enq_type = '<span>' . ($row->fk_int_purpose_id ? $row->vchr_purpose : "None") . '</span>';
            //
            //Lead Type
            $row->lead_type = '<span>' . ($row->lead_type_id ? $row->lead_type : "") . '</span>';
            //
            //Status
            $row->feedback = '<span>' . ($row->vchr_status ?? "None") . '</span>';
            //Created By
            $row->created = '<span>' . ($row->vchr_user_name ?? "System") . '</span>';
            //
            try {
                if ($row->created_at) {
                    $diff = Carbon::now()->diffInDays(Carbon::parse($row->created_at));

                    $row->created_date =
                        '<span>' .
                        ($diff < 3 ? Carbon::parse($row->created_at)->diffForHumans() : Carbon::parse($row->created_at)->format('d M Y h:i A')) .
                        '</span>';
                } else {
                    $row->created_date = '<span>' . "None" . '</span>';
                }
                //
                if ($row->updated_at) {
                    $diff = Carbon::now()->diffInDays(Carbon::parse($row->updated_at));
                    $row->updated_date =
                        '<span>' .
                        ($diff < 3 ? Carbon::parse($row->updated_at)->format('d M Y h:i A') : Carbon::parse($row->updated_at)->format('d M Y h:i A')) .
                        '</span>';
                    // }
                } else {
                    $row->updated_date = '<span>' . "None" . '</span>';
                }
            } catch (\Exception $exp) {
            }
            $row->feedback = '<span style="color:' . $row->vchr_color . '">' . ($row->vchr_status ?? " ") . '</span>';
            if ($row->int_status == 1) {
                $row->show =
                    '                     
                                        <a href="#"  class="btn btn-clean btn-hover-light-primary btn-sm btn-icon dot-icon" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <img src="/backend/images/dots.png">
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-sm dropdown-menu-right">
                                        
                                        <ul class="navi navi-hover act">
                                        <li class="navi-item">
                                        <a href="#" enquiry-id="' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal">
                                        <span class="navi-icon mr-2">
                                        <img src="/backend/images/edit.png">
                                        </span>
                                        <span class="navi-text"> Edit</span>
                                        </a>
                                        </li>';
                if ($user->int_role_id == \App\User::USERS || $user->is_co_admin == 1) {
                    $row->show .=
                        '          <li class="navi-item">
                                        <a href="#" id="delete_plan" enquiry-id="' .
                        $row->pk_int_enquiry_id .
                        '"  class="navi-link py-2 enquiry-delete">
                                        <span class="navi-icon mr-2">
                                        <img src="/backend/images/delete.png">
                                        </span>
                                        <span class="navi-text"> Delete</span>
                                        </a>
                                        </li>';
                }
                $row->show .=
                    '          <li class="navi-item">
                                        <a href="' .
                    'template-whatsapp/' .
                    $row->pk_int_enquiry_id .
                    '" target="_blank" class="navi-link py-2">
                                        <span class="navi-icon mr-2">
                                        <img src="/backend/images/whatsapp.svg" width="28px">
                                        </span>
                                        <span class="navi-text"> WhatsApp</span>
                                        </a>
                                        </li>
                                        <li class="navi-item">
                                        <a target="_blank" href="' .
                    'enquiry-sms/' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2">
                                        <span class="navi-icon mr-2">
                                        <img src="/backend/images/chat.svg" width="28px">
                                        </span>
                                        <span class="navi-text"> Message</span>
                                        </a>
                                        </li>
                                        <li class="navi-item">
                                        <a target="_blank" href="' .
                    'sales/orders/create/' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2">
                                        <span class="navi-icon mr-2">
                                        <img src="/backend/images/cart.svg" width="28px">
                                        </span>
                                        <span class="navi-text"> Order</span>
                                        </a>
                                        </li>
                                        </ul>
                                        
                                        </div>
                                        ';
            } //If Status is not in active state
            else {
                $row->show =
                    '
                                        
                                        <a href="#" class="btn btn-clean btn-hover-light-primary btn-sm btn-icon dot-icon" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <img src="assets/media/task/dots.png">
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-sm dropdown-menu-right">
                                        
                                        <ul class="navi navi-hover act">
                                        <li class="navi-item">
                                        <a href="#" enquiry-id="' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal" >
                                        <span class="navi-icon mr-2">
                                        <img src="/backend/images/edit.png">
                                        </span>
                                        <span class="navi-text"> Edit</span>
                                        </a>
                                        </li>';
                if ($user->int_role_id == \App\User::USERS || $user->is_co_admin == 1) {
                    $row->show .=
                        '         <li class="navi-item">
                                        <a href="#" id="delete_plan" enquiry-id="' .
                        $row->pk_int_enquiry_id .
                        '"  class="navi-link py-2 enquiry-delete">
                                        <span class="navi-icon mr-2">
                                        <img src="/backend/images/delete.png">
                                        </span>
                                        <span class="navi-text"> Delete</span>
                                        </a>
                                        </li>';
                }

                $row->show .=
                    '        <li class="navi-item">
                                        <a href="' .
                    'template-whatsapp/' .
                    $row->pk_int_enquiry_id .
                    '" target="_blank" class="navi-link py-2">
                                        <span class="navi-icon mr-2">
                                        <img src="/backend/images/whatsapp.svg" width="28px">
                                        </span>
                                        <span class="navi-text"> WhatsApp</span>
                                        </a>
                                        </li>
                                        <li class="navi-item">
                                        <a target="_blank" href="' .
                    'enquiry-sms/' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2">
                                        <span class="navi-icon mr-2">
                                        <img src="/backend/images/chat.svg" width="28px">
                                        </span>
                                        <span class="navi-text"> Message</span>
                                        </a>
                                        </li>
                                        <li class="navi-item">
                                        <a target="_blank" href="' .
                    'sales/orders/create/' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2">
                                        <span class="navi-icon mr-2">
                                        <img src="/backend/images/cart.svg" width="28px">
                                        </span>
                                        <span class="navi-text"> Order</span>
                                        </a>
                                        </li>
                                        
                                        </ul>
                                        
                                        </div>';
            }

            $assignedToUser = $row->staff_name;

            if ($assignedToUser == "") {
                $assignedTo = "Assign to Agent";
            } else {
                $assignedTo = $assignedToUser;
            }

            if ($request->filled('campaign_id') && $campaignType != LeadCampaign::POOL) {
                $row->assigned_to = '<span "><a >' . $assignedTo . '</a></span>';
            } else {
                $row->assigned_to = '<span "><a href="javascript:showEditModal(' . $row->pk_int_enquiry_id . ')">' . $assignedTo . '</a></span>';
            }
            if ($row->lastTask) {
                $row->next_follow_up_data = '<span><a>' . Carbon::parse($row->lastTask->scheduled_date)->diffForHumans() . '</a></span>';
                $row->setRelation('lastTask', null);
            } else {
                $row->next_follow_up_data = '<span><a href="javascript:showNextFollowUpModal(' . $row->pk_int_enquiry_id . ')" class="add-followup" >Add <i class="fa fa-plus"></i></a></span>';
            }
            $enquiry_additional_details = $additional_details->where('enquiry_id', $row->pk_int_enquiry_id);

            if ($additional_fields) {
                foreach ($additional_fields as $add_field) {
                    if ($add_field->show_in_list == 1) {
                        $enquiry_additional_detail = $enquiry_additional_details->where('field_id', $add_field->id)->first();
                        if ($enquiry_additional_detail) {
                            // Accessing the 'value' attribute directly
                            $row['additional_' . $add_field->id] =
                                is_array($enquiry_additional_detail->value) ? implode(",", $enquiry_additional_detail->value) : $enquiry_additional_detail->value;
                        } else {
                            $row['additional_' . $add_field->id] = '';
                        }
                    }
                }
            }

        }
        return ['draw' => $draw, 'recordsTotal' => $enquiries->total(), 'recordsFiltered' => $enquiries->total(), 'data' => $enquiries];
    }


    private function isMeilieSearchEnabled(int $vendorId): bool
    {
        return FeatureReleaseChecker::isEnabled('meilisearch', new TargetContext($vendorId));
    }
}