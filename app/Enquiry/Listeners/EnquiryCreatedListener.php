<?php

declare(strict_types=1);

namespace App\Enquiry\Listeners;

use App\Enquiry\Events\EnquiryCreated;
use App\Enquiry\Jobs\ProcessNewEnquiry;
use DateInterval;
use DateTimeInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

final class EnquiryCreatedListener implements ShouldQueue
{
    /**
     * The number of seconds before the job should be made available.
     */
    public DateTimeInterface|DateInterval|int|null $delay = 1;

    public function handle(EnquiryCreated $event): void
    {
        Log::withContext([
            'enquiry_id' => $event->enquiryId,
            'vendor_id' => $event->vendorId,
            'enquiry_type_id' => $event->enquiryTypeId,
            'source' => $event->source,
        ]);

        Bus::dispatch(new ProcessNewEnquiry(
            enquiryId: $event->enquiryId,
            vendorId: $event->vendorId,
        ));
    }
}
