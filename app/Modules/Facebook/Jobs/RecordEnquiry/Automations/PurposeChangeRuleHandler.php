<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Jobs\RecordEnquiry\Automations;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

final class PurposeChangeRuleHandler extends AbstractRuleHandler
{
    public function __construct(
        private readonly AssignAgent $assignAgent,
    ) {
    }

    public function handle(Enquiry $enquiry, Collection $rules): void
    {
        $purposeId = $enquiry->fk_int_purpose_id;

        Log::withContext([
            'enquiry_id' => $enquiry->pk_int_enquiry_id,
            'vendor_id' => $enquiry->fk_int_user_id,
            'purpose_id' => $purposeId,
        ]);

        $rule = $rules->where('trigger', '=', 'purpose_change')
            ->where('action', '=', 'assign')
            ->where('enquiry_purpose_id', '=', $purposeId)
            ->first();

        if ($rule instanceof AutomationRule) {
            $this->assignAgent->byRule(rule: $rule, enquiry: $enquiry);
            return;
        }

        Log::info('Purpose change assign agent automation rule not found');

        parent::handle(enquiry: $enquiry, rules: $rules);
    }
}
