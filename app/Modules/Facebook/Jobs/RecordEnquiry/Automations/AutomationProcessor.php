<?php

namespace App\Modules\Facebook\Jobs\RecordEnquiry\Automations;

use App\BackendModel\Enquiry;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class AutomationProcessor
{
    private AutomationRuleHandler $handlerChain;

    public function __construct(AssignAgent $assignAgent)
    {
        $autoAssignHandler = new AutoAssignRuleHandler($assignAgent);
        $statusChangeHandler = new StatusChangeRuleHandler($assignAgent);
        $purposeChangeHandler = new PurposeChangeRuleHandler($assignAgent);
        $sourceChangeHandler = new SourceChangeRuleHandler($assignAgent);
        $additionalFieldHandler = new AdditionalFieldRuleHandler($assignAgent);

        $autoAssignHandler->setNext(handler: $statusChangeHandler);
        $statusChangeHandler->setNext(handler: $purposeChangeHandler);
        $purposeChangeHandler->setNext(handler: $sourceChangeHandler);
        $sourceChangeHandler->setNext(handler: $additionalFieldHandler);

        $this->handlerChain = $autoAssignHandler;
    }

    public function process(Enquiry $enquiry, Collection $rules): void
    {
        Log::info(sprintf('Processing automation rules for %s', get_class($this->handlerChain)), [
            'enquiry_id' => $enquiry->pk_int_enquiry_id,
        ]);

        $this->handlerChain->handle($enquiry, $rules);
    }
}