<?php

namespace App\Services;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\FrontendModel\LeadAdditionalDetails;
use App\Modules\Facebook\Jobs\RecordEnquiry\Automations\AssignAgent;
use App\Task;
use Carbon\Carbon;
use App\Common\Common;
use App\BackendModel\FeedbackStatus;

class AutomationService
{
    public function newLeadFunctions($leadId, $is_notify = null)
    {
        $enquiry = Enquiry::query()->find($leadId);

        if (!$enquiry instanceof Enquiry) {
            return $leadId;
        }

        // Fetch automation rules
        $automationRules = AutomationRule::query()
            ->where('vendor_id', $enquiry->fk_int_user_id)
            ->orderBy('id', 'DESC')
            ->get();

        // Handle different automation scenarios
        $this->handleNewLeadAutoAssign($automationRules, $enquiry, $leadId, $is_notify);
        $this->handleSourceChangeAutomation($automationRules, $enquiry, $leadId, $is_notify);
        $this->handlePurposeChangeAutomation($automationRules, $enquiry);
        $this->handleStatusChangeAutomation($automationRules, $enquiry, $leadId, $is_notify);
        $this->handleAdditionalFieldAutomation($automationRules, $enquiry, $leadId, $is_notify);
        $this->handleApiAutomation($enquiry);

        return $leadId;
    }

    private function handleNewLeadAutoAssign($automationRules, $enq, $lead, $is_notify)
    {
        if (!$enq->staff_id) {
            $assignSourceData = AutomationRule::query()
                ->where('vendor_id', $enq->fk_int_user_id)
                ->where('trigger', 'new_lead')
                ->where('action', 'assign')
                ->orderby('id', 'DESC')
                ->where('enquiry_source_id', $enq->fk_int_enquiry_type_id)
                ->first();
            if ($assignSourceData) {
                AutomationRule::autoassign($assignSourceData, $lead, $is_notify);
            } else {

                $assignData = AutomationRule::where('vendor_id', $enq->fk_int_user_id)
                    ->where('trigger', 'new_lead')
                    ->where('action', 'assign')
                    ->whereNull('enquiry_source_id')
                    ->orderby('id', 'DESC')
                    ->first();
                if ($assignData) {
                    AutomationRule::autoassign($assignData, $lead, $is_notify);
                }
            }
        }
    }

    private function handleSourceChangeAutomation($automationRules, $enq, $lead, $is_notify)
    {
        // Auto-assign based on source change
        $assignSourceData = $automationRules
            ->where('trigger', 'source_change')
            ->where('enquiry_source_id', $enq->fk_int_enquiry_type_id)
            ->where('action', 'assign')
            ->first();

        if ($assignSourceData) {
            AutomationRule::autoassign($assignSourceData, $lead, $is_notify);
        }

        // Task creation logic
        $automationRuleTask = $automationRules
            ->where('trigger', 'source_change')
            ->where('action', 'task')
            ->first();

        if ($automationRuleTask) {
            $scheduledDate = $automationRuleTask->duration
                ? Carbon::now()->addMinutes($automationRuleTask->duration)
                : Carbon::now();

            Task::create([
                'name' => $automationRuleTask->task_title,
                'description' => $automationRuleTask->task_description,
                'scheduled_date' => $scheduledDate,
                'task_category_id' => $automationRuleTask->task_category_id,
                'assigned_to' => $automationRuleTask->task_assigned_to,
                'assigned_by' => $enq->fk_int_user_id,
                'vendor_id' => $enq->fk_int_user_id,
                'enquiry_id' => $enq->pk_int_enquiry_id,
                'status' => 0,
            ]);
        }
    }

    private function handlePurposeChangeAutomation($automationRules, Enquiry $enquiry): void
    {
        $purposeRule = $automationRules
            ->where('trigger', 'purpose_change')
            ->where('enquiry_purpose_id', $enquiry->fk_int_purpose_id)
            ->where('action', 'assign')
            ->first();

        if (!$purposeRule instanceof AutomationRule) {
            return;
        }
        (new AssignAgent())->byRule($purposeRule, $enquiry);
    }


    private function handleStatusChangeAutomation($automationRules, $enq, $lead, $is_notify)
    {
        // Auto-assign based on status change
        $assignStatus = $automationRules
            ->where('trigger', 'status_change')
            ->where('feedback_status_id', $enq->feedback_status)
            ->where('action', 'assign')
            ->first();

        if ($assignStatus) {
            if ($enq->fk_int_user_id == 196) {
                \Log::info('handleStatusChangeAutomation' . $enq->vchr_customer_name);
            }
            AutomationRule::autoassign($assignStatus, $enq->pk_int_enquiry_id, $is_notify);
        }
    }

    private function handleAdditionalFieldAutomation($automationRules, $enq, $lead, $is_notify)
    {
        $assignAdditionalData = $automationRules
            ->where('trigger', 'value_change')
            ->where('enquiry_source_id', $enq->fk_int_enquiry_type_id)
            ->where('action', 'assign');

        foreach ($assignAdditionalData as $rule) {
            $leadAdd = LeadAdditionalDetails::where('enquiry_id', $enq->pk_int_enquiry_id)
                ->where('field_id', $rule->additional_field)
                ->where('value', $rule->additional_field_value)
                ->first();
            if ($leadAdd) {
                AutomationRule::autoassign($rule, $lead, $is_notify);
                if ($enq->fk_int_user_id == 196) {
                    \Log::info('handleAdditionalFieldAutomation' . $enq->vchr_customer_name);
                }
                break;
            }
        }
    }

    private function handleApiAutomation($enq)
    {
        $automation_rule_api = AutomationRule::where('vendor_id', $enq->fk_int_user_id)
            ->where('action', 'api')
            ->where('trigger', 'new_lead')
            ->where('enquiry_source_id', $enq->fk_int_enquiry_type_id)
            ->orderBy('id', 'DESC')
            ->first();

        if ($automation_rule_api && $automation_rule_api->api != null) {
            try {
                $status = FeedbackStatus::where('pk_int_feedback_status_id', $enq->feedback_status)
                    ->select('vchr_status')
                    ->first();
                $commonObj = new Common();
                $post_data = [
                    'customer_name' => $enq->vchr_customer_name,
                    'email' => $enq->vchr_customer_email,
                    'status' => $status->vchr_status ?? null,
                    'phone' => $enq->vchr_customer_mobile,
                    'mobile' => $enq->mobile_no,
                    'flag' => "new_lead",
                    'state' => ($enq->fk_int_user_id == 1119) ? $enq->additional_details->where('field_id', '=', 317 /* state id is for bazani */)->value('value') ?? null : null
                ];
                $commonObj->postToWebHook($automation_rule_api->api, $post_data);
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
        }
    }
}
