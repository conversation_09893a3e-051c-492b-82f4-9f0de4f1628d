<?php

declare(strict_types=1);

namespace App\Providers;

use App\Contracts\FcmServiceInterface;
use App\Http\Middleware\AdminMenuMiddleware;
use App\Http\Middleware\UserMiddleware;
use App\Ivr\IVRWebhook\GLDialer\ParseGLDialerEventPayload;
use App\Ivr\IVRWebhook\Jobs\AgentAssignedCallProcessor;
use App\Ivr\IVRWebhook\Jobs\AnsweredCallProcessor;
use App\Ivr\IVRWebhook\Jobs\DisconnectedCallProcessor;
use App\Ivr\IVRWebhook\Jobs\IncomingCallProcessor;
use App\Ivr\IVRWebhook\Jobs\IvrEventProcessorFactory;
use App\Ivr\IVRWebhook\Jobs\IvrProviderEventPayloadParserFactory;
use App\Ivr\IVRWebhook\Jobs\MissedCallProcessor;
use App\Ivr\IVRWebhook\Jobs\OutGoingCallProcessor;
use App\Ivr\IVRWebhook\Voxbay\VoxbayEventPayloadParser;
use App\Logging\QueryLoggingProvider;
use App\Services\AutomationService;
use App\Services\FcmService;
use App\Services\FileUploadService;
use App\User;
use CuyZ\Valinor\Cache\FileSystemCache;
use CuyZ\Valinor\Cache\FileWatchingCache;
use CuyZ\Valinor\MapperBuilder;
use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Foundation\Application;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;
use Shared\Mediator\Mediator;
use Shared\Mediator\MediatorInterface;
use Shared\Mediator\Middlewares\LoggerMiddleware;
use Shared\Mediator\Middlewares\WithCache;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if ($this->app->environment('production')) {
            URL::forceScheme('https');
        }

        Schema::defaultStringLength(191);
        app()->router->aliasMiddleware('user_auth', UserMiddleware::class);

        app()->router->aliasMiddleware('admin_menu', AdminMenuMiddleware::class);
        app()->router->aliasMiddleware('admin_auth', Authenticate::class);
        Validator::extend('date_multi_format', static function ($attribute, $value, $formats) {
            // iterate through all formats
            foreach ($formats as $format) {

                // parse date with current format
                $parsed = date_parse_from_format($format, $value);

                // if value matches given format return true=validation succeeded
                if ($parsed['error_count'] === 0 && $parsed['warning_count'] === 0) {
                    return true;
                }
            }

            // value did not match any of the provided formats, so return false=validation failed
            return false;
        });

        Gate::define('viewPulse', static fn (User $user) => $user->isAdmin());
    }

    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(FcmServiceInterface::class, FcmService::class);

        $this->app->singleton('fileupload', static fn () => new FileUploadService());
        $this->app->singleton('automationService', static fn ($app) => new AutomationService());

        Collection::macro('paginate', function ($perPage, $total = null, $page = null, $pageName = 'page') {
            $page = $page ?: LengthAwarePaginator::resolveCurrentPage($pageName);
            return new LengthAwarePaginator(
                $this->forPage($page, $perPage),
                $total ?: $this->count(),
                $perPage,
                $page,
                [
                    'path' => LengthAwarePaginator::resolveCurrentPath(),
                    'pageName' => $pageName,
                ]
            );
        });

        $this->app->singleton(MediatorInterface::class, static fn (Application $app) => new Mediator(
            $app,
            $app->make(LoggerMiddleware::class),
            $app->make(WithCache::class),
        ));

        $this->app->singleton(
            MapperBuilder::class,
            function (): MapperBuilder {
                $cache = new FileSystemCache($this->app->bootstrapPath('cache' . DIRECTORY_SEPARATOR . 'mapper'));

                if ($this->app->environment('local')) {
                    $cache = new FileWatchingCache($cache);
                }

                return (new MapperBuilder())
                    ->withCache($cache);
            }
        );

        $this->app->singleton(
            IvrEventProcessorFactory::class,
            static fn ($app) => new IvrEventProcessorFactory([
                $app->make(IncomingCallProcessor::class),
                $app->make(AgentAssignedCallProcessor::class),
                $app->make(DisconnectedCallProcessor::class),
                $app->make(MissedCallProcessor::class),
                $app->make(AnsweredCallProcessor::class),
                $app->make(OutGoingCallProcessor::class),
            ])
        );

        $this->app->singleton(
            IvrProviderEventPayloadParserFactory::class,
            static fn ($app) => new IvrProviderEventPayloadParserFactory([
                $app->make(VoxbayEventPayloadParser::class),
                $app->make(ParseGLDialerEventPayload::class),
            ])
        );

        if (! Config::get('logging.query.enabled')) {
            return;
        }

        $this->app->register(QueryLoggingProvider::class);
    }
}
