<?php

declare(strict_types=1);

namespace App\Admin\Events;

use App\Admin\Models\Admin;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AdminLoggedIn
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Admin $admin,
        public ?string $ipAddress = null,
        public ?string $userAgent = null
    ) {
    }
}