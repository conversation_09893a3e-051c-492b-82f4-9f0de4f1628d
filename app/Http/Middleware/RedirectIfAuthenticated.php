<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use App\Common\Variables;


class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        if (Auth::guard($guard)->check()) {

            switch (Auth::user()->int_role_id) {
                case '1':
                    return redirect('/admin/dashboard');
                    break;
                case '2':
                    return redirect('/user/home');
                    break;
                case '3':
                    return redirect('/user/home');
                    break;
                default:
                    return redirect('/user/home');
            }
        }

        return $next($request);
    }
}
