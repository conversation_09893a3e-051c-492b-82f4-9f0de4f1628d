<?php

namespace App\Http\Middleware;

use App\BillingSubscription;
use App\Common\Common;
use App\Common\Variables;
use App\User;
use Closure;
use Auth;

class CheckGLPromoSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!Auth::check())
            return redirect('login');

        if (Auth::user()->int_role_id == User::USERS || Auth::user()->int_role_id == User::STAFF) {
            $commonObj = new Common();
            $userSubscription = $commonObj->checkUserSubscription(User::getVendorId(), Variables::SERVICE_CRM);

            if ($userSubscription) {
                return $next($request);
            } else {
                $message = 'You are not subscribed GL Promo or plan expired. please contact your administrator.';
                $data['message'] = $message;
                $data['type'] = Variables::NOT_SUBSCRIBED;
                return view('backend.user-pages.dashboard.subscription-dashboard')->with('data', $data);
            }
        }
    }
}
