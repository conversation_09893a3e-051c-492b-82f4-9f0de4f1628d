<?php

namespace App\Http\Middleware;

use App\BillingSubscription;
use App\Common\Common;
use App\Common\Variables;
use App\User;
use Carbon\Carbon;
use Closure;
use Auth;

class CheckSMSSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!Auth::check())
            return redirect('login');
        if (Auth::user()->int_role_id == User::USERS || Auth::user()->int_role_id == User::STAFF) {

            $commonObj = new Common();
            $userSubscription = $commonObj->checkUserSubscription(User::getVendorId(), Variables::SERVICE_CRM);
   
            if ($userSubscription) {
                return $next($request);
            } else {
                $message = 'You are not subscribed to SMS service or plan expired. please contact your administrator.';
                $data['message'] = $message;
                $data['type'] = Variables::NOT_SUBSCRIBED;
                return view('backend.user-pages.dashboard.subscription-dashboard')->with('data', $data);
            }

//            $user = User::with([
//                'subscriptions' => function ($query) {
//                    $query->where('date_to', '>=', Carbon::today());
//                },
//                'subscriptions.plans.services' => function ($query) {
//                    $query->where('service', Variables::SERVICE_SMS);
//                }])
//                ->where('pk_int_user_id', User::getVendorId())->first();
//            $exists = $user->subscriptions->contains('user_id', User::getVendorId());
//            if ($exists) {
//                $searchService = new Variables();
//                $service_name = $searchService->getGetleadService($user->subscriptions, Variables::SERVICE_SMS);
//                if ($service_name == Variables::SERVICE_SMS) {
//                    return $next($request);
//                } else {
//                    $message = 'You are not subscribed SMS service . please contact your administrator.';
//                    $data['message'] = $message;
//                    $data['type'] = Variables::NOT_SUBSCRIBED;
//                    return view('backend.user-pages.dashboard.subscription-dashboard')->with('data', $data);
//                }
//            } else {
//                $message = 'You are  not subscribed  to any plans.Please contact your administrator.';
//                $data['message'] = $message;
//                $data['type'] = Variables::NOT_SUBSCRIBED;
//                return view('backend.user-pages.dashboard.subscription-dashboard')->with('data', $data);
//            }
        }

        // return $next($request);
    }
}
