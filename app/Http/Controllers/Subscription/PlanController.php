<?php

namespace App\Http\Controllers\Subscription;

use App\BackendModel\Smsroute;
use App\Common\Variables;
use App\Subscription\Plan;

use App\Subscription\PlanService;
use App\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Validator;
use DataTables;
use DB;
use App\Subscription\UserServiceSubscription;


class PlanController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        $smsRoutes = Smsroute::get();
        $users = User::where('int_role_id', User:: USERS)->where('int_status', User::ACTIVATE)->get();
        return view('backend.pages.plans.index')
            ->with('users', $users)
            ->with('smsRoutes', $smsRoutes);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $input = $request->all();
        $validator = validator::make($input, Plan::$rule, Plan::$message);
        if ($validator->passes()) {
            try {
                DB::beginTransaction();
                $plan = new Plan();
                $plan->fill($input);
                $plan->vendor_id = $input['vendor_id'];
                $plan->status = Plan::ACTIVATE;

                if ($request->has('tax_inclusive')) {
                    $plan->tax_inclusive = 1;
                    $tax_amount = $request->amount - ($request->amount * (100 / (100 + $request->tax)));
                    $plan->tax_amount = round($tax_amount);
                    $plan->net_amount = round($request->amount - $tax_amount);
                } else {
                    $tax_amount = $request->amount * $request->tax / 100;
                    $plan->tax_amount = round($tax_amount);
                    $plan->tax_inclusive = 0;
                    $plan->net_amount = $request->amount;
                }
                $plan->save();
                /*------------------Add Services---------------------*/
                if ($request->service) {
                    foreach ($request->service as $service) {
                        if (array_key_exists('name', $service)) {
                            $plan_service = new PlanService();
                            $plan_service->plan_id = $plan->id;
                            $plan_service->service = $service['name'];
                            if ($service['name'] == Variables::SERVICE_CRM) {
                                $plan_service->user_count = $request->usercount;
                            }

                            if ($service['name'] == "GL Scratch") {
                                $plan_service->scratch_count = $service['count'];
                            }


                            if ($service['name'] == Variables::SERVICE_CRM || $service['name'] == Variables::SERVICE_MISSEDCALL || $service['name'] == Variables::SERVICE_IVR) {
                                if (array_key_exists('count', $service)) {
                                    if ($service['count'] != null) {
                                        $plan_service->count = $service['count'];
                                    } else {
                                        if ($service['name'] == Variables::SERVICE_CRM) {
                                            $msg = 'Agent Count';
                                        } else {
                                            $msg = 'Count';
                                        }
                                        return response()->json(['msg' => 'Add ' . $msg, 'status' => false]);
                                    }
                                } else {
                                    return response()->json(['msg' => 'Something Went wrong . Please try again later', 'status' => false]);
                                }
                            } else if ($service['name'] == Variables::SERVICE_SMS) {
                                if (array_key_exists('sms_route', $service)) {
                                    $count = 0;
                                    foreach ($service['sms_route'] as $smsRoute) {
                                        $count += $smsRoute['count'];
                                    }
                                    $sms_count = json_encode($service['sms_route'], true);
                                    //  $sms_count;
                                    $plan_service->sms_count = $sms_count;
                                    $plan_service->count = $count;
                                }
                            }
                            $plan_service->save();
                        }
                    }
                }
                DB::commit();
                return response()->json(['msg' => 'Plan added.', 'status' => true]);
            } catch (\Exception $e) {
                DB::rollBack();
                return response()->json(['msg' => $e->getMessage(), 'status' => false]);
            }
        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'false']);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Add Custom Plan
     */
    public function addCustomPlan(Request $request)
    {
        $input = $request->all();

        $rule = [
            'name' => 'required',
            'description' => 'required',
            'amount' => 'required',
            'duration' => 'required|numeric',
            //'service' => 'required|in:1',
        ];
        $message = [
            'name.required' => 'Plan name is required',
            'description.required' => 'Plan Description is required',
            'amount.required' => 'Plan amount is required',
            'duration.required' => 'Plan duration is required',
            'duration.numeric' => 'Plan duration should be a number',
            //'services.required' =>'Atleast one of services should be checked'
        ];
        $validator = validator::make($input, $rule, $message);
        if ($validator->passes()) {
            try {
                DB::beginTransaction();
                $plan = new Plan();
                $plan->fill($input);
                $plan->status = Plan::ACTIVATE;


                if ($request->has('tax_inclusive')) {
                    $plan->tax_inclusive = 1;

                } else {
                    $plan->tax_inclusive = 0;

                }

                $plan->save();
                /*------------------Add Services---------------------*/
                $plan_service = new PlanService();
//                    if (array_key_exists('sms_route', $service)) {
//                        $count = 0;
//                        foreach ($service['sms_route'] as $smsRoute) {
//                            $count += $smsRoute['count'];
//                        }
//                        $sms_count = json_encode($service['sms_route'], true);
//                        //  $sms_count;
//                        $plan_service->sms_count = $sms_count;
//                        $plan_service->count = $count;
                $plan_service->save();

                DB::commit();
                return response()->json(['msg' => 'Plan added.', 'status' => true]);
            } catch (\Exception $e) {
                DB::rollBack();
                return response()->json(['msg' => $e->getMessage(), 'status' => false]);
            }
        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'false']);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Plan $plan
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $plan = Plan::with('services')->with('country')->find($id);
        if ($plan) {
            foreach ($plan->services as $service) {
                if ($service->sms_count != NULL) {
                    $service->sms_count = json_decode($service->sms_count, true);
                }
            }
            return response()->json(['msg' => 'data present', 'status' => true, 'data' => $plan]);
        } else {
            return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
        }
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Plan $plan
     * @return \Illuminate\Http\Response
     */
    public function edit(Plan $plan)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Plan $plan
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Plan $plan)
    {
        //return $request->all();
        $validator = validator::make($request->all(), Plan::$rule, Plan::$message);
        if ($validator->passes()) {
            try {
                $plan->name = $request->name;
                $plan->alias = $request->alias;
                $plan->description = $request->description;
                $plan->amount = $request->amount;
                $plan->duration = $request->duration;
                $plan->vendor_id = $request->vendor_id;
                if ($request->has('tax_inclusive')) {
                    $plan->tax_inclusive = 1;
                    $tax_amount = $request->amount - ($request->amount * (100 / (100 + $request->tax)));
                    $plan->tax_amount = $tax_amount;
                    $plan->net_amount = $request->amount - $tax_amount;
                } else {
                    $tax_amount = $request->amount * $request->tax / 100;
                    $plan->tax_amount = $tax_amount;
                    $plan->tax_inclusive = 0;
                    $plan->net_amount = $request->amount;
                }
                $flag = $plan->save();
                /*------------------Update Service ---------------------*/
                if ($request->service) {
                    foreach ($request->service as $service) {
                        $plan_service = PlanService::find($service['id']);
                        if ($plan_service) {
                            $plan_service->service = $service['name'];
                            
                            if ($service['name'] == "GL Scratch") {
                                $plan_service->scratch_count = $service['count'];
                            }
                            if ($service['name'] == "CRM") {
                                $plan_service->count = $service['count'];
                                try{
                                    $change = UserServiceSubscription::where('vendor_id',$request->vendor_id)->where('service','CRM')->first();
                                    if($change){
                                        $change->count = $service['count'];
                                        $change->save();
                                    }
                                }catch(\Exception $e){

                                }
                            }

                            //  $plan_service->duration = $service['duration'];
                            if ($service['name'] == Variables::SERVICE_SMS) {
                                if (array_key_exists('sms_route', $service)) {
                                    $count = 0;
                                    foreach ($service['sms_route'] as $smsRoute) {
                                        $count += $smsRoute['count'];
                                    }
                                    $sms_count = json_encode($service['sms_route'], true);
                                    //  $sms_count;
                                    $plan_service->sms_count = $sms_count;
                                    $plan_service->count = $count;
                                }
                            }
                            $plan_service->save();
                        }
                    }
                }
                if ($flag) {
                    return response()->json(['msg' => 'Plan Updated.', 'status' => 'success']);
                } else {
                    return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                }

            } catch (\Exception $e) {
                return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Plan $plan
     * @return \Illuminate\Http\Response
     */
    public function destroy(Plan $plan)
    {
        if ($plan) {
            $plan->delete();
            $servces = PlanService::where('plan_id', $plan->id)->delete();
            return response()->json(['msg' => 'Plan Deleted.', 'status' => true]);
        } else {
            return response()->json(['msg' => 'Something went wrong.Please try again later.', 'status' => true]);
        }
    }

    public function getTotalPlans()
    {
        $plans = Plan::orderby('id', 'DESC')->get();
        foreach ($plans as $key => $row) {
            $row->slno = ++$key;
        }
        return Datatables::of($plans)
            ->editColumn('user', function ($plans) {
                $user = User::where('pk_int_user_id', $plans->vendor_id)->first();
                if ($user) {
                    return $user->vchr_user_name . '(' . $user->vchr_user_mobile . ')';
                } else {
                    return "All Users";
                }
            })->editColumn('position', function ($plans) {
                if ($plans->vendor_id == 0) {
                    if ($plans->position == NULL) {
                        return '<a class="badge badge-success  plan-position" data-plan-id="' . $plans->id . '" data-toggle="tooltip" title="Set as 1st Position ">Up</a>';
                    } else {
                        return '<span class="badge badge-info">First</span>';
                    }
                }
            })
            ->addColumn('show', function ($plans) {
                if ($plans->status == 1) {
                    return '<div class="dropdown show">
                                <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fa fa-circle" aria-hidden="true"></i>
                                    <i class="fa fa-circle" aria-hidden="true"></i>
                                    <i class="fa fa-circle" aria-hidden="true"></i>   
                                </a>
                                <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                    <a plan-id="' . $plans->id . '" class="dropdown-item  ks-izi-modal-trigger1" data-target="#ks-izi-modal-large"  data-toggle="tooltip" title="View" > 
                                        <i class="f-16 fa fa-angle-double-right"></i>&nbsp;View
                                    </a>
                                    <a id="delete_plan" plan-id="' . $plans->id . '" class="dropdown-item plan-delete"> 
                                        <i class="f-16 fa fa-trash-o"></i>&nbsp;Delete
                                    </a>
                                    <a class="dropdown-item btn-activate plan-act" plan-id="' . $plans->id . '" data-toggle="tooltip" title="Deactivate"> 
                                        <i class="f-18 fa fa-check-circle active"></i>&nbsp;Deactivate
                                    </a>
                                </div>
                            </div>';
                } //If Status is not in active state
                else {
                    return '<div class="dropdown show">
                                <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fa fa-circle" aria-hidden="true"></i>
                                    <i class="fa fa-circle" aria-hidden="true"></i>
                                    <i class="fa fa-circle" aria-hidden="true"></i>   
                                </a>
                                <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                    <a plan-id="' . $plans->id . '" class="dropdown-item  ks-izi-modal-trigger1" data-target="#ks-izi-modal-large"  data-toggle="tooltip" title="View" > 
                                        <i class="f-16 fa fa-angle-double-right"></i>&nbsp;View
                                    </a>
                                    <a href="" plan-id="' . $plans->id . '" class="dropdown-item plan-delete"> 
                                        <i class="f-16 fa fa-trash-o"></i>&nbsp;Delete
                                    </a>
                                    <a class="dropdown-item plan-act" plan-id="' . $plans->id . '" data-toggle="tooltip" title="Activate"> 
                                        <i class="f-18 fa fa-times-circle"></i>&nbsp;Activate
                                    </a>
                                </div>
                            </div>';
                }
            })
            ->rawColumns(['show', 'position'])
            ->toJson(true);
    }

    /**Activate Plan
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function activate($id)
    {
        $plan = Plan::find($id);
        if ($plan) {
            $plan->status = Plan::ACTIVATE;
            $plan->save();
            return response()->json(['msg' => 'Plan Activated.', 'status' => true]);
        } else {
            return response()->json(['msg' => 'Something went wrong.Please try again later.', 'status' => false]);

        }
    }

    /**
     * Deactivate Plan
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deactivate($id)
    {
        $plan = Plan::find($id);
        if ($plan) {
            $plan->status = Plan::DEACTIVATE;
            $plan->save();
            return response()->json(['msg' => 'Plan Deactivated.', 'status' => true]);
        } else {
            return response()->json(['msg' => 'Something went wrong.Please try again later.', 'status' => true]);
        }
    }

    public function setAsFirst($planId)
    {

        Plan::where('position', 1)->update(['position' => NULL]);
        $plan = Plan::find($planId);
        if ($plan) {
            $plan->position = 1;
            $plan->save();
            return response()->json(['msg' => 'Plan Position Updated.', 'status' => true]);

        } else {
            return response()->json(['msg' => 'Something went wrong.Please try again later.', 'status' => true]);

        }
    }
}
