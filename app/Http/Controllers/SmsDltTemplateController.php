<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Validator;
use Auth;
use App\SmsDltTemplate;
use App\SmsDltHeader;
use App\User;
use DataTables;
use Session;

class SmsDltTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    protected $vendorId;
    public function __construct()
    {
        if (!Auth::check())
            return redirect('login');
        $this->vendorId = Auth::user()->int_role_id==1 ? Session::get('current_user_id')  : User::getVendorId();
    }
    public function index()
    {
        $headers = SmsDltHeader::where('fk_int_user_id',$this->vendorId)->get();
        return view('backend.user-pages.sms-dlt-template.index',compact('headers'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $input = $request->all();
        $validator=validator::make($input,SmsDltTemplate::$rule,SmsDltTemplate::$message);

        if($validator->passes())
        {
            if($this->vendorId==0)
                    return response()->json(['msg'=>'Something went wrong..Try again.', 'status'=>'error']);
            try{
                $exist=SmsDltTemplate::where('header_id',$request->header_id)->where('template_id',$request->template_id)->where('fk_int_user_id',$this->vendorId)->first();
                if( $exist)
                {
                    $msg=['template_id'=>'The template id has already been taken'];
                    return response()->json(['msg'=>$msg, 'status'=>'error']); 
                }
                $header = SmsDltHeader::find($request->header_id);
                $smsDltTemplate = new SmsDltTemplate();
                $smsDltTemplate->title = $request->title;
                $smsDltTemplate->template = $request->template;
                $smsDltTemplate->entity_id = $header->entity_id;
                $smsDltTemplate->header_id = $request->header_id;
                $smsDltTemplate->template_id = $request->template_id;
                $smsDltTemplate->sender_id = $header->sender;
                $smsDltTemplate->fk_int_user_id = $this->vendorId;
                $smsDltTemplate->status = SmsDltTemplate::APPROVED;
                
                if($smsDltTemplate->save()){

                    return response()->json(['msg'=>'SMS DLT Template added.', 'status'=>'success']);
                    
                }else{

                    return response()->json(['msg'=>'Something went wrong..Try again.', 'status'=>'error']);
                }
                
            }catch (\Exception $e) {
    
                return response()->json(['status'=>'error','msg' => $e->getMessage()], 500);
            }
        }
        else{
            
            return response()->json(['status' => 'error','msg' =>$validator->messages()]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateDlt(Request $request)
    {
         $input = $request->all();    
         $updaterule = SmsDltTemplate::$updaterule;
         $updaterule['template_id'] = ['required','unique:sms_dlt_templates,template_id,'.$request->id];
         $validator = validator::make($input, SmsDltTemplate::$updaterule, SmsDltTemplate::$updatemessage);
        
        if ($validator->passes()){

            try{
                $header = SmsDltHeader::find($request->header_id);
                
                $smsDltTemplate = SmsDltTemplate::find($request->id);
                if(($smsDltTemplate->fk_int_user_id != $this->vendorId && Auth::user()->int_role_id!=1) || $this->vendorId==0)
                    return response()->json(['msg'=>'Something went wrong..Try again.', 'status'=>'error']);
                
                $smsDltTemplate->title = $request->title;
                $smsDltTemplate->template = $request->template;
                $smsDltTemplate->entity_id = $header->entity_id;
                $smsDltTemplate->header_id = $request->header_id;
                $smsDltTemplate->template_id = $request->template_id;
                $smsDltTemplate->sender_id = $header->sender;
                $smsDltTemplate->fk_int_user_id = $this->vendorId;
                $smsDltTemplate->status = $request->status;
                
                if($smsDltTemplate->save()){
                    if($request->status == 1){

                       return response()->json(['msg'=>'SMS DLT Template updated..', 'status'=>'success']);
                    }else{

                       return response()->json(['msg'=>'SMS DLT Template updated.Waiting for admin approval.', 'status'=>'success']);
                    }
                }else{

                    return response()->json(['msg'=>'Something went wrong..Try again.', 'status'=>'error']);
                }
                 
           }catch (\Exception $e) {

                return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);
            }
        }else{

            return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $template = SmsDltTemplate::find($id);

        if ($template) {
            if ($template->delete()) {

                return response()->json(['msg' => "SMS DLT Template deleted.", 'status' => 'success']);
            } else {

                return response()->json(['msg' => "Not able to delete.Please try again", 'status' => 'error']);
            }
        } else {

            return response()->json(['msg' => "SMS DLT Template not found.", 'status' => 'error']);
        }
    }

    /**
     * Function to get all dlt template data
     *
     * @return mixed
     */
    public function getSmsDltData()
    {

        $templates = SmsDltTemplate::with(['header','header.entity'])->orderby('created_at','DESC');

            $templates = $templates->where('fk_int_user_id',$this->vendorId);
            $templates = $templates->get();
  
        foreach ($templates as $key => $row) {
            $row->slno=++$key;
        }

        return Datatables::of($templates)
        ->addColumn('header_value', function ($template) {
            return $template->header ? $template->header->header.'('.$template->header->sender.')' : '';
        })
        ->addColumn('show', function ($template) {

        //   if ($template->status == 1) {
                       
        //     return '<button class="btn btn-sm btn-success btn-activate "   smstemplate-id="' . $template->id . '" data-toggle="tooltip" title="SMS DLT template is active"> <i class="fa fa-check"></i></button>';
        //   } //If Status is not in active state
        //   else {
              
            return '<div class="dropdown show">
                        <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>   
                        </a>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                            <a href="#" smstemplate-id="' . $template->id . '" data-status="'.$template->status.'" data-id="'.$template->id.'" data-title="'.$template->title.'" data-template="'.$template->template.'" data-entity_id="'.$template->entity_id.'" data-header_id="'.$template->header_id.'" data-template_id="'.$template->template_id.'" data-sender_id="'.$template->sender_id.'" class="dropdown-item ks-izi-modal-trigger-template1" data-target="#ks-izi-modal-large-template1"  data-toggle="modal">
                                <i class="fa fa-edit mg-r-5"></i>&nbsp;Edit
                            </a>
                            <a href="#" id="delete_plan" smstemplate-id="' . $template->id . '" class="dropdown-item smddlttemplate-delete"> 
                                <i class="fa fa-trash-o mg-r-5"></i>&nbsp;delete
                            </a>
                        </div>
                    </div>';
                    // <button class="btn btn-sm btn-warning" smstemplate-id="' . $template->id . '" data-toggle="tooltip" title="Admin approval pending"> <i class="fa fa-times"></i></button>';
        //   }
        })
        ->rawColumns(['show'])
        ->toJson(true);
    }
}
