<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Common\SendTelegram;
use Validator;
use App\User;
use DB;

/**
 * Class send telegram message controller
 */
class SendTelegramMessageController extends Controller
{
    /** @var $rule */
    private static $rule = [
        'message' => 'required',
        'user_id' => 'required'
    ];
 
    /** @var $message */
    private static $message = [
        'message.required' =>'Message field is required',
        'user_id'          =>'User ID required'
    ];

    /**
     * Function to send message to telegram
     *
     * @return void
     */
    public function sendTelegramMessage(Request $request)
    {
        $vendorId = $request->user_id;
        $apiKey   = User::select('telegram_api_key','telegram_id')->find($vendorId);
        if($apiKey->telegram_api_key){
          $input     = $request->all();
          $validator = validator::make($input, self::$rule, self::$message);
          if($validator->passes()) {
            try{
                    if($apiKey->telegram_id){
                        $telegram =  new SendTelegram;
                        $telegram->telegram($apiKey->telegram_id, $request->message);
                        DB::table('tbl_telegram_msgs')->insert(
                            [
                                'chat_id' => $apiKey->telegram_id,
                                'user_id' => $vendorId,
                                'message' => $request->message
                            ]
                        );
                        return response()->json(['msg'=>'Message send sucessfully.', 'status'=>'success']);
                    } else {

                        return response()->json(['msg'=>'Telegram ID not found.Try with adding ID', 'status'=>'fail']);
                    }
    
                    return response()->json(['msg'=>'SMS DLT Template added..Waiting for admin approval.', 'status'=>'success']);
                
                
            }catch (\Exception $e) {
    
                return response()->json(['msg' => $e->getMessage(), 'status'=>'fail']);
            }
        }else {
            return response()->json(['msg' =>$validator->messages(), 'status' => 'fail']);
        }
      }else{

        return response()->json(['msg'=>'Unauthorized', 'status'=>'fail']);
      }
    }
}
