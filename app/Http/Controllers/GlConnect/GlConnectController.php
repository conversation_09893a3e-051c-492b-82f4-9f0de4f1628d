<?php

namespace App\Http\Controllers\GlConnect;

use App\BackendModel\Settings;
use App\CloudTelephonySetting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use App\WebHook;
use Getlead\Indiamart\Models\Indiamart;
use Getlead\Shiprocket\Models\ShipRocket;

class GlConnectController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $settings = CloudTelephonySetting::where('vendor_id', User::getVendorId())->get();
        $checkTally = Settings::where('vchr_settings_type', 'Tally')->where('fk_int_user_id', User::getVendorId())->first();
        $mailerCloud = Settings::where('vchr_settings_type', 'mailer-cloud')
            ->where('fk_int_user_id', User::getVendorId())->where('int_status', 1)->first();
        if ((clone $settings)->count() > 0) {
            $is_ivr = true;
            $voxbay = (clone $settings)->whereIn('operator', [1, 3])->where('default')->first();
            $bonvoice = (clone $settings)->where('operator', 2)->where('default')->first();
        } else {
            $is_ivr = false;
            $voxbay = [];
            $bonvoice = [];
        }
        $indiaMart = Indiamart::where('fk_int_user_id', User::getVendorId())->exists() ? true : false;
        $webhooks = WebHook::where('vendor_id', User::getVendorId())->exists() ? true : false;
        $shiprocket = ShipRocket::where('fk_int_user_id', User::getVendorId())->get();

        return view('gl-connect.home.index', compact('is_ivr', 'voxbay', 'bonvoice', 'checkTally', 'indiaMart', 'mailerCloud', 'webhooks', 'shiprocket'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function changeTallyData(Request $request)
    {

        $settings = Settings::where('vchr_settings_type', 'Tally')->where('fk_int_user_id', User::getVendorId())->first();
        if ($settings) {
            $settings->vchr_settings_value = ($request->value) ? 'true' : 'false';
            $settings->save();
        } else {
            $settings = new Settings();
            $settings->vchr_settings_type = 'Tally';
            $settings->vchr_settings_value = ($request->value) ? 'true' : 'false';
            $settings->fk_int_user_id = User::getVendorId();
            $settings->int_status = 0;
            $settings->save();
        }

        return response()->json(['data' => 'success', 'status' => true], 200);
    }
}
