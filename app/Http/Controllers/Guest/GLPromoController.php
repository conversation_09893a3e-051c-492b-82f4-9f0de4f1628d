<?php

namespace App\Http\Controllers\Guest;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\GlPromo;
use App\BackendModel\GlPromocode;
use App\BackendModel\GlApiTokens;
use App\BackendModel\Campaign;
use App\BackendModel\SenderId;
use App\Jobs\PromoPackageExpiredJobs;
use App\BackendModel\EnquiryType;
use App\BackendModel\Enquiry;
use App\User;
use Validator;
use DataTables;
use App\Common\Variables;
use DB;
use Illuminate\Support\Facades\Input;
use Auth;
use Carbon\Carbon;
use App\Common\Common;
use App\Core\CustomClass;
use App\Common\SingleSMS;
use App\Common\Notifications;
use App\SmsPanel;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;


//use App\Http\Resources\Testimonial\TestimonialResource;

class GLPromoController extends Controller
{


    //GL Promo api for all users
    public function index(Request $request)
    {
        $input = $request->all();
        $mobileno = $request->mobileno;
        $rule = [
            'token' => 'required',
            'mobileno' => 'required|numeric|digits_between:8,14'
        ];

        $validator = validator::make($input, $rule);
        if ($validator->passes()) {
            try {
                $check_token = GlApiTokens::where('vchr_token', $request->token)->first();
                if (!empty($check_token)) {
                    $userId = $check_token->fk_int_user_id;
                    $vendor_id = User::getVendorIdApi($userId);
                    $tokenId = $check_token->pk_int_token_id;
                    $block = new Common();
                    $blockSubscribedUsers = $block->checkUserSubscription($userId, Variables::SERVICE_GLP);
                    if ($blockSubscribedUsers) {
                        $campaign = Campaign::where('fk_int_user_id', $userId)->where('int_promo_campaign_status', Campaign::ACTIVATE)->first();
                        if ($campaign) {
                            $campaignId = $campaign->pk_int_promo_campaign_id;
                            $campaignDiscount = $campaign->vchr_promo_campaign_discount;
                            $rede_exist = GlPromocode::where('fk_int_token_id', $tokenId)->where('int_status', GlPromocode:: DEACTIVATE)->where('fk_int_promo_campaign', $campaignId)->where('vchr_gl_promo_missedcall_number', $mobileno)->first();
                            if ($rede_exist) {
                                return response()->json(['message' => 'Already Redemeed', 'status' => 'fail']);
                            } else {
                                Enquiry::getCRMUsers(EnquiryType::GLPROMO, $mobileno, $userId);
                                $missedcallno_exist = GlPromocode::where('vchr_gl_promo_missedcall_number', $mobileno)->where('fk_int_token_id', $tokenId)->where('fk_int_promo_campaign', $campaignId)->first();
                                if ($missedcallno_exist) {
                                    $promocodes = $missedcallno_exist->vchr_promocode;
                                    //Send Sms
                                    $user = User::find($userId);
                                    $username = $user->vchr_user_name;
                                    $message = 'Hey,Here is your Unique Promo Code ' . $promocodes . '.Redeem this code at our ' . $username . ' to avail ' . $campaignDiscount . ' off on Purchases.
                                                T&C Apply.
                                                Thank you';
                                    $defaultSenderId = SingleSMS:: getSenderid($vendor_id, EnquiryType::GLPROMO);
                                    $defaultRoute = SingleSMS:: getRoute($vendor_id, EnquiryType::GLPROMO);
                                    $apitemplate = CustomClass::userDefaultApiTemplate($vendor_id);
                                    if (!empty($apitemplate)) {
                                        $input['template_id'] = $apitemplate->pk_int_api_template_id;
                                        $input['template'] = $apitemplate->text_api_template_description;
                                        $response = CustomClass::urlReplacement($input['template'], $message, $mobileno, $defaultSenderId);
                                    } else {
                                        $send = new SingleSMS();
                                        $smsPanel = $send->getSmsPanel($defaultRoute, $vendor_id);
                                        $balanceSms = $send->getSMSBalance($vendor_id, $defaultRoute, $smsPanel);
                                        $messageType = 0;
                                        if ($balanceSms > 0) {
                                            if ($smsPanel->title == SmsPanel::ALERTBOX) {
                                                $routeCode = $send->getRouteDetails($defaultRoute)->int_sms_route_code;
                                                $smsUrl = $send->getSmsUrl($defaultSenderId, $mobileno, $message, $defaultRoute, $routeCode, $vendor_id, '0');
                                                $smsCount = $send->getInputSMSCount($message, '0');
                                                $templateId = $send->getSmsTemplateId($defaultRoute,$vendor_id);
                                                $routeName = $send->getRouteDetails($defaultRoute)->vchr_sms_route;
                                                $insertSms = $send->storeSmsData($vendor_id, $templateId, $mobileno, $defaultSenderId, '0', $routeName, $message, EnquiryType::GLPROMO, $routeCode, $defaultRoute, '1', $smsCount);
                                                $response = $send->sendSms($defaultSenderId, $mobileno, $message, $routeCode, $balanceSms, $templateId, $defaultRoute, '0', $vendor_id, $smsUrl);

                                                $response = $send->getResponse($insertSms, $response, $templateId, $defaultRoute,$vendor_id, $smsCount);
                                            } else {
                                                $routeCode = $send->getRouteDetails($defaultRoute)->short_code;
                                                $smsUrl = $send->getSmsMerabtUrl($defaultSenderId, $mobileno, $message, $defaultRoute, $routeCode, $vendor_id, $messageType);
                                                $smsCount = $send->getInputSMSCount($message, $messageType);
                                                $templateId = $send->getSmsTemplateId($defaultRoute, $vendor_id);
                                                $routeName = $send->getRouteDetails($defaultRoute)->vchr_sms_route;
                                                $insertSms = $send->storeSmsData($vendor_id, $templateId, $mobileno, $defaultSenderId, $messageType, $routeName, $message, EnquiryType::GLPROMO, $routeCode, $defaultRoute, '1', $smsCount);
                                                $response = $send->sendSms($defaultSenderId, $mobileno, $message, $routeCode, $balanceSms, $templateId, $defaultRoute, $messageType,$vendor_id, $smsUrl);
                                                $response = $send->getMetabtResponse($insertSms, $response, $templateId, $defaultRoute, $vendor_id, $smsCount);
                                            }
                                        }
                                    }
                                    //
                                    $userObject = User::getUserDetails($vendor_id);
                                    $userAdminObject = User::getSingleAdminDetails();
                                    //Notifications
                                    $notifications = new Notifications();
                                    $from = env('MAIL_FROM_ADDRESS');
                                    $to = $userObject->email;
                                    $subject = "GL Promo Notifications";
                                    $name = $userObject->vchr_user_name;
                                    $logo = $userAdminObject->vchr_logo;
                                    $attachment = "";
                                    $telegramId = $userObject->telegram_id;
                                    $mobileNumber = $userObject->vchr_user_mobile;
                                    $defaultSenderIdAdmin = SingleSMS:: getSenderid($userAdminObject->pk_int_user_id, '');
                                    $defaultRouteAdmin = SingleSMS:: getRoute($userAdminObject->pk_int_user_id, '');
                                    $content1 = "New Leads via GL Promo- " . $mobileno . "";
                                    $content2 = "You have new leads via GL Promo-" . $input['mobileno'] . "";
                                    $dataSend['message'] = $content1;
                                    $dataSend['user_id'] =  $userId ?? $vendor_id;
                                    $dataSend['page'] = 'gl_promo';
                                    $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId,$vendor_id, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin,$dataSend);
                                    //-----------------
                                    return response()->json(['message' => 'Promo Code Generated', 'promocode' => $promocodes, 'status' => 'success']);

                                } else {

                                    $user = User::find($userId);
                                    $username = $user->vchr_user_name;
                                    $data['email'] = $user->email;
                                    $fusername = ucwords(strtolower(substr($username, 0, 1)));
                                    $lusername = ucwords(substr($username, -1));
                                    $random = rand(100, 999);

                                    $promo = new GlPromocode;
                                    $promo->fk_int_token_id = $tokenId;
                                    $promo->vchr_promocode = 'GL' . $fusername . $lusername . $random;
                                    $promocodes = 'GL' . $fusername . $lusername . $random;
                                    $promo->fk_int_gl_promoid = 0;
                                    $promo->vchr_gl_promo_missedcall_number = $mobileno;
                                    $promo->fk_int_promo_campaign = $campaignId;
                                    $promo->int_status = 1;
                                    $flag = $promo->save();

                                    /**Send to Gdeal Server**/

                                    //$gdeal = $this->savePromocodeDetailsinGdeal($promocodes, $mobileno, $user->vchr_user_mobile, $user->vchr_user_name, $campaign->pk_int_promo_campaign_id, $campaign->vchr_promo_campaign, $campaign->vchr_promo_campaign_discount, $campaign->int_promo_campaign_status);


                                    //Send Sms
                                    $message = 'Hey,Here is your Unique Promo Code ' . $promocodes . '.Redeem this code at our ' . $username . ' to avail ' . $campaignDiscount . ' off on Purchases.
                                                T&C Apply.
                                                Thank you';
                                    $defaultSenderId = SingleSMS:: getSenderid($vendor_id, EnquiryType::GLPROMO);
                                    $defaultRoute = SingleSMS:: getRoute($vendor_id, EnquiryType::GLPROMO);
                                    $apitemplate = CustomClass::userDefaultApiTemplate($vendor_id);
                                    if (!empty($apitemplate)) {
                                        $input['template_id'] = $apitemplate->pk_int_api_template_id;
                                        $input['template'] = $apitemplate->text_api_template_description;
                                        $response = CustomClass::urlReplacement($input['template'], $message, $mobileno, $defaultSenderId);

                                    } else {
                                        $send = new SingleSMS();
                                        $smsPanel = $send->getSmsPanel($defaultRoute, $vendor_id);
                                        $balanceSms = $send->getSMSBalance($vendor_id, $defaultRoute, $smsPanel);
                                        $messageType = 0;
                                        if ($balanceSms > 0) {
                                            if ($smsPanel->title == SmsPanel::ALERTBOX) {
                                                $routeCode = $send->getRouteDetails($defaultRoute)->int_sms_route_code;
                                                $smsUrl = $send->getSmsUrl($defaultSenderId, $mobileno, $message, $defaultRoute, $routeCode, $vendor_id, '0');
                                                $smsCount = $send->getInputSMSCount($message, '0');
                                                $templateId = $send->getSmsTemplateId($defaultRoute, $vendor_id);
                                                $routeName = $send->getRouteDetails($defaultRoute)->vchr_sms_route;
                                                $insertSms = $send->storeSmsData($vendor_id, $templateId, $mobileno, $defaultSenderId, '0', $routeName, $message, EnquiryType::GLPROMO, $routeCode, $defaultRoute, '1', $smsCount);
                                                $response = $send->sendSms($defaultSenderId, $mobileno, $message, $routeCode, $balanceSms, $templateId, $defaultRoute, '0', $vendor_id, $smsUrl);

                                                $response = $send->getResponse($insertSms, $response, $templateId, $defaultRoute, $vendor_id, $smsCount);
                                            } else {
                                                $routeCode = $send->getRouteDetails($defaultRoute)->short_code;
                                                $smsUrl = $send->getSmsMerabtUrl($defaultSenderId, $mobileno, $message, $defaultRoute, $routeCode, $vendor_id, $messageType);
                                                $smsCount = $send->getInputSMSCount($message, $messageType);
                                                $templateId = $send->getSmsTemplateId($defaultRoute, $vendor_id);
                                                $routeName = $send->getRouteDetails($defaultRoute)->vchr_sms_route;
                                                $insertSms = $send->storeSmsData($vendor_id, $templateId, $mobileno, $defaultSenderId, $messageType, $routeName, $message, EnquiryType::GLPROMO, $routeCode, $defaultRoute, '1', $smsCount);
                                                $response = $send->sendSms($defaultSenderId, $mobileno, $message, $routeCode, $balanceSms, $templateId, $defaultRoute, $messageType, $vendor_id, $smsUrl);
                                                $response = $send->getMetabtResponse($insertSms, $response, $templateId, $defaultRoute, $vendor_id, $smsCount);
                                            }
                                        }
                                    }
                                    $userObject = User::getUserDetails($vendor_id);
                                    $userAdminObject = User::getSingleAdminDetails();
                                    //Notifications
                                    $notifications = new Notifications();
                                    $from = env('MAIL_FROM_ADDRESS');
                                    $to = $userObject->email;
                                    $subject = "GL Promo Notifications";
                                    $name = $userObject->vchr_user_name;
                                    $logo = $userAdminObject->vchr_logo;
                                    $attachment = "";
                                    $telegramId = $userObject->telegram_id;
                                    $mobileNumber = $userObject->vchr_user_mobile;
                                    $defaultSenderIdAdmin = SingleSMS:: getSenderid($userAdminObject->pk_int_user_id, '');
                                    $defaultRouteAdmin = SingleSMS:: getRoute($userAdminObject->pk_int_user_id, '');
                                    $content1 = "New Leads via GL Promo- " . $mobileno . "";
                                    $content2 = "You have new leads via GL Promo- " . $mobileno . "";
                                    $dataSend['message'] = $content1;
                                    $dataSend['user_id'] =  $userId ?? $vendor_id;
                                    $dataSend['page'] = 'gl_promo';
                                    $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $vendor_id, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin,$dataSend);
                                    //-----------------
                                    return response()->json(['message' => 'Promo Code Generated', 'promocode' => $promocodes, 'status' => 'success']);
                                }
                            }
                        } else {
                            return response()->json(['message' => 'Please add the campaign', 'status' => 'fail']);
                        }
                    } else {
                        return response()->json(['message' => 'Plan Expired', 'status' => 'fail']);
                    }
                } else {
                    return response()->json(['message' => 'Please check the parameters', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['message' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['message' => $validator->messages(), 'status' => 'fail']);
        }
    }

    /**
     * @param $promocodes
     * @param $mobileno
     * @param $user_user_mobile
     * @param $user_name
     * @param $campaignID
     * @param $promo_campaign
     * @param $campaign_discount
     * @param $promo_campaign_status
     * @return int
     * @throws \GuzzleHttp\Exception\GuzzleException
     * Send Promo code details to Gdeal Server
     * Created on 04 july-2019 at 01.15 PM
     */
    public function savePromocodeDetailsinGdeal($promocodes, $mobileno, $user_user_mobile, $user_name, $campaignID, $promo_campaign, $campaign_discount, $promo_campaign_status)
    {

        $client = new Client();
        $login_url = Variables::GDEAL_DOMAIN . "api/auth/login";
        //$login_url = "http://gdeal.local/api/auth/login";
        $loginRes = $client->request('POST', $login_url, [
            'form_params' => ['mobile' => '8453555000', 'password' => 'admin123']
        ]);
        $loginResponse = json_decode($loginRes->getBody(), true);
        $loginResponse['access_token'];
        //$url = "http://gdeal.local/api/auth/add-promo-code";
        $url = Variables::GDEAL_DOMAIN . "api/auth/add-promo-code";
        $res = $client->request('POST', $url, [
            'headers' => ['Authorization' => 'Bearer' . $loginResponse['access_token'],
                'Accept' => 'application/json'],
            'form_params' => [
                'promocodes' => $promocodes,
                'misscall_number' => $mobileno,
                'business_number' => $user_user_mobile,
                'promo_campaign_id' => $campaignID,
                'promo_campaign' => $promo_campaign,
                'discount' => $campaign_discount,
                'user_name' => $user_name,
                'promo_campaign_status' => $promo_campaign_status
            ]
        ]);
        return $res->getStatusCode();
    }
}
