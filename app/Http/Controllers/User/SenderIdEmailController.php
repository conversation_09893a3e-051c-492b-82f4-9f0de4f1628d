<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use App\BackendModel\EmailDomain;
use App\BackendModel\EmailSenderId;
use DB;
use Carbon\Carbon;
use DataTables;
use Auth;
use App\Common\SendTelegram;
use App\Common\Notifications;
use App\Common\SingleSMS;


class SenderIdEmailController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $domains=EmailDomain::where('vendor_id',User::getVendorId())->where('status',EmailDomain::ACTIVATE)->get();
         return view('backend.user-pages.bulkemail.senderid',compact('domains'));
    }

    public function store(Request $request)
    {
        $input = $request->all();
        if ($input) {
            try
            {
                $email= new EmailSenderId();
                $email->fill($input);
                $email->senderid = $request->senderid.$request->domain;
                $email->vendor_id =User::getVendorId();
                $email->status = EmailSenderId::DEACTIVATE;
                $flag=$email->save();
                if($flag)
                {
                    //Notifications
                    $notifications=new Notifications();
                    $userName=User::getUserDetails(User::getVendorId())->vchr_user_name;
                    $from=env('MAIL_FROM_ADDRESS');
                    $subject="Mail Senderid Approval";
                    $attachment="";
                    $content1=$userName." is waiting for Mail Senderid "."(".$input['senderid'].$request->domain.") Approval";
                    $content2=$userName." is waiting for Mail Senderid "."(".$input['senderid'].$request->domain.")  Approval";

                    foreach(User::getMultipleAdminDetails() as $adminDetail)
                        {
                            $to= $adminDetail->email;
                            $name=$adminDetail->vchr_user_name;
                            $logo=$adminDetail->vchr_logo;
                            $telegramId=$adminDetail->telegram_id;
                            $mobileNumber=$adminDetail->vchr_user_mobile;
                            $defaultSenderIdAdmin=SingleSMS:: getSenderid($adminDetail->pk_int_user_id,'');
                            $defaultRouteAdmin=SingleSMS:: getRoute($adminDetail->pk_int_user_id,'');

                            $notifications->notifications($from,$to,$subject,$name,$content1,$content2,$logo,$attachment,$telegramId,$adminDetail->pk_int_user_id,$mobileNumber,$defaultRouteAdmin,$defaultSenderIdAdmin);
                        }
                    //-----------------
                   return response()->json(['msg'=>'SenderId added.', 'status'=>'success']);
                }
               else
               {
                   return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
               }

            }
            catch(\Exception $e)
            {
                return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

                    //return $e->getMessage();
            }

        } else 
        {
            return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);
        }
    }


    public function getEmailSenderId()
    {
        $domain=EmailSenderId::where('vendor_id',User::getVendorId())->orderby('id','DESC')->get();
        
        foreach ($domain as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($domain)
        ->editColumn('display_name', function ($domain) {
            if ($domain->display_name != null) {
                return $domain->display_name;
            } else {
                return "No Display Name";
            }
        })
        ->addColumn('show', function ($domain) {
                    if ($domain->status == 1) {
                        return '
                                
                                  <button id="delete_designation" testimonial-id="' . $domain->id . '" class="btn btn-sm btn-danger testimonial-delete"> <i class="f-16 fa fa-trash-o"></i></button>

                                    <a class="btn btn-sm btn-success btn-activate designation-act" designation-id="' . $domain->id . '" data-toggle="tooltip" title="Approved"> <i class="f-18 fa fa-check-circle active"></i></a>
                                ';
                    } //If Status is not in active state
                    else {
                        return '
                                <button testimonial-id="' . $domain->id . '" class="btn btn-sm btn-danger testimonial-delete "> <i class="f-16 fa fa-trash-o"></i></button>

                                    <a class="btn btn-sm btn-warning designation-act " designation-id="' . $domain->id . '" data-toggle="tooltip" title="Approval Pending"> <i class="f-18 fa fa-times-circle"></i></a>
                                ';
                    }
                })
                ->rawColumns(['show'])
                ->toJson(true);
    }



    public function destroy($id)
    {
        try 
        {
            $domain = EmailSenderId::find($id);
            if ($domain) {
                EmailSenderId::where('id', $id)->delete();
                return response(['msg' => 'SenderId  is deleted.', 'status' => 'success']);

            }
            else
            {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            }
        }
        catch (\Exception $ex) {
            return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);

        }
    }



     
}
