<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use Auth;
use DB;
use App\BackendModel\SmsTemplates;
use Carbon\Carbon;
use App\Core\CustomClass;
use DataTables;
use App\Common\SendTelegram;

class SmsTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
         return view('backend.user-pages.smstemplate.index');
        
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $input = $request->only(['smstemplatetitle', 'userid','smstemplatebody']);
        $input['fk_int_user_id']=User::getVendorId();
        $input['text_sms_template_description']=$request->smstemplatebody;


         $validator = validator::make($input, SmsTemplates::$rule, SmsTemplates::$message);
        
        if ($validator->passes()) 
        {
           
            try
            {
                $user=User::select('pk_int_user_id','vchr_user_name')->find(User::getVendorId());
                if(!empty($user))
                {
                    DB::beginTransaction();
                    $userDefaultSmsTemplateCount=SmsTemplates::where('fk_int_user_id',User::getVendorId())->where('int_default_sms_template_id',SmsTemplates::DEFAULT)->count();

                    $smstemplate = new SmsTemplates;
                    $smstemplate->vchar_sms_template_title=$input['smstemplatetitle'] ;
                    $smstemplate->fk_int_user_id=$input['fk_int_user_id'] ;
                    $smstemplate->text_sms_template_description=$input['smstemplatebody'] ;
                    $smstemplate->int_sms_template_count=CustomClass::smsLength($input['smstemplatebody']);
                      //return $smstemplate;
                    //first sms template set as default

                    // if($userDefaultSmsTemplateCount>0)
                    // {
                    //     $smstemplate->int_default_sms_template_id=SmsTemplates::UNDEFAULT;
                    // }
                    // else
                    // {
                    //      $smstemplate->int_default_sms_template_id=SmsTemplates::DEFAULT;
                    // }
                    $smstemplate->int_default_sms_template_id=SmsTemplates::UNDEFAULT;
                    $smstemplate->int_sms_template_status=SmsTemplates::DEACTIVATE;
                    $flag=$smstemplate->save();
                    if($flag)
                    {
                        ///Notifications
                        $notifications=new Notifications();
                        $userName=User::getUserDetails(User::getVendorId())->vchr_user_name;
                        $from=env('MAIL_FROM_ADDRESS');
                        $subject="Whatsapp Template Approval";
                        $attachment="";
                        $content1=$userName." is waiting for SMSTemplate Approval";
                        $content2=$userName." is waiting for SMS Template Approval";

                        foreach(User::getMultipleAdminDetails() as $adminDetail)
                            {
                                $to= $adminDetail->email;
                                $name=$adminDetail->vchr_user_name;
                                $logo=$adminDetail->vchr_logo;
                                $telegramId=$adminDetail->telegram_id;
                                $mobileNumber=$adminDetail->vchr_user_mobile;
                                $defaultSenderIdAdmin=SingleSMS:: getSenderid($adminDetail->pk_int_user_id,'');
                                $defaultRouteAdmin=SingleSMS:: getRoute($adminDetail->pk_int_user_id,'');

                                $notifications->notifications($from,$to,$subject,$name,$content1,$content2,$logo,$attachment,$telegramId,$adminDetail->pk_int_user_id,$mobileNumber,$defaultRouteAdmin,$defaultSenderIdAdmin);
                            }
                    //-----------------
                        DB::commit();

                        //trigger email to admin 

                        return response()->json([
                                'status' => 'success',
                                'msg' => 'Sms template id is added,waiting for admin approval'
                               ], 200);

                    }
                    else
                    {
                        DB::rollBack();
                        return response()->json([
                                    'status' => 'error',
                                    'msg' =>'Could not stored to database.',
                            ], 400);
                    }
                }
                else
                {
                    return response()->json([
                                'status' => 'error',
                                'msg' =>'User not found',
                            ], 404);
                }
            }
            catch (\Exception $e) {
            // something went wrong whilst attempting to encode the token
            return response()->json(['status'=>'error','msg' => $e->getMessage()], 500);
            }
            

        }
        else
        {
            return response()->json([
                'status' => 'error',
                'msg' =>$validator->messages(),
            ]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
         $apitemplate=SmsTemplates::where('fk_int_user_id',User::getVendorId())->where('pk_int_sms_template_id',$id)->first();
        if($apitemplate)
        {
            
             
            return response()->json(['msg'=>'data present', 'status'=>'success','data'=>$apitemplate]);
        }
        else
        {
             return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $input = $request->all();
         
         $validator = validator::make($input, SmsTemplates::$updateRule, SmsTemplates::$updateMessage);
        
        if ($validator->passes()) 
        {


            try
            {
                $smstemplate=SmsTemplates::FindOrFail($id);
                if(!empty($smstemplate))
                {

                    DB::beginTransaction();
                    $smstemplate->vchar_sms_template_title=$input['smstemplatetitle'] ;
                    $smstemplate->text_sms_template_description=$input['smstemplatebody'] ;
                    $smstemplate->int_sms_template_status=SmsTemplates::DEACTIVATE;
                    $smstemplate->int_sms_template_count=CustomClass::smsLength($input['smstemplatebody']);


                    $flag=$smstemplate->save();
                    if($flag)
                    {
                        ///Notifications
                        $notifications=new Notifications();
                        $userName=User::getUserDetails(User::getVendorId())->vchr_user_name;
                        $from=env('MAIL_FROM_ADDRESS');
                        $subject="Whatsapp Template Approval";
                        $attachment="";
                        $content1=$userName." is waiting for SMSTemplate Approval";
                        $content2=$userName." is waiting for SMS Template Approval";

                        foreach(User::getMultipleAdminDetails() as $adminDetail)
                            {
                                $to= $adminDetail->email;
                                $name=$adminDetail->vchr_user_name;
                                $logo=$adminDetail->vchr_logo;
                                $telegramId=$adminDetail->telegram_id;
                                $mobileNumber=$adminDetail->vchr_user_mobile;
                                $defaultSenderIdAdmin=SingleSMS:: getSenderid($adminDetail->pk_int_user_id,'');
                                $defaultRouteAdmin=SingleSMS:: getRoute($adminDetail->pk_int_user_id,'');

                                $notifications->notifications($from,$to,$subject,$name,$content1,$content2,$logo,$attachment,$telegramId,$adminDetail->pk_int_user_id,$mobileNumber,$defaultRouteAdmin,$defaultSenderIdAdmin);
                            }
                    //-----------------
                        DB::commit();

                        //trigger mail to admin for resubmission of smstemplate id
                        return response()->json([
                                'status' => 'success',
                                'msg' => 'Sms template is updated,waiting for admin approval'
                               ], 200);

                    }
                    else
                    {
                        DB::rollBack();
                        return response()->json([
                                    'status' => 'error',
                                    'msg' =>'Could not stored to database.',
                            ], 400);
                    }
                }
                else
                {
                    return response()->json([
                                'status' => 'error',
                                'msg' =>'User not found',
                            ], 404);
                }
            }
            catch (\Exception $e) {
            // something went wrong whilst attempting to encode the token
            return response()->json(['status'=>'error','msg' => $e->getMessage()], 500);
            }
           

        }
        else
        {
            return response()->json([
                'status' => 'error',
                'msg' =>$validator->messages(),
            ], 400);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try
        {    
            DB::beginTransaction();

           $flag=false;

            $smstemplate = SmsTemplates::findOrfail($id);
            if($smstemplate->int_default_sms_template_id == 1)
            {
                 
                 
                 // get previous sms tempalte id
                 $previous = SmsTemplates::where('pk_int_sms_template_id', '<', $id)->where('int_default_sms_template_id',SmsTemplates::UNDEFAULT)->where('int_sms_template_status',SmsTemplates::ACTIVATE)->max('pk_int_sms_template_id');

                // get next sms tempalte id
                 $next = SmsTemplates::where('pk_int_sms_template_id', '>', $id)->where('int_default_sms_template_id',SmsTemplates::UNDEFAULT)->where('int_sms_template_status',SmsTemplates::ACTIVATE)->min('pk_int_sms_template_id');
                 
                 return "$next & $previous";
                 if(isset($previous))
                 {
                    $defaultSmsTemplate=SmsTemplates::find($previous);
                    $defaultSmsTemplate->int_default_sms_template_id=SmsTemplates::DEFAULT;
                    $flag1=$defaultSmsTemplate->save();
                    if($flag1)
                     {
                         $flag=true;
                     }
                 }
                 else if(isset($next))
                 {
                    $defaultSmsTemplate=SmsTemplates::find($next);
                    $defaultSmsTemplate->int_default_sms_template_id=SmsTemplates::DEFAULT;
                    $flag1=$defaultSmsTemplate->save();
                    if($flag1)
                     {
                         $flag=true;
                     }
                 }
                 else
                 {
                    $flag=true;
                 }
                 
            }
            else
            {
               $flag=true; 
            }
               
            if($smstemplate->delete()) 
            {
                if($flag)
                {
                    DB::commit();
                     return response([
                     'status' => 'success',
                     'message' => 'Deleted successfully'
                    ], 200);
                }
                else
                {
                    DB::rollBack();
                        return response([
                            'status' => 'error',
                            'message' =>'Could not delete.',
                        ], 400);
                }
            }
            else{
                 DB::rollBack();
                        return response([
                            'status' => 'error',
                            'message' =>'Could not delete.',
                        ], 400);

            } 
        }
        catch (\Exception $e) {
                DB::rollBack();
            // something went wrong whilst attempting to encode the token
            return response()->json(['status'=>'error','message'=> $e->getMessage()], 500);
        }
    }

    public function getInfo()
    {
        $template = DB::table('tbl_users')
        ->select('vchr_user_name','pk_int_sms_template_id','vchar_sms_template_title','text_sms_template_description','int_default_sms_template_id','int_sms_template_status')
        ->join('tbl_sms_template', 'tbl_users.pk_int_user_id', '=', 'tbl_sms_template.fk_int_user_id')->orderby('pk_int_sms_template_id','DESC')
        ->whereNull('tbl_sms_template.deleted_at')
        ->where('tbl_users.pk_int_user_id',User::getVendorId())
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

        if ($template->int_sms_template_status == 1) {
                        return '
                                <button smstemplate-id="' . $template->pk_int_sms_template_id . '" class="btn btn-sm btn-primary mg-b-10 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal"> <i class="fa fa-edit mg-r-5"></i></button>

                                    <button id="delete_plan" smstemplate-id="' . $template->pk_int_sms_template_id . '" class="btn btn-sm btn-danger smstemplate-delete px-3"> <i class="fa fa-trash-o mg-r-5"></i></button>
                                    <button class="btn btn-sm btn-success btn-activate  px-3 "   smstemplate-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="SMS template  is active"> <i class="fa fa-check"></i></button>
                                    
                                    ';
                    } //If Status is not in active state
                    else {
                        return '
                                 <button smstemplate-id="' . $template->pk_int_sms_template_id . '" class="btn btn-sm btn-primary mg-b-10 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal"> <i class="fa fa-edit mg-r-5"></i></button>

                                    <button id="delete_plan" smstemplate-id="' . $template->pk_int_sms_template_id . '" class="btn btn-sm btn-danger smstemplate-delete px-3"> <i class="fa fa-trash-o mg-r-5"></i></button>
                                    <button class="btn btn-sm btn-warning  px-3" smstemplate-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Admin approval pending"> <i class="fa fa-times"></i></button>
                                    
                                    ';
                    }
                })->addColumn('default', function ($template) {

        if ($template->int_default_sms_template_id == 1) {
                        return '
                                 <div class="btn-group btn-collection btn-icon-group" title="Default Api template">
                                    <button id="" smstemplate-id="' . $template->pk_int_sms_template_id . '" class="btn btn-outline-success  px-3" style="color:green" >DEFAULT</button>
                                    <button class="btn btn-outline-success ks-no-text">
                                            <i class="fa fa-check"></i>
                                        </button>
                                     
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                     <button class="btn btn-outline-danger ks-no-text default-smstemplate" smstemplate-id="' . $template->pk_int_sms_template_id . '" title="Click to activate default SMStemplate">
                                            <i class="fa fa-times"></i>
                                        </button>
                                   
                                </div>';
                    }
                })
                ->rawColumns(['show','default'])
                ->toJson(true);
    }

    public function defaulSmsTemplateId($id)
     {

        try
        { 
            DB::beginTransaction();

            $sender=SmsTemplates::find($id);
            $isActive=$sender->int_sms_template_status;

            if($isActive!=SmsTemplates::ACTIVATE)
            {
                 return response()->json([
                                'status' => 'error',
                                'msg' =>'Could not set as default before admin approval',
                            ]);
            }
            
             

            $flag=false;
            $defaultSender = SmsTemplates::where('int_default_sms_template_id',SmsTemplates::DEFAULT)->first();
            if(!empty($defaultSender))
            {
                $reserDefault=SmsTemplates::find($defaultSender->pk_int_sms_template_id);

                $reserDefault->int_default_sms_template_id=SmsTemplates::UNDEFAULT;
                $flag1=$reserDefault->save();
                if ($flag1) {
                    $flag=true;
                }
                if($flag)
                { 
                    
                    $sender->int_default_sms_template_id=SmsTemplates::DEFAULT;
                     $flag2=$sender->save();
                     if($flag2)
                     {
                         DB::commit();
                         return response()->json([
                         'status' => 'success',
                         'msg' => 'set as default successfully'
                        ]);
                     }
                     else{
                     DB::rollBack();
                            return response()->json([
                                'status' => 'error',
                                'msg' =>'Could not set as default',
                            ]);

                     } 
                }
                else{
                     DB::rollBack();
                            return response([
                                'status' => 'error',
                                'message' =>'Could not set as default',
                            ], 400);

                } 

            }
            else
            {
                  $smstemplate=SmsTemplates::find($id);
                    $smstemplate->int_default_sms_template_id=SmsTemplates::DEFAULT;
                     $flag2=$smstemplate->save();
                     if($flag2)
                     {
                         DB::commit();
                         return response()->json([
                         'status' => 'success',
                         'msg' => 'set as default successfully'
                        ]);
                     }
                     else{
                     DB::rollBack();
                            return response()->json([
                                'status' => 'error',
                                'msg' =>'Could not set as default',
                            ]);

                     } 
            }
        }
        catch (\Exception $e) {
                DB::rollBack();
            // something went wrong whilst attempting to encode the token
            return response()->json(['status'=>'error','message'=> $e->getMessage()], 500);
        }
     }
}
