<?php

namespace App\Http\Controllers\User;

use App\AgentDepartment;
use App\FrontendModel\LeadAdditionalField;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\GlApiTokens;
use App\BackendModel\Enquiry;
use App\User;
use App\CustomField;
use App\CustomFieldValue;
use Auth;
use Carbon\Carbon;
use App\Common\Notifications;
use App\Common\SingleSMS;
use App\BackendModel\Country;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\VirtualNumber;
use App\Common\Common;
use App\DealStage;
use App\Department;
use App\Events\LeadAssigned;
use App\Events\TaskAssigned;
use App\Task;
use App\WebHook;
use App\Events\CreateFollowup;
use App\Traits\DealTrait;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class WebsiteContactController extends Controller
{
    use DealTrait;

    public function websiteContactApi(Request $request)
    {
        $input = $request->all();
        Log::info('Contact via GL connect api received', [
            'request' => $request->all()
        ]);

        $rule = [
            'token' => 'required',
            'name' => 'required',
            'email' => 'nullable|email',
            'mobileno' => 'required'
        ];
        try {
            $request = $this->checkMobile($request);
        } catch (\Exception $e) {
            Log::log($e->getMessage());
        }

        if ($request->token && $request->type) {
            $check_token = GlApiTokens::where('vchr_token', $request->token)->first();
            if (!empty($check_token)) {
                $rule = CustomField::validations($check_token->fk_int_user_id, $request->type, $rule);
            }
        }

        $validator = Validator::make($input, $rule);
        if ($validator->passes()) {
            if (strlen($request->mobileno) < 8 || strlen($request->mobileno) > 14 || !ctype_digit($request->mobileno))
                return response()->json(['message' => "The mobileno must be valid and between 8 and 14 digits.", 'status' => 'fail']);
            try {
                $check_token = GlApiTokens::where('vchr_token', $request->token)->first();
                $vendor_id = User::getVendorIdApi($check_token->fk_int_user_id);
                if (!empty($check_token)) {
                    if (!$request->has('status') && !$request->filled('status')) {
                        $request->merge([
                            'status' => 'New'
                        ]);
                    }
                    $exist = Enquiry::select('pk_int_enquiry_id', 'fk_int_user_id', 'vchr_customer_mobile', 'mobile_no')->where('fk_int_user_id', $vendor_id)
                        ->where(function ($q) use ($request) {
                            $q->where('vchr_customer_mobile', $request->countrycode . $request->mobileno)
                                ->orWhere('mobile_no', $request->mobileno);
                        })
                        ->first();

                    $enquiry_id = Enquiry::getCRMWebsiteUsers($request->source ?? 'Website Enquiry', $request->mobileno, $check_token->fk_int_user_id, $request->name, $request->email, $request->feedback, $request->countrycode, $request->company_name, $request);

                    //Notifications-------------
                    if (!$exist || $request->notification_all) {
                        $userObject = User::getUserDetails($vendor_id);
                        $userAdminObject = User::getSingleAdminDetails();
                        $sub = $request->source ?? 'Website';
                        $lead_enquiry = $exist ? 'enquiry' : 'lead';
                        $notifications = new Notifications();
                        $from = env('MAIL_FROM_ADDRESS');
                        $to = $userObject->email;
                        $subject = $sub . " Enquiry Notifications";
                        $name = $userObject->vchr_user_name;
                        $logo = $userAdminObject->vchr_logo;
                        $attachment = "";
                        $telegramId = $userObject->telegram_id;
                        $mobileNumber = $userObject->vchr_user_mobile;
                        $defaultSenderIdAdmin = SingleSMS:: getSenderid($userAdminObject->pk_int_user_id, '');
                        $defaultRouteAdmin = SingleSMS:: getRoute($userAdminObject->pk_int_user_id, '');
                        $content1 = [
                            'text' => "Hey , You have got a new " . $lead_enquiry . " from your " . strtolower($sub) . ".
    Customer : " . $request->name . "
    Number : +" . $request->countrycode . $request->mobileno . "
    Date and Time : " . Carbon::now()->format('d M Y h:i A'),
                            'buttons' => [[
                                'text' => '🔆 Click to Chat in WhatsApp',
                                'url' => 'https://wa.me/' . str_replace("+", "", $request->countrycode . $request->mobileno)
                            ]]
                        ];
                        $content2 = "You have new " . $lead_enquiry . " via " . $sub . " Enquiry- Requested by : " . $request->name . " , " . $request->countrycode . "-" . $request->mobileno . " , Message : " . $request->feedback . ',Email :  ' . $request->email . ' , Company : ' . $request->company_name;
                        $dataSend['message'] = $content1;
                        $dataSend['user_id'] = $check_token->fk_int_user_id ?? $vendor_id;
                        $dataSend['page'] = 'api_enquiry';
                        $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $vendor_id, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin, $dataSend);
                    }    //---------------------------------------------------

                    if ($exist) {
                        return response()->json(['message' => 'Lead already exists', 'status' => 'fail']);
                    }
                    return response()->json(['message' => 'Lead Added Successfully', 'status' => 'success']);
                } else {
                    return response()->json(['message' => 'Please check the parameters', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['message' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['message' => $validator->messages(), 'status' => 'fail']);
        }
    }

    public function viewfile(Request $request)
    {
        $custom_field_value = CustomFieldValue::where('field_value', $request->file_name)->first();
        if ($custom_field_value) {
            $field = CustomField::where('id', $custom_field_value->field_id)->first();
            if ($field && Auth::user()->pk_int_user_id == $field->user_id)
                return response()->file(storage_path('app/' . $request->file_name));
            else
                abort(404);
        } else
            abort(404);
    }

    public function showApi()
    {
        $additional_fields = LeadAdditionalField::query()
            ->select('id', 'field_name', 'input_type', 'values')
            ->where('vendor_id', User::getVendorId())
            ->get();
        return view('backend.user-pages.crm.contacts-api', compact('additional_fields'));
    }

    /** Api creation for log note */

    public function noteCreationApi(Request $request)
    {
        Log::info('Note creation api request received', [
            'request' => $request->all()
        ]);

        $input = $request->all();

        $validate_fields = [
            'token' => 'required',
            'note' => 'required|max:2000',
        ];

        $validate_messages = [
            'token.required' => 'Token Required',
            'note.required' => 'Note Required',
        ];
        $validator = Validator::make($request->all(), $validate_fields, $validate_messages);

        if ($validator->passes()) {
            try {
                $check_token = GlApiTokens::where('vchr_token', $request->token)->first();
                $vendor_id = User::getVendorIdApi($check_token->fk_int_user_id);

                if (!empty($check_token)) {
                    $exist = Enquiry::where('fk_int_user_id', $vendor_id)
                        ->where(function ($q) use ($request) {
                            $q->where('vchr_customer_mobile', $request->countrycode . $request->mobile_no)
                                ->orWhere('mobile_no', $request->mobile_no);
                        })
                        ->first();
                    if (!$exist) {
                        $exist = Enquiry::where('fk_int_user_id', $vendor_id)
                            ->where('vchr_customer_email', 'LIKE', $request->email)->first();
                    }

                    if ($exist) {
                        try {
                            event(new CreateFollowup(request('note'), EnquiryFollowup::TYPE_NOTE, $exist->pk_int_enquiry_id, $check_token->fk_int_user_id));
                        } catch (\Exception $e) {
                            Log::info($e->getMessage());
                        }

                        return response()->json(['message' => 'Note Added Successfully', 'status' => 'success']);
                    } else {
                        return response()->json(['message' => 'Enquiry Not Found', 'status' => 'success']);
                    }
                } else {
                    return response()->json(['message' => 'Please check the parameters', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['message' => $e->getMessage(), 'status' => 'fail']);
            }
        }
    }

    // Api creation for task
    public function taskCreationApi(Request $request)
    {
        $validate_fields = [
            'token' => 'required',
            'name' => 'required',
            'description' => 'max:2000',
        ];

        $validate_messages = [
            'token.required' => 'Token Required',
            'name.required' => ' name Required',
        ];

        $validator = Validator::make($request->all(), $validate_fields, $validate_messages);

        if ($validator->passes()) {
            try {
                $check_token = GlApiTokens::where('vchr_token', $request->token)->first();
                $vendor_id = User::getVendorIdApi($check_token->fk_int_user_id);
                if (!empty($check_token)) {
                    $exist = Enquiry::where('fk_int_user_id', $vendor_id)
                        ->where(function ($q) use ($request) {
                            $q->where('vchr_customer_mobile', $request->countrycode . $request->mobile_no)
                                ->orWhere('mobile_no', $request->mobile_no);
                        })
                        ->first();
                    if (!$exist) {
                        $exist = Enquiry::where('fk_int_user_id', $vendor_id)
                            ->where('vchr_customer_email', 'LIKE', $request->email)->first();
                    }

                    if ($request->assigned_to) {
                        $stf = User::where('vchr_user_name', 'LIKE', '%' . $request->assigned_to . '%')
                            ->where('parent_user_id', $vendor_id)
                            ->first();
                        if ($stf) {
                            $staff_id = $stf->pk_int_user_id;
                        } else {
                            $staff_id = $check_token->fk_int_user_id;
                        }
                    } elseif ($exist) {
                        $staff_id = $exist->staff_id;
                    } else {
                        $staff_id = $check_token->fk_int_user_id;
                    }

                    $input = [
                        'name' => $request->name,
                        'description' => $request->description,
                        'scheduled_date' => $request->scheduled_date ?? now()->addMinutes(30)->toDateTimeString(),
                        'task_category_id' => $request->task_category_id ?? 1,
                        'assigned_to' => $staff_id,
                        'assigned_by' => $check_token->fk_int_user_id,
                        'vendor_id' => $vendor_id,
                        'enquiry_id' => $exist ? $exist->pk_int_enquiry_id : null,
                        'comment' => $request->comment ?? '',
                        'status' => 0,
                    ];
                    if ($request->task_category_id == 2) {
                        $existing_task = null;

                        if ($vendor_id != 1880)
                            $existing_task = Task::callTaskClose($exist ? $exist->pk_int_enquiry_id : null, $vendor_id, $check_token->fk_int_user_id);

                        if ($existing_task == null) {
                            try {
                                $task = Task::create($input);
                                event(new TaskAssigned($task));
                            } catch (\Exception $e) {
                                return response()->json(['message' => $e->getMessage(), 'status' => 'fail']);
                            }

                        } else {
                            return response()->json(['message' => 'Call Task Already Exists', 'status' => 'fail']);
                        }
                    } else {
                        $task = Task::create($input);
                        event(new TaskAssigned($task));
                    }

                    return response()->json(['message' => 'Task Added Successfully', 'status' => 'success']);
                } else {
                    return response()->json(['message' => 'Please check the parameters', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['message' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['message' => $validator->errors(), 'status' => 'fail']);
        }
    }

    // Api lead status change
    public function statusChangeApi(Request $request)
    {
        $validate_fields = [
            'token' => 'required',
            'status' => 'required|max:2000',
            'mobile_no' => 'required|numeric',
        ];

        $validate_messages = [
            'token.required' => 'Token Required',
            'status.required' => 'Status Required',
            'mobile_no.required' => 'Mobile number Required',
        ];

        $validator = Validator::make($request->all(), $validate_fields, $validate_messages);

        if ($validator->passes()) {
            try {
                $check_token = GlApiTokens::where('vchr_token', $request->token)->first();
                $vendor_id = User::getVendorIdApi($check_token->fk_int_user_id);
                if (!empty($check_token)) {
                    $exist = Enquiry::where([
                        'fk_int_user_id' => $vendor_id,
                        'vchr_customer_mobile' => $request->countrycode . $request->mobile_no
                    ])->first();

                    if (!$exist) {
                        $exist = Enquiry::where('fk_int_user_id', $vendor_id)
                            ->where('vchr_customer_email', 'LIKE', "%" . $request->email . "%")->first();
                    }

                    $search = mb_strtolower(str_replace(' ', '', request()->input('status')));
                    if ($exist) {
                        $status = FeedbackStatus::whereRaw('LOWER(REPLACE(vchr_status," ","")) LIKE ? ', ['%' . $search . '%'])
                            ->where('fk_int_user_id', $vendor_id)
                            ->pluck('pk_int_feedback_status_id')->first();
                        if ($status) {
                            $old_status_id = $exist->feedback_status;
                            $exist->feedback_status = $status;
                            $exist->save();
                            Enquiry::statusChangeFunction($exist);
                            ($this)->statusChangeAutomation($exist, $request);

                            $enquiryfollowup = new EnquiryFollowup();
                            $enquiryfollowup->note = $status;
                            $enquiryfollowup->log_type = EnquiryFollowup::TYPE_STATUS;
                            $enquiryfollowup->enquiry_id = $exist->pk_int_enquiry_id;
                            $enquiryfollowup->created_by = $check_token->fk_int_user_id;
                            $enquiryfollowup->old_status_id = $old_status_id;
                            $enquiryfollowup->save();

                            return response()->json(['message' => 'Status change Successfully', 'status' => 'success']);
                        } else {
                            return response()->json(['message' => 'Invalid status', 'status' => 'fail']);
                        }

                    } else {
                        return response()->json(['message' => 'Enquiry Not Found', 'status' => 'fail']);
                    }
                } else {
                    return response()->json(['message' => 'Please check the parameters', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['message' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['message' => $validator->errors(), 'status' => 'fail']);
        }
    }

    // function for status change automation
    public function statusChangeAutomation($enquiry, $request)
    {
        $commonObj = new Common();
        /** __________AUTOMATION_API_________**/
        $rule_api = $commonObj->getRuleStatusChange($enquiry->fk_int_user_id, 'status_change', 'api', $request->status);
        if ($rule_api && $rule_api->api != NULL) {
            $status = FeedbackStatus::where('pk_int_feedback_status_id', $request->status)
                ->select('vchr_status')
                ->first();
            if ($status) {
                $lead_status = $status->vchr_status;
            } else {
                $lead_status = "New Status";
            }
            $post_data = [
                'customer_name' => $enquiry->vchr_customer_name,
                'email' => $enquiry->vchr_customer_email,
                'status' => $lead_status,
                'phone' => $enquiry->vchr_customer_mobile,
                'mobile' => $enquiry->mobile_no,
                'flag' => "status_change",
            ];
            try {
                $commonObj->postToWebHook($rule_api->api, $post_data);
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }
        }
        /** __________AUTOMATION__API_________**/
        /** __________AUTOMATION_WEBHOOK_________**/
        $automation_rule = $commonObj->getRule($enquiry->fk_int_user_id, 'status_change', 'webhook', '');
        if (count($automation_rule) > 0) {
            foreach ($automation_rule as $w_hook) {
                if ($w_hook->webhook_id != NULL && $w_hook->feedback_status_id == $request->status) {
                    $webHook = WebHook::select('url')->where('id', $w_hook->webhook_id)->first();
                    if ($webHook) {
                        try {
                            $commonObj->postToWebHook($webHook->url, $post_data);
                        } catch (\Exception $e) {
                            Log::info($e->getMessage());
                        }
                    }
                }
            }
        }

        /** __________AUTOMATION_WEBHOOK_________**/


        /**--TASK---------------------------------------------**/
        $automation_rule_task = $commonObj->getRuleStatusChange($enquiry->fk_int_user_id, 'status_change', 'task', $request->status);
        if ($automation_rule_task) {
            $vendorId = User::getVendorId();
            $input = [
                'name' => $automation_rule_task->task_title,
                'description' => $automation_rule_task->task_description,
                'scheduled_date' => Carbon::tomorrow(),
                'task_category_id' => $automation_rule_task->task_category_id,
                'assigned_to' => $automation_rule_task->task_assigned_to,
                'assigned_by' => $vendorId,
                'vendor_id' => $vendorId,
                'enquiry_id' => $enquiry->pk_int_enquiry_id,
                'status' => 0,
            ];
            Task::create($input);
        }
        /**--TASK---------------------------------------------**/
    }

    // FUnction for asign agent vise deprtment
    public function assignAgentViseDepartment($token)
    {

        $check_token = GlApiTokens::where('vchr_token', $token)->first();
        $vendor_id = User::getVendorIdApi($check_token->fk_int_user_id);
        if (!empty($check_token)) {
            if (request()->has('SourceNumber') && strlen(request()->SourceNumber) == 11 && substr(request()->SourceNumber, 0, 1) == "0")
                request()->merge([
                    'SourceNumber' => '91' . substr(request()->SourceNumber, 1, 10)
                ]);

            if (request()->has('SourceNumber') && strlen(request()->SourceNumber) == 10)
                request()->merge([
                    'SourceNumber' => '91' . request()->SourceNumber
                ]);

            $exist = Enquiry::where([
                'fk_int_user_id' => $vendor_id,
                'vchr_customer_mobile' => request()->SourceNumber
            ])->first();

            if ($exist) {
                $department = Department::where('vendor_id', $vendor_id)
                    ->whereRaw('UPPER( departments.name ) LIKE ?', ['%' . strtoupper(request()->department) . '%'])
                    ->first();

                if ($department) {
                    $count = AgentDepartment::where('department_id', $department->id)->min('count');
                    $staff = AgentDepartment::where('department_id', $department->id)->where('count', $count)->first();
                    if (!$exist->staff_id) {
                        if ($staff) {
                            $staff->count = $staff->count + 1;
                            $staff->save();

                            $exist->staff_id = $staff->agent_id;
                            $exist->save();
                        }

                        event(new LeadAssigned($exist->pk_int_enquiry_id, $exist->created_by));

                        return response()->json(['message' => "Lead assigned successful", 'status' => 'success']);
                    } else {
                        return response()->json(['message' => "Staff already assigned", 'status' => 'fail']);
                    }
                } else {
                    return response()->json(['message' => "Invalid department", 'status' => 'fail']);
                }
            } else {
                return response()->json(['message' => "Invalid lead", 'status' => 'fail']);
            }
        }
    }

    /** API for deal creation
     *
     * */
    public function dealCreationApi(Request $request)
    {
        $input = $request->all();
        $rule = [
            'token' => 'required',
            'mobile' => 'required|numeric',
            'status' => 'required'
        ];

        $validator = Validator::make($input, $rule);
        if ($validator->passes()) {
            if (strlen($request->mobile) < 8 || strlen($request->mobile) > 14)
                return response()->json(['message' => "The mobile no must be between 8 and 14 digits.", 'status' => 'fail']);
            try {
                $check_token = GlApiTokens::where('vchr_token', $request->token)->first();

                if (!empty($check_token)) {
                    $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                    if ($request->mobile && strpos($request->mobile, "+") !== false) {
                        $ccode = "91";
                        $countries = Country::orderBy('code', 'ASC')->get();
                        foreach ($countries as $country) {
                            if (strpos($request->mobile, "+" . $country->code) !== false) {
                                $ccode = $country->code;
                                break;
                            }
                        }
                        $request->merge([
                            'countrycode' => (request('country_code')) ?? $ccode,
                            'mobile' => str_replace("+" . $ccode, "", $request->mobile)
                        ]);
                    }
                    $exist = Enquiry::where('fk_int_user_id', $vendorId)
                        ->where('vchr_customer_mobile', $request->countrycode . $request->mobile)
                        ->first();

                    $status = DealStage::where('fk_int_user_id', $vendorId)
                        ->where('deal_stage_name', $request->status)
                        ->first();

                    if (!$exist) {
                        return response()->json(['message' => "Lead does not exist", 'status' => 'fail']);
                    } else {
                        if ($status) {
                            $request->merge([
                                'deal_status' => $status->pk_int_deal_stage_id,
                            ]);
                        } else {
                            return response()->json(['message' => "Deal status does not exist", 'status' => 'fail']);
                        }

                        $deal_id = $this->dealCreationService($request, $exist, $vendorId);
                    }

                    return response()->json(['message' => getDealName(true) . ' Added Successfully', 'status' => 'success']);
                } else {
                    return response()->json(['message' => 'Invalid user tokern', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['message' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['message' => $validator->messages(), 'status' => 'fail']);
        }
    }

    public function leadUpdate()
    {
        $input = request()->all();
        $rule = [
            'token' => 'required',
            'mobile' => 'required'
        ];

        $validator = Validator::make($input, $rule);
        if ($validator->passes()) {
            $check_token = GlApiTokens::where('vchr_token', request('token'))->first();

            if (!empty($check_token)) {
                $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                $exist = Enquiry::where('fk_int_user_id', $vendorId)
                    ->where(function ($q) {
                        $q->where('mobile_no', request('mobile'))
                            ->orWhere('vchr_customer_mobile', request('mobile'));
                    })
                    ->first();
                if ($exist) {
                    if (request('staff_name')) {
                        $user = User::where('parent_user_id', $vendorId)
                            ->where(function ($q) {
                                $q->where('mobile', request('staff_name'))
                                    ->orWhere('vchr_user_mobile', request('staff_name'));
                            })->first();
                        if ($user) {
                            $exist->staff_id = $user->pk_int_user_id;
                            $exist->assigned_date = Carbon::today();
                            $exist->save();

                            try {
                                $note = $user->vchr_user_name . " has been designated as the lead via api";
                                event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $exist->pk_int_enquiry_id, $vendorId));
                            } catch (\Exception $e) {
                                Log::info($e->getMessage());
                            }
                        }
                    }
                    if (request('lead_purpose')) {
                        $purpose = EnquiryPurpose::where('fk_int_user_id', $vendorId)
                            ->where('vchr_purpose', 'LIKE', "%" . request('lead_purpose') . "%")
                            ->first();
                        if ($purpose) {
                            $exist->fk_int_purpose_id = $purpose->pk_int_purpose_id;
                            $exist->save();

                            try {
                                event(new CreateFollowup($purpose->pk_int_purpose_id, EnquiryFollowup::ENQ_PURPOSE, $exist->pk_int_enquiry_id, $vendorId));
                            } catch (\Exception $e) {
                                Log::info($e->getMessage());
                            }
                        }
                    }

                    if (request('lead_name')) {
                        if ($user) {
                            $exist->vchr_customer_name = request('lead_name');
                            $exist->assigned_date = Carbon::today();
                            $exist->save();

                            try {
                                $note = "Lead name has been changed via api";
                                event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $exist->pk_int_enquiry_id, $vendorId));
                            } catch (\Exception $e) {
                                Log::info($e->getMessage());
                            }
                        }
                    }

                    return response()->json(['message' => 'Lead Updated Successfully', 'status' => true]);
                } else {
                    return response()->json(['message' => 'Lead Not Found', 'status' => false]);
                }
            } else {
                return response()->json(['message' => 'Invalid user token', 'status' => false]);
            }
        } else {
            return response()->json(['message' => $validator->messages(), 'status' => false]);
        }
    }

    public static function checkMobile(Request $request)
    {
        $removePlusSymbol = str_replace('+', '', $request->mobileno);

        $numberCount = strlen($removePlusSymbol);

        // Read the JSON file
        $json = Storage::disk('public_storage')->get('json/data.json');
        // Decode the JSON data into a collection
        $countries = collect(json_decode($json, true));

        switch ($numberCount) {
            case 8:
                $request->merge([
                    'countrycode' => $request->countrycode ?? "",
                    'mobileno' => $removePlusSymbol
                ]);
                break;
            case 9:
                $request->merge([
                    'countrycode' => $request->countrycode ?? "",
                    'mobileno' => $removePlusSymbol
                ]);
                break;
            case 10:
                $request->merge([
                    'countrycode' => $request->countrycode ?? "",
                    'mobileno' => $removePlusSymbol
                ]);
                break;
            case 11:
                $getCodeIntl = self::splitString($removePlusSymbol, 3);
                $c_codeIntl = $countries->firstWhere('phoneCode', $getCodeIntl[0]);
                if ($c_codeIntl) {
                    $request->merge([
                        'countrycode' => $getCodeIntl[0],
                        'mobileno' => $getCodeIntl[1]
                    ]);
                } else {
                    $request->merge([
                        'countrycode' => $request->countrycode ?? "",
                        'mobileno' => $removePlusSymbol
                    ]);
                }
                break;
            case 12:
                $getCode = self::splitString($removePlusSymbol, 2);
                $getCodeIntl = self::splitString($removePlusSymbol, 3);
                $c_code = $countries->firstWhere('phoneCode', $getCode[0]);
                $c_codeIntl = $countries->firstWhere('phoneCode', $getCodeIntl[0]);
                if ($c_codeIntl) {
                    $request->merge([
                        'countrycode' => $getCodeIntl[0],
                        'mobileno' => $getCodeIntl[1]
                    ]);
                } else if ($c_code) {
                    $request->merge([
                        'countrycode' => $getCode[0],
                        'mobileno' => $getCode[1]
                    ]);
                } else {
                    $request->merge([
                        'countrycode' => $getCode[0],
                        'mobileno' => $getCode[1]
                    ]);
                }
                break;
            default:
                $getCode = self::splitString($removePlusSymbol, 2);
                $request->merge([
                    'countrycode' => $request->countrycode ?? "",
                    'mobileno' => $getCode[1]
                ]);
                break;
        }

        return $request;
    }

    public static function splitString($string, $length)
    {
        if (strlen($string) >= $length) {
            $part1 = substr($string, 0, $length);
            $part2 = substr($string, $length);
            return [$part1, $part2];
        } else {
            return ["", $string]; // Return an empty part1 and the original string in part2
        }
    }
}