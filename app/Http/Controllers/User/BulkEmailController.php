<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Input;
use App\BackendModel\MasterEmailHistory;
use App\BackendModel\SendEmailHistorys;
use App\BackendModel\EmailSenderId;
use App\BackendModel\Smsroute;
use App\Common\Variables;

use App\Common\Email;
use Illuminate\Http\Request;

use Flash;
use Carbon\Carbon;
use DataTables;
use Validator;
use App\User;
use Excel;
use Auth;
use DB;

class BulkEmailController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $senderids=EmailSenderId::where('vendor_id',User::getVendorId())->where('status',EmailSenderId::ACTIVATE)->get();
        return view('backend.user-pages.bulkemail.index',compact('senderids'));
    }
    public function sendEmail(Request $request)
    {
    	$input=$request->all();
        $vendorId = User::getVendorId();
    	$rule=[ 
    	    'email_address' => 'required',
    	   
    	];

    	$validator = validator::make($input,$rule);
    	if ($validator->passes()) 
    	{
    		try
    		{
    			$email = $request->email_address;
    			$resStr = str_replace(" ", " , ", $email);
    			$resStr = str_replace("\n", " , ", $resStr);
    			$contact = explode(" , ", $resStr);
    			$emailCount = count($contact);
    			$sendEmail=new Email();
    			$routeDetails=$sendEmail->getRouteDetails(Smsroute::EMAIL);
    			//get route details
				//balance
    			$smsPanel=$sendEmail->getSmsPanel($routeDetails->pk_int_sms_route_id,$vendorId);
    			$emailBalance=$sendEmail->getBalance($vendorId,$routeDetails->pk_int_sms_route_id,$smsPanel);
    			if($emailBalance>=$emailCount)
    			{
    				$email_address = implode(',', $contact);
    				$emailAddresses= str_replace("\r", "", $email_address);
    				$explodeEmailAddress=explode(",", $emailAddresses);
    				$jsonEmailAddress = json_encode(explode(",", $emailAddresses));

                    $senderids=EmailSenderId::where('vendor_id',$vendorId)->where('senderid',$request->sender_id)->first();
                    
    				$master_email=new Email();

                    $masterId=$master_email->storeMaster($request->copyright,$request->replyto,$request->subject,$request->message,$vendorId,$emailCount,$jsonEmailAddress,MasterEmailHistory::BULKEMAIL,$routeDetails->pk_int_sms_route_id,$request->sender_id,$senderids->display_name,'');
    			
    				for($x=0; $x<$emailCount;$x++)
    				{
                        $master_email->storeHistory($masterId,'0',$explodeEmailAddress[$x],$vendorId,$request->message,'','',SendEmailHistorys::SUBMITTED,'','');
    					
                    	//return 1;
                    }
    				DB::commit();
    				Flash::Success('Email Send successfully!');
    				return redirect('/user/bulk-email')->with('Success', 'Email Send successfully!');
    			}
    			else
    			{
    				$message = "Sorry! You don't have credits to send Email please add credits .Your balance is " . "<strong>" . $emailBalance . "</strong>";
    				Flash::error($message);
    				return redirect('user/bulk-email')->withErrors($message)->withInput();
    			}
    		}
    		catch(\Exception $e)
    		{
    			DB::rollback();
    			Flash::error('Whoops! Form validation failed');
            	return redirect('user/bulk-email')->withErrors("Someting Went wrong")->withInput();

    		}
    	}
    	else
    	{
    		Flash::error('Whoops! Form validation failed');
           return redirect('user/bulk-email')->withErrors($validator)->withInput();
    	}
        
    }


    public function getEmailHistoryPage()
    {

        return view('backend.user-pages.bulkemail.master-history');
    }

    public function getEmailHistoryData()
    {

        $id = User::getVendorId();
        $history = MasterEmailHistory::where('vendor_id', $id)
           ->orderBy('id', 'DES')
            ->get();
        foreach ($history as $key => $row) {
            $row->slno = ++$key;
        }
        return Datatables::of($history)
            ->addColumn('date', function ($history) {
                if ($history->created_at != NULL) {
                    $date = Variables::dateFormat($history->created_at) . ' , ' . Variables::changeTimeFormat($history->created_at);
                    return $date;
                    //  return $history->created_at;
                } else {
                    return "No Date";
                }
            })->addColumn('credit', function ($history) {
                return $history->email_count . ' [ ' . $history->email_count . ' ]';
            })->editColumn('message', function ($history) {
                if ($history->message != null) {
                    $message = preg_replace('/[^A-Za-z0-9\-]/', ' ', $history->message);
                    //   return $history->text_message;
                } else {
                    $message = "No Message";
                }
                
                $content = '<div>
                                <div class="row">
                                    <div class="col-sm-4">Subject:</div>
                                    <div class="col-sm-8">' . $history->subject . ' </div>
                                </div> 
                                <div class="row">
                                    <div class="col-sm-4">SenderId:</div>
                                    <div class="col-sm-8">' . $history->sender_id . ' </div>
                                </div>
                               
                                <div class="row">
                                    <div class="col-sm-4">Reply To:</div>
                                    <div class="col-sm-8">' . $history->replyto . ' </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-4">GLTrackId:</div>
                                    <div class="col-sm-8">' . $history->id . ' </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12">
                                    	<a class="" href="' . url('user/view-bulk-email/' . $history->id) . '"  >(View Mail)
                                    	</a>
                                    </div>
                                   
                                </div>
                           </div>';
                return $content;
            })->addColumn('show', function ($history) {
                return '<div class="btn-group btn-collection btn-icon-group">
                            <a class="btn btn-sm btn-default view-enquiry-type" href="' . url('user/email-history/' . $history->id) . '" title="View" ><i class="fa fa-eye"></i></a>
                        </div>
                        <div class="btn-group btn-collection btn-icon-group">
                            <a class="btn btn-sm btn-default view-enquiry-type" href="' . url('user/email-export/' . $history->id) . '" title="Quick Export" ><i class="fa fa-download"></i></a>
                        </div>
                        ';
            })->rawColumns(['show', 'message'])
            ->toJson(true);
    }


    public function getHistoryPage($id)
    {

        $master_sms = MasterEmailHistory::where('id', $id)->first();

        if ($master_sms) {
            $date = Variables::dateFormatWithYears($master_sms->created_at) . ' , ' . Variables::changeTimeFormat($master_sms->created_at);
            $master_sms->date = $date;
            //  $master_sms->message = preg_replace('/[^A-Za-z0-9\-]/', ' ', $master_sms->message);
        }

        $vendorId = User::getVendorId();
        $history = SendEmailHistorys::where('vendor_id', $vendorId)
            ->where('gl_track_id', $id)
            ->orderBy('id', 'DES')
            ->select('*')
            ->paginate(25);
        //   ->get();
        foreach ($history as $key => $row) {
            $row->slno = ++$key;
        }
        return view('backend.user-pages.bulkemail.email-history')
            ->with('master_sms_data', $master_sms)
            ->with('history', $history);
    }
    public function viewEmail($id)
    {
        $master_sms = MasterEmailHistory::where('id', $id)->first();
        $created_at=$master_sms->created_at;
        $year=Carbon::createFromFormat('Y-m-d H:i:s', $created_at)->year;

        if ($master_sms) {
            $date = Variables::dateFormatWithYears($master_sms->created_at) . ' , ' . Variables::changeTimeFormat($master_sms->created_at);
            $master_sms->date = $date;
            //  $master_sms->message = preg_replace('/[^A-Za-z0-9\-]/', ' ', $master_sms->message);
        }

        $vendorId = User::getVendorId();
        $history = SendEmailHistorys::where('vendor_id', $vendorId)
            ->where('gl_track_id', $id)
            ->orderBy('id', 'DES')
            ->select('*')
            ->paginate(25);
        //   ->get();
        foreach ($history as $key => $row) {
            $row->slno = ++$key;
        }
        return view('backend.user-pages.bulkemail.view-email')
            ->with('master_sms_data', $master_sms)
            ->with('history', $history)
            ->with('year', $year);
    }

    public function quickExport($id)
    {
       // return $id;
        $sms = SendEmailHistorys::where('gl_track_id',$id) ->get();
        $users=SendEmailHistorys::where('gl_track_id',$id) ->first()->vendor_id;
        $getUser=User::getUserDetails($users);
        return View('backend.pages.bulkemail.quickexport',compact('sms','getUser','id'));
    }

    public function viewReport()
    {
        $users=User::getVendorId();
        return View('backend.user-pages.bulkemail.email-report',compact('users'));
    }

    public function reports(Request $request)
    {
        $users=$request->users;
        $getUser=User::getUserDetails($users);
        $fromdate=$request->fromdate;
        $todate=$request->todate;
        $date1 = str_replace('-', '/', $todate);
        $nextdate = date('Y-m-d',strtotime($date1 . "+1 days"));
        $from_date=Variables::dateFormatWithYears($fromdate);
        $to_date=Variables::dateFormatWithYears($todate);
        $sms = SendEmailHistorys::whereBetween('created_at', [$fromdate, $nextdate])->where('vendor_id',$users)->get();
        return View('backend.user-pages.bulkemail.excel',compact('from_date','to_date','sms','getUser'));
    }

}