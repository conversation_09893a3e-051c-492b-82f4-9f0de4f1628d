<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use App\BackendModel\SenderId;
use DB;
use Carbon\Carbon;
use DataTables;
use Auth;
use App\Common\SendTelegram;
use App\Common\Notifications;
use App\Common\SingleSMS;


class SenderIdController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('backend.user-pages.senderid.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     * Get Sender Id data
     */
    public function getSenderid()
    {
        $data = SenderId::select('pk_int_sender_id as id', 'vchr_sender_id as name')
            ->where('int_sender_id_status', SenderId::ACTIVATE)
            ->where('fk_int_user_id', User::getVendorId())
            ->get();

        if (sizeof($data) != 0) {
            return response(['msg' => 'data found', 'status' => 'success', 'data' => $data]);
        }
        return response(['msg' => 'no data found', 'status' => 'failed']);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $input = $request->only(['sendername', 'senderid']);
        $vendorId = User::getVendorId();
        $input['fk_int_user_id'] = $vendorId;

        $validator = validator::make($input, SenderId::$rule, SenderId::$message);

        if ($validator->passes()) {
            try {
                $user = User::find($input['fk_int_user_id']);
                if (!empty($user)) {
                    DB::beginTransaction();
                    $userDefaultSenderIdCount = SenderId::where('fk_int_user_id', $vendorId)->where('int_default_sender_id', SenderId::DEFAULT)->count();

                    $sender = new SenderId;
                    $sender->vchr_sender_id_name = $input['sendername'];
                    $sender->fk_int_user_id = $vendorId;
                    $sender->vchr_sender_id = $input['senderid'];

                    //first sender id set as default
                    // if($userDefaultSenderIdCount>0){
                    ////     $sender->int_default_sender_id=SenderId::UNDEFAULT;
                    // }
                    // else{
                    //      $sender->int_default_sender_id=SenderId::DEFAULT;
                    // }
                    $sender->int_default_sender_id = SenderId::UNDEFAULT;
                    $sender->int_sender_id_status = SenderId::DEACTIVATE;
                    $flag = $sender->save();
                    if ($flag) {
                        //Notifications
                        $notifications = new Notifications();
                        $userName = User::getUserDetails($vendorId)->vchr_user_name;
                        $from = env('MAIL_FROM_ADDRESS');
                        $subject = "Senderid Approval";
                        $attachment = "";
                        $content1 = $userName . " is waiting for Senderid " . "(" . $input['senderid'] . ") Approval";
                        $content2 = $userName . " is waiting for Senderid " . "(" . $input['senderid'] . ")  Approval";

                        foreach (User::getMultipleAdminDetails() as $adminDetail) {
                            $to = $adminDetail->email;
                            $name = $adminDetail->vchr_user_name;
                            $logo = $adminDetail->vchr_logo;
                            $telegramId = $adminDetail->telegram_id;
                            $mobileNumber = $adminDetail->vchr_user_mobile;
                            $defaultSenderIdAdmin = SingleSMS:: getSenderid($adminDetail->pk_int_user_id, '');
                            $defaultRouteAdmin = SingleSMS:: getRoute($adminDetail->pk_int_user_id, '');

                            $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $adminDetail->pk_int_user_id, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin);
                        }
                        //-----------------
                        DB::commit();

                        //trigger email to admin
                        return response()->json(['msg' => 'Senderid added,Waiting for admin approval', 'status' => 'success']);
                    } else {
                        DB::rollBack();
                        return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                    }
                } else {
                    return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                // something went wrong whilst attempting to encode the token
                return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $senderid = SenderId::where('fk_int_user_id', User::getVendorId())->where('pk_int_sender_id', $id)->first();
        if (sizeof($senderid) != 0) {
            return response()->json(['msg' => 'data present', 'status' => 'success', 'data' => $senderid]);
        } else {
            return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $input = $request->all();
        if (!isset($id)) {
            DB::rollBack();
            return response([
                'status' => 'error',
                'message' => 'id not found',
            ], 404);
        }
        $validator = validator::make($input, SenderId::$updateRule, SenderId::$updateMessage);
        if ($validator->passes()) {
            try {
                $sender = SenderId::FindOrFail($id);
                if (!empty($sender)) {
                    DB::beginTransaction();
                    $sender->vchr_sender_id_name = $input['sendername'];
                    $sender->vchr_sender_id = $input['senderid'];
                    $sender->int_sender_id_status = SenderId::DEACTIVATE;
                    $flag = $sender->save();
                    if ($flag) {
                        //Notifications
                        $notifications = new Notifications();
                        $userName = User::getUserDetails(User::getVendorId())->vchr_user_name;
                        $from = env('MAIL_FROM_ADDRESS');
                        $subject = "Senderid Approval";
                        $attachment = "";
                        $content1 = $userName . " is waiting for Senderid " . "(" . $input['senderid'] . ") Approval";
                        $content2 = $userName . " is waiting for Senderid " . "(" . $input['senderid'] . ")  Approval";

                        foreach (User::getMultipleAdminDetails() as $adminDetail) {
                            $to = $adminDetail->email;
                            $name = $adminDetail->vchr_user_name;
                            $logo = $adminDetail->vchr_logo;
                            $telegramId = $adminDetail->telegram_id;
                            $mobileNumber = $adminDetail->vchr_user_mobile;
                            $defaultSenderIdAdmin = SingleSMS:: getSenderid($adminDetail->pk_int_user_id, '');
                            $defaultRouteAdmin = SingleSMS:: getRoute($adminDetail->pk_int_user_id, '');

                            $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $adminDetail->pk_int_user_id, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin);
                        }
                        //-----------------
                        DB::commit();

                        //trigger mail to admin for resubmission of sender id
                        return response()->json(['msg' => 'Senderid is updated,Waiting for admin approval', 'status' => 'success']);
                    } else {
                        DB::rollBack();
                        return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                    }
                } else {
                    return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                // something went wrong whilst attempting to encode the token
                return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            $flag = false;
            $sender = SenderId::findOrfail($id);
            if ($sender->int_default_sender_id == 1) {
                // get previous user id
                $previous = SenderId::where('pk_int_sender_id', '<', $id)->where('int_default_sender_id', SenderId::UNDEFAULT)->max('pk_int_sender_id');
                // get next user id
                $next = SenderId::where('pk_int_sender_id', '>', $id)->where('int_default_sender_id', SenderId::UNDEFAULT)->min('pk_int_sender_id');
                if (isset($previous)) {
                    $defaultSender = SenderId::find($previous);
                    $defaultSender->int_default_sender_id = SenderId::DEFAULT;
                    $flag1 = $defaultSender->save();
                    if ($flag1) {
                        $flag = true;
                    }
                } else if (isset($next)) {
                    $defaultSender = SenderId::find($next);
                    $defaultSender->int_default_sender_id = SenderId::DEFAULT;
                    $flag1 = $defaultSender->save();
                    if ($flag1) {
                        $flag = true;
                    }
                } else {
                    $flag = true;
                }
            } else {
                $flag = true;
            }
            if ($sender->delete()) {
                if ($flag) {
                    DB::commit();
                    return response()->json(['status' => 'success', 'msg' => 'Deleted successfully']);
                } else {
                    DB::rollBack();
                    return response()->json(['status' => 'error', 'msg' => 'Could not delete.',]);
                }
            } else {
                DB::rollBack();
                return response()->json(['status' => 'error', 'msg' => 'Could not delete.',]);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            // something went wrong whilst attempting to encode the token
            return response()->json(['status' => 'error', 'msg' => $e->getMessage()]);
        }
    }


    public function getInfo()
    {
        $template = DB::table('tbl_users')
            ->select('vchr_user_name', 'pk_int_sender_id', 'vchr_sender_id_name', 'vchr_sender_id', 'int_default_sender_id', 'int_sender_id_status')
            ->join('tbl_sender_id', 'tbl_users.pk_int_user_id', '=', 'tbl_sender_id.fk_int_user_id')->orderby('pk_int_sender_id', 'DESC')
            ->whereNull('tbl_sender_id.deleted_at')
            ->where('tbl_users.pk_int_user_id', User::getVendorId())
            ->get();

        foreach ($template as $key => $row) {
            $row->slno = ++$key;
        }
        return Datatables::of($template)
            ->addColumn('show', function ($template) {

                if ($template->int_sender_id_status == 1) {
                    return '<div class="dropdown-action" style="display: flex;align-items: center;"> 
                                <div class="dropdown show">
                                    <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>   
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <a class="dropdown-item ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32"><path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4l15-15zm-5-5L24 7.6l-3 3L17.4 7l3-3zM6 22v-3.6l10-10l3.6 3.6l-10 10H6z"/></svg>
                                        &nbsp;Edit
                                        </a>
                                        <a class="dropdown-item senderid-delete" href="#" senderid-id="' . $template->pk_int_sender_id . '">
                                            <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete
                                        </a>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-success feedback-act" data-toggle="tooltip" title="Senderid is active"> 
                                    <i class="fa fa-check"></i>
                                </button>
                            </div> ';
                } //If Status is not in active state
                else {
                    return '
                    
                    <div class="dropdown-action" style="display: flex;align-items: center;"> 
                                <div class="dropdown show">
                                    <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>   
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <a class="dropdown-item ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal" senderid-id="' . $template->pk_int_sender_id . '">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32"><path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4l15-15zm-5-5L24 7.6l-3 3L17.4 7l3-3zM6 22v-3.6l10-10l3.6 3.6l-10 10H6z"/></svg>
                                        &nbsp;Edit
                                        </a>
                                        <a class="dropdown-item senderid-delete" href="#"  id="delete_plan" senderid-id="' . $template->pk_int_sender_id . '">
                                            <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete
                                        </a>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-warning  feedback-act" data-toggle="tooltip" title="Admin approval pending"> 
                                <i class="fa fa-times"></i>
                                </button>
                            </div> ';
                }
            })
            ->rawColumns(['show', 'default'])
            ->toJson(true);
    }

    /**
     * @return mixed
     * Get user special info
     */
    public function getUserSpecialInfo()
    {
        $template = DB::table('tbl_users')
            ->select('vchr_user_name', 'pk_int_sender_id', 'vchr_sender_id_name', 'vchr_sender_id', 'int_default_sender_id', 'int_sender_id_status')
            ->join('tbl_sender_id', 'tbl_users.pk_int_user_id', '=', 'tbl_sender_id.fk_int_user_id')->orderby('pk_int_sender_id', 'DESC')
            ->whereNull('tbl_sender_id.deleted_at')
            ->where('tbl_users.pk_int_user_id', User::getVendorId())
            ->get();
        foreach ($template as $key => $row) {
            $row->slno = ++$key;
        }
        return Datatables::of($template)
            ->addColumn('show', function ($template) {
                if ($template->int_sender_id_status == 1) {
                    return '<div class="btn-group btn-collection btn-icon-group">
                                      <a senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-info px-3"  href="' . url('/user/senderid') . '"  data-toggle="tooltip" title="More Actions Click here" > <i class="f-16 la la-eye"></i></a>
                                      <a class="btn btn-success btn-activate  px-3 "   senderid-id="' . $template->pk_int_sender_id . '" data-toggle="tooltip" title="Sender id is active"> <i class="f-18 la la-check-circle active"></i></a>
                                </div>';
                } //If Status is not in active state
                else {
                    return '<div class="btn-group btn-collection btn-icon-group">
                                <a senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-info px-3"  href="' . url('/user/senderid') . '"  data-toggle="tooltip" title="More Actions Click here" > <i class="f-16 la la-eye"></i></a>
                                <a class="btn btn-warning  px-3" senderid-id="' . $template->pk_int_sender_id . '" data-toggle="tooltip" title="Admin approval pending"> <i class="f-18 la la-times-circle"></i></a>
                            </div>';
                }
            })->addColumn('default', function ($template) {
                if ($template->int_default_sender_id == 1) {
                    return '<div class="btn-group btn-collection btn-icon-group" title="Default sender id">
                                    <button id="" senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-outline-success  px-3" style="color:green" >DEFAULT</button>
                                    <button class="btn btn-outline-success ks-no-text">
                                            <span class="la la-check ks-icon"></span>
                                    </button>                          
                                </div>';
                } //If Status is not in active state
                else {
                    return '<div class="btn-group btn-collection btn-icon-group">          
                                     <button class="btn btn-outline-danger ks-no-text default-senderid" senderid-id="' . $template->pk_int_sender_id . '" title="Inactive">
                                            <span class="la la-close ks-icon"></span>
                                     </button>
                          </div>';
                }
            })->rawColumns(['show', 'default'])->toJson(true);
    }


}
