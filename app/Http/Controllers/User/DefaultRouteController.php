<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use App\BackendModel\DefaultRoute;
use App\BackendModel\Smsroute;
use DB;
use Carbon\Carbon;
use DataTables;
use Auth;


class DefaultRouteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $route=Smsroute::where('int_sms_route_status',Smsroute::ACTIVATE)->where('vchr_sms_route','!=', Smsroute::EMAIL)->where('vchr_sms_route','!=',DefaultRoute::INTERNATIONAL)->get();
          return view('backend.user-pages.default.route',compact('route'));
        
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
   public function show($id)
    {
        
         $route=DefaultRoute::where('id',$id)->first();
        if($route)
        {
            
             
            return response()->json(['msg'=>'data present', 'status'=>'success','data'=>$route]);
        }
        else
        {
             return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
        }
          
           
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
   
    public function getDefaultRoute()
    {
        
       $route = DB::table('tbl_sms_route')
          ->join('default_route', 'default_route.route', '=', 'tbl_sms_route.pk_int_sms_route_id')
          ->where('vendor_id',User::getVendorId())
            ->get();
       
        foreach ($route as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($route)
        ->editColumn('route', function ($route) {
            if ($route->vchr_sms_route != null) {
                return $route->vchr_sms_route;
            } else {
                return "'No Date'";
            }
        })
       
        
        ->addColumn('show', function ($route) {
        
        $user=User::select('countrycode')->find(User::getVendorId());
        $countryCode=$user->countrycode;

            if($countryCode==91)
            {
                        return '<button route-id="' . $route->id . '" class="btn btn-sm btn-info  ks-izi-modal-trigger1" data-target="#ks-izi-modal-large"  data-toggle="tooltip" title="View" > <i class="f-16 fa fa-angle-double-right"></i></button>';
            }
            else
            {
                return "No Actions";
                   
            }
        })
        
                ->rawColumns(['show'])
                ->toJson(true);
    }

     public function update(Request $request, $id)
    {

        $input = $request->all();
        //return $id;
        $validator = validator::make($input, DefaultRoute::$rule, DefaultRoute::$message);
        if ($validator->passes()) 
        {
           
            try
            {
                $route=DefaultRoute::find($id);
                if(!empty($route))
                {
                    DB::beginTransaction();
                   
                    $route=DefaultRoute::find($id);
                    $route->route=$input['route'];
                    $flag=$route->save();
                    //return 1;
                    if($flag)
                    {
                        //trigger email 
                        DB::commit();
                        return response([
                                'status' => 'success',
                                'msg' => 'Route Updated Successfully'
                               ]);
                    }
                    else
                    {
                        DB::rollBack();
                        return response([
                                    'status' => 'error',
                                    'msg' =>'Could not stored to database.',
                            ]);
                    }
                }
                else
                {
                    return response([
                                'status' => 'error',
                                'msg' =>'Route not found',
                            ]);
                }
            }
            catch (\Exception $e) {
            // something went wrong whilst attempting to encode the token
            return response()->json(['status'=>'error','msg' => $e->getMessage()]);
            }
            

        }
        else
        {
            return response([
                'status' => 'error',
                'msg' =>$validator->messages(),
            ]);
        }
    }

}
