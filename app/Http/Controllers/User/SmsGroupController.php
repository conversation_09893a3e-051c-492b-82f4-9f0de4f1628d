<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\GroupUsers;
use App\User;

use App\BackendModel\Group;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use Validator;
use DataTables;
use App\Common\Variables;
use DB;
use Auth;


class SmsGroupController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('backend.user-pages.group.index');
    }
    public function assignGroupManually(Request $request)
    {
        $input = $request->all();
        $feedback = new Group();
        $feedback->vchr_name= $input['groupname'];
        $feedback->vchr_description= $input['groupdescription'];
        $feedback->fk_int_user_id = User::getVendorId();
        $feedback->int_status = Group::ACTIVATE;
        $flag = $feedback->save();
        $groupid=$feedback->pk_int_group_id;
        $explode_contacts= explode(PHP_EOL, $input['contacts']);
        $explode_name= explode(PHP_EOL, $input['name']);
        $count=count($explode_contacts);
        for($i=0;$i<$count;$i++)
        {
        $group = new GroupUsers();
        $group->fk_int_group_id = $groupid;
        $group->fk_int_user_id = User::getVendorId();
        $group->int_mobile =  $explode_contacts[$i];
        if (array_key_exists($i, $explode_name)) {
            $group->vchr_name =  $explode_name[$i];
        }
        else
        {
            $group->vchr_name = '';
        }
        $group->vchr_type = 'Manually Enter Number(s) & Name(s)';
       	$group->int_status = GroupUsers::ACTIVATE;
        $flag = $group->save();
        }
        if ($flag) {
            return response()->json(['msg' => "New Group Created.", 'status' => 'success']);
        } else {
            return response()->json(['msg' => "Something went wrong, please try again later", 'status' => 'fail']);
        }
    }

    public function assignGroupUpload(Request $request)
    {
        $input = $request->all();
        $feedback = new Group();
        $feedback->vchr_name= $input['groupname'];
        $feedback->vchr_description= $input['groupdescription'];
        $feedback->int_status = Group::ACTIVATE;
         $feedback->fk_int_user_id =User::getVendorId();
        $flag = $feedback->save();
        $groupid=$feedback->pk_int_group_id;
            $file_mimes = array('text/x-comma-separated-values', 'text/comma-separated-values', 'application/octet-stream', 'application/vnd.ms-excel', 'application/x-csv', 'text/x-csv', 'text/csv', 'application/csv', 'application/excel', 'application/vnd.msexcel', 'text/plain', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            if(isset($_FILES['myfile']['name']) && in_array($_FILES['myfile']['type'], $file_mimes)) {
                $arr_file = explode('.', $_FILES['myfile']['name']);
                $extension = end($arr_file);
                if('csv' == $extension){
                    $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                } else {
        //$reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                }
                $spreadsheet = $reader->load($_FILES['myfile']['tmp_name']);
                $sheetData = $spreadsheet->getActiveSheet()->toArray();
                foreach($sheetData as $key=> $data)
                {
                    $group = new GroupUsers();
                    $group->fk_int_group_id = $groupid;
                    $group->fk_int_user_id = User::getVendorId();
                    $group->int_mobile =  $sheetData[$key][1];
                    $group->vchr_name =  $sheetData[$key][0];
                    $group->vchr_type = 'CSV File Upload';
                    $group->int_status = GroupUsers::ACTIVATE;
                    $fl=$group->save();
                    // echo $sheetData[$key+1][0];
                    // echo $sheetData[$key+1][1];
                    // die();
                }

            }
        
                if ($flag) {
            return response()->json(['msg' => "New Group Created.", 'status' => 'success']);
        } else {
            return response()->json(['msg' => "Something went wrong, please try again later", 'status' => 'fail']);
        }
    }

   
    public function getsmsGroups()
    {
        return view('backend.user-pages.group.show');
    }

     public function getGroups()
    {
      $id=User::getVendorId();
      $group = Group::where('fk_int_user_id',$id)->get();

        
        foreach ($group as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($group)
        ->editColumn('contacts', function ($group) {
            $groupUsers = GroupUsers::where('fk_int_group_id',$group->pk_int_group_id)->get();
            $count=count($groupUsers);
            return $count;
            
        })
        ->editColumn('name', function ($group) {
            if ($group->vchr_name != null) {
                return $group->vchr_name;
            } else {
                return "No Name";
            }
        })
        ->editColumn('description', function ($group) {
            if ($group->vchr_description != null) {
                return $group->vchr_description;
            } else {
                return "No description";
            }
        })
        ->addColumn('show', function ($group) 
        {
           return '<div class="dropdown-action" style="display: flex;align-items: center;"> 
                    <div class="dropdown show">
                        <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>   
                        </a>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                            <a href="#" class="dropdown-item ks-izi-modal-trigger1" feedback-id="' . $group->pk_int_group_id . '" data-target="#ks-izi-modal-large1"  data-toggle="modal">
                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32"><path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4l15-15zm-5-5L24 7.6l-3 3L17.4 7l3-3zM6 22v-3.6l10-10l3.6 3.6l-10 10H6z"/></svg>
                            &nbsp;Edit
                            </a>
                            <a class="dropdown-item feedback-delete" href="#"   id="delete_plan" feedback-id="' . $group->pk_int_group_id . '">
                                <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete
                            </a>
                            <a href="'.'view-contacts'.'/' . $group->pk_int_group_id . '" class="dropdown-item" testimonial-id="' . $group->pk_int_group_id . '">
                                <i class="fa fa-eye" aria-hidden="true"></i>&nbsp;view
                            </a>
                            <a href="'.'add-group-contacts'.'/' . $group->pk_int_group_id . '" class="dropdown-item" testimonial-id="' . $group->pk_int_group_id . '" >
                                <i class="fa fa-plus-circle" aria-hidden="true"></i>&nbsp;add more contacts to this group
                            </a>

                        </div>
                    </div>
                </div> ';
            
        })
        ->rawColumns(['show'])
        ->toJson(true);
    }

    public function showgroup($id)
    {
        $data=Group::find($id);
        if($data)
        {
              return response()->json(['msg' => "Group detail found.", 'status' => 'success', 'data' => $data]);
        }
        else {
            return response()->json(['msg' => "Group detail not found.", 'status' => 'fail']);
        }
    }

    public function edit(Request $request, $id)
    {
         // get the POST data
        $input = $request->all();
        
        if ($input) {
            try
            {
                $data = Group::find($id);
                $data->fill($input);
                $flag=$data->save();
                if($flag)
                {
                     return response()->json(['msg'=>'Group Details updated.', 'status'=>'success']);
                }
                else
                {
                     return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                }
                
            }
            catch(\Exception $e)
            {
                return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

                //return $e->getMessage();
            }

        } else {
             // return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);

           // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
        }
    }

    public function destroy($id)
    {
         try {
            $data = Group::find($id);
            if ($data) {
                Group::where('pk_int_group_id', $id)->delete();
                GroupUsers::where('fk_int_group_id', $id)->delete();
                return response(['msg' => 'Group is deleted.', 'status' => 'success']);

            }
            else
            {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            }
        }
    catch (\Exception $ex) {
              return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            
        }
    }

    public function getContacts($id)
    {
        $group = Group::find($id);
        return view('backend.user-pages.group.view-groups',compact('group','id'));
    }

     public function showContacts($id)
    {
      $group=GroupUsers::where('fk_int_group_id', $id)->get();
        foreach ($group as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($group)
        
        ->editColumn('name', function ($group) {
            if ($group->vchr_name != null) {
                return $group->vchr_name;
            } else {
                return "No Name";
            }
        })
        ->editColumn('mobile', function ($group) {
            if ($group->int_mobile != null) {
                return $group->int_mobile;
            } else {
                return "No Mobile";
            }
        })
        ->addColumn('show', function ($group) 
        {
           return '<div class="dropdown-action" style="display: flex;align-items: center;"> 
                    <div class="dropdown show">
                        <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>   
                        </a>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                            <a href="#" feedback-id="' . $group->pk_int_group_user_id . '" class="dropdown-item ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal">
                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32"><path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4l15-15zm-5-5L24 7.6l-3 3L17.4 7l3-3zM6 22v-3.6l10-10l3.6 3.6l-10 10H6z"/></svg>
                            &nbsp;Edit
                            </a>
                            <a href="#" id="delete_plan" feedback-id="' . $group->pk_int_group_user_id . '" class="dropdown-item feedback-delete">
                                <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete
                            </a>
                        </div>
                    </div>
                </div>';
            
        })
        ->rawColumns(['show'])
        ->toJson(true);
    }

    public function showgroupUsers($id)
    {
        $data=GroupUsers::find($id);
        if($data)
        {
              return response()->json(['msg' => "Group detail found.", 'status' => 'success', 'data' => $data]);
        }
        else {
            return response()->json(['msg' => "Group detail not found.", 'status' => 'fail']);
        }
    }

     public function editUsers(Request $request, $id)
    {
         // get the POST data
        $input = $request->all();
        
        if ($input) {
            try
            {
                $data = GroupUsers::find($id);
                $data->fill($input);
                $flag=$data->save();
                if($flag)
                {
                     return response()->json(['msg'=>'Contact Details updated.', 'status'=>'success']);
                }
                else
                {
                     return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                }
                
            }
            catch(\Exception $e)
            {
                return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

                //return $e->getMessage();
            }

        } else {
             // return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);

           // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
        }
    }

    public function destroyUsers($id)
    {
         try {
            $data = GroupUsers::find($id);
            if ($data) {
                GroupUsers::where('pk_int_group_user_id', $id)->delete();
              return response(['msg' => 'Contact is deleted.', 'status' => 'success']);

            }
            else
            {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            }
        }
    catch (\Exception $ex) {
              return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            
        }
    }

    public function addgroupUsers($id)
    {
        $group = Group::find($id);
        return view('backend.user-pages.group.add',compact('group','id'));
    }

    public function assignGroupManuallyAdd(Request $request)
    {
        $input = $request->all();
        $groupid=$input['groupid'];
        $explode_contacts= explode(PHP_EOL, $input['contacts']);
        $explode_name= explode(PHP_EOL, $input['name']);
        $count=count($explode_contacts);
        for($i=0;$i<$count;$i++)
        {
        $group = new GroupUsers();
        $group->fk_int_group_id = $groupid;
        $group->fk_int_user_id = User::getVendorId();
        $group->int_mobile =  $explode_contacts[$i];
        if (array_key_exists($i, $explode_name)) {
            $group->vchr_name =  $explode_name[$i];
        }
        else
        {
            $group->vchr_name = '';
        }
        $group->vchr_type = 'Manually Enter Number(s) & Name(s)';
        $group->int_status = GroupUsers::ACTIVATE;
        $flag = $group->save();
        }
        if ($flag) {
            return response()->json(['msg' => "Contacts Added.", 'status' => 'success']);
        } else {
            return response()->json(['msg' => "Something went wrong, please try again later", 'status' => 'fail']);
        }
    }

    public function assignGroupUploadAdd(Request $request)
    {
        $input = $request->all();
        $groupid=$input['groupid'];
            $file_mimes = array('text/x-comma-separated-values', 'text/comma-separated-values', 'application/octet-stream', 'application/vnd.ms-excel', 'application/x-csv', 'text/x-csv', 'text/csv', 'application/csv', 'application/excel', 'application/vnd.msexcel', 'text/plain', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            if(isset($_FILES['myfile']['name']) && in_array($_FILES['myfile']['type'], $file_mimes)) {
                $arr_file = explode('.', $_FILES['myfile']['name']);
                $extension = end($arr_file);
                if('csv' == $extension){
                    $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                } else {
        //$reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                }
                $spreadsheet = $reader->load($_FILES['myfile']['tmp_name']);
                $sheetData = $spreadsheet->getActiveSheet()->toArray();
                foreach($sheetData as $key=> $data)
                {
                    $group = new GroupUsers();
                    $group->fk_int_group_id = $groupid;
                    $group->fk_int_user_id = User::getVendorId();
                    $group->int_mobile =  $sheetData[$key][1];
                    $group->vchr_name =  $sheetData[$key][0];
                    $group->vchr_type = 'CSV File Upload';
                    $group->int_status = GroupUsers::ACTIVATE;
                    $flag=$group->save();
                    // echo $sheetData[$key+1][0];
                    // echo $sheetData[$key+1][1];
                    // die();
                }

            }
        
                if ($flag) {
            return response()->json(['msg' => "Contacts Added.", 'status' => 'success']);
        } else {
            return response()->json(['msg' => "Something went wrong, please try again later", 'status' => 'fail']);
        }
    }

}