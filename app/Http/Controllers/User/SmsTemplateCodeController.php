<?php

namespace App\Http\Controllers\User;

use App\BackendModel\SmsTemplateCode;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use Auth;
use DB;

use Carbon\Carbon;
use App\Core\CustomClass;
use DataTables;
use App\Common\SendTelegram;
use App\Common\Notifications;
use App\Common\SingleSMS;

class SmsTemplateCodeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        return view('backend.user-pages.sms-template.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        $input=$request->all();
        $userid=User::getVendorId();
        $validator=validator::make($input,SmsTemplateCode::$rule,SmsTemplateCode::$message);
        if($validator->passes())
        {
                try
                    {
                        $template=new SmsTemplateCode();
                        $template->vchr_sms_template_title=$request->vchr_sms_template_title;
                        $template->sms_template_code=$request->sms_template_code;
                        $template->fk_int_user_id=$userid;
                        $template->int_status =SmsTemplateCode::DEACTIVATE;
                        $template->created_by=Auth::user()->pk_int_user_id;
                        $flag=$template->save();
                        if($flag)
                        {
                             //Notifications
                            $notifications=new Notifications();
                            $userName=User::getUserDetails(User::getVendorId())->vchr_user_name;
                            $from=env('MAIL_FROM_ADDRESS');
                            $subject="SMS Template Approval";
                            $attachment="";
                            $content1=$userName." is waiting for SMS Template Approval";
                            $content2=$userName." is waiting for SMS Template Approval";

                            foreach(User::getMultipleAdminDetails() as $adminDetail)
                                {
                                    $to= $adminDetail->email;
                                    $name=$adminDetail->vchr_user_name;
                                    $logo=$adminDetail->vchr_logo;
                                    $telegramId=$adminDetail->telegram_id;
                                    $mobileNumber=$adminDetail->vchr_user_mobile;
                                    $defaultSenderIdAdmin=SingleSMS:: getSenderid($adminDetail->pk_int_user_id,'');
                                    $defaultRouteAdmin=SingleSMS:: getRoute($adminDetail->pk_int_user_id,'');

                                    $notifications->notifications($from,$to,$subject,$name,$content1,$content2,$logo,$attachment,$telegramId,$adminDetail->pk_int_user_id,$mobileNumber,$defaultRouteAdmin,$defaultSenderIdAdmin);
                                }
                    //-----------------
                            return response()->json(['msg'=>'SMS Template is added,waiting for admin approval.', 'status'=>'success']);
                        }                       
                    }
                    catch (\Exception $e) {
                        // something went wrong whilst attempting to encode the token
                        return response()->json(['status'=>'error','msg' => $e->getMessage()], 500);
                    }
        }
        else
        {
            return response()->json([
                'status' => 'error',
                'msg' =>$validator->messages(),
            ]);
        }
        
       
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\BackendModel\SmsTemplateCode  $smsTemplateCode
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
        // return("hello");
        $template=SmsTemplateCode::where('pk_int_sms_template_id',$id)->first();
        if($template)
        {
            
             
            return response()->json(['msg'=>'data present', 'status'=>'success','data'=>$template]);
        }
        else
        {
             return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
        }
    
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\BackendModel\SmsTemplateCode  $smsTemplateCode
     * @return \Illuminate\Http\Response
     */
    public function edit(SmsTemplateCode $smsTemplateCode)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\BackendModel\SmsTemplateCode  $smsTemplateCode
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
        $input = $request->all();
         
        $validator = validator::make($input, SmsTemplateCode::$updaterule, SmsTemplateCode::$updatemessage);
       
       if ($validator->passes()) 
       {


           try
           {
               $template = SmsTemplateCode::find($id);
               $template->vchr_sms_template_title=$request->vchr_sms_template_title;
               $template->sms_template_code=$request->sms_template_code;
                $template->int_status = SmsTemplateCode::DEACTIVATE;
               $template->updated_by = Auth::user()->pk_int_user_id;
               $flag=$template->save();
                if($flag)
                {
                 //Notifications
                    $notifications=new Notifications();
                    $userName=User::getUserDetails(User::getVendorId())->vchr_user_name;
                    $from=env('MAIL_FROM_ADDRESS');
                    $subject="SMS Template Approval";
                    $attachment="";
                    $content1=$userName." is waiting for SMS Template Approval";
                    $content2=$userName." is waiting for SMS Template Approval";

                    foreach(User::getMultipleAdminDetails() as $adminDetail)
                    {
                        $to= $adminDetail->email;
                        $name=$adminDetail->vchr_user_name;
                        $logo=$adminDetail->vchr_logo;
                        $telegramId=$adminDetail->telegram_id;
                        $mobileNumber=$adminDetail->vchr_user_mobile;
                        $defaultSenderIdAdmin=SingleSMS:: getSenderid($adminDetail->pk_int_user_id,'');
                        $defaultRouteAdmin=SingleSMS:: getRoute($adminDetail->pk_int_user_id,'');

                        $notifications->notifications($from,$to,$subject,$name,$content1,$content2,$logo,$attachment,$telegramId,$adminDetail->pk_int_user_id,$mobileNumber,$defaultRouteAdmin,$defaultSenderIdAdmin);
                    }
                    //-----------------
                   return response()->json(['msg'=>'SMS Template is updated,Waiting for Admin Approval.', 'status'=>'success']);
                }  
          }
           catch (\Exception $e) {
               return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);
           }
       }
       else
       {
           return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);
       }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\BackendModel\SmsTemplateCode  $smsTemplateCode
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
        
        $template = SmsTemplateCode::find($id);
        if ($template) {
            $flag = $template->delete();
            if ($flag) {
                return response()->json(['msg' => "SMS Template is deleted.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "SMS Template could not be deleted.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "SMS Template not found.", 'status' => 'fail']);
        }
    }
    public function getInfo(){
        $template = DB::table('tbl_users')
        ->select('vchr_user_name','vchr_sms_template_title','sms_template_code','sms_templates.int_status','pk_int_sms_template_id')
        ->join('sms_templates', 'tbl_users.pk_int_user_id', '=', 'sms_templates.created_by')
        ->where('fk_int_user_id',User::getVendorId())->orderby('pk_int_sms_template_id','DESC')
        ->get();
  
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

        if ($template->int_status == 1) {

            return '<div class="dropdown-action" style="display: flex;align-items: center;"> 
                        <div class="dropdown show">
                            <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa fa-circle" aria-hidden="true"></i>
                                <i class="fa fa-circle" aria-hidden="true"></i>
                                <i class="fa fa-circle" aria-hidden="true"></i>   
                            </a>
                            <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                <a class="dropdown-item ks-izi-modal-trigger1" href="#" smstemplate-id="' . $template->pk_int_sms_template_id . '" data-target="#ks-izi-modal-large1"  data-toggle="modal">
                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32"><path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4l15-15zm-5-5L24 7.6l-3 3L17.4 7l3-3zM6 22v-3.6l10-10l3.6 3.6l-10 10H6z"/></svg>
                                &nbsp;Edit
                                </a>
                                <a class="dropdown-item smstemplate-delete" href="#" id="delete_plan" smstemplate-id="' . $template->pk_int_sms_template_id . '">
                                    <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete
                                </a>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-success btn-activate "   smstemplate-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="SMS template  is active"> 
                            <i class="fa fa-check"></i>
                        </button>
                    </div>';
        } //If Status is not in active state
        else {
            return '  <div class="dropdown-action" style="display: flex;align-items: center;">  
                        <div class="dropdown show">
                            <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa fa-circle" aria-hidden="true"></i>
                                <i class="fa fa-circle" aria-hidden="true"></i>
                                <i class="fa fa-circle" aria-hidden="true"></i>   
                            </a>
                            <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                <a class="dropdown-item ks-izi-modal-trigger1" href="#" smstemplate-id="' . $template->pk_int_sms_template_id . '" data-target="#ks-izi-modal-large1"  data-toggle="modal">
                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32"><path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4l15-15zm-5-5L24 7.6l-3 3L17.4 7l3-3zM6 22v-3.6l10-10l3.6 3.6l-10 10H6z"/></svg>
                                &nbsp;Edit
                                </a>
                                <a class="dropdown-item smstemplate-delete" href="#" id="delete_plan" smstemplate-id="' . $template->pk_int_sms_template_id . '">
                                    <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete
                                </a>
                            </div>
                            <button class="btn btn-sm btn-warning" smstemplate-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Admin approval pending">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>';
                    }
                
                })
                ->rawColumns(['show'])
                ->toJson(true);

    }
}
