<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\UserSubscriptionMessage;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use App\BackendModel\SenderId;
use DB;
use Carbon\Carbon;
use DataTables;
use Auth;
use App\Core\CustomClass;

class SmsSubscriptionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $user=UserSubscriptionMessage::where('fk_int_user_id',User::getVendorId())->first();

        if(empty($user))
        {

            $message1="Thanks for subscribing";
            $message2="You are unsubscribed";
            $userMessage=new UserSubscriptionMessage();
            $userMessage->fk_int_user_id=User::getVendorId();
            $userMessage->text_subscription_message=$message1;
            $userMessage->text_unsubscription_message=$message2;
            $userMessage->int_subscription_message_count=CustomClass::smsLength($message1);
            $userMessage->int_unsubscription_message_count=CustomClass::smsLength($message2);
            $userMessage->int_subscription_message_status=UserSubscriptionMessage::ACTIVATE;
            // return $userMessage;
            $userMessage->save();

            $user=UserSubscriptionMessage::where('fk_int_user_id',User::getVendorId())->first();

            return view('backend.user-pages.subscription-message.index',compact('user'));
        }
        else
        {
             return view('backend.user-pages.subscription-message.index',compact('user'));
        }

        // return $user;
        

        
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
          // get the POST data
          $input = $request->all();

        //DO validation here
        $validator=validator::make($input, UserSubscriptionMessage::$rule, UserSubscriptionMessage::$message);
        //dd($input);

        // if validator passes
        if ($validator->passes()) {
            
            try
            {
                 $userMessage=UserSubscriptionMessage::find($id);
                $userMessage->fk_int_user_id=User::getVendorId();
                $userMessage->text_subscription_message=$request->text_subscription_message;
                $userMessage->text_unsubscription_message=$request->text_unsubscription_message;
                $userMessage->int_subscription_message_count=CustomClass::smsLength($request->text_subscription_message);
                $userMessage->int_unsubscription_message_count=CustomClass::smsLength($request->text_unsubscription_message);
                $userMessage->int_subscription_message_status=UserSubscriptionMessage::DEACTIVATE;
                // return $userMessage;
                $flag=$userMessage->save();
                if($flag)
                {
                    return response()->json(['msg'=>'Subscription message is updated.Waiting for admin approval', 'status'=>'success']);
                }
            
                else
                {
                     return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                }
                
            }
            catch(\Exception $e)
            {
                return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

                //return $e->getMessage();
            }

        } else {
             return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);

           // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function getInfo()
    {
        $template = DB::table('tbl_users')
        ->select('*')
        ->join('tbl_user_subscription_message', 'tbl_users.pk_int_user_id', '=', 'tbl_user_subscription_message.fk_int_user_id')->orderby('pk_int_subscription_message_id','DESC')
        ->whereNull('tbl_user_subscription_message.deleted_at')
         ->where('tbl_user_subscription_message.fk_int_user_id',User::getVendorId())
        
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

     
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                   
                                   

                                     <a class="btn btn-info btn-activate  px-3 "  href="'.url("user/subscription-message").'" sms-id="' . $template->pk_int_subscription_message_id . '" target="_blank" title="View"> <i class="f-18 la la-eye active"></i></a>
                                </div>';
                   
                })
                ->rawColumns(['show'])
                ->toJson(true);
    }
}
