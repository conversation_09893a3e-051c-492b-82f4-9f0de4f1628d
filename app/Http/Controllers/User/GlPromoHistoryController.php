<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\GlPromo;
use App\BackendModel\GlPromoHistory;
use App\User;

use Validator;
use DataTables;
use App\Common\Variables;
use DB;
use Illuminate\Support\Facades\Input;
use Auth;
//use App\Http\Resources\Testimonial\TestimonialResource;

class GlPromoHistoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        
        return view('backend.user-pages.promo.history');
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    
    public function getPromoHistory()
    {

        $id=User::getVendorId();
        $promo = DB::table('tbl_users')
        ->join('tbl_gl_promo_history', 'tbl_users.pk_int_user_id', '=', 'tbl_gl_promo_history.fk_int_user_id')
        ->where('tbl_gl_promo_history.fk_int_user_id',$id)
        ->orderby('tbl_gl_promo_history.pk_int_promo_history_id','Desc')
        ->get();
     
     
        foreach ($promo as $key => $row) 
        {
         $row->slno=++$key;
        }
            return Datatables::of($promo)
            ->editColumn('date', function ($promo) 
            {
                if ($promo->created_at != null) {
                    return $promo->created_at;
                } else 
                {
                    return "'No Mobile No'";
                }
            })
            ->editColumn('username', function ($promo) 
            {
                if ($promo->vchr_user_name != null) 
                {
                    return $promo->vchr_user_name;
                } 
                else 
                {
                    return "'No Username'";
                }
            })
            ->editColumn('mobno', function ($promo) 
            {
                if ($promo->vchr_user_mobile != null) 
                {
                    return $promo->vchr_user_mobile;
                } 
                else 
                {
                return "'No Mobile No'";
                }
            })
            ->editColumn('package', function ($promo) 
            {
                if ($promo->int_purchased_count_id != null) 
                {
                    return $promo->int_purchased_count_id;
                }
                else 
                {
                return "'No Package'";
                }
            })
            ->editColumn('remarks', function ($promo) 
            {
                if ($promo->vchr_gl_promo_remarks != null) {
                    return $promo->vchr_gl_promo_remarks;
                } 
                else 
                {
                return "No Remarks";
                }
            })
            ->toJson(true);
    }
}
