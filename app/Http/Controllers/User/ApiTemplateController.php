<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use App\BackendModel\ApiTemplate;
use DB;
use Carbon\Carbon;
use DataTables;
use Auth;
use App\Common\SendTelegram;

class ApiTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
     public function index()
    {
         return view('backend.user-pages.apitemplate.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $vendorId = User::getVendorId();
        $input = $request->only(['vchr_api_template_title', 'userid','text_api_template_description']);
        $input['fk_int_user_id']=$vendorId;

         $validator = validator::make($input, ApiTemplate::$rule, ApiTemplate::$message);
        
        if ($validator->passes()) 
        {
           
            try
            {
                $user=User::find($input['fk_int_user_id']);
                if(!empty($user))
                {
                    DB::beginTransaction();
                    $userDefaultApiTemplateCount=ApiTemplate::where('fk_int_user_id',$vendorId)->where('int_default_api_template_id',ApiTemplate::DEFAULT)->count();

                    $apitemplate = new ApiTemplate;
                    $apitemplate->vchr_api_template_title=$input['vchr_api_template_title'] ;
                    $apitemplate->fk_int_user_id=$vendorId;
                    $apitemplate->text_api_template_description=$input['text_api_template_description'] ;
                    $apitemplate->created_by=Auth::user()->pk_int_user_id;

                    //first sender id set as default

                    // if($userDefaultApiTemplateCount>0)
                    // {
                    //     $apitemplate->int_default_api_template_id=ApiTemplate::UNDEFAULT;
                    // }
                    // else
                    // {
                    //      $apitemplate->int_default_api_template_id=ApiTemplate::DEFAULT;
                    // }
                    $apitemplate->int_default_api_template_id=ApiTemplate::UNDEFAULT;
                    $apitemplate->int_api_template_status=ApiTemplate::DEACTIVATE;
                    $flag=$apitemplate->save();
                    if($flag)
                    {
                         //Telegram-----------------
                         $userName=User::getUserName($vendorId);
                        $getAdmins=User::where('int_role_id',User::ADMIN)->get();
                         foreach($getAdmins as $getAdmin)
                         {
                             $adminId=$getAdmin->pk_int_user_id;
                             $telegramId=User::getTelegramId($adminId);
                             $message=$userName." is waiting for Api Template Approval";
                             $sendTelegrams = new SendTelegram();
                             $sendTelegram=$sendTelegrams->telegram($telegramId,$message);
                         }

                        DB::commit();

                        //trigger email to admin 

                         return response()->json(['msg'=>'ApiTemplate added,Waiting for admin approval', 'status'=>'success']);
                            
                       

                    }
                    else
                    {
                        DB::rollBack();
                       return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                           
                    }
                }
                else
                { 
                 return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                }
            }
            catch (\Exception $e) {
            // something went wrong whilst attempting to encode the token
                 return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);
                           
            
            }
            

        }
        else
        {
            return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        
         $apitemplate=ApiTemplate::where('fk_int_user_id',User::getVendorId())->where('pk_int_api_template_id',$id)->first();
         
        if($apitemplate)
        {
            
             
            return response()->json(['msg'=>'data present', 'status'=>'success','data'=>$apitemplate]);
        }
        else
        {
             return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
        }
          
           
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $input = $request->all();
         if(!isset($id))
         {
            DB::rollBack();
            return response()->json([
            'status' => 'error',
            'message' =>'id not found',
            ]);
         }

         $validator = validator::make($input, ApiTemplate::$updateRule, ApiTemplate::$updateMessage);
        
        if ($validator->passes()) 
        {
           
            try
            {
                $template=ApiTemplate::FindOrFail($id);
                if(!empty($template))
                {
                    DB::beginTransaction();
                    $template->vchr_api_template_title=$input['apitemplatetitle'] ;
                    $template->text_api_template_description=$input['apitemplatebody'] ;
                      $template->updated_by=Auth::user()->pk_int_user_id;
                    $template->int_api_template_status=ApiTemplate::DEACTIVATE;
                    $flag=$template->save();
                    if($flag)
                    {
                        DB::commit();

                        //trigger mail to admin for resubmission of sender id
                   
                         return response()->json(['msg'=>'ApiTemplate is updated,Waiting for admin approval', 'status'=>'success']);

                    }
                    else
                    {
                        DB::rollBack();

                        return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                    }
                }
                else
                {
                     return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                }
            }
            catch (\Exception $e) {
            // something went wrong whilst attempting to encode the token
            
             return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);
 
            }
            

        }
        else
        {
            return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
         try
        {    
            DB::beginTransaction();

           $flag=false;

            $template = ApiTemplate::findOrfail($id);
            if($template->int_default_api_template_id == 1)
            {
                 
                 
                 // get previous user id
                 $previous = ApiTemplate::where('pk_int_api_template_id', '<', $id)->where('int_default_api_template_id',ApiTemplate::UNDEFAULT)->max('pk_int_api_template_id');

                // get next user id
                 $next = ApiTemplate::where('pk_int_api_template_id', '>', $id)->where('int_default_api_template_id',ApiTemplate::UNDEFAULT)->min('pk_int_api_template_id');
                 
                 if(isset($previous))
                 {
                    $defaultSender=ApiTemplate::find($previous);
                    $defaultSender->int_default_api_template_id=ApiTemplate::DEFAULT;
                    $flag1=$defaultSender->save();
                     if($flag1)
                     {
                         $flag=true;
                     }
                 }
                 else if($next)
                 {
                    $defaultSender=ApiTemplate::find($next);
                    $defaultSender->int_default_api_template_id=ApiTemplate::DEFAULT;
                     $flag1=$defaultSender->save();
                     if($flag1)
                     {
                         $flag=true;
                     }
                 }
                 else
                 {
                    $flag=true;
                 }   
            }
            else
            {
               $flag=true; 
            }
               
            if($template->delete()) 
            {
                if($flag)
                {
                    DB::commit();
                     return response()->json([
                     'status' => 'success',
                     'msg' => 'Deleted successfully'
                    ]);
                }
                else
                {
                    DB::rollBack();
                        return response()->json([
                            'status' => 'error',
                            'msg' =>'Could not delete.',
                        ]);
                }
            }
            else{
                 DB::rollBack();
                        return response()->json([
                            'status' => 'error',
                            'msg' =>'Could not delete.',
                        ]);

            } 
        }
        catch (\Exception $e) {
                DB::rollBack();
            // something went wrong whilst attempting to encode the token
            return response()->json(['status'=>'error','msg'=> $e->getMessage()]);
        }
    }


    public function getInfo()
    {
        


        $template = DB::table('tbl_users')
        ->select('vchr_user_name','pk_int_api_template_id','vchr_api_template_title','text_api_template_description','int_default_api_template_id','int_api_template_status')
        ->join('tbl_api_template', 'tbl_users.pk_int_user_id', '=', 'tbl_api_template.fk_int_user_id')->orderby('pk_int_api_template_id','DESC')
        ->whereNull('tbl_api_template.deleted_at')
        ->where('tbl_users.pk_int_user_id',User::getVendorId())
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

        if ($template->int_api_template_status == 1) {
                        return '
                                    <button apitemplate-id="' . $template->pk_int_api_template_id . '" class="btn btn-sm btn-primary mg-b-10 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal"> <i class="fa fa-edit mg-r-5"></i></button>

                                    <button id="delete_plan" apitemplate-id="' . $template->pk_int_api_template_id . '" class="btn btn-sm btn-danger apitemplate-delete"> <i class="fa fa-trash-o mg-r-5"></i></button>
                                    <button class="btn btn-sm btn-success btn-activate"   apitemplate-id="' . $template->pk_int_api_template_id . '" data-toggle="tooltip" title="Api template  is active"> <i class="fa fa-check"></i></button>
                                    ';
                    } //If Status is not in active state
                    else {
                        return '
                                     <button apitemplate-id="' . $template->pk_int_api_template_id . '" class="btn btn-sm btn-primary mg-b-10 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal"> <i class="fa fa-edit mg-r-5"></i></button>

                                    <button id="delete_plan" apitemplate-id="' . $template->pk_int_api_template_id . '" class="btn btn-sm btn-danger apitemplate-delete"> <i class="fa fa-trash-o mg-r-5"></i></button>
                                    <button class="btn btn-sm btn-warning" apitemplate-id="' . $template->pk_int_api_template_id . '" data-toggle="tooltip" title="Admin approval pending"> <i class="fa fa-times"></i></button>
                                </div>';
                    }
                })->addColumn('default', function ($template) {

        if ($template->int_default_api_template_id == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group" title="Default Api template">
                                   
                                    <button id="" apitemplate-id="' . $template->pk_int_api_template_id . '" class="btn btn-outline-success  px-3" style="color:green" >DEFAULT</button>
                                    <button class="btn btn-outline-success ks-no-text">
                                            <i class="fa fa-check"></i>
                                        </button>
                                     
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    
                                     <button class="btn btn-outline-danger ks-no-text default-apitemplate" apitemplate-id="' . $template->pk_int_api_template_id . '" title="Click to activate default apitemplate">
                                            <i class="fa fa-times"></i>
                                        </button>
                                   
                                </div>';
                    }
                })
                ->rawColumns(['show','default'])
                ->toJson(true);
    }

    public function getUserSpecialInfo()
    {
        $template = DB::table('tbl_users')
        ->select('vchr_user_name','pk_int_api_template_id','vchr_api_template_title','text_api_template_description','int_default_api_template_id','int_api_template_status')
        ->join('tbl_api_template', 'tbl_users.pk_int_user_id', '=', 'tbl_api_template.fk_int_user_id')->orderby('pk_int_api_template_id','DESC')
        ->whereNull('tbl_api_template.deleted_at')
        ->where('tbl_users.pk_int_user_id',User::getVendorId())
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

        if ($template->int_api_template_status == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                     <a apitemplate-id="' . $template->pk_int_api_template_id . '" class="btn btn-info px-3"  href="'.url('/user/apitemplate').'"  data-toggle="tooltip" title="More Actions Click here" > <i class="f-16 la la-eye"></i></a>

                                   

                                     <a class="btn btn-success btn-activate  px-3 "   apitemplate-id="' . $template->pk_int_api_template_id . '" data-toggle="tooltip" title="Api template  is active"> <i class="f-18 la la-check-circle active"></i></a>
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                        <a apitemplate-id="' . $template->pk_int_api_template_id . '" class="btn btn-info px-3"  href="'.url('/user/apitemplate').'"  data-toggle="tooltip" title="More Actions Click here" > <i class="f-16 la la-eye"></i></a>

                                   
                                    <a class="btn btn-warning  px-3" apitemplate-id="' . $template->pk_int_api_template_id . '" data-toggle="tooltip" title="Admin approval pending"> <i class="f-18 la la-times-circle"></i></a>
                                </div>';
                    }
                })->addColumn('default', function ($template) {

        if ($template->int_default_api_template_id == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group" title="Default Api template">
                                   
                                    <button id="" apitemplate-id="' . $template->pk_int_api_template_id . '" class="btn btn-outline-success  px-3" style="color:green" >DEFAULT</button>
                                    <button class="btn btn-outline-success ks-no-text">
                                            <span class="la la-check ks-icon"></span>
                                        </button>
                                     
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    
                                     <button class="btn btn-outline-danger ks-no-text default-apitemplate" apitemplate-id="' . $template->pk_int_api_template_id . '" title="Inactive">
                                            <span class="la la-close ks-icon"></span>
                                        </button>
                                   
                                </div>';
                    }
                })
                ->rawColumns(['show','default'])
                ->toJson(true);
    }
     public function defaulApiTemplateId($id)
     {

        try
        { 
            DB::beginTransaction();
            
             $sender=ApiTemplate::find($id);
             $isActive=$sender->int_api_template_status;

            if($isActive!=ApiTemplate::ACTIVATE)
            {
                 return response()->json([
                                'status' => 'error',
                                'msg' =>'Could not set as default before admin approval',
                            ]);
            }

            $flag=false;
            $defaultSender = ApiTemplate::where('int_default_api_template_id',ApiTemplate::DEFAULT)->where('fk_int_user_id', User::getVendorId())->first();
            if(!empty($defaultSender))
            {
                $reserDefault=ApiTemplate::find($defaultSender->pk_int_api_template_id);

                $reserDefault->int_default_api_template_id=ApiTemplate::UNDEFAULT;
                $flag1=$reserDefault->save();
                if ($flag1) {
                    $flag=true;
                }
                if($flag)
                { 
                    
                    $sender->int_default_api_template_id=ApiTemplate::DEFAULT;
                     $flag2=$sender->save();
                     if($flag2)
                     {
                         DB::commit();
                         return response()->json([
                         'status' => 'success',
                         'msg' => 'set as default successfully'
                        ]);
                     }
                     else{
                     DB::rollBack();
                            return response()->json([
                                'status' => 'error',
                                'msg' =>'Could not set as default',
                            ]);

                     } 
                }
                else{
                     DB::rollBack();
                            return response([
                                'status' => 'error',
                                'message' =>'Could not set as default',
                            ], 400);

                } 

            }
            else
            {
                  $sender=ApiTemplate::find($id);
                    $sender->int_default_api_template_id=ApiTemplate::DEFAULT;
                     $flag2=$sender->save();
                     if($flag2)
                     {
                         DB::commit();
                         return response()->json([
                         'status' => 'success',
                         'msg' => 'set as default successfully'
                        ]);
                     }
                     else{
                     DB::rollBack();
                            return response()->json([
                                'status' => 'error',
                                'msg' =>'Could not set as default',
                            ]);

                     } 
            }
        }
        catch (\Exception $e) {
                DB::rollBack();
            // something went wrong whilst attempting to encode the token
            return response()->json(['status'=>'error','message'=> $e->getMessage()], 500);
        }
     }
}
