<?php

namespace App\Http\Controllers\User;

use App\AutomationRule;
use App\BackendModel\District;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\MailConfiguration;
use App\FrontendModel\LeadAdditionalField;
use App\FrontendModel\LeadAdditionalDetails;
use App\User;
use App\BackendModel\EnquiryStatus;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\EnquiryEmail;
use App\Mail\EnquiryUserEmail;
use App\Common\Common;
use App\Common\Notifications;
use App\Common\SingleSMS;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\Taluk;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Task;
use App\TaskCategory;
use App\Events\TaskAssigned;
use App\BackendModel\EmailTemplateHtml;
use App\BackendModel\LeadType;
use App\CloudTelephonySetting;
use App\DealStage;
use App\DealType;
use Getlead\Campaign\Models\CampaignLead;
use App\Events\CreateFollowup;
use App\FrontendModel\DealAdditionalField;
use Illuminate\Support\Str;
use App\Currency;
use App\TaskHistory;
use App\Traits\EnquiryTrait;
use App\Traits\PosTrait;
use App\Common\Variables;
use App\Http\Controllers\Guest\BonvoiceController;
use App\Http\Controllers\Guest\VoxbayController;
use App\Constants\MarbleGallery;
use App\Agency;
use App\Facades\FileUpload;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Symfony\Component\Mime\Address;

use function GuzzleHttp\json_decode;

class TimeLineController extends Controller
{
    use EnquiryTrait, PosTrait;

    protected bool $mailConfig = false;

    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            /**
             * @var User|null $user
             */
            $user = Auth::user();
            if ($user instanceof User) {
                //Get the mail settings.
                $settings = MailConfiguration::query()
                    ->where('vendor_id', $user->getBusinessId())
                    ->where('status', MailConfiguration::ACTIVATE)
                    ->first();

                if ($settings instanceof MailConfiguration) {
                    $this->mailConfig = true;
                    //settting up mail config for the logged in user.
                    config(['mail' => ['from' => ['address' => $settings->from_address, 'name' => $settings->from_name], 'host' => $settings->host, 'port' => $settings->port, 'username' => $settings->username, 'password' => $settings->password, 'encryption' => $settings->encryption, 'driver' => $settings->driver]]);
                }
            }
            return $next($request);
        });

    }

    public function getEnquiryTimelinePage(string $id)
    {
        $id = (int)$id;
        /**
         * @var User $user
         */
        $user = auth()->user();
        $vendorId = $user->getBusinessId();

        $enquiry = Enquiry::query()
            ->select([
                'vchr_customer_name',
                'followup_required',
                'vchr_enquiry_feedback',
                'vchr_customer_email',
                'vchr_customer_mobile',
                'vchr_customer_company_name',
                'et.vchr_enquiry_type as enquiry_type',
                'pk_int_enquiry_id',
                'tbl_enquiries.int_status',
                'fk_int_purpose_id',
                'vchr_purpose',
                'fk_int_enquiry_type_id',
                'vchr_color',
                'feedback_status',
                't_user.vchr_user_name',
                'tbl_enquiries.created_at',
                'tbl_enquiries.updated_at',
                'vchr_status',
                'tbl_users.vchr_user_name as created_by_user',
                'vchr_enquiry_feedback',
                'fb_ad_id',
                'fb_ad_info',
                'fk_int_purpose_id',
                'purchase_date',
                'exp_wt_grams',
                'tbl_enquiries.address',
                'tbl_enquiries.mobile_no',
                'tbl_enquiries.country_code',
                'tbl_enquiries.taluk_id',
                'tbl_enquiries.district_id',
                'tbl_enquiries.next_follow_up',
                'tbl_enquiries.model_id',
                'tbl_enquiries.purchase_plan',
                'tbl_enquiries.date_of_purchase',
                'tbl_enquiries.live_deal',
                'tbl_enquiries.remarks',
                'tbl_enquiries.competing_model',
                'tbl_enquiries.more_phone_numbers',
                'lead_type_id',
                'agency_id',
                'tbl_enquiries.read_status'])
            ->leftJoin('tbl_enquiry_types as et', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'et.pk_int_enquiry_type_id')
            ->leftJoin('tbl_enquiry_purpose as pp', 'tbl_enquiries.fk_int_purpose_id', '=', 'pp.pk_int_purpose_id')
            ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id', 'created_at')
            ->leftJoin('tbl_users', 'tbl_enquiries.created_by', '=', 'tbl_users.pk_int_user_id')
            ->leftjoin('tbl_users as t_user', 'tbl_enquiries.staff_id', '=', 't_user.pk_int_user_id')
            ->leftJoin('tbl_taluks', 'tbl_enquiries.taluk_id', '=', 'tbl_taluks.id')
            ->leftJoin('tbl_districts', 'tbl_enquiries.district_id', '=', 'tbl_districts.id')
            ->where('pk_int_enquiry_id', $id)
            ->where('tbl_enquiries.fk_int_user_id', $vendorId)
            ->with(['additionalDetails' => function ($q) {
                $q->select("id", "enquiry_id", "field_id", "field_name", "value");
            }])
            ->first();

        if (!$enquiry instanceof Enquiry) {
            abort(404);
        }

        $districts = District::query()
            ->select('id', 'name')
            ->where('vendor_id', $vendorId)
            ->where('status', 1)
            ->get();

        if (!$enquiry->read_status) {
            $enquiry->read_status = 1;
            $enquiry->save();
        }

        $enquiryFollowups = EnquiryFollowup::query()
            ->with(['user', 'added_by', 'assigned', 'custom_field_values', 'ivrdata'])
            ->where('enquiry_id', $id)
            ->orderBy('created_at', 'desc');

        $checkLog = (clone $enquiryFollowups)->whereIn('log_type', [1, 2, 3, 4, 13, 14, 18])->exists();
        $enquiryFollowups = $enquiryFollowups->get();

        // ----------------- Tasks activities -------------------
        $tasks = Task::query()
            ->select([
                'tasks.id',
                'tasks.name',
                'tasks.description',
                'tasks.scheduled_date',
                'tasks.task_category_id',
                'tasks.task_order',
                'tasks.assigned_to',
                'tasks.assigned_by',
                'tasks.vendor_id',
                'tasks.enquiry_id',
                'tasks.call_status_id',
                'tasks.description',
                'tasks.comment',
                'tasks.status',
                'tasks.created_at',
                'tasks.campaign_id',
                'task_categories.name as task_category',
                'tbl_enquiries.vchr_customer_name as customer_name',
                'tbl_enquiries.vchr_customer_mobile',
                'tbl_users.vchr_user_name as assigned_user_name',
                't_user.vchr_user_name as assigned_by_user'
            ])
            ->join('task_categories', 'task_category_id', '=', 'task_categories.id')
            ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
            ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->leftjoin('tbl_users as t_user', 'assigned_by', '=', 't_user.pk_int_user_id')
            ->where('tasks.vendor_id', $vendorId)
            ->where('enquiry_id', $id)
            ->orderBy('tasks.id', 'DESC')
            ->get();

        $checkTask = $tasks->isNotEmpty();
        $enquiryFollowups = collect($tasks)->merge($enquiryFollowups)->sortByDesc('created_at');
        // -----------------End Tasks activitiess-------------------//

        // ----------------- Deals activitiess-------------------//
        $deal = $enquiry->deal()->withTrashed();
        $dealData = (clone $deal)->selectRaw('COUNT(*) as total_deals, SUM(deal_amount) as total_amount')->first();
        $totalDeals = $dealData->total_deals;
        $totalAmount = $dealData->total_amount;
        $message = 'Total ' . $totalDeals . ' ' . ($totalDeals > 1 ? getDealName() : getDealName(true)) . ' & Amount: ';
        $message1 = $totalAmount;
        $deal = $deal->get();
        $checkDeal = $deal->isNotEmpty();
        $enquiryFollowups = collect($deal)->merge($enquiryFollowups);
        // -----------------End Deals activitiess-------------------//

        // ----------------- Visits activitiess-------------------//
        $visit = $enquiry->visit;
        $enquiryFollowups = collect((clone $visit))->merge($enquiryFollowups)->sortByDesc('created_at');
        // -----------------End Visits activities -------------------//

        $sortedarray = array();

        foreach ($enquiryFollowups as $member) {
            $member['group_id'] = $member->created_at?->format('F Y');
            $groupid = $member['group_id'];
            if (isset($sortedarray[$groupid])) {
                $sortedarray[$groupid][] = $member;
            } else {
                $sortedarray[$groupid] = array($member);
            }
        }
        $enquiryFollowups = $sortedarray;

        $staffId = User::active()
            ->where(function ($query) use ($vendorId) {
                $query->where('parent_user_id', $vendorId)
                    ->orWhere('pk_int_user_id', $vendorId);
            })
            ->select('pk_int_user_id', 'vchr_user_name', 'parent_user_id', 'int_role_id', 'int_status')
            ->get();

        $agents = $staffId;
        $enquiry_status = FeedbackStatus::UsedStatuses($vendorId)->select('fk_int_user_id', 'pk_int_feedback_status_id', 'vchr_status')->get();
        $enquiry_purpose = EnquiryPurpose::where('fk_int_user_id', $vendorId)->select('fk_int_user_id', 'pk_int_purpose_id', 'vchr_purpose')->get();
        $additional_details = $enquiry->additionalDetails->pluck('value', 'field_id');
        $shipRocketTrackingDetails = [];
        $additional_fields = LeadAdditionalField::where('vendor_id', $vendorId)->with('additionalPurpose')->get();

        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
        }
        $setting_currency = Variables::checkCurrencyEnableSetting('currency');
        $currency = $setting_currency ? Currency::where('id', $setting_currency)->value('code') : '₹';
        $lead_types = LeadType::where('vendor_id', $vendorId)->get();
        $moreNumbers = ($enquiry->more_phone_numbers) ? (!is_array($enquiry->more_phone_numbers) ? $enquiry->more_phone_numbers : json_decode($enquiry->more_phone_numbers, true)) : null;

        // ----------------------- Deals add popup data ------------------//
        $dealTypes = DealType::where('fk_int_user_id', $vendorId)->select('fk_int_user_id', 'pk_int_deal_type_id', 'deal_type_name')->get();
        $dealStatus = DealStage::where('fk_int_user_id', $vendorId)->select('fk_int_user_id', 'pk_int_deal_stage_id', 'deal_stage_name')->get();
        $currencies = Currency::where('status', 1)->get();
        $additional_fieldsDeal = DealAdditionalField::where('vendor_id', $vendorId)->get();
        foreach ($additional_fieldsDeal as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
        }
        $additionalRequiredDropdown = (clone $additional_fieldsDeal)->where('input_type', 2)->where('is_required', 1)->values();
        // ---------------------------- End Deals add popup data ------------------- //

        $page = 'lead';

        $mailTemplates = EmailTemplateHtml::query()
            ->where('user_id', $user->getBusinessId())
            ->where('status', EmailTemplateHtml::ACTIVATE)
            ->select('id', 'template_title', 'status', 'user_id')
            ->get();

        $mailSettings = MailConfiguration::query()
            ->select('vendor_id', 'default', 'status')
            ->where('vendor_id', $user->getBusinessId())
            ->where('status', MailConfiguration::ACTIVATE)
            ->exists();

        $telephony = CloudTelephonySetting::query()
            ->where('default', 1)
            ->where('vendor_id', $vendorId)
            ->first();

        $agencies = Agency::query()
            ->where('vendor_id', $vendorId)
            ->select('id', 'name')
            ->get();

        return view('backend.user-pages.timeline.time-line')
            ->with('enquiry', $enquiry)
            ->with('enquiry_status', $enquiry_status)
            ->with('staffId', $staffId)
            ->with('enquiryFollowups', $enquiryFollowups)
            ->with('additional_details', $additional_details)
            ->with('additional_fields', $additional_fields)
            ->with('enquiry_purpose', $enquiry_purpose)
            ->with('districts', $districts)
            ->with('tracking_data', $shipRocketTrackingDetails)
            ->with('message', $message)
            ->with('message1', $message1)
            ->with('moreNumbers', $moreNumbers)
            ->with('lead_types', $lead_types)
            ->with('dealTypes', $dealTypes)
            ->with('dealStatus', $dealStatus)
            ->with('agents', $agents)
            ->with('additionalRequiredDropdown', $additionalRequiredDropdown)
            ->with('additional_fieldsDeal', $additional_fieldsDeal)
            ->with('page', $page)
            ->with('checkTask', $checkTask)
            ->with('checkLog', $checkLog)
            ->with('checkDeal', $checkDeal)
            ->with('currency', $currency)
            ->with('mailTemplates', $mailTemplates)
            ->with('mailSettings', $mailSettings)
            ->with('telephony', $telephony)
            ->with('agencies', $agencies)
            ->with('currencies', $currencies);
    }

    /**
     * @param $enqId
     * @param $timelineId
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * Get Timeline edit page
     */
    public function getEditPage($enqId, $timelineId)
    {
        $vendorId = User::getVendorId();
        $enq = Enquiry::where('pk_int_enquiry_id', $enqId)->where('fk_int_user_id', $vendorId)->first();
        if (!$enq)
            abort(404);
        if (Auth::user()->int_role_id == User::STAFF && Auth::user()->is_co_admin == 0) {
            $enq = Enquiry::where('pk_int_enquiry_id', $enqId)->where('staff_id', Auth::user()->pk_int_user_id)->first();
            if (!$enq)
                abort(404);
        }
        $enquiry = Enquiry::leftJoin('tbl_enquiry_types as et', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'et.pk_int_enquiry_type_id')
            ->leftJoin('tbl_enquiry_purpose as pp', 'tbl_enquiries.fk_int_purpose_id', '=', 'pp.pk_int_purpose_id')
            ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id', 'created_at')
            ->leftJoin('tbl_users', 'tbl_enquiries.created_by', '=', 'tbl_users.pk_int_user_id')
            ->select('vchr_customer_name', 'vchr_enquiry_feedback', 'vchr_customer_email', 'vchr_customer_mobile', 'vchr_customer_company_name', 'et.vchr_enquiry_type as enquiry_type', 'pk_int_enquiry_id', 'tbl_enquiries.int_status', 'fk_int_purpose_id', 'vchr_purpose', 'fk_int_enquiry_type_id', 'vchr_color', 'feedback_status', 'vchr_user_name', 'tbl_enquiries.created_at', 'tbl_enquiries.updated_at', 'vchr_status', 'tbl_users.vchr_user_name as created_by_user', 'tbl_enquiries.next_follow_up')
            ->where('tbl_enquiries.pk_int_enquiry_id', $enqId)
            ->first();
        $staffId = User::where('int_role_id', User::STAFF)->where('parent_user_id', $vendorId)->get();
        $enquiryFollowup = EnquiryFollowup::find($timelineId);
        if ($enquiryFollowup) {
            $enquiryFollowup->time = Carbon::parse($enquiryFollowup->date)->format('g:i A');
            $enquiryFollowup->date = Carbon::parse($enquiryFollowup->date)->format('m/d/Y');
        }
        return view('backend.user-pages.timeline.edit-time-line')
            ->with('enquiry', $enquiry)
            ->with('staffId', $staffId)
            ->with('enquiryFollowup', $enquiryFollowup);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * Add Timeline Status
     */
    public function addStatus(Request $request)
    {
        try {
            $input = $request->all();
            $vendorId = User::getVendorId();
            $enquiry = Enquiry::find($input['id']);
            $old_status_id = $enquiry->feedback_status;
            $enquiry->feedback_status = $input['status'];
            $enquiry->updated_at = now();
            $flag = $enquiry->save();

            try {
                $enquiryfollowups = event(new CreateFollowup($input['status'], EnquiryFollowup::TYPE_STATUS, $input['id'], Auth::user()->pk_int_user_id, $old_status_id));
                $enquiryfollowups = $enquiryfollowups[1];
            } catch (\Exception $e) {
                \Log::info('add status:' . $e->getMessage());
            }


            Enquiry::statusChangeFunction($enquiry);

            $commonObj = new Common();
            $status = FeedbackStatus::where('pk_int_feedback_status_id', $input['status'])->select('vchr_status')
                ->first();
            if ($status) {
                $lead_status = $status->vchr_status;
            } else {
                $lead_status = "New Status";
            }
            $post_data = [
                'customer_name' => $enquiry->vchr_customer_name,
                'email' => $enquiry->vchr_customer_email,
                'status' => $lead_status,
                'phone' => $enquiry->vchr_customer_mobile,
                'mobile' => $enquiry->mobile_no,
                'flag' => "status_change",
                'state' => ($vendorId == 1119) ? $enquiry->additional_details->where('field_id', '=', 317 /* state id is for bazani */)->value('value') ?? null : null,
            ];

            /**--WEBHOOK---------------------------------------------**/
            $automation_rule = $commonObj->getRule($vendorId, 'status_change', 'webhook');
            if (count($automation_rule) > 0) {
                foreach ($automation_rule as $w_hook) {
                    if ($w_hook->webhook_id != NULL && $w_hook->feedback_status_id == $input['status']) {
                        $webHook = $commonObj->getWebHookById($w_hook->webhook_id);
                        if ($webHook) {
                            try {
                                $commonObj->postToWebHook($webHook->url, $post_data);
                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                            }
                        }
                    }
                }
            }

            /**--WEBHOOK---------------------------------------------**/
            /**--WHATSAPP---------------------------------------------**/
            $automation_rule_whatsapp = $commonObj->getRule($vendorId, 'status_change', 'whatsapp');

            /**--API---------------------------------------------**/
            $automation_rule_api = AutomationRule::where('vendor_id', $vendorId)
                ->where('trigger', 'status_change')
                ->where('action', 'api')
                ->where('feedback_status_id', $input['status'])
                ->orderBy('id', 'DESC')
                ->first();
            if ($automation_rule_api) {
                if ($automation_rule_api->api != NULL) {
                    try {
                        $commonObj->postToWebHook($automation_rule_api->api, $post_data);
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }
            }
            /**--API---------------------------------------------**/
            /**--TASK---------------------------------------------**/
            $automation_rule_task = $commonObj->getRuleStatusChange($vendorId, 'status_change', 'task', $input['status']);
            if ($automation_rule_task) {
                $input = [
                    'name' => $automation_rule_task->task_title,
                    'description' => $automation_rule_task->task_description,
                    'scheduled_date' => Carbon::tomorrow(),
                    'task_category_id' => $automation_rule_task->task_category_id,
                    'assigned_to' => $automation_rule_task->task_assigned_to,
                    'assigned_by' => $vendorId,
                    'vendor_id' => $vendorId,
                    'enquiry_id' => $enquiry->pk_int_enquiry_id,
                    'status' => 0,
                ];
                Task::create($input);
            }
            /**--TASK---------------------------------------------**/

            // Prepare some data for ajax response
            $status_log_html = view('backend.user-pages.timeline.status.enquiry-status', [
                'enquiry_status' => FeedbackStatus::UsedStatuses($vendorId)->get(),
                'ef' => $enquiryfollowups,
            ])->render();

            $automation_rule_email = AutomationRule::where('vendor_id', $vendorId)
                ->where('trigger', 'status_change')
                ->where('action', 'email')
                ->where('feedback_status_id', $input['status'])
                ->orderBy('id', 'DESC')
                ->first();
            if ($automation_rule_email) {
                AutomationRule::sendMail($enquiry->pk_int_enquiry_id, $automation_rule_email);
            }

            return response()->json([
                'status' => true,
                'status_log_html' => $status_log_html,
            ]);
        } catch (\Exception $e) {
            \Log::info($e->getMessage());
        }
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * Add Timeline Status
     */

    public function addAgency(Request $request)
    {
        $input = $request->all();

        $enquiry = Enquiry::where('pk_int_enquiry_id', $input['id'])->first();
        $enquiry->agency_id = $input['agency'];
        $enquiry->updated_at = now();
        $enquiry->save();

        try {
            $enquiryfollowups = event(new CreateFollowup($input['agency'], EnquiryFollowup::TYPE_AGENCY, $input['id'], Auth::user()->pk_int_user_id));
            $enquiryfollowups = $enquiryfollowups[1];
        } catch (\Exception $e) {
            \Log::info($e->getMessage());
        }

        return response()->json(['msg' => "Update agency", 'status' => 'success']);
    }

    public
    function addEnquiryPurpose(Request $request)
    {
        $input = $request->all();

        try {
            $enquiryfollowups = event(new CreateFollowup($input['enq_purpose'], EnquiryFollowup::ENQ_PURPOSE, $input['id'], Auth::user()->pk_int_user_id));
            $enquiryfollowups = $enquiryfollowups[1];
        } catch (\Exception $e) {
            \Log::info('add purpose:' . $e->getMessage());
        }

        $enquiry = Enquiry::where('pk_int_enquiry_id', $input['id'])->first();
        $enquiry->fk_int_purpose_id = $input['enq_purpose'];
        $enquiry->updated_at = now();
        $enquiry->save();

        Enquiry::handlePurposeChangeAutomation($enquiry);


        // Prepare some data for ajax response
        $status_log_html = view('backend.user-pages.timeline.status.enquiry-purpose', [
            'enquiry_purpose' => EnquiryPurpose::where('fk_int_user_id', User::getVendorId())->get(),
            'ef' => $enquiryfollowups,
        ])->render();

        return response()->json([
            'status' => true,
            'status_log_html' => $status_log_html,
        ]);
    }

    public function addEnqType(Request $request)
    {
        $input = $request->all();
        $enquiry = Enquiry::where('pk_int_enquiry_id', $input['id'])->first();
        $old_lead_type_id = $enquiry->lead_type_id;
        $enquiry->lead_type_id = $input['lead_type'];
        $enquiry->updated_at = now();
        $enquiry->save();
        $note = '';
        if ($input['lead_type'] && $input['lead_type'] != $old_lead_type_id) {
            $old_lead_type = LeadType::where('id', $old_lead_type_id)->first();
            $lead_type = LeadType::where('id', $input['lead_type'])->first();
            if ($old_lead_type)
            {
                $note = 'Lead Type Changed from ' . ($old_lead_type ? $old_lead_type->name : 'NA') . ' to ' . ($lead_type ? $lead_type->name : '');
            }
            else
            {
                $note = 'Lead Type Changed';
            }
        }
        if (isset($note)) {
            $enquiryfollowup = new EnquiryFollowup();
            $enquiryfollowup->note = $note;
            $enquiryfollowup->log_type = EnquiryFollowup::TYPE_ACTIVITY;
            $enquiryfollowup->enquiry_id = $input['id'];
            $enquiryfollowup->created_by = auth()->user()->pk_int_user_id;
            $enquiryfollowup->save();
        }


        // Prepare some data for ajax response
        $status_log_html = view('backend.user-pages.timeline.status.enquiry-type', [
            'enquiry_type' => LeadType::where('vendor_id', User::getVendorId())->get(),
            'ef' => $enquiryfollowup,
        ])->render();

        return response()->json([
            'status' => true,
            'status_log_html' => $status_log_html,
        ]);
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * Add Task
     */

    public function addTask(Request $request)
    {
        $input = $request->all();
        $category = TaskCategory::where('id', $request->task)->first();

        if ($input['date'] != " " && $input['time'] != "") {
            $date_time =Carbon::parse($input['date'])->format('Y-m-d'). " " . Carbon::createFromFormat('H:i A', $input['time'])->toTimeString();
        }
     
        $task_data = [
            'name' => $request->name,
            'description' => $request->description,
            'task_category_id' => $category->id,
            'assigned_to' => $request->user_id ?? Auth::user()->pk_int_user_id,
            'assigned_by' => Auth::user()->pk_int_user_id,
            'vendor_id' => User::getVendorId(),
            'enquiry_id' => $request->id,
            'comment' => $request->comment,
            'status' => 0,
        ];
        if ($input['date'] != " " && $input['time'] != "")
        {
            $task_data['scheduled_date'] = $date_time ?? now();
        }
        else
        {
            $task_data['scheduled_date'] = now();
        }

        //  change call status
        if ($category->id == 2) {
            $task = Task::callTaskClose($request->id, User::getVendorId(), Auth::user()->pk_int_user_id);
            if ($task == null) {
                $task = Task::create($task_data);
            } else {
                return response()->json(['msg' => "You already have a call task", 'status' => 'failed']);
            }
        } else {
            $task = Task::create($task_data);
        }
        //Create Task


        // $enquiryfollowups->task_id = $task->id;
        // $enquiryfollowups->save();
        //
        //Get VendorId
        $enquiry = Enquiry::where('pk_int_enquiry_id', $input['id'])->first();
        $vendorId = $enquiry->fk_int_user_id;
        $enquiry->updated_at = now();
        $enquiry->save();
        //Notifications
        event(new TaskAssigned($task));

        //-----------------
        return response()->json(['msg' => "Next follow up added.", 'status' => 'success']);
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * Update Task
     */

    public
    function updateTask(Request $request, $id)
    {
        $category = TaskCategory::where('id', $request->task)->first();
        $task = Task::find(request('task_id'));
        $input = $request->all();
        $vendorId = User::getVendorId();
        // $enquiryfollowups = EnquiryFollowup::find($id);
        $oldDate = $task->scheduled_date;
        if ($task) {
            if ($input['date'] != " " && $input['time'] != "") {
                $date_time = Carbon::createFromFormat('m/d/Y', $input['date'])->toDateString() . ' ' . Carbon::createFromFormat('H:i A', $input['time'])->toTimeString();
            }

            $task_data = [
                'name' => $request->name,
                'description' => $request->description,
                'task_category_id' => $category->id,
                'assigned_to' => $request->assigned_to ?? Auth::user()->pk_int_user_id,
                'assigned_by' => Auth::user()->pk_int_user_id,
                'vendor_id' => $vendorId,
                'comment' => $request->comment,
            ];
            if ($input['date'] != " " && $input['time'] != "")
            {
                $task_data['scheduled_date'] = $date_time;
            }
            else{
                $task_data['scheduled_date'] = now();
            }

            if ($task->task != 2) {
                if ($task->task_category_id != 2) {
                    $exist = Task::nonCampaign()
                        ->where('enquiry_id', $id)
                        ->where('status', 0)
                        ->where('task_category_id', 2)
                        ->whereNotIn('id', [$task->id])
                        ->first();

                    if ($exist) {
                        return response()->json(['status' => false, 'message' => 'Exist call task']);
                    }
                }
            }

            // if ($task_id) {
            $task->update($task_data);
            TaskHistory::create(['task_id' => $task->id,
                'task_status' => 'Task Updated',
                'updated_by' => Auth::id(),
                'vendor_id' => $vendorId,
                'comment' => $request->comment ? $request->comment : $request->description,
                'date' => Carbon::today()
            ]);

            //Get VendorId
            $enquiry = Enquiry::where('pk_int_enquiry_id', $id)->first();
            $vendorId = $enquiry->fk_int_user_id;
            $enquiry->updated_at = now();
            $enquiry->save();
            //Notifications
            //return $oldDate."-".$date_time;
            if ($oldDate != $date_time) {
                $userObject = User::getUserDetails($vendorId);
                $userAdminObject = User::getSingleAdminDetails();
                $notifications = new Notifications();
                $from = env('MAIL_FROM_ADDRESS');
                $to = $userObject->email;
                $subject = "Tasks";
                $name = $userObject->vchr_user_name;
                $logo = $userAdminObject->vchr_logo;
                $attachment = "";
                $telegramId = $userObject->telegram_id;
                $mobileNumber = $userObject->vchr_user_mobile;
                $defaultSenderIdAdmin = SingleSMS:: getSenderid($userAdminObject->pk_int_user_id, '');
                $defaultRouteAdmin = SingleSMS:: getRoute($userAdminObject->pk_int_user_id, '');
                if ($input['assigned_to'] != NULL) {
                    $assignedStaff = " assigned to " . User::getUserDetails($input['assigned_to'])->vchr_user_name;
                } else {
                    $assignedStaff = "";
                }
                $content1 = "Task (" . $input['name'] . ") on " . Carbon::parse($oldDate)->format('M d') . " at " . Carbon::parse($oldDate)->format('g:i a') . " updated to " . Carbon::parse($date_time)->format('M d') . " at " . Carbon::parse($date_time)->format('g:i a') . $assignedStaff;
                $content2 = "You have task (" . $input['name'] . ") on " . Carbon::parse($oldDate)->format('M d') . " at " . Carbon::parse($oldDate)->format('g:i a') . " updated to " . Carbon::parse($date_time)->format('M d') . " at " . Carbon::parse($date_time)->format('g:i a') . $assignedStaff;
                $dataSend['message'] = $content1;
                $dataSend['user_id'] = $input['assigned_to'] ?? $vendorId;
                $dataSend['page'] = 'task';
                $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $vendorId, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin, $dataSend);
            }
            //-----------------
            return response()->json(['status' => true, 'message' => 'Exist call task']);
            return redirect('user/enquiry-timeline/' . $task->enquiry_id);
        } else {
            return back();
        }
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Add Timeline Note
     */
    public
    function addNote(Request $request)
    {
        $input = $request->all();
        if ($input['note'] != '') {
            try {
                $enquiry = Enquiry::find($input['id']);
                $enquiry->lead_attension = 0;
                $enquiry->updated_at = now();
                $enquiry->update();
                $timelineNote = event(new CreateFollowup($input['note'], EnquiryFollowup::TYPE_NOTE, $input['id'], Auth::user()->pk_int_user_id));
                $timelineNote = $timelineNote[1];
            } catch (\Exception $e) {
                \Log::info('add note:' . $e->getMessage());
            }

        } else {
            return response()->json(['msg' => "Note is required", 'status' => false]);
        }
        if ($timelineNote) {
            return response()->json(['msg' => "Success.", 'status' => true]);
        } else {
            return response()->json(['msg' => "Something went wrong, please try again later", 'status' => false]);
        }
    }

    /**
     * @param Request $request
     * @param $timelineId
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * Update Timeline note
     */
    public
    function updateNote(Request $request, $timelineId)
    {
        $note = EnquiryFollowup::find($timelineId);
        if ($note) {
            $note->note = $request->note;
            $note->updated_by = Auth::user()->pk_int_user_id;
            $note->save();

            $enquiry = Enquiry::find($note->enquiry_id);
            $enquiry->lead_attension = 0;
            $enquiry->updated_at = now();
            $enquiry->update();

            return redirect('user/enquiry-timeline/' . $note->enquiry_id);
        } else {
            return back();
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Add Log
     */
    public
    function addLog(Request $request)
    {
        $input = $request->all();
        $vendorId = User::getVendorId();
        $limelineLog = new EnquiryFollowup();

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $extension = $file->getClientOriginalExtension();
            $originalFilename = $file->getClientOriginalName();
            $name = explode('.', $originalFilename)[0];
            $slug = Str::slug($name);
            $originalName = Str::random(3) . substr(time(), 6, 8) . '-' . $slug . '.' . $extension;

            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                $type_note = EnquiryFollowup::TYPE_FILE_NOTE;
            } else if (in_array($extension, ['pdf', 'docx', 'csv'])) {
                $type_note = EnquiryFollowup::TYPE_DOCUMENT;
            } else if (in_array($extension, ['mpga', 'mp3', 'wav', 'aac', 'mp4', 'm4a', 'awb'])) {
                $type_note = EnquiryFollowup::TYPE_VOICE_NOTE;
            } else {
                $type_note = EnquiryFollowup::TYPE_FILE_NOTE;
            }
            if ($type_note == EnquiryFollowup::TYPE_FILE_NOTE) {
                $path = 'file_notes/' . $vendorId . '/' . $originalName;
                $filename = \Storage::disk('s3')->put($path, file_get_contents($file));
                $limelineLog->note = $path;
                $limelineLog->log_type = EnquiryFollowup::TYPE_FILE_NOTE;
            } else if ($type_note == EnquiryFollowup::TYPE_DOCUMENT) {
                $path = 'document_notes/' . $vendorId . '/' . $originalName;
                $filename = \Storage::disk('s3')->put($path, file_get_contents($file));
                $limelineLog->note = $path;
                $limelineLog->log_type = EnquiryFollowup::TYPE_DOCUMENT;
            } else if ($type_note == EnquiryFollowup::TYPE_VOICE_NOTE) {
                $path = 'voice_notes/' . $vendorId . '/' . $originalName;
                $filename = \Storage::disk('s3')->put($path, file_get_contents($file));
                $limelineLog->note = $path;
                $limelineLog->log_type = EnquiryFollowup::TYPE_VOICE_NOTE;
            }

            $limelineLog->created_by = Auth::user()->pk_int_user_id;
            $limelineLog->response = $input['answer'];
            $limelineLog->enquiry_id = $input['enquiry_id'];
            if ($input['date'] != " " && $input['time'] != "") {
                $date_time = date('Y-m-d', strtotime($input['date'])) . " " . ($input['time']) ? Carbon::createFromFormat('H:i A', $input['time'])->toTimeString() : '';
                $limelineLog->date = $date_time;
            }
            $limelineLog->save();
        } else {
            $limelineLog->note = $input['note'];
            $limelineLog->created_by = Auth::user()->pk_int_user_id;
            $limelineLog->log_type = $input['log_type'] ?? EnquiryFollowup::TYPE_NOTE;
            $limelineLog->response = $input['answer'];
            $limelineLog->enquiry_id = $input['enquiry_id'];
            if ($input['date'] != " " && $input['time'] != "") {
                // $date_time = date('m/d/Y', strtotime($input['date']))." ". ($input['time']) ? Carbon::createFromFormat('H:i A', $input['time'])->toTimeString() : '';
                $date_time = Carbon::createFromFormat('m/d/Y', $input['date'])->toDateString() . ' ' . Carbon::createFromFormat('H:i A', $input['time'])->toTimeString();
                $limelineLog->date = $date_time;
            }
            $limelineLog->save();
        }

        $enquiry = Enquiry::find($input['enquiry_id']);
        $enquiry->lead_attension = 0;
        $enquiry->updated_at = now();
        $enquiry->update();

        // Prepare some data for ajax response
        $response = [];
        if ($limelineLog) {
            $response['msg'] = 'Success.';
            $response['status'] = true;
        } else {
            $response['msg'] = 'Something went wrong, plese try again later.';
            $response['status'] = false;
        }

        switch ($limelineLog->log_type) {
            case EnquiryFollowup::TYPE_NOTE:
                $blade = 'enquiry-log-note';
                break;
            case EnquiryFollowup::TYPE_VOICE_NOTE:
                $blade = 'enquiry-log-note';
                break;
            case EnquiryFollowup::TYPE_DOCUMENT:
                $blade = 'enquiry-log-note';
                break;
            case EnquiryFollowup::TYPE_FILE_NOTE:
                $blade = 'enquiry-log-note';
                break;
            case EnquiryFollowup::TYPE_LOG_CALL:
                $blade = 'enquiry-log-call';
                break;

            case EnquiryFollowup::TYPE_LOG_EMAIL:
                $blade = 'enquiry-log-email';
                break;

            case EnquiryFollowup::TYPE_LOG_MEETING:
                $blade = 'enquiry-log-meeting';
                break;

            default:
                throw new \Exception('This type is not yet catched for ajax requests!');
        }

        $response['status_log_html'] = view('backend.user-pages.timeline.status.' . $blade, ['ef' => $limelineLog])->render();

        return response()->json($response);
    }


    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * update call
     */
    public
    function updateCall(Request $request, $id)
    {
        $enquiry_follow_up = EnquiryFollowup::find($id);
        if ($enquiry_follow_up) {
            $enquiry_follow_up->note = $request->note;
            $enquiry_follow_up->response = $request->response;
            if ($request->date != " " && $request->time != "") {
                $date_time = Carbon::createFromFormat('m/d/Y', $request->date)->toDateString() . ' ' . Carbon::createFromFormat('H:i A', $request->time)->toTimeString();
                $enquiry_follow_up->date = $date_time;
            }
            $enquiry_follow_up->updated_by = Auth::user()->pk_int_user_id;
            $enquiry_follow_up->save();
            return redirect('user/enquiry-timeline/' . $enquiry_follow_up->enquiry_id);
        } else {
            return back();
        }
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * Update Email
     */
    public
    function updateEmail(Request $request, $id)
    {
        $enquiry_follow_up = EnquiryFollowup::find($id);
        if ($enquiry_follow_up) {
            $enquiry_follow_up->note = $request->note;
            if ($request->date != " " && $request->time != "") {
                $date_time = Carbon::createFromFormat('m/d/Y', $request->date)->toDateString() . ' ' . Carbon::createFromFormat('H:i A', $request->time)->toTimeString();
                $enquiry_follow_up->date = $date_time;
                $enquiry_follow_up->updated_by = Auth::user()->pk_int_user_id;
                $enquiry_follow_up->save();
            }
            return redirect('user/enquiry-timeline/' . $enquiry_follow_up->enquiry_id);
        } else {
            return back();
        }
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * Update meeting
     */
    public
    function updateMeeting(Request $request, $id)
    {
        $enquiry_follow_up = EnquiryFollowup::find($id);
        if ($enquiry_follow_up) {
            $enquiry_follow_up->note = $request->note;
            if ($request->date != " " && $request->time != "") {
                $date_time = Carbon::createFromFormat('m/d/Y', $request->date)->toDateString() . ' ' . Carbon::createFromFormat('H:i A', $request->time)->toTimeString();
                $enquiry_follow_up->date = $date_time;
                $enquiry_follow_up->updated_by = Auth::user()->pk_int_user_id;
                $enquiry_follow_up->save();
            }
            return redirect('user/enquiry-timeline/' . $enquiry_follow_up->enquiry_id);
        } else {
            return back();
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Add Schedule
     */
    public
    function addSchedule(Request $request)
    {
        EnquiryFollowup::addSchedule($request->all(), Auth::user()->pk_int_user_id);
    }

    /**
     * @param Request $request
     * @param $timelineId
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * Update Time line schedule
     */
    public
    function updateSchedule(Request $request, $id)
    {
        $input = $request->all();
        $enquiryfollowups = EnquiryFollowup::find($id);
        if ($enquiryfollowups) {
            EnquiryFollowup::updateSchedule($input, $id, Auth::user()->pk_int_user_id);

            return redirect('user/enquiry-timeline/' . $enquiryfollowups->enquiry_id);
        } else {

            return back();
        }
    }

// * Send Email
    public
    function sendEmail(Request $request, $id)
    {
        $input = $request->all();
        $vendorId = User::getVendorId();
        if (!$this->mailConfig)
        {
            return response()->json(['msg' => "Please Configure Your Email", 'status' => 'fail']);
        }

        $text_content = $input['text_content'] ?? '';
        $feedback = Enquiry::find($id);
        $enquiryfollowups = new EnquiryFollowup();
        $enquiryfollowups->log_type = EnquiryFollowup::TYPE_EMAIL;
        $enquiryfollowups->enquiry_id = $id;
        $enquiryfollowups->task_type = $input['vchr_subject'];
        $enquiryfollowups->response = $input['vchr_cc_email'];
        $enquiryfollowups->bcc = $input['vchr_bcc_email'];
        $enquiryfollowups->duration = $feedback->vchr_customer_email;
        $enquiryfollowups->created_by = Auth::user()->pk_int_user_id;

        $users = User::select('*')->where('pk_int_user_id', $vendorId)->first();
        $logo = FileUpload::viewFile('uploads/user-profile/' . $users->vchr_logo, 's3');
        try {
            $settings = MailConfiguration::where('vendor_id', $vendorId)/* ->where('default', MailConfiguration::DEFAULT) */ ->where('status', MailConfiguration::ACTIVATE)->first();
            if ($settings) {
                $enq = Enquiry::where('pk_int_enquiry_id', $id)->first();
                $email = new EnquiryEmail();
                $email->vchr_subject = $input['vchr_subject'];
                $email->fk_int_user_id = $vendorId;
                $email->fk_int_enquiry_id = $id;
                $email->int_status = EnquiryEmail::ACTIVATE;

                if ($request->hasFile('vchr_attachment')) {
                    $image = $request->file('vchr_attachment');
                    $name = mt_rand() . '.' . $image->getClientOriginalExtension();
                    $path = 'uploads/email/';
                    FileUpload::uploadFile($image, $path, $name, 's3');
                    $email->vchr_attachment = $name;
                    $enquiryfollowups->name = $name;
                } else {
                    $path = "";
                }
                $from_name = $input['from_name'] ?? $settings->from_name;
                $from_email = $settings->from_address;
                $data['email'] = $enq->vchr_customer_email;
                $data['subject'] = $input['vchr_subject'];
                $data['content'] = $text_content;
                $data['name'] = $enq->vchr_customer_name;
                $data['from'] = $from_email;
                $data['from_name'] = $from_name;
                $data['logo'] = $logo;
                if ($path != "") {
                    $data['path'] = FileUpload::viewFile('uploads/email/' . $name, 's3');
                } else {
                    $data['path'] = "";
                }
                $data['cc'] = $input['vchr_cc_email'];
                $data['bcc'] = $input['vchr_bcc_email'];
                $data['reply_email'] = $from_email;
                $data['reply_name'] = $from_name;
                $emailTemplate = new Common();

                if ($input['toggle'] != 'template') {
                    $validator = validator($input, EnquiryEmail::$rule, EnquiryEmail::$message);
                    if ($validator->passes()) {
                        $data['template'] = "";
                    } else {
                        $errorsArray = $validator->messages()->all();

                        // Join the array into a single string, each error on a new line
                        $errorsString = implode("\n", $errorsArray);

                        return response()->json(['msg' => $errorsString, 'status' => 'fail']);
                    }
                } else {
                    $data['template'] = $emailTemplate->emailTemplate($vendorId, $data['name'], $data['content'], $input['templete_id'], $enq);
                    $text_content = $data['template'];
                }

                config([
                    'mail.driver' => $settings->driver,
                    'mail.host' => $settings->host,
                    'mail.port' => $settings->port,
                    'mail.username' => $settings->username,
                    'mail.password' => $settings->password,
                    'mail.encryption' => $settings->encryption,
                    'mail.from.address' => $from_email,
                    'mail.from.name' => $from_name
                ]);

                // Set dynamic mail configuration based on request inputs
                $mailConfig = [
                    'transport' => 'smtp',
                    'host' => $settings->host,
                    'port' => $settings->port,
                    'encryption' => $settings->encryption,
                    'username' => $settings->username,
                    'password' => $settings->password,
                    'from' => [
                        'address' => $settings->from_address, // Make sure 'from' matches 'username'
                        'name' => $from_name,
                    ],
                ];

                // Apply dynamic configuration
                config(['mail.mailers.smtp' => $mailConfig]);
                config(['mail.from.address' => $mailConfig['from']['address']]);
                config(['mail.from.name' => $mailConfig['from']['name']]);

                Mail::mailer('smtp')->to(new Address($data['email'], $data['name']))
                    ->send(new EnquiryUserEmail($data));

                $enquiryfollowups->note = $text_content;
                $email->text_content = $text_content;
                $enquiryfollowups->save();
                $flag = $email->save();

                return response()->json(['msg' => 'Email Send Successfully.', 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Please Configure Your Email", 'status' => 'fail']);
            }
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return response()->json(['msg' => $e->getMessage(), 'status' => 'fails']);

        }
        // }
    }


// * Delete Timeline
    public
    function deleteFollowUp($id)
    {
        // $enq_follow_up = EnquiryFollowup::find($id);
        $enq_follow_up = Task::find($id);

        // delete next follow up date and time
        DB::table('tbl_enquiries')->where('pk_int_enquiry_id', $enq_follow_up->enquiry_id)
            ->update(['next_follow_up' => NULL]);
        // ends
        if ($enq_follow_up) {
            Task::where('id', $enq_follow_up->id)->where('vendor_id', User::getVendorId())->delete();
        }
        // $log_type = $enq_follow_up->log_type;
        // $id = $enq_follow_up->enquiry_id;
        if ($enq_follow_up) {
            try {
                return response()->json(['msg' => " Deleted.", 'status' => true]);
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
        } else {
            return response()->json(['msg' => " Not Found.", 'status' => false]);
        }
    }

    /**
     * function to update lead profile from detail page
     *
     * @param Request $request
     */
    public
    function updateLeadProfile(Request $request)
    {
        try {
            $enquiry = Enquiry::find($request->lead_id);
            $enquiry->vchr_customer_name = $request->lead_name;
            $enquiry->vchr_customer_email = $request->lead_email;
            $enquiry->mobile_no = $request->lead_mobile;


            if ($enquiry->save()) {
                return response()->json(['msg' => 'Updated sucessfully', 'status' => 'success']);
            } else {

                return response()->json(['msg' => 'Something went wrong..Try again.', 'status' => 'error']);
            }

        } catch (\Exception $e) {

            return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
        }
    }

    public
    function deleteMultipleLeads(Request $request): JsonResponse
    {
        try {
            $leadIds = explode(',', $request->get('lead_ids'));

            Enquiry::query()
                ->whereIn('pk_int_enquiry_id', $leadIds)
                ->update([
                    'deleted_by' => Auth::user()->pk_int_user_id,
                    'deleted_at' => now(),
                ]);

            Task::query()->whereIn('enquiry_id', $leadIds)->delete();

//            CampaignLead::query()
//                ->where('lead_id', $leadIds)
//                ->delete();
            return response()->json(['msg' => "Enquiry deleted.", 'status' => 'success']);

        } catch (\Exception $e) {

            return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
        }
    }

    /**
     * Function to add schedule for next follow up
     *
     * @param Request $request
     */
    public
    function addScheduleForNextFollowUp(Request $request)
    {
        $input = $request->all();

        if ($request->followup_required == 1) {
            $rules = [
                'next_follow_up_date' => ['required'],
                'next_follow_up_time' => ['required'],

            ];
            $rulesMessage = [
                'next_follow_up_date.required' => 'Date required.',
                'next_follow_up_time.required' => 'Time required.',
            ];
        } else {
            $rules = ['followup_required' => ['required']];
            $rulesMessage = ['followup_required.required' => 'Follow up field is  required.'];
        }

        $validator = validator($input, $rules, $rulesMessage);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->messages(), 'status' => 'fail', 'msg' => 'Validation Failed']);

        }

        if (Enquiry::where('pk_int_enquiry_id', $request->id)->exists()) {

            // if ($request->followup_required == 1) {
            $newDate = \Carbon\Carbon::createFromFormat('Y-m-d', $request->next_follow_up_date)
                ->format('m/d/Y');
            $followup_required = 1;
            $next_follow_up_time_value = $request->next_follow_up_time_value;

            DB::table('tbl_enquiries')->where('pk_int_enquiry_id', $request->id)
                ->update(['next_follow_up' => $newDate . ' ' . $request->next_follow_up_time_value]);

            $data = $request->all();
            $data['name'] = 'Follow up';
            $data['duration'] = '';
            $data['note'] = $request->note;
            $data['id'] = $request->id;
            $data['date'] = $newDate;
            $data['followup_required'] = $followup_required;
            $data['time'] = $next_follow_up_time_value;

            EnquiryFollowup::addSchedule($data, User::getVendorId());

            return response()->json(['msg' => "Next follow up added.", 'status' => 'success']);
        } else {

            return response()->json(['msg' => "Enquiry not exist in our data base.", 'status' => 'error']);
        }
    }

    /**
     * Function to update next follow up schedule
     *
     * @param Request $request
     * @param int $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public
    function updateNextFollowUpSchedule(Request $request, $id)
    {
        $enquiryfollowups = EnquiryFollowup::find($id);

        if ($enquiryfollowups) {

            $data = DB::table('tbl_enquiries')->where('pk_int_enquiry_id', $enquiryfollowups->enquiry_id)
                ->update(['next_follow_up' => $request->date . ' ' . $request->time]);

            $data = $request->all();
            $data['name'] = $request->name ?? 'Follow up';
            $data['duration'] = '';
            $data['note'] = $request->note;
            $data['id'] = $id;
            $data['date'] = $request->date;
            $data['time'] = $request->time;
            EnquiryFollowup::updateSchedule($data, $id, Auth::user()->pk_int_user_id);

            return redirect('user/enquiry-timeline/' . $enquiryfollowups->enquiry_id);
        } else {

            return back();
        }
    }

    public function updateAdditionalField(Request $request)
    {
        $value = $request->value;
        $index = $request->field_id;
        $id = $request->enquiry_id;
        $fieldName = '';
        if ($value && $value != "" && $index && $id) {
            $field_name = LeadAdditionalField::find($index);
            $exists_field = LeadAdditionalDetails::where('field_id', $field_name->id)
                ->where('field_name', $field_name->field_name)
                ->where('enquiry_id', $id)
                ->first();
            $fieldName = $field_name->field_name;
            if ($exists_field) {
                if ($field_name->type_text == 'Image') {
                    $filename = $value->store('public/custom_field_image');
                    $exists_field->value = $filename;
                } elseif ($field_name->input_type == 8) {// Multi Select DropDown
                    $exists_field->value = json_encode($value);
                } else {
                    $exists_field->value = $value;
                }
                $exists_field->save();
            } else {
                $fieldName = $field_name->field_name;
                $additionalDetails = new LeadAdditionalDetails();
                $additionalDetails->enquiry_id = $id;
                $additionalDetails->field_id = $index;
                $additionalDetails->field_name = $field_name->field_name;

                if ($field_name->type_text == 'Image') {
                    $filename = $value->store('public/custom_field_image');
                    $additionalDetails->value = $filename;
                } elseif ($field_name->input_type == 8) {// Multi Select DropDown
                    $additionalDetails->value = json_encode($value);
                } else {
                    $additionalDetails->value = $value;
                }
                $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                $additionalDetails->save();
            }
            $enq = Enquiry::find($id);

            //  Code for marble gallary
            if ($field_name->field_name == MarbleGallery::LEAD_COMPLETE && $enq->fk_int_user_id == MarbleGallery::MARBLE_GALLERY_ID)
                if ($value == 'Yes')
                    $this->changeAttandanceStatusAndPushToPOS($enq, Auth::user()->int_role_id);

            // Additional field automation when update

            $assignAdditionalData = AutomationRule::where('trigger', 'value_change')
                ->where('vendor_id', $enq->fk_int_user_id)
                // ->where('enquiry_source_id',$enq->fk_int_enquiry_type_id)
                ->where('action', 'assign')
                ->orderby('id', 'DESC')
                ->get();

            if (count($assignAdditionalData) > 0) {
                $data = $this->checkAdditionalFieldAutomation($assignAdditionalData, $enq, $field_name);
                $assignAdditionalData = $data['assignAdditionalData'];
                $leadAdd = $data['leadAdd'];

                $assignAdditionalData = $assignAdditionalData->first();

                if ($assignAdditionalData && $leadAdd) {
                    $fieldd = LeadAdditionalField::find($assignAdditionalData->additional_field);
                    if ($fieldd->input_type == 2) {
                        if ($leadAdd->field_id == $assignAdditionalData->additional_field && $leadAdd->value == $assignAdditionalData->additional_field_value) {
                            AutomationRule::autoassign($assignAdditionalData, $id);
                        }
                    } elseif ($fieldd->input_type == 8) {
                        if ($leadAdd->field_id == $assignAdditionalData->additional_field) {
                            if (in_array($assignAdditionalData->additional_field_value, json_decode($leadAdd->value)))
                                AutomationRule::autoassign($assignAdditionalData, $id);
                        }
                    }
                }
            }

            $taskAdditionalData = AutomationRule::where('trigger', 'value_change')
                ->where('vendor_id', $enq->fk_int_user_id)
                // ->where('enquiry_source_id',$enq->fk_int_enquiry_type_id)
                ->where('action', 'task')
                ->orderby('id', 'DESC')
                ->get();

            if (count($taskAdditionalData) > 0) {
                $data = $this->checkAdditionalFieldAutomation($taskAdditionalData, $enq, $field_name);
                $taskAdditionalData = $data['assignAdditionalData'];
                $leadAddTask = $data['leadAdd'];

                $taskAdditionalData = $taskAdditionalData->first();

                if ($taskAdditionalData && $leadAddTask) {
                    $fieldd = LeadAdditionalField::find($taskAdditionalData->additional_field);
                    if ($fieldd->input_type == 2) {
                        if ($leadAddTask->field_id == $taskAdditionalData->additional_field && $leadAddTask->value == $taskAdditionalData->additional_field_value) {
                            AutomationRule::taskCreate($taskAdditionalData, $enq->fk_int_user_id, $enq);
                        }
                    } elseif ($fieldd->input_type == 8) {
                        if ($leadAddTask->field_id == $taskAdditionalData->additional_field) {
                            if (in_array($taskAdditionalData->additional_field_value, json_decode($leadAddTask->value)))
                                AutomationRule::taskCreate($taskAdditionalData, $enq->fk_int_user_id, $enq);
                        }
                    }
                }
            }

            try {
                $data = event(new CreateFollowup($fieldName . ' Field updated', EnquiryFollowup::TYPE_ACTIVITY, $id, Auth::user()->pk_int_user_id));
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }

            // Prepare some data for ajax response
            $status_log_html = view('backend.user-pages.timeline.status.activity-additional', [
                'ef' => $data[1],
            ])->render();

            return response()->json([
                'status' => true,
                'status_log_html' => $status_log_html,
            ]);

        } else {
            $field_name = LeadAdditionalField::find($request->field_id);
            $exists_field = LeadAdditionalDetails::where('field_id', $field_name->id)
                ->where('enquiry_id', $id)
                ->delete();

            try {
                $data = event(new CreateFollowup($field_name->field_name . ' field removed', EnquiryFollowup::TYPE_ACTIVITY, $id, Auth::user()->pk_int_user_id));
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }

            // Prepare some data for ajax response
            $status_log_html = view('backend.user-pages.timeline.status.activity-additional', [
                'ef' => $data[1],
            ])->render();

            return response()->json([
                'status' => true,
                'status_log_html' => $status_log_html,
            ]);
        }

    }


//LEADS LOG NOT EDIT-DELETE OPTION -------------------------------------------------------------

    public function editLeadLogNote($id)
    {
        $log_note = EnquiryFollowup::where('id', $id)->first();
        if ($log_note) {
            if ($log_note->created_at != NULL) {
                $dt = $log_note->created_at;
                $log_note->time_field = Carbon::parse($log_note->created_at)->format('h:ia');
                $log_note->created_at = Carbon::parse($log_note->created_at)->format('Y-m-d');
                $log_note->created_at_edit = Carbon::parse($dt)->format('m-d-Y');
            }
            return response()->json(['msg' => "Successfull", 'status' => true, 'data' => $log_note]);
        } else {
            return response()->json(['msg' => "Failed", 'status' => false, 'data' => null]);
        }
    }


    public function updateLeadLogNote(Request $request)
    {

        $log_note_id = $request->log_note_id;
        $log_note = $request->log_note;
        $log_typeid = $request->log_type;

        /*var_dump($log_note_id);
        var_dump($log_note);
        var_dump($log_typeid);
        dd();
        */
        if ($log_note != "" && $log_typeid != "" && $log_note_id != "") {
            $note1 = [
                'log_type' => $log_typeid,
                'note' => $log_note,
                'updated_by' => Auth::user()->pk_int_user_id,
            ];

            $result = EnquiryFollowup::where('id', $log_note_id)->update($note1);

            if ($result) {
                return response()->json(['msg' => "Log note successfully updated", 'status' => true]);
            } else {
                return response()->json(['msg' => "Failed", 'status' => false]);
            }
        } else {
            return response()->json(['msg' => "Log note missing..!", 'status' => false]);
        }
    }


    public function deleteLeadLogNote($id)
    {
        try {

            $log_note = EnquiryFollowup::where('id', $id)->first();
            $log_note->delete();

            return response()->json(['msg' => "Log note successfully deleted.", 'status' => true]);

        } catch (\Exception $e) {

            return response()->json(['msg' => $e->getMessage(), 'status' => false]);
        }
    }

    public function clickToCall(Request $request)
    {
        Log::info('Request received for click to call from web app', ['request' => $request->all()]);

        $settings = CloudTelephonySetting::where('vendor_id', User::getVendorId())
            ->where('status', 1)
            ->where('default', 1)
            ->first();

        if ($settings) {
            if ($settings->operator == "2") {
                try {
                    $callBonvoice = new BonvoiceController();
                    return $callBonvoice->clickToCallBonvoice($request, auth()->user());
                } catch (\Exception $e) {
                }

            } elseif ($settings->operator == "3") {
                $callVoxbay = new VoxbayController();
                return $callVoxbay->clickToCallVoxbayX($request, auth()->user());

            } else {
                $callVoxbay = new VoxbayController();
                return $callVoxbay->clickToCallVoxbay($request, auth()->user());
            }
        } else {
            return response()->json(['data' => [], 'message' => 'You dont have IVR Subscription!', 'status' => 0], 200);
        }
    }

}
