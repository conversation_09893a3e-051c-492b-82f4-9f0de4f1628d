<?php

namespace App\Http\Controllers\BackendController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\SmsDltTemplate;
use DataTables;

class SmsDltTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('backend.pages.sms-dlt-template.index');
    }

    /**
     * Function to approve sms dlt template
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function approveSmsDltTemplate(Request $request)
    {
        try{

            $smsDltTemplate = SmsDltTemplate::find($request->id);
            $smsDltTemplate->status = SmsDltTemplate::APPROVED;
                
            if($smsDltTemplate->save()){
                   
                return response()->json(['msg'=>'SMS DLT Template approved..', 'status'=>'success']);
                   
            }else{

                return response()->json(['msg'=>'Something went wrong..Try again.', 'status'=>'error']);
            }
                 
        }catch (\Exception $e) {

            return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);
        }
    }

    /**
     * Function to de-approve sms dlt template
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function deApproveSmsDltTemplate(Request $request)
    {
        try{

            $smsDltTemplate = SmsDltTemplate::find($request->id);
            $smsDltTemplate->status = SmsDltTemplate::PENDING;
                
            if($smsDltTemplate->save()){
                   
                return response()->json(['msg'=>'Deactivated SMS DLT Template..', 'status'=>'success']);
                   
            }else{

                return response()->json(['msg'=>'Something went wrong..Try again.', 'status'=>'error']);
            }
                 
        }catch (\Exception $e) {

            return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);
        }
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $template = SmsDltTemplate::find($id);

        if ($template) {
            if ($template->delete()) {

                return response()->json(['msg' => "SMS DLT Template deleted.", 'status' => 'success']);
            } else {

                return response()->json(['msg' => "Not able to delete.Please try again", 'status' => 'error']);
            }
        } else {

            return response()->json(['msg' => "SMS DLT Template not found.", 'status' => 'error']);
        }
    }

    /**
     * Function to get all sms dlt template data
     *
     * @return mixed
     */
    public function getSmsDltTemplate()
    {
        $templates = SmsDltTemplate::orderby('created_at','DESC')->get();

        foreach ($templates as $key => $row) {
            $row->slno=++$key;
        }

        return Datatables::of($templates)

        ->addColumn('show', function ($template) {

          if ($template->status == 1) {
                       
            return '<div class="dropdown show">
                        <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>   
                        </a>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                            <a id="delete_plan" smstemplate-id="' . $template->id . '" class="dropdown-item smddlttemplate-delete"> 
                                <i class="fa fa-trash-o mg-r-5"></i>&nbsp;Delete
                            </a>
                            <a data-status="'.$template->status.'" data-id="'.$template->id.'" data-title="'.$template->title.'" data-template="'.$template->template.'" data-entity_id="'.$template->entity_id.'" data-header_id="'.$template->header_id.'" data-template_id="'.$template->template_id.'" data-sender_id="'.$template->sender_id.'" class="dropdown-item btn-activate deactivate-btn" data-target="#deactivate_popup"  data-toggle="modal"  smstemplate-id="' . $template->id . '" data-toggle="tooltip" title="SMS template  is active"> 
                                <i class="fa fa-check"></i>&nbsp;SMS template  is active
                            </a>
                        </div>
                    </div>';
          } //If Status is not in active state
          else {
            return '<div class="dropdown show">
                        <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>   
                        </a>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                            <a id="delete_plan" smstemplate-id="' . $template->id . '" class="dropdown-item smddlttemplate-delete"> 
                                <i class="fa fa-trash-o mg-r-5"></i>&nbsp;Delete
                            </a>
                            <a class="dropdown-item btn-warning approve_btn" data-target="#approve_popup"  data-toggle="modal" data-status="'.$template->status.'" data-id="'.$template->id.'" data-title="'.$template->title.'" data-template="'.$template->template.'" data-entity_id="'.$template->entity_id.'" data-header_id="'.$template->header_id.'" data-template_id="'.$template->template_id.'" data-sender_id="'.$template->sender_id.'" smstemplate-id="' . $template->id . '" data-toggle="tooltip" title="Admin approval pending"> 
                                <i class="fa fa-times"></i>&nbsp;Admin approval pending
                            </a>
                        </div>
                    </div>';
          }
        })
        ->rawColumns(['show'])
        ->toJson(true);
    }
}
