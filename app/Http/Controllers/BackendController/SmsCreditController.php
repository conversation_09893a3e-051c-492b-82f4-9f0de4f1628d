<?php

namespace App\Http\Controllers\BackendController;

use App\BackendModel\Smsroute;
use App\Common\Variables;
use App\Subscription\SmsCount;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DataTables;
use Flash;
use App\BackendModel\SmsCreditHistory;

class SmsCreditController extends Controller
{
    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        $users = User::where('int_status', Variables::ACTIVE)
            ->orderBy('vchr_user_name', 'ASC')
            ->select('pk_int_user_id', 'vchr_user_name','vchr_user_mobile','mobile')
            ->where('int_role_id', '!=', User::ADMIN)
            ->get();
        $routes = Smsroute::select('pk_int_sms_route_id', 'vchr_sms_route')
            ->where('int_sms_route_status', Variables::ACTIVE)
            ->get();
        return view('backend.pages.sms-credit.index')
            ->with('users', $users)
            ->with('routes', $routes);
    }

    /**
     * @param Request $request
     * @return array
     */
    public function store(Request $request)
    {
        $smsCount = SmsCount::where('vendor_id', $request->user_id)
            ->where('route_id', $request->route_id)->first();
        if (!$smsCount) {
            $smsCount = new SmsCount();
            $smsCount->vendor_id = $request->user_id;
            $smsCount->route_id = $request->route_id;
            $smsCount->credit = $request->credit;
            $smsCount->save();
        }
        $smsCount->credit = $smsCount->credit + $request->credit;
        $smsCount->save();
        $history = new SmsCreditHistory;
        $history->fk_int_user_id = $request->user_id;
        $history->fk_int_route_id = $request->route_id;
        $history->credit = $request->credit;
        $history->action = 1;
        $history->save();
        Flash::success('Sms Credit Added!');
        return redirect('/admin/user-sms-credits');


    }
 public function deductusersms(Request $request)
    {
       $smsCount = SmsCount::where('vendor_id', $request->user_id)
            ->where('route_id', $request->route_id)->first();
            $dataCredit = $smsCount->credit - $request->credit;

             // $data=$smsCount->credit-30;
             // echo $data;
  $total_credit = $smsCount->total_count + $dataCredit - $smsCount->used_sms_count;
  // echo $total_credit;
               
if($total_credit>=0)
{
   if (!$smsCount) {
            $smCount = new SmsCount();
            $smCount->vendor_id = $request->user_id;
            $smCount->route_id = $request->route_id;
            $smCount->total_count = $request->credit;
            $smCount->save();
        }
        $smsCount->credit = $smsCount->credit - $request->credit;
        $smsCount->save();
        $history = new SmsCreditHistory;
        $history->fk_int_user_id = $request->user_id;
        $history->fk_int_route_id = $request->route_id;
        $history->credit = $request->credit;
        $history->action = 2;
        $history->save();
        Flash::success('Sms Deduct!');
        return redirect('/admin/user-sms-credits');
}
if($total_credit<0)
{
     Flash::success('Sms Not Deduct!');
        return redirect('/admin/user-sms-credits');
}
        // if (!$smsCount) {
        //     $smCount = new SmsCount();
        //     $smCount->vendor_id = $request->user_id;
        //     $smCount->route_id = $request->route_id;
        //     $smCount->total_count = $request->credit;
        //     $smCount->save();
        // }
        // $smsCount->credit = $smsCount->credit - $request->credit;
        // $smsCount->save();
        // Flash::success('Sms Credit Added!');
        // return redirect('/admin/user-sms-credits');


    }
    /**
     *Get users SMS count info
     */
    public function getUserSmsCount()
    {
        $sms_counts = SmsCount::join('tbl_users', 'tbl_users.pk_int_user_id', '=', 'sms_counts.vendor_id')
            ->join('tbl_sms_route', 'tbl_sms_route.pk_int_sms_route_id', '=', 'sms_counts.route_id')
            ->orderby('id', 'DESC')
            ->select('vchr_user_name', 'credit', 'total_count', 'used_sms_count', 'vchr_sms_route')
            ->get();

        foreach ($sms_counts as $key => $row) {
            $row->slno = ++$key;
        }
        return Datatables::of($sms_counts)
            ->addColumn('balance', function ($template) {
                $total_credit = $template->total_count + $template->credit - $template->used_sms_count;
                return $total_credit;
            })
            ->toJson(true);
    }
    public function getUserSmsCreditHistory()
    {
        $sms_counts = SmsCreditHistory::join('tbl_users', 'tbl_users.pk_int_user_id', '=', 'sms_credit_histories.fk_int_user_id')
            ->join('tbl_sms_route', 'tbl_sms_route.pk_int_sms_route_id', '=', 'sms_credit_histories.fk_int_route_id')
            ->orderby('sms_credit_histories.id', 'DESC')
            ->select('vchr_user_name', 'credit', 'action', 'sms_credit_histories.created_at', 'vchr_sms_route')
            ->get();

        foreach ($sms_counts as $key => $row) {
            $row->slno = ++$key;
        }
        return Datatables::of($sms_counts)
            ->addColumn('date', function ($template) {
                return $template->created_at->format('d M Y h:i A');
            })
            ->addColumn('action_name', function ($template) {
                return $template->action_name;
            })
            ->toJson(true);
    }
}
