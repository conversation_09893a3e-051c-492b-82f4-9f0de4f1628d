<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Validator;
use DataTables;
use App\Common\Variables;
use DB;
use Illuminate\Support\Facades\Input;
use App\BackendModel\SmsTemplates;
use App\BackendModel\ShortCodes;
use App\BackendModel\SmsTemplate;
use App\BackendModel\EmailTemplate;
use App\Jobs\SendEmailJob;
use App\User;
use Mail;
use App\Common\SendTelegram;

class SmsTemplateAppController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
         return view('backend.pages.apitemplates.smstemplate');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
       
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
     public function show($id)
    {
        
         $template=SmsTemplates::where('pk_int_sms_template_id',$id)->first();
        if($template)
        {
            
             
            return response()->json(['msg'=>'data present', 'status'=>'success','data'=>$template]);
        }
        else
        {
             return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
        }
          
           
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
     try {
            $template = SmsTemplates::find($id);
            if ($template) {
                
                SmsTemplates::where('pk_int_sms_template_id', $id)->delete();
                return response(['msg' => 'SMS Template is deleted.', 'status' => 'success']);

            }
            else
            {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            }
        }
        catch (\Exception $ex) {
                  return response(['msg' =>'Something Went Wrong'  , 'status' => 'failed']);
                
            }
    }
    
     public function getInfoAll()
    {
        $template = DB::table('tbl_users')
        ->select('vchr_user_name','vchar_sms_template_title','text_sms_template_description','int_sms_template_status','pk_int_sms_template_id')
        ->join('tbl_sms_template', 'tbl_users.pk_int_user_id', '=', 'tbl_sms_template.fk_int_user_id')->orderby('pk_int_sms_template_id','DESC')
        ->whereNull('tbl_sms_template.deleted_at')
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {
                    if ($template->int_sms_template_status == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                   
                                    <button id="delete_plan" template-id="' . $template->pk_int_sms_template_id . '" class="btn btn-sm btn-danger template-delete"> <i class="f-16 fa fa-trash-o"></i></button>
                                    <a class="btn btn-sm btn-success btn-activate template-act ks-izi-modal-trigger1"  data-target="#ks-izi-modal-large1" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Reject"> <i class="f-18 fa fa-check-circle active"></i></a>
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    <button href="" template-id="' . $template->pk_int_sms_template_id . '" class="btn btn-sm btn-danger template-delete"> <i class="f-16 fa fa-trash-o"></i></button>
                                    <a class="btn btn-sm btn-warning template-act" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Approve"> <i class="f-18 fa fa-times-circle"></i></a>
                                </div>';
                    }
                })
                ->rawColumns(['show'])
                ->toJson(true);
    }

    public function getInfo($id)
    {
        $template = DB::table('tbl_users')

        ->select('vchr_user_name','vchar_sms_template_title','text_sms_template_description','int_sms_template_status','pk_int_sms_template_id','int_default_sms_template_id')
        ->join('tbl_sms_template', 'tbl_users.pk_int_user_id', '=', 'tbl_sms_template.fk_int_user_id')->orderby('pk_int_sms_template_id','DESC')
        ->whereNull('tbl_sms_template.deleted_at')
         ->where('tbl_sms_template.fk_int_user_id',$id)
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {
                    if ($template->int_sms_template_status == 1) {
                        return '
                                
                                   
                                    <button id="delete_plan" template-id="' . $template->pk_int_sms_template_id . '" class="btn btn-danger template-delete "> <i class="f-16 fa fa-trash-o"></i></button>
                                    <a class="btn btn-success btn-activate template-act ks-izi-modal-trigger1"  data-target="#ks-izi-modal-large3" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Reject"> <i class="f-18 fa fa-check-circle active"></i></a>
                                ';
                    } //If Status is not in active state
                    else {
                        return '
                               
                                    
                                    <button href="" template-id="' . $template->pk_int_sms_template_id . '" class="btn btn-danger template-delete"> <i class="f-16 fa fa-trash-o"></i></button>
                                    <a class="btn btn-warning template-act" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Approve"> <i class="f-18 fa fa-times-circle"></i></a>
                               ';
                    }
                })->addColumn('default', function ($template) {

            if ($template->int_default_sms_template_id == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group" title="Default sender id">
                                   
                                    <button id="" senderid-id="' . $template->pk_int_sms_template_id . '" class="btn btn-outline-success  px-3" style="color:green" >DEFAULT</button>
                                    <button class="btn btn-outline-success ks-no-text">
                                            <span class="fa fa-check ks-icon"></span>
                                        </button>
                                     
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    
                                     <button class="btn btn-outline-danger ks-no-text default-senderid" senderid-id="' . $template->pk_int_sms_template_id . '" title="Click to activate default senderid">
                                            <span class="fa fa-close ks-icon"></span>
                                        </button>
                                   
                                </div>';
                    }
                })
                ->rawColumns(['show','default'])
                ->toJson(true);
    }

    public function getUserInfo()
    {
        $template = DB::table('tbl_users')
        ->select('vchr_user_name','vchar_sms_template_title','text_sms_template_description','int_sms_template_status','pk_int_sms_template_id')
        ->join('tbl_sms_template', 'tbl_users.pk_int_user_id', '=', 'tbl_sms_template.fk_int_user_id')->orderby('pk_int_sms_template_id','DESC')
        ->whereNull('tbl_sms_template.deleted_at')
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {
                    if ($template->int_sms_template_status == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                   
                                    <button id="delete_plan" template-id="' . $template->pk_int_sms_template_id . '" class="btn btn-danger template-delete px-3"> <i class="f-16 la la-trash-o"></i></button>
                                    <a class="btn btn-success btn-activate template-act px-3 ks-izi-modal-trigger1"  data-target="#ks-izi-modal-large" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Reject"> <i class="f-18 fa fa-check-circle active"></i></a>
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    <button href="" template-id="' . $template->pk_int_sms_template_id . '" class="btn btn-danger template-delete px-3"> <i class="f-16 fa fa-trash-o"></i></button>
                                    <a class="btn btn-warning template-act px-3" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Approve"> <i class="f-18 fa fa-times-circle"></i></a>
                                </div>';
                    }
                })
                ->rawColumns(['show'])
                ->toJson(true);
    }
    

    public function deactivate(Request $request)
    {
        $input=$request->all();
        //echo $request->id; die();

        $template = SmsTemplates::where('pk_int_sms_template_id', $request->id)->first();
        if ($template) {

            try
            {

            $user=User::select('email','vchr_user_name','pk_int_user_id')->find($template->fk_int_user_id);
            $input['email']=$user->email;
            $input['username']=$user->vchr_user_name;

            //check email template and short code for use
            $code=ShortCodes::where('vchr_short_code_template','GL_REJECTION_REASON')->first();
                if($code)
                {
                     return response()->json(['msg'=>'Please add short code "GL_REJECTION_REASON"', 'status'=>'fail']);
                }

                $emailtemplate=EmailTemplate::where('vchr_email_template_name','USER SMS TEMPLATE REJECTION')->first();
                if($emailtemplate)
                {
                     return response()->json(['msg'=>'Add EMAIL Template "USER  SMS TEMPLATE REJECTION" with "GL_REJECTION_REASON" shortcode  before you procced', 'status'=>'fail']);
                }


            $template->int_sms_template_status = SmsTemplates::DEACTIVATE;
            $flag = $template->save();
            if ($flag) {

                //email for template
                $body = str_replace('[GL_REJECTION_REASON]',$request->reason, $emailtemplate->vchr_email_template_code);

                $data=['template'=>'welcome','email'=>$input['email'],'name'=>$input['username'],'subject'=>'SMS Template Rejected','body'=>$body];
                                       // dd($data);
                //dispatch(new SendEmailJob($data));
                Mail::send("emails.".$data['template'], $data, function($message) use ($data)
                 {
                    $message
                        ->from('<EMAIL>',config('app.name'))
                        ->to($data['email'],$data['name'])
                        ->subject($data['subject']);
                 });
               

            
                return response()->json(['msg' => "SMS Template rejected.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "SMS Template could not be rejected.", 'status' => 'fail']);
            }
            }
            catch(\Exception $e)
            {
                return response()->json(['msg' =>$e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "SMS Template not found.", 'status' => 'fail']);
        }
    }

    public function activate($id)
    {
        $template = SmsTemplates::where('pk_int_sms_template_id', $id)->first();
        if ($template) {
            $template->int_sms_template_status = SmsTemplates::ACTIVATE;
            $flag = $template->save();
            if ($flag) {
                //Telegram
                $telegramId=User::getTelegramId($template->fk_int_user_id);
                $message=' Sms Template Approved Successfully';
                $sendTelegrams = new SendTelegram();
                $sendTelegram=$sendTelegrams->telegram($telegramId,$message);
                //---------------
                return response()->json(['msg' => "SMS Template approved.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "SMS Template could not be approved.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "SMS Template not found.", 'status' => 'fail']);
        }
    }



}
