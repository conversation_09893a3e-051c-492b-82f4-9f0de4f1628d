<?php

namespace App\Http\Controllers\BackendController;

use App\BackendModel\SmsTemplateCode;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use Auth;
use DB;

use Carbon\Carbon;
use App\Core\CustomClass;
use DataTables;
use App\Common\SendTelegram;
use App\Common\SingleSMS;
use App\Common\Notifications;

class SmsTemplateCodeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        return view('backend.pages.sms-template.index');

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\BackendModel\SmsTemplateCode  $smsTemplateCode
     * @return \Illuminate\Http\Response
     */
    public function show(SmsTemplateCode $smsTemplateCode)
    {
        //
        $template=SmsTemplateCode::where('pk_int_sms_template_id',$id)->first();
        if($template)
        {
            
             
            return response()->json(['msg'=>'data present', 'status'=>'success','data'=>$template]);
        }
        else
        {
             return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\BackendModel\SmsTemplateCode  $smsTemplateCode
     * @return \Illuminate\Http\Response
     */
    public function edit(SmsTemplateCode $smsTemplateCode)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\BackendModel\SmsTemplateCode  $smsTemplateCode
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, SmsTemplateCode $smsTemplateCode)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\BackendModel\SmsTemplateCode  $smsTemplateCode
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //

        try {
            $template = SmsTemplateCode::find($id);
            if ($template) {
                
                SmsTemplateCode::where('pk_int_sms_template_id', $id)->delete();
                return response(['msg' => 'SMS Template is deleted.', 'status' => 'success']);

            }
            else
            {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            }
        }
        catch (\Exception $ex) {
                return response(['msg' =>'Something Went Wrong'  , 'status' => 'failed']);
                
        }
    }
    public function getInfoAll()
    {
        $template = DB::table('tbl_users')
        ->select('vchr_user_name','vchr_sms_template_title','sms_template_code','sms_templates.int_status','pk_int_sms_template_id')
        ->join('sms_templates', 'tbl_users.pk_int_user_id', '=', 'sms_templates.fk_int_user_id')->orderby('pk_int_sms_template_id','DESC')
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {
                    if ($template->int_status == 1) {
                        return '<div class="dropdown show">
                                    <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>   
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <a id="delete_plan" template-id="' . $template->pk_int_sms_template_id . '" class="dropdown-item template-delete"> 
                                            <i class="f-16 fa fa-trash-o"></i>&nbsp;Delete
                                        </a>
                                        <a class="dropdown-item btn-activate template-act ks-izi-modal-trigger1"  data-target="#ks-izi-modal-large1" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Reject"> 
                                            <i class="f-18 fa fa-check-circle active"></i>&nbsp;Reject
                                        </a>
                                    </div>
                                </div>';
                    } //If Status is not in active state
                    else {
                        return ' <div class="dropdown show">
                                    <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>   
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <a href="" template-id="' . $template->pk_int_sms_template_id . '" class="dropdown-item template-delete"> 
                                            <i class="f-16 fa fa-trash-o"></i>&nbsp;Delete
                                        </a>
                                        <a class="dropdown-item template-act" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Approve"> 
                                            <i class="f-18 fa fa-times-circle"></i>&nbsp;Approve
                                        </a>
                                    </div>
                                </div>';
                    }
                })
                ->rawColumns(['show'])
                ->toJson(true);
    }

    public function getInfo($id)
    {
        $template = DB::table('tbl_users')

        ->select('vchr_user_name','vchar_sms_template_title','text_sms_template_description','int_sms_template_status','pk_int_sms_template_id','int_default_sms_template_id')
        ->join('sms_templates', 'tbl_users.pk_int_user_id', '=', 'sms_template.fk_int_user_id')->orderby('pk_int_sms_template_id','DESC')
        ->whereNull('sms_template.deleted_at')
         ->where('sms_template.fk_int_user_id',$id)
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {
                    if ($template->int_sms_template_status == 1) {
                        return '
                                
                                   
                                    <button id="delete_plan" template-id="' . $template->pk_int_sms_template_id . '" class="btn btn-danger template-delete "> <i class="f-16 fa fa-trash-o"></i></button>
                                    <a class="btn btn-success btn-activate template-act ks-izi-modal-trigger1"  data-target="#ks-izi-modal-large3" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Reject"> <i class="f-18 fa fa-check-circle active"></i></a>
                                ';
                    } //If Status is not in active state
                    else {
                        return '
                               
                                    
                                    <button href="" template-id="' . $template->pk_int_sms_template_id . '" class="btn btn-danger template-delete"> <i class="f-16 fa fa-trash-o"></i></button>
                                    <a class="btn btn-warning template-act" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Approve"> <i class="f-18 fa fa-times-circle"></i></a>
                               ';
                    }
                })->addColumn('default', function ($template) {

            if ($template->int_default_sms_template_id == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group" title="Default sender id">
                                   
                                    <button id="" senderid-id="' . $template->pk_int_sms_template_id . '" class="btn btn-outline-success  px-3" style="color:green" >DEFAULT</button>
                                    <button class="btn btn-outline-success ks-no-text">
                                            <span class="fa fa-check ks-icon"></span>
                                        </button>
                                     
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    
                                     <button class="btn btn-outline-danger ks-no-text default-senderid" senderid-id="' . $template->pk_int_sms_template_id . '" title="Click to activate default senderid">
                                            <span class="fa fa-close ks-icon"></span>
                                        </button>
                                   
                                </div>';
                    }
                })
                ->rawColumns(['show','default'])
                ->toJson(true);
    }

    public function getUserInfo()
    {
        $template = DB::table('tbl_users')
        ->select('vchr_user_name','vchar_sms_template_title','text_sms_template_description','int_sms_template_status','pk_int_sms_template_id')
        ->join('sms_templates', 'tbl_users.pk_int_user_id', '=', 'sms_template.fk_int_user_id')->orderby('pk_int_sms_template_id','DESC')
        ->whereNull('sms_template.deleted_at')
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {
                    if ($template->int_sms_template_status == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                   
                                    <button id="delete_plan" template-id="' . $template->pk_int_sms_template_id . '" class="btn btn-danger template-delete px-3"> <i class="f-16 la la-trash-o"></i></button>
                                    <a class="btn btn-success btn-activate template-act px-3 ks-izi-modal-trigger1"  data-target="#ks-izi-modal-large" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Reject"> <i class="f-18 fa fa-check-circle active"></i></a>
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    <button href="" template-id="' . $template->pk_int_sms_template_id . '" class="btn btn-danger template-delete px-3"> <i class="f-16 fa fa-trash-o"></i></button>
                                    <a class="btn btn-warning template-act px-3" template-id="' . $template->pk_int_sms_template_id . '" data-toggle="tooltip" title="Approve"> <i class="f-18 fa fa-times-circle"></i></a>
                                </div>';
                    }
                })
                ->rawColumns(['show'])
                ->toJson(true);
    }
    

    public function deactivate($id)
    {
        // return ("hello");

        //echo $request->id; die();

        $template = SmsTemplateCode::where('pk_int_sms_template_id', $id)->first();
        
        

            $template->int_status = SmsTemplateCode::DEACTIVATE;
            $flag = $template->save();
            if ($flag){
                return response()->json(['msg' => "SMS Template rejected Successfully.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "SMS Template could not be rejected.", 'status' => 'fail']);
            }
            
            
         
    }

    public function activate($id)
    {
        // return("hello");
        $template = SmsTemplateCode::where('pk_int_sms_template_id', $id)->first();
        if ($template) {
            $template->int_status = SmsTemplateCode::ACTIVATE;
            $flag = $template->save();
            if ($flag) {
                $userObject = User::getUserDetails($template->fk_int_user_id);
                $userAdminObject = User::getSingleAdminDetails();
                //Notifications
                $notifications=new Notifications();
                $from=env('MAIL_FROM_ADDRESS');
                $to=$userObject->email;
                $subject="SMS Template Approved";
                $name=$userObject->vchr_user_name;
                $logo=$userAdminObject->vchr_logo;
                $attachment="";
                $telegramId=$userObject->telegram_id;
                $mobileNumber=$userObject->vchr_user_mobile;
                $defaultSenderIdAdmin=SingleSMS:: getSenderid($userAdminObject->pk_int_user_id,'');
                $defaultRouteAdmin=SingleSMS:: getRoute($userAdminObject->pk_int_user_id,'');
                $content1="SMS Template Approved";
                $content2="SMS Template Approved";
                $dataSend['message'] = $content1;
                $dataSend['user_id'] = $template->fk_int_user_id ?? null;
                $dataSend['page'] = 'sender';
                $notifications->notifications($from,$to,$subject,$name,$content1,$content2,$logo,$attachment,$telegramId,$template->fk_int_user_id,$mobileNumber,$defaultRouteAdmin,$defaultSenderIdAdmin,$dataSend);
                                //-----------------
                return response()->json(['msg' => "SMS Template approved.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "SMS Template could not be approved.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "SMS Template not found.", 'status' => 'fail']);
        }
    }

}
