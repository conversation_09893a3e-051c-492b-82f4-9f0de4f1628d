<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\Smspricing;
use App\BackendModel\Smsroute;
use Validator;
use DataTables;
use App\Common\Variables;
use DB;
use Illuminate\Support\Facades\Input;
//use App\Http\Resources\Testimonial\TestimonialResource;

class SmsPricingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $route=Smsroute::where('int_sms_route_status',Smsroute::ACTIVATE)->get();
        return view('backend.pages.sms.pricing',compact('route'));
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
      
         // get the POST data
        $input = $request->all();
        //print_r($input);
        //DO validation here
        $validator=validator::make($input, Smspricing::$rule, Smspricing::$message);
        //dd($input);

        // if validator passes
        if ($validator->passes()) {
            
            try
            {
                $pricing = new Smspricing();
                $pricing->fk_int_sms_route = $input['vchr_sms_pricing_route'];
                $pricing->vchr_sms_pricing_rangefrom = $input['vchr_sms_pricing_rangefrom'];
                $pricing->vchr_sms_pricing_rangeto= $input['vchr_sms_pricing_rangeto'];
                $pricing->vchr_sms_pricing_rate_sms= $input['vchr_sms_pricing_rate_sms'];
                $pricing->int_sms_pricing_status = Smspricing::ACTIVATE;
                $flag=$pricing->save();
                if($flag)
                {
                     return response()->json(['msg'=>'Sms pricing added.', 'status'=>'success']);
                }
                else
                {
                     return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                }
                
            }
            catch(\Exception $e)
            {
                return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

                //return $e->getMessage();
            }

        } else {
             return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);

           // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $pricing=Smspricing::find($id);
        if($pricing)
        {
              return response()->json(['msg' => "Sms Pricing detail found.", 'status' => 'success', 'data' => $pricing]);
        }
        else {
            return response()->json(['msg' => "Testimonial detail not found.", 'status' => 'fail']);
        }

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        
         // get the POST data
        $input = $request->all();
        //print_r($input);
        //DO validation here
        $validator=validator::make($input, Smspricing::$rule, Smspricing::$message);
        //dd($input);

        // if validator passes
        if ($validator->passes()) {
            
            try
            {
                $pricing=Smspricing::find($id);
                $pricing->fk_int_sms_route = $input['vchr_sms_pricing_route'];
                $pricing->vchr_sms_pricing_rangefrom = $input['vchr_sms_pricing_rangefrom'];
                $pricing->vchr_sms_pricing_rangeto= $input['vchr_sms_pricing_rangeto'];
                $pricing->vchr_sms_pricing_rate_sms= $input['vchr_sms_pricing_rate_sms'];
                //$pricing->int_sms_pricing_status = Smspricing::ACTIVATE;
                $flag=$pricing->save();
                if($flag)
                {
                     return response()->json(['msg'=>'Sms pricing updated.', 'status'=>'success']);
                }
                else
                {
                     return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                }
                
            }
            catch(\Exception $e)
            {
                return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

                //return $e->getMessage();
            }

        } else {
             return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);

           // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
        }
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
     try {
            $pricing = SmsPricing::find($id);
            if ($pricing) {
                
                Smspricing::where('pk_int_sms_pricing_id', $id)->delete();
                return response(['msg' => 'Sms Pricing is deleted.', 'status' => 'success']);

            }
            else
            {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            }
        }
    catch (\Exception $ex) {
              return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            
        }

    }
    public function getsmspricing()
    {
        $pricing = DB::table('tbl_sms_pricing')
          ->join('tbl_sms_route', 'tbl_sms_pricing.fk_int_sms_route', '=', 'tbl_sms_route.pk_int_sms_route_id')
          ->orderby('vchr_sms_pricing_rangefrom','ASC')
          ->get();
        
        foreach ($pricing as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($pricing)
        ->editColumn('route', function ($pricing) {
            if ($pricing->vchr_sms_route != null) {
                return $pricing->vchr_sms_route;
            } else {
                return "'No route'";
            }
        })
        ->editColumn('range', function ($pricing) {
            if ($pricing->vchr_sms_pricing_rangefrom != null) {
                return $pricing->vchr_sms_pricing_rangefrom.' '. '-'.' '. $pricing->vchr_sms_pricing_rangeto;
            } else {
                return "'No range'";
            }
        })
        ->editColumn('rate_sms', function ($pricing) {
            if ($pricing->vchr_sms_pricing_rate_sms != null) {
                return $pricing->vchr_sms_pricing_rate_sms;
            } else {
                return "'No Rate/sms'";
            }
        })
        
        ->addColumn('show', function ($pricing) {
            if ($pricing->int_sms_pricing_status == 1) {
               return '
                                
                                    <button testimonial-id="' . $pricing->pk_int_sms_pricing_id . '" class="btn btn-info  ks-izi-modal-trigger1" data-target="#ks-izi-modal-large"  data-toggle="tooltip" title="View" > <i class="f-16 fa fa-angle-double-right"></i></button>

                                    <button id="delete_plan" testimonial-id="' . $pricing->pk_int_sms_pricing_id . '" class="btn btn-danger testimonial-delete"> <i class="f-16 fa fa-trash-o"></i></button>

                                    <a class="btn btn-success btn-activate testimonial-act " testimonial-id="' . $pricing->pk_int_sms_pricing_id . '" data-toggle="tooltip" title="Deactivate"> <i class="f-18 fa fa-check-circle active"></i></a>
                                ';
            } //If Status is not in active state
            else {
                return '
                                
                                    <button testimonial-id="' . $pricing->pk_int_sms_pricing_id . '" class="btn btn-info  ks-izi-modal-trigger1" data-target="#ks-izi-modal-large"  data-toggle="tooltip" title="View" > <i class="f-16 fa fa-angle-double-right"></i></button>

                                    <button href="" testimonial-id="' . $pricing->pk_int_sms_pricing_id . '" class="btn btn-danger testimonial-delete"> <i class="f-16 fa fa-trash-o"></i></button>

                                    <a class="btn btn-warning testimonial-act" testimonial-id="' . $pricing->pk_int_sms_pricing_id . '" data-toggle="tooltip" title="Activate"> <i class="f-18 fa fa-times-circle"></i></a>
                                ';
            }
        })
        ->rawColumns(['show'])
        ->toJson(true);
    }

    public function deactivate($id)
    {
        $pricing = Smspricing::where('pk_int_sms_pricing_id', $id)->first();
        if ($pricing) {
            $pricing->int_sms_pricing_status = Smspricing::DEACTIVATE;
            $flag = $pricing->save();
            if ($flag) {
                return response()->json(['msg' => "Sms Pricing deactivated.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Sms Pricing could not be deactivated.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Sms Pricing not found.", 'status' => 'fail']);
        }
    }

    public function activate($id)
    {
        $pricing = Smspricing::where('pk_int_sms_pricing_id', $id)->first();
        if ($pricing) {
            $pricing->int_sms_pricing_status = Smspricing::ACTIVATE;
            $flag = $pricing->save();
            if ($flag) {
                return response()->json(['msg' => "Sms Pricing activated.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Sms Pricing could not be activated.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Sms Pricing not found.", 'status' => 'fail']);
        }
    }

   
}
