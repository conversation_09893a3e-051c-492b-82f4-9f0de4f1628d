<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\MasterSms;
use App\BackendModel\MasterSmsHistory;
use Validator;
use DataTables;
use App\Common\Variables;
use DB;
use Illuminate\Support\Facades\Input;
use App\BackendModel\ShortCodes;
use App\BackendModel\SmsTemplate;
use Carbon\Carbon;
use App\Core\CustomClass;
use App\User;
use App\Subscription\UserSubscription;
use App\Subscription\Plan;


class MasterSmsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function __construct()
    {
        date_default_timezone_set('Asia/Kolkata');
    }

    public function index()
    {
        $master = MasterSms::where('vchr_master_sms_name', 'Master Sms')->first();
        $latest = MasterSmsHistory::orderby('pk_int_master_sms_history_id', 'DESC')->where('int_master_sms_history_type', MasterSmsHistory::SELFCREDIT)->first();
        if (!empty($latest)) {
            //  Parse date with carbon
            $carbonated_date = Carbon::parse($latest->created_at);
            $diff_date = $carbonated_date->diffForHumans(Carbon::now());
        } else {
            $diff_date = null;
        }
        return view('backend.pages.mastersms.index', compact('master', 'diff_date'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $input = $request->all();
        $validator = validator::make($input, MasterSms::$rule, MasterSms::$message);
        if ($validator->passes()) {
            try {
                $code = ShortCodes::where('vchr_short_code_template', 'GL_MASTERSMS')->first();
                if (!$code) {
                    return response()->json(['msg' => 'Please add short code "GL_MASTERSMS"', 'status' => 'fail']);
                }
                $template = SmsTemplate::where('vchr_sms_template_name', 'MASTER SMS')->first();
                if (!$template) {
                    return response()->json(['msg' => 'Add SMS Template "MASTER SMS" before you procced', 'status' => 'fail']);
                }
                $master_sms = MasterSms::find($request->id);
                $master_sms->int_master_sms_count = $master_sms->int_master_sms_count + $request->int_master_sms_count;
                $smstemplate = $template->vchr_sms_template_code;
                $smstemplate = str_replace('[ GL_MASTERSMS ]', $request->int_master_sms_count, $smstemplate);
                $flag = $master_sms->save();
                if ($flag) {
                    $history = new MasterSmsHistory();
                    $history->vchr_master_sms_history_statement = $smstemplate;
                    $history->int_master_sms_history_type = MasterSmsHistory::SELFCREDIT;
                    $flag1 = $history->save();
                    if ($flag1) {
                        return response()->json(['msg' => 'Master SMS added.', 'status' => 'success', 'data' => $master_sms->int_master_sms_count]);
                    } else {
                        return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                    }
                } else {
                    return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
                //return $e->getMessage();
            }

        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
            // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function getHistory($id)
    {
        if ($id == MasterSmsHistory::SELFCREDIT) {
            $history = MasterSmsHistory::orderby('pk_int_master_sms_history_id', 'DESC')->where('int_master_sms_history_type', MasterSmsHistory::SELFCREDIT)->get();
            foreach ($history as $key => $row) {
                $row->slno = ++$key;
            }
            return Datatables::of($history)
                ->toJson(true);
        } else if ($id == MasterSmsHistory::CREDIT) {
            $history = MasterSmsHistory::orderby('pk_int_master_sms_history_id', 'DESC')->where('int_master_sms_history_type', MasterSmsHistory::CREDIT)->get();

            foreach ($history as $key => $row) {
                $row->slno = ++$key;
            }
            return Datatables::of($history)
                ->toJson(true);
        } else if ($id == MasterSmsHistory::OTPVERIFICATION) {
            $history = MasterSmsHistory::orderby('pk_int_master_sms_history_id', 'DESC')->where('int_master_sms_history_type', MasterSmsHistory::OTPVERIFICATION)->get();
            foreach ($history as $key => $row) {
                $row->slno = ++$key;
            }
            return Datatables::of($history)
                ->toJson(true);
        }
    }


    public function userCreditView()
    {
        $master = MasterSms::where('vchr_master_sms_name', 'Master Sms')->first();
        $latest = MasterSmsHistory::orderby('pk_int_master_sms_history_id', 'DESC')->where('int_master_sms_history_type', MasterSmsHistory::CREDIT)->first();
        if (!empty($latest)) {
            //  Parse date with carbon
            $carbonated_date = Carbon::parse($latest->created_at);
            $diff_date = $carbonated_date->diffForHumans(Carbon::now());
        } else {
            $diff_date = null;
        }
        return view('backend.pages.mastersms.user-credit', compact('master', 'diff_date'));

    }

    public function addUserCredit(Request $request)
    {
        // get the POST data
        $input = $request->all();
        //DO validation here
        $validator = validator::make($input, MasterSms::$creditRule, MasterSms::$creditMessage);
        if ($validator->passes()) {
            // try
            // {
            $smsCount = CustomClass::getMasterSms();
            if ($smsCount <= 0) {
                return response()->json(['msg' => 'Low MASTER SMS', 'status' => 'fail']);
            }
            $code = ShortCodes::where('vchr_short_code_template', 'GL_MASTERSMS')->first();
            if ($code) {
                return response()->json(['msg' => 'Please add short code "GL_MASTERSMS"', 'status' => 'fail']);
            }
            $code = ShortCodes::where('vchr_short_code_template', 'GL_USERNAME')->first();
            if ($code) {
                return response()->json(['msg' => 'Please add short code "GL_USERNAME"', 'status' => 'fail']);
            }
            $template = SmsTemplate::where('vchr_sms_template_name', 'USER CREDITED SMS')->first();
            if ($template) {
                return response()->json(['msg' => 'Add SMS Template "USER CREDITED SMS" before you procced with "GL_MASTERSMS and GL_USERNAME" code ', 'status' => 'fail']);
            }
            //begin transaction
            DB::beginTransaction();
            $mastersms = MasterSms::where('vchr_master_sms_name', 'Master Sms')->first();
            if (empty($mastersms)) {
                return response()->json(['msg' => 'Add Master SMS before you proceed', 'status' => 'fail']);
            }

            $smscount = $mastersms->int_master_sms_count - $request->int_master_sms_count;
            if ($smscount <= 0) {
                return response()->json(['msg' => 'Add credit below master sms', 'status' => 'fail']);
            }
            $mastersms->int_master_sms_count = $smscount;
            $smstemplate = $template->vchr_sms_template_code;
            $smstemplate = str_replace('[ GL_MASTERSMS ]', $request->int_master_sms_count, $smstemplate);
            $user = User::select('vchr_user_name','pk_int_user_id','int_status')->find($request->username);
            $smstemplate = str_replace('[ GL_USERNAME ]', $user->vchr_user_name, $smstemplate);
            $flag = $mastersms->save();
            if ($flag) {
                $subscrptionUser = UserSubscription::where('fk_int_user_id', $user->pk_int_user_id)->first();
                $subscrptionId = $subscrptionUser->pk_int_user_subscription_id;
                //plan
                $plan = Plan::where('name', 'Free')->first();
                //$request->days+$plan->grace_period;
                $subscription = UserSubscription::find($subscrptionId);
                $subscription->date_plan_to_date = Carbon::parse($subscription->date_plan_to_date)->addDays($request->days);
                $subscription->date_grace_period = Carbon::parse($subscription->date_grace_period)->addDays(($request->days + $plan->grace_period));
                $subscription->int_user_total_sms = $subscription->int_user_total_sms + $request->int_master_sms_count;
                $flag1 = $subscription->save();
                if ($flag1) {
                    $history = new MasterSmsHistory();
                    $history->vchr_master_sms_history_statement = $smstemplate;
                    $history->int_master_sms_history_type = MasterSmsHistory::CREDIT;
                    $flag2 = $history->save();
                    if ($flag2) {
                        //if user is inactive make user active
                        if ($user->int_status == User::DEACTIVATE) {
                            $user->int_status = User::ACTIVATE;
                            $user->save();
                        }
                        DB::commit();
                        return response()->json(['msg' => 'SMS added for user ' . $user->vchr_user_name, 'status' => 'success', 'data' => $smscount]);

                    } else {
                        DB::rollBack();
                        return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                    }
                } else {
                    DB::rollBack();
                    return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                }
            } else {
                DB::rollBack();
                return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
            }
            // }
            // catch(\Exception $e)
            // {
            //     return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

            //     //return $e->getMessage();
            // }

        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);

            // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
        }
    }

    public function getUserData($id)
    {
        try {
            $user = DB::table('tbl_users')->select('vchr_user_name', 'int_status', 'date_plan_to_date')->Join('tbl_user_subscription', 'tbl_user_subscription.fk_int_user_id', '=', 'tbl_users.pk_int_user_id')->where('tbl_users.pk_int_user_id', $id)->first();
            if (!empty($user)) {
                return response()->json(['msg' => 'data present', 'status' => 'success', 'data' => $user]);
            } else {
                return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
            }
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
        }
    }

}
