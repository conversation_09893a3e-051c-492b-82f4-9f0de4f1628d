<?php

namespace App\Http\Controllers\BackendController;

use App\BackendModel\SmsApi;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Auth;
use Validator;
use DataTables;

class SmsApiController extends Controller
{

	public function pushSms(Request $request)
	{
		$input=$request->all();
		$rule=[ 
			'mobileno' => 'required|numeric|digits:12',
			'sender'=>'required',
			'message'=>'required',
		];

		$validator = validator::make($input,$rule);
		if ($validator->passes()) 
		{
			try
			{
				$api= new SmsApi();
                $api->vchr_sender=$request->sender;
               	$api->vchr_mobile=$request->mobileno;
               	$api->txt_message=$request->message;
               	$flag=$api->save();
               	if($flag)
               	{
               		return response()->json(['message'=> 'Message Send Successfully', 'status' => 'success']); 
               	}
               	else
               	{
               		return response()->json(['message'=>'Failed', 'status' => 'fail']);
               	}
				
			}
			catch(\Exception $e)
			{
				return response()->json(['message'=>$e->getMessage(), 'status' => 'fail']);

			}
		}
		else
		{
				return response()->json(['message'=>$validator->messages(), 'status' => 'fail']);
		}
	}

	public function index()
    {
        
        return view('backend.pages.bulkpush.view-pushsms');
    }

    public function getPushSms()
    {
        $api = SmsApi::all();
		
        // print_r($enquiry_purpose); die;
        foreach ($api as $key => $row) {
            $row->slno=++$key;
        }
        return Datatables::of($api)
        ->editColumn('date', function ($api) {
            if ($api->created_at != null) {
                return $api->created_at;
            } else {
                return "'No Date'";
            }
        })
        ->editColumn('sender', function ($api) {
            if ($api->vchr_sender != null) {
                return $api->vchr_sender;
            } else {
                return "'No Sender'";
            }
        })
        ->editColumn('mobno', function ($api) {
            if ($api->vchr_mobile != null) {
                return $api->vchr_mobile;
            } else {
                return "'No Mobile no.'";
            }
        })
         ->editColumn('message', function ($api) {
            if ($api->txt_message != null) {
                return $api->txt_message;
            } else {
                return "'No Message.'";
            }
        })
        
        ->rawColumns(['show'])
        ->toJson(true);
    }

    public function api()
    {
        
        return view('backend.pages.bulkpush.view-pushsms-api');
    }
}