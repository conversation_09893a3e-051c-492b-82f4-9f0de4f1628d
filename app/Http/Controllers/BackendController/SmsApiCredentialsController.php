<?php

namespace App\Http\Controllers\BackendController;

use App\BackendModel\SmsApiCredentials;
use App\BackendModel\SmsDomain;
use App\BackendModel\Smsroute;
use App\Common\Variables;
use App\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Auth;
use Validator;
use DataTables;

class SmsApiCredentialsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $routeName = '';
        if ($request->has('priority')) {
            $routes = Smsroute::where('priority', $request->priority)->first();
            if ($routes) {
                $routeName = $routes->vchr_sms_route;
            } else {
                $routeName = '';
            }
        }
        $routes = Smsroute::where('int_sms_route_status', Smsroute::ACTIVATE)->get();
        $domains = SmsDomain::where('status', SmsDomain::ACTIVATE)->get();
        $users = User::where('int_status', User::ACTIVATE)->get();
        return View('backend.pages.sms-api.credentials')
            ->with('routes', $routes)
            ->with('domains', $domains)
            ->with('users', $users)
            ->with('route_name', $routeName);
    }

    public function getCredentials(Request $request)
    {
        $query = SmsApiCredentials::orderBy('vendor_id', 'ASC');
        if ($request->has('priority')) {
            $sms_route = Smsroute::where('priority', trim($request->priority))->select('pk_int_sms_route_id')->first();
            if ($sms_route) {
                $query = $query->where('route_id', $sms_route->pk_int_sms_route_id);
            }
        }

        $credentials = $query->get();
        foreach ($credentials as $key => $row) {
            $row->slno = ++$key;
        }
        return Datatables::of($credentials)
            ->editColumn('user', function ($credentials) {
                if ($credentials->vendor_id != 0) {
                    $user = User::find($credentials->vendor_id);
                    return $user->vchr_user_name;
                } else {
                    return "All Users";
                }
            })
            /* ->editColumn('route', function ($credentials) {
                 if ($credentials->route_id != null) {
                     $route = Smsroute::find($credentials->route_id);
                     return $route->vchr_sms_route;
                 } else {
                     return "No Route";
                 }
             })*/
            ->editColumn('domain', function ($credentials) {
                if ($credentials->sms_domain_id != null) {
                    $domain = SmsDomain::find($credentials->sms_domain_id);
                    return $domain->domain . '(' . $domain->title . ')';
                } else {
                    return "No Domain";
                }
            })->editColumn('credentials', function ($credentials) {
                if ($credentials->username != null) {
                    if ($credentials->status == Variables::ACTIVE) {
                        $status = "Active";
                    } else {
                        $status = "InActive";
                    }
                    $content = '<div>
                                <div class="row">
                                    <div class="col-sm-4">UserName:</div>
                                    <div class="col-sm-7">' . $credentials->username . ' </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-4">Password:</div>
                                    <div class="col-sm-7">' . $credentials->password . ' </div>
                                </div>
                                 <div class="row">
                                    <div class="col-sm-4">Api Password:</div>
                                    <div class="col-sm-7">' . $credentials->api_password . ' </div>
                                </div> 
                                 <div class="row">
                                    <div class="col-sm-4">Status:</div>
                                    <div class="col-sm-7">' . $status . ' </div>
                                </div>
                                
                           </div>';
                    return $content;
                } else {
                    return "No Credentials";
                }
            })
            ->addColumn('show', function ($credentials) {
                if ($credentials->status == Variables::ACTIVE) {
                    return '<div class="dropdown show">
                                <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fa fa-circle" aria-hidden="true"></i>
                                    <i class="fa fa-circle" aria-hidden="true"></i>
                                    <i class="fa fa-circle" aria-hidden="true"></i>   
                                </a>
                                <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                    <a designation-id="' . $credentials->id . '" class="dropdown-item  ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="tooltip" title="View" > 
                                        <i class="f-16 fa fa-angle-double-right"></i>&nbsp;View
                                    </a>
                                    <a id="delete_designation" designation-id="' . $credentials->id . '" class="dropdown-item designation-delete"> 
                                        <i class="f-16 fa fa-trash-o"></i>&nbsp;Delete
                                    </a>
                                    <a class="dropdown-item btn-activate designation-act" designation-id="' . $credentials->id . '" data-toggle="tooltip" title="Deactivate"> 
                                        <i class="f-18 fa fa-check-circle active"></i>&nbsp;Deactivate
                                    </a>
                                </div>
                            </div>';
                } else {
                    return '<div class="dropdown show">
                                <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fa fa-circle" aria-hidden="true"></i>
                                    <i class="fa fa-circle" aria-hidden="true"></i>
                                    <i class="fa fa-circle" aria-hidden="true"></i>   
                                </a>
                                <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                    <a designation-id="' . $credentials->id . '" class="dropdown-item  ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="tooltip" title="View" > 
                                        <i class="f-16 fa fa-angle-double-right"></i>&nbsp;View
                                    </a>
                                    <a href="" designation-id="' . $credentials->id . '" class="dropdown-item designation-delete "> 
                                        <i class="f-16 fa fa-trash-o"></i>&nbsp;Delete
                                    </a>
                                    <a class="dropdown-item designation-act " designation-id="' . $credentials->id . '" data-toggle="tooltip" title="Activate"> 
                                        <i class="f-18 fa fa-times-circle"></i>&nbsp;Activate
                                    </a
                                </div>
                            </div>';
                }
            })->rawColumns(['credentials', 'show'])->toJson(true);
    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $credentials = SmsApiCredentials::find($id);
            if ($credentials) {
                $credentials->delete();
                return response(['msg' => 'Credentials is deleted.', 'status' => 'success']);
            } else {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            }
        } catch (\Exception $ex) {
            return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
        }
    }


    public function deactivate($id)
    {
        $credentials = SmsApiCredentials::where('id', $id)->first();
        if ($credentials) {
            $credentials->status = SmsApiCredentials::DEACTIVATE;
            $flag = $credentials->save();
            if ($flag) {
                return response()->json(['msg' => "Credentials deactivated.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Credentials could not be deactivated.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Domain not found.", 'status' => 'fail']);
        }
    }

    public function activate($id)
    {
        $credentials = SmsApiCredentials::where('id', $id)->first();
        if ($credentials) {
            SmsApiCredentials::where('vendor_id', $credentials->vendor_id)
                ->where('route_id', $credentials->route_id)
                ->update(['status' => SmsApiCredentials::DEACTIVATE]);
            $credentials->status = SmsApiCredentials::ACTIVATE;
            $flag = $credentials->save();
            if ($flag) {
                return response()->json(['msg' => "Credentials activated.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Credentials could not be activated.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Credentials not found.", 'status' => 'fail']);
        }
    }


    public function store(Request $request)
    {
        // get the POST data
        $input = $request->all();
        // if validator passes
        $validator = validator::make($input, SmsApiCredentials::$rule, SmsApiCredentials::$message);
        if ($validator->passes()) {

            try {
                $id = $request->vendor_id;
                SmsApiCredentials::where('vendor_id', $id)->where('route_id', $request->route_id)->update(['status' => SmsApiCredentials::DEACTIVATE]);
                $credentials = new SmsApiCredentials();
                $credentials->fill($input);
                $credentials->status = SmsApiCredentials::ACTIVATE;
                $flag = $credentials->save();
                if ($flag) {
                    return response()->json(['msg' => 'Credentials added.', 'status' => 'success']);
                } else {
                    return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
        }
    }

    public function show($id)
    {
        //$credentials=SmsApiCredentials::find($id);
        $credentials = DB::table('sms_domain')
            ->select('*')
            ->join('sms_api_credentials', 'sms_api_credentials.sms_domain_id', '=', 'sms_domain.id')->where('sms_api_credentials.id', $id)->first();
        if ($credentials) {
            return response()->json(['msg' => "Credentials detail found.", 'status' => 'success', 'data' => $credentials]);
        } else {
            return response()->json(['msg' => "Credentials detail not found.", 'status' => 'fail']);
        }
    }

    public function update(Request $request, $id)
    {
        // get the POST data
        $input = $request->all();
        $validator = validator::make($input, SmsApiCredentials::$rule, SmsApiCredentials::$message);
        if ($validator->passes()) {
            try {
                $credentials = SmsApiCredentials::find($id);
                $credentials->fill($input);
                //$credentials->status = SmsApiCredentials::ACTIVATE;
                $flag = $credentials->save();
                if ($flag) {
                    return response()->json(['msg' => 'Credentials updated.', 'status' => 'success']);
                } else {
                    return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
        }
    }
    public function showRoutes(Request $request)
    {
        if($request->ajax())
        {
            $query = SmsApiCredentials::with(['subdomain','user'])->groupBy(['vendor_id','route_id'])->orderBy('vendor_id', 'ASC');
            

            $credentials = $query->get();
            foreach ($credentials as $key => $row) {
                $row->slno = ++$key;
                $row->domains= SmsApiCredentials::with(['subdomain','user','route'])->where('vendor_id',$row->vendor_id)->where('route_id',$row->route_id)->get();
                
            }
            return Datatables::of($credentials)
                ->editColumn('user', function ($credentials) {
                    if ($credentials->vendor_id != 0) {
                        $user = $credentials->user;
                        return $user->vchr_user_name;
                    } else {
                        return "All Users";
                    }
                })
                 ->editColumn('route', function ($credentials) {
                    if ($credentials->route_id != null) {
                        $route = $credentials->route;
                        return $route->vchr_sms_route;
                    } else {
                        return "No Route";
                    }
                })
                ->editColumn('domain', function ($credentials) {
                    $cred=SmsApiCredentials::with(['subdomain','user'])->active()->where('vendor_id',$credentials->vendor_id)->where('route_id',$credentials->route_id)->first();
                    if ($cred && $cred->sms_domain_id != null) {
                        $domain = $cred->subdomain;
                        return $domain->domain . '(' . $domain->title . ')';
                    } else {
                        return "No Domain";
                    }
                })
                ->editColumn('username', function ($credentials) {
                    $cred=SmsApiCredentials::active()->where('vendor_id',$credentials->vendor_id)->where('route_id',$credentials->route_id)->first();
                    if ($cred ) {
                        return $cred->username;
                    } else {
                        return "No Default Domain";
                    }
                })
                ->addColumn('edit', function ($credentials) {
                    $domains=[];
                    foreach($credentials->domains as $dom)
                    {
                        $domains[]=['username'=>$dom->username,'title'=>$dom->subdomain ? $dom->subdomain->title : "No Domain",'id'=>$dom->id];
                    }
                    $data=collect($domains)->sortBy('title')->values()->all();
                   return '<button credential-id="' . $credentials->id . '" class="btn btn-sm btn-info btn-edit" data-domains=\''.json_encode($data).'\'" data-toggle="tooltip" title="Edit" > <i class="f-16 fa fa-pencil"></i></button>';
                })->rawColumns(['credentials', 'edit'])->toJson(true);
            }
            $routeName = '';
            if ($request->has('priority')) {
                $routes = Smsroute::where('priority', $request->priority)->first();
                if ($routes) {
                    $routeName = $routes->vchr_sms_route;
                } else {
                    $routeName = '';
                }
            }
            $routes = Smsroute::where('int_sms_route_status', Smsroute::ACTIVATE)->get();
            $domains = SmsDomain::where('status', SmsDomain::ACTIVATE)->get();
            $users = User::where('int_status', User::ACTIVATE)->get();
            
            return View('backend.pages.sms-api.routes')
            ->with('routes', $routes)
            ->with('domains', $domains)
            ->with('users', $users)
            ->with('route_name', $routeName); 
    }
    public function updateDefaultRoute(Request $request, $id)
    {
        // get the POST data
        $input = $request->all();
        $validator = validator::make($input, ['default_id'=>'required'],[]);
        if ($validator->passes()) {
            try {
                $credential = SmsApiCredentials::find($id);
                $flag = SmsApiCredentials::where('vendor_id',$credential->vendor_id)
                ->where('route_id',$credential->route_id)->update(['status'=>0]);
                $flag = SmsApiCredentials::where('id',$request->default_id)->update(['status'=>1]);
                if ($flag) {
                    return response()->json(['msg' => 'Updated.', 'status' => 'success']);
                } else {
                    return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
        }
    }

}