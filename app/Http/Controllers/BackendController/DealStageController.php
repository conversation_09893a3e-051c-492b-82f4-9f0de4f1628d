<?php

namespace App\Http\Controllers\BackendController;

use App\DealStage;
use App\DealCategory;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use Validator;
use App\User;
use DataTables;
use DB;

class DealStageController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $dealCategory = DealCategory::all();
        $sl_deal_name = getDealName(true);
        return view('gl-crm.pages.user.deals.deal_stages',compact('dealCategory','sl_deal_name'));

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $vendorId = User::getVendorId();
        $input = $request->all();
        $input['fk_int_user_id'] = $vendorId;
        $validator = validator($input, DealStage::$rules, DealStage::$rulesMessage);

        if ($validator->passes()) {
            $exist = DealStage::where("fk_int_user_id",$vendorId)->where(\DB::raw('LOWER(`deal_stage_name`)'),strtolower($input['deal_stage_name']))->first();
            if($exist)
            { return response()->json(['msg' => 'The deal stage already exists.', 'status' => 's-error']);}

            $priority = DealStage::where("fk_int_user_id",$vendorId)->where('priority',$input['deal-priority'])->first();
            if($priority)
             { return response()->json(['msg' => 'Priority exist', 'status' => 's-error']);}

           
            $stage = new DealStage();
            $stage->deal_stage_name=request('deal_stage_name');
            $stage->deal_category_id = request('stage');
            $stage->priority = request('deal-priority');
            $stage->deal_stage_description = $request->deal_stage_description??'';
            $stage->fk_int_user_id = $vendorId;
            $flag = $stage->save();
            if ($flag) {
                return response()->json(['msg' => getDealName(true)."Status Added.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Something went wrong, please try again later", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $dealStage = DealStage::where('pk_int_deal_stage_id', $id)->first();

        if ($dealStage) {
            return response()->json(['msg' => "Deal Status detail found.", 'status' => 'success', 'data' => $dealStage]);
        } else {
            return response()->json(['msg' => "Deal Status detail not found.", 'status' => 'fail']);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $vendorId = User::getVendorId();
        $dealStage = DealStage::find($id);
        if ($dealStage) {
            $input = $request->all();
            $validator = validator($input, DealStage::$rules, DealStage::$rulesMessage);

            if ($validator->passes()) {
                $exist = dealStage::where("fk_int_user_id",$vendorId)->where(\DB::raw('LOWER(`deal_stage_name`)'),strtolower($input['deal_stage_name']))->where("pk_int_deal_stage_id","!=",$id)->first();
                if($exist)
                 { return response()->json(['msg' => 'Deal Status exist', 'status' => 'error']); }

                $priority = DealStage::where("fk_int_user_id",$vendorId)->where('priority',$input['deal-priority'])->first();
                if($priority && request('deal-priority') != $dealStage->priority )
                   { return response()->json(['msg' => 'Priority exist', 'status' => 'error']); }

                $dealStage->deal_stage_name=request('deal_stage_name');
                $dealStage->deal_category_id = request('stage');
                $dealStage->priority = request('deal-priority');
                $dealStage->deal_stage_description = $request->deal_stage_description??'';
                $dealStage->fk_int_user_id = $vendorId;
                $flag = $dealStage->save();
                if ($flag) {
                    return response()->json(['msg' => "Deal Status Updated.", 'status' => 'success']);
                } else {
                    return response()->json(['msg' => "Something went wrong, please try again later", 'status' => 'fail']);
                }
            } else {
                return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $dealStage=DealStage::find($id);
        if($dealStage->deals()?->exists()){
            return response()->json(['msg' => "Status could not be deleted,Deal exists", 'status' => 'fail']);
        }

        if (!empty($dealStage)) {
            $flag = $dealStage->delete();
            if ($flag) {
                return response()->json(['msg' => "Deal Status deleted.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Deal Status could not be deleted.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Deal Status not found.", 'status' => 'fail']);
        }
    }

    public function getDealStage()
    {
        $vendorId = User::getVendorId();
        $dealStage = DealStage::where('fk_int_user_id', $vendorId)
        ->orderby('pk_int_deal_stage_id','DESC')

        ->get();
        // print_r($dealStage); die;
        foreach ($dealStage as $key => $row) {
            $row->slno=++$key;
        }
        return DataTables::of($dealStage)
        ->editColumn('deal_status', function ($dealStage) {
            if ($dealStage->deal_stage_name != null) {
                return $dealStage->deal_stage_name;
            } else {
                return "No Status";
            }
        })
        ->editColumn('deal_stage', function ($dealStage) {
            if ($dealStage->dealCategory) {
                return $dealStage->dealCategory->name;
            } else {
                return "No Stage";
            }
        })
        ->addColumn('show', function ($dealStage) {
                return '<div class="dropdown show">
                            <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa fa-circle" aria-hidden="true"></i>
                                <i class="fa fa-circle" aria-hidden="true"></i>
                                <i class="fa fa-circle" aria-hidden="true"></i> 
                            </a>
                            <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                <a class="dropdown-item ks-izi-modal-trigger1" href="#" feedback-id="' . $dealStage->pk_int_deal_stage_id . '" data-target="#ks-izi-modal-large1"  data-toggle="modal">
                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32"><path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4l15-15zm-5-5L24 7.6l-3 3L17.4 7l3-3zM6 22v-3.6l10-10l3.6 3.6l-10 10H6z"/></svg>
                                &nbsp;Edit
                                </a>
                                <a href="#" class="dropdown-item feedback-delete" id="delete_plan" feedback-id="' . $dealStage->pk_int_deal_stage_id . '">
                                    <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete
                                </a>
                            </div>
                        </div>';
            
        })
        ->rawColumns(['show'])
        ->toJson(true);
    }

    public function getDealStageList()
    {
        $dealStage = DealStage::select('deal_type_name as name', 'pk_int_deal_type_id  as id')
        ->where('fk_int_user_id',User::getVendorId())
        ->get();
        if(count($dealStage)){
            return response()->json(['msg' => 'Deal Status available.', 'status' => 'success', 'data' => $dealStage]);
        }else{
            return response()->json(['msg' => 'Deal Status not available.', 'status' => 'fail']);
        }
    }
}
