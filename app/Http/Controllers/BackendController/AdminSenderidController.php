<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use App\BackendModel\SenderId;
use DB;
use Carbon\Carbon;
use DataTables;
use Auth;
use App\BackendModel\ShortCodes;
use App\BackendModel\SmsTemplate;
use App\BackendModel\EmailTemplate;
use App\BackendModel\SenderidHistory;


class AdminSenderidController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
          return view('backend.pages.adminsenderid.index');
        
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
     public function store(Request $request)
    {
        $input = $request->only(['sendername','senderid']);
        $input['fk_int_user_id']=Auth::user()->pk_int_user_id;

         $validator = validator::make($input, SenderId::$rule, SenderId::$message);
        
        if ($validator->passes()) 
        {
           
            try
            {
                $user=User::find($input['fk_int_user_id']);
                if(!empty($user))
                {
                    DB::beginTransaction();

                    //check sms template and short code for use
                $code=ShortCodes::where('vchr_short_code_template','GL_SENDERID')->first();
                if($code)
                {
                     return response()->json(['msg'=>'Please add short code "GL_SENDERID"', 'status'=>'fail']);
                }

                $smstemplate=SmsTemplate::where('vchr_sms_template_name','SENDERID HISTORY')->first();
                if(empty($smstemplate))
                {
                     return response()->json(['msg'=>'Add SMS Template "SENDERID HISTORY"  with "GL_SENDERID" shortcode before you procced', 'status'=>'fail']);
                }

                    $userDefaultSenderIdCount=SenderId::where('fk_int_user_id',Auth::user()->pk_int_user_id)->where('int_default_sender_id',SenderId::DEFAULT)->count();

                    $sender = new SenderId;
                    $sender->vchr_sender_id_name=$input['sendername'] ;
                    $sender->fk_int_user_id=Auth::user()->pk_int_user_id ;
                    $sender->vchr_sender_id=$input['senderid'] ;

                    //first sender id set as default

                    if($userDefaultSenderIdCount>0)
                    {
                        $sender->int_default_sender_id=SenderId::UNDEFAULT;
                    }
                    else
                    {
                         $sender->int_default_sender_id=SenderId::DEFAULT;
                    }
                    // $sender->int_default_sender_id=SenderId::UNDEFAULT;
                    $sender->int_sender_id_status=SenderId::ACTIVATE;
                    $flag=$sender->save();
                    if($flag)
                    {
                        $history=new SenderidHistory();
                        $body = str_replace('[GL_SENDERID]',$input['senderid'], $smstemplate->vchr_sms_template_code);
                        
                        $history->text_senderid_statement=$body;
                        $history->fk_int_user_id=Auth::user()->pk_int_user_id;
                        $history->int_status=SenderidHistory::ACTIVATE;
                        $history->save();

                        DB::commit();

                        //trigger email to admin 

                         return response()->json(['msg'=>'Senderid added', 'status'=>'success']);
                            
                       

                    }
                    else
                    {
                        DB::rollBack();
                       return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                           
                    }
                }
                else
                { 
                 return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                }
            }
            catch (\Exception $e) {
            // something went wrong whilst attempting to encode the token
                 return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);
                           
            
            }
            

        }
        else
        {
            return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);
        }

    }

      public function adminStore(Request $request)
    {
        $input = $request->only(['fk_int_user_id','sendername','senderid']);

         $validator = validator::make($input, SenderId::$rule, SenderId::$message);
        
        if ($validator->passes()) 
        {
           
            try
            {
                $user=User::find($input['fk_int_user_id']);
                if(!empty($user))
                {
                    DB::beginTransaction();

                    //check sms template and short code for use
                $code=ShortCodes::where('vchr_short_code_template','GL_SENDERID')->first();
                if($code)
                {
                     return response()->json(['msg'=>'Please add short code "GL_SENDERID"', 'status'=>'fail']);
                }

                $smstemplate=SmsTemplate::where('vchr_sms_template_name','SENDERID HISTORY')->first();
                if(empty($smstemplate))
                {
                     return response()->json(['msg'=>'Add SMS Template "SENDERID HISTORY"  with "GL_SENDERID" shortcode before you procced', 'status'=>'fail']);
                }

                    $userDefaultSenderIdCount=SenderId::where('fk_int_user_id',Auth::user()->pk_int_user_id)->where('int_default_sender_id',SenderId::DEFAULT)->count();

                    $sender = new SenderId;
                    $sender->vchr_sender_id_name=$input['sendername'] ;
                    $sender->fk_int_user_id=$input['fk_int_user_id'] ;
                    $sender->vchr_sender_id=$input['senderid'] ;

                    //first sender id set as default

                    if($userDefaultSenderIdCount>0)
                    {
                        $sender->int_default_sender_id=SenderId::UNDEFAULT;
                    }
                    else
                    {
                         $sender->int_default_sender_id=SenderId::DEFAULT;
                    }
                    // $sender->int_default_sender_id=SenderId::UNDEFAULT;
                    $sender->int_sender_id_status=SenderId::ACTIVATE;
                    $flag=$sender->save();
                    if($flag)
                    {
                        $history=new SenderidHistory();
                        $body = str_replace('[GL_SENDERID]',$input['senderid'], $smstemplate->vchr_sms_template_code);
                        
                        $history->text_senderid_statement=$body;
                        $history->fk_int_user_id=Auth::user()->pk_int_user_id;
                        $history->int_status=SenderidHistory::ACTIVATE;
                        $history->save();

                        DB::commit();

                        //trigger email to admin 

                         return response()->json(['msg'=>'Senderid added', 'status'=>'success']);
                            
                       

                    }
                    else
                    {
                        DB::rollBack();
                       return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                           
                    }
                }
                else
                { 
                 return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                }
            }
            catch (\Exception $e) {
            // something went wrong whilst attempting to encode the token
                 return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);
                           
            
            }
            

        }
        else
        {
            return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);
        }

    }
    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        
         $senderid=SenderId::where('fk_int_user_id',Auth::user()->pk_int_user_id)->where('pk_int_sender_id',$id)->first();
        if($senderid)
        {
            
             
            return response()->json(['msg'=>'data present', 'status'=>'success','data'=>$senderid]);
        }
        else
        {
             return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
        }
          
           
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        
        $input = $request->all();
         if(!isset($id))
         {
            DB::rollBack();
            return response([
            'status' => 'error',
            'message' =>'id not found',
            ], 404);
         }

         $validator = validator::make($input, SenderId::$updateRule, SenderId::$updateMessage);
        
        if ($validator->passes()) 
        {
           
            try
            {
                $sender=SenderId::FindOrFail($id);
                if(!empty($sender))
                {
                    DB::beginTransaction();
                    $sender->vchr_sender_id_name=$input['sendername'] ;
                    $sender->vchr_sender_id=$input['senderid'] ;
                    $flag=$sender->save();
                    if($flag)
                    {
                        DB::commit();

                        //trigger mail to admin for resubmission of sender id
                   
                         return response()->json(['msg'=>'Senderid is updated,', 'status'=>'success']);

                    }
                    else
                    {
                        DB::rollBack();

                        return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                    }
                }
                else
                {
                     return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                }
            }
            catch (\Exception $e) {
            // something went wrong whilst attempting to encode the token
            
             return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);
 
            }
            

        }
        else
        {
            return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
        public function destroy($id)
    {
         try
        {    
            DB::beginTransaction();

           $flag=false;

            $sender = SenderId::findOrfail($id);
            if($sender->int_default_sender_id == 1)
            {
                 
                 
                 // get previous user id
                 $previous = SenderId::where('pk_int_sender_id', '<', $id)->where('int_default_sender_id',SenderId::UNDEFAULT)->max('pk_int_sender_id');

                // get next user id
                 $next = SenderId::where('pk_int_sender_id', '>', $id)->where('int_default_sender_id',SenderId::UNDEFAULT)->min('pk_int_sender_id');
                 
                 if(isset($previous))
                 {
                    $defaultSender=SenderId::find($previous);
                    $defaultSender->int_default_sender_id=SenderId::DEFAULT;
                     $flag1=$defaultSender->save();
                     if($flag1)
                     {
                         $flag=true;
                     }
                 }
                 else if(isset($next))
                 {
                    $defaultSender=SenderId::find($next);
                    $defaultSender->int_default_sender_id=SenderId::DEFAULT;
                     $flag1=$defaultSender->save();
                     if($flag1)
                     {
                         $flag=true;
                     }
                 }
                 else
                 {
                    $flag=true;
                 } 
            }
            else
            {
               $flag=true; 
            }
               
            if($sender->delete()) 
            {
                if($flag)
                {
                    DB::commit();
                     return response()->json([
                     'status' => 'success',
                     'msg' => 'Deleted successfully'
                    ]);
                }
                else
                {
                    DB::rollBack();
                        return response()->json([
                            'status' => 'error',
                            'msg' =>'Could not delete.',
                        ]);
                }
            }
            else{
                 DB::rollBack();
                        return response()->json([
                            'status' => 'error',
                            'msg' =>'Could not delete.',
                        ]);

            } 
        }
        catch (\Exception $e) {
                DB::rollBack();
            // something went wrong whilst attempting to encode the token
            return response()->json(['status'=>'error','msg'=> $e->getMessage()]);
        }
    }

        public function getInfo()
    {
        $template = DB::table('tbl_users')
        ->select('vchr_user_name','pk_int_sender_id','vchr_sender_id_name','vchr_sender_id','int_default_sender_id','int_sender_id_status')
        ->join('tbl_sender_id', 'tbl_users.pk_int_user_id', '=', 'tbl_sender_id.fk_int_user_id')->orderby('pk_int_sender_id','DESC')
        ->whereNull('tbl_sender_id.deleted_at')
        ->where('tbl_users.pk_int_user_id',Auth::user()->pk_int_user_id)
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

        if ($template->int_sender_id_status == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                      <button senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-info px-3 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large"  data-toggle="tooltip" title="View" > <i class="f-16 la la-angle-double-right"></i></button>

                                    <button id="delete_plan" senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-danger senderid-delete px-3"> <i class="f-16 la la-trash-o"></i></button>

                                     <a class="btn btn-success btn-activate senderid-act px-3 "   senderid-id="' . $template->pk_int_sender_id . '" data-toggle="tooltip" title="Sender id is active"> <i class="f-18 la la-check-circle active"></i></a>
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                       <button senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-info px-3 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large"  data-toggle="tooltip" title="View" > <i class="f-16 la la-angle-double-right"></i></button>

                                    <button href="" senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-danger senderid-delete px-3"> <i class="f-16 la la-trash-o"></i></button>
                                    <a class="btn btn-warning  px-3 senderid-act" senderid-id="' . $template->pk_int_sender_id . '" data-toggle="tooltip" title="Admin approval pending"> <i class="f-18 la la-times-circle"></i></a>
                                </div>';
                    }
                })->addColumn('default', function ($template) {

        if ($template->int_default_sender_id == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group" title="Default sender id">
                                   
                                    <button id="" senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-outline-success  px-3" style="color:green" >DEFAULT</button>
                                    <button class="btn btn-outline-success ks-no-text">
                                            <span class="la la-check ks-icon"></span>
                                        </button>
                                     
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    
                                     <button class="btn btn-outline-danger ks-no-text default-senderid" senderid-id="' . $template->pk_int_sender_id . '" title="Click to activate default senderid">
                                            <span class="la la-close ks-icon"></span>
                                        </button>
                                   
                                </div>';
                    }
                })
                ->rawColumns(['show','default'])
                ->toJson(true);
    }

     public function getSingleInfo(Request $request)
    {
       if(isset($request->id))
       {
             $template = DB::table('tbl_users')
            ->select('vchr_user_name','pk_int_sender_id','vchr_sender_id_name','vchr_sender_id','int_default_sender_id','int_sender_id_status')
            ->join('tbl_sender_id', 'tbl_users.pk_int_user_id', '=', 'tbl_sender_id.fk_int_user_id')->orderby('pk_int_sender_id','DESC')
            ->whereNull('tbl_sender_id.deleted_at')
            ->where('tbl_users.pk_int_user_id',$request->id)
            ->where('tbl_sender_id.int_sender_id_status',SenderId::ACTIVATE)
            ->get();
       }
       else
       {
            $template=[];
       }

        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

     
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    <button id="delete_plan" senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-danger senderid-delete px-3"> <i class="f-16 la la-trash-o"></i></button>

                                     
                                </div>';
                    
                })->addColumn('default', function ($template) {

        if ($template->int_default_sender_id == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group" title="Default sender id">
                                   
                                    <button id="" senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-outline-success  px-3" style="color:green" >DEFAULT</button>
                                    <button class="btn btn-outline-success ks-no-text">
                                            <span class="la la-check ks-icon"></span>
                                        </button>
                                     
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    
                                     <button class="btn btn-outline-danger ks-no-text default-senderid" senderid-id="' . $template->pk_int_sender_id . '" title="Click to activate default senderid">
                                            <span class="la la-close ks-icon"></span>
                                        </button>
                                   
                                </div>';
                    }
                })
                ->rawColumns(['show','default'])
                ->toJson(true);
    }
    

    public function getInfoHistory()
    {

        $template =SenderidHistory::orderby('pk_int_senderid_history_id','DESC')->where('fk_int_user_id',Auth::user()->pk_int_user_id)->get();
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
                ->toJson(true);
    }
    
    public function defaultSenderId($id)
     {

        try
        { 
            DB::beginTransaction();
            
             $sender=SenderId::find($id);
             $isActive=$sender->int_sender_id_status;

            

            $flag=false;
            $defaultSender = SenderId::where('int_default_sender_id',SenderId::DEFAULT)->where('fk_int_user_id',Auth::user()->pk_int_user_id)->first();
            if(!empty($defaultSender))
            {
                $reserDefault=SenderId::find($defaultSender->pk_int_sender_id);

                $reserDefault->int_default_sender_id=SenderId::UNDEFAULT;
                $flag1=$reserDefault->save();
                if ($flag1) {
                    $flag=true;
                }
                if($flag)
                { 
                   
                    $sender->int_default_sender_id=SenderId::DEFAULT;
                     $flag2=$sender->save();
                     if($flag2)
                     {
                         DB::commit();
                         return response()->json([
                         'status' => 'success',
                         'msg' => 'set as default successfully'
                        ]);
                     }
                     else{
                     DB::rollBack();
                            return response()->json([
                                'status' => 'error',
                                'msg' =>'Could not set as default',
                            ]);

                     } 
                }
                else{
                     DB::rollBack();
                            return response([
                                'status' => 'error',
                                'message' =>'Could not set as default',
                            ], 400);

                } 

            }
            else
            {
                  $sender=SenderId::find($id);
                    $sender->int_default_sender_id=SenderId::DEFAULT;
                     $flag2=$sender->save();
                     if($flag2)
                     {
                         DB::commit();
                         return response()->json([
                         'status' => 'success',
                         'msg' => 'set as default successfully'
                        ]);
                     }
                     else{
                     DB::rollBack();
                            return response()->json([
                                'status' => 'error',
                                'msg' =>'Could not set as default',
                            ]);

                     } 
            }
        }
        catch (\Exception $e) {
                DB::rollBack();
            // something went wrong whilst attempting to encode the token
            return response()->json(['status'=>'error','message'=> $e->getMessage()], 500);
        }
     }
    public function deactivate( $id)
    {
         $template = SenderId::where('pk_int_sender_id', $id)->first();
        if ($template) {
            if($template->int_default_sender_id==SenderId::DEFAULT)
            {
                return response()->json(['msg' => "Default Sender id could not be deactivated.", 'status' => 'fail']);
            }
            $template->int_sender_id_status = SenderId::DEACTIVATE;
            $flag = $template->save();
            if ($flag) {
                return response()->json(['msg' => "Sender id deactivated.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Sender id could not be deactivated.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Sender id not found.", 'status' => 'fail']);
        }
    }

    public function activate($id)
    {
        $template = SenderId::where('pk_int_sender_id', $id)->first();
        if ($template) {
            $template->int_sender_id_status = SenderId::ACTIVATE;
            $flag = $template->save();
            if ($flag) {
                return response()->json(['msg' => "Sender id approved.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Sender id could not be approved.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Sender id not found.", 'status' => 'fail']);
        }
    }
}
