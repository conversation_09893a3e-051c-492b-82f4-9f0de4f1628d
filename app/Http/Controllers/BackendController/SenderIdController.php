<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Validator;
use DataTables;
use App\Common\Variables;
use DB;
use Illuminate\Support\Facades\Input;
use App\BackendModel\SenderId;
use App\BackendModel\ShortCodes;
use App\BackendModel\SmsTemplate;
use App\BackendModel\EmailTemplate;
use App\Jobs\SendEmailJob;
use App\User;
use Mail;
use App\Common\SendTelegram;
use App\Common\SingleSMS;
use App\Common\Notifications;


class SenderIdController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
         return view('backend.pages.apitemplates.senderid');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
     try {
            $template = SenderId::find($id);
            if ($template) {
                SenderId::where('pk_int_sender_id', $id)->delete();
                return response(['msg' => 'Sender id  is deleted.', 'status' => 'success']);
            }
            else {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            }
        }
        catch (\Exception $ex) {
            return response(['msg' =>'Something Went Wrong'  , 'status' => 'failed']);
                
        }
    }

/**
 * [getInfo description]
 * @return [type] [description]
 */
    public function getInfo()
    {
        $template = DB::table('tbl_users')
                    ->select('vchr_user_name','pk_int_sender_id','vchr_sender_id_name','vchr_sender_id','int_default_sender_id','int_sender_id_status')
                    ->join('tbl_sender_id', 'tbl_users.pk_int_user_id', '=', 'tbl_sender_id.fk_int_user_id')
                    ->orderby('pk_int_sender_id','DESC')
                    ->whereNull('tbl_sender_id.deleted_at')
                    ->get();   
        foreach ($template as $key => $row) {
            $row->slno=++$key;
        }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {
        if ($template->int_sender_id_status == 1) {
                        return '<div class="dropdown show">
                                    <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>   
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <a id="delete_plan" senderid-id="' . $template->pk_int_sender_id . '"  class="dropdown-item senderid-delete"> 
                                            <i class="f-16 fa fa-trash-o"></i>&nbsp;Delete
                                        </a>
                                        <a class="dropdown-item btn-activate senderid-act ks-izi-modal-trigger1"  data-target="#ks-izi-modal-large" senderid-id="' . $template->pk_int_sender_id . '" data-toggle="tooltip" title="Reject"> 
                                            <i class="f-18 fa fa-check-circle active"></i>&nbsp;Reject
                                        </a>
                                    </div>
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '<div class="dropdown show">
                                    <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>
                                        <i class="fa fa-circle" aria-hidden="true"></i>   
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <a href="" senderid-id="' . $template->pk_int_sender_id . '" class="dropdown-item senderid-delete"> 
                                            <i class="f-16 fa fa-trash-o"></i>&nbsp;Delete
                                        </a>
                                        <a class="dropdown-item btn-warning senderid-act" senderid-id="' . $template->pk_int_sender_id . '" data-toggle="tooltip" title="Approve">
                                            <i class="f-18 fa fa-times-circle"></i>&nbsp;Approve
                                        </a>';
                    }
                })->rawColumns(['show'])->toJson(true);
    }
    
/**
 * [deactivate description]
 * @param  Request $request [description]
 * @return [type]           [description]
 */
    public function deactivate(Request $request)
    {
        $template = SenderId::where('pk_int_sender_id', $request->id)->first();
        if ($template) {
            try {
            $user=User::select('email','vchr_user_name','pk_int_user_id')->find($template->fk_int_user_id);
            $input['email']=$user->email;
            $input['username']=$user->vchr_user_name;
            //check email template and short code for use
            $code=ShortCodes::where('vchr_short_code_template','GL_REJECTION_REASON')->first();
                if(sizeof($code) == 0){
                     return response()->json(['msg'=>'Please add short code "GL_REJECTION_REASON"', 'status'=>'fail']);
                }
                $emailtemplate = EmailTemplate::where('vchr_email_template_name','USER SENDER ID REJECTION')->first();
                if(!$emailtemplate) {
                     return response()->json(['msg'=>'Add EMAIL Template "USER SENDER ID REJECTION" with "GL_REJECTION_REASON" shortcode  before you procced', 'status'=>'fail']);
                }
                $template->int_sender_id_status = SenderId::DEACTIVATE;
                $flag = $template->save();
                if ($flag) {
                    //email for template
                    $body = str_replace('[GL_REJECTION_REASON]',$request->reason, $emailtemplate->vchr_email_template_code);

                    $data=['template'=>'welcome','email'=>$input['email'],'name'=>$input['username'],'subject'=>'SENDERID Rejected','body'=>$body];
                                         
                    Mail::send("emails.".$data['template'], $data, function($message) use ($data)
             {
                $message
                    ->from('<EMAIL>',config('app.name'))
                    ->to($data['email'],$data['name'])
                    ->subject($data['subject']);
             });
                return response()->json(['msg' => "Sender id rejected.", 'status' => 'success']);
                } else {
                    return response()->json(['msg' => "Sender id could not be rejected.", 'status' => 'fail']);
                }
            }
            catch(\Exception $e){
                return response()->json(['msg' =>$e->getMessage(), 'status' => 'fail']);
            }  
        } else {
            return response()->json(['msg' => "Sender id not found.", 'status' => 'fail']);
        }
    }

    /**
     * [activate description]
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    public function activate($id)
    {
        $template = SenderId::where('pk_int_sender_id', $id)->first();
        if ($template) {
            $template->int_sender_id_status = SenderId::ACTIVATE;
            $flag = $template->save();
            if ($flag) {
                $userObject = User::getUserDetails($template->fk_int_user_id);
                $userAdminObject = User::getSingleAdminDetails();
                //Notifications
                $notifications = new Notifications();
                $from = env('MAIL_FROM_ADDRESS');
                $to = $userObject->email;
                $subject = "SenderId Approved";
                $name = $userObject->vchr_user_name;
                $logo = $userAdminObject->vchr_logo;
                $attachment = "";
                $telegramId = $userObject->telegram_id;
                $mobileNumber = $userObject->vchr_user_mobile;
                $defaultSenderIdAdmin = SingleSMS:: getSenderid($userAdminObject->pk_int_user_id,'');
                $defaultRouteAdmin = SingleSMS:: getRoute($userAdminObject->pk_int_user_id,'');
                $content1 = "SenderId(".$template->vchr_sender_id.")Approved";
                $content2 = "SenderId(".$template->vchr_sender_id.")Approved";
                $dataSend['message'] = $content1;
                $dataSend['user_id'] = $template->fk_int_user_id ?? null;
                $dataSend['page'] = 'sender';
                $notifications->notifications($from,$to,$subject,$name,$content1,$content2,$logo,$attachment,$telegramId,$template->fk_int_user_id,$mobileNumber,$defaultRouteAdmin,$defaultSenderIdAdmin,$dataSend);
                //-----------------                     
                return response()->json(['msg' => "Sender id approved.", 'status' => 'success']);
            } 
            else {
                return response()->json(['msg' => "Sender id could not be approved.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Sender id not found.", 'status' => 'fail']);
        }
    }

/**
 * [getPending description]
 * @return [type] [description]
 */
     public function getPending()
    {
        $template = DB::table('tbl_users')
        ->select('vchr_user_name','pk_int_sender_id','vchr_sender_id_name','vchr_sender_id','int_default_sender_id','int_sender_id_status')
        ->join('tbl_sender_id', 'tbl_users.pk_int_user_id', '=', 'tbl_sender_id.fk_int_user_id')->orderby('pk_int_sender_id','DESC')
        ->whereNull('tbl_sender_id.deleted_at')
        ->where('int_sender_id_status',SenderId::DEACTIVATE)
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

        if ($template->int_sender_id_status == 1) {
                        return '<button id="delete_plan" senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-sm btn-danger senderid-delete"> <i class="f-16 fa fa-trash-o"></i></button>
                        <a class="btn btn-sm btn-success btn-activate senderid-act ks-izi-modal-trigger1"  data-target="#ks-izi-modal-large" senderid-id="' . $template->pk_int_sender_id . '" data-toggle="tooltip" title="Reject"> <i class="f-18 fa fa-check-circle active"></i></a> ';
                    } //If Status is not in active state
                    else {
                        return '<button href="" senderid-id="' . $template->pk_int_sender_id . '" class="btn btn-sm btn-danger senderid-delete"> <i class="f-16 fa fa-trash-o"></i></button>
                            <a class="btn btn-sm btn-warning senderid-act" senderid-id="' . $template->pk_int_sender_id . '" data-toggle="tooltip" title="Approve"> <i class="f-18 fa fa-times-circle"></i></a>';
                    }
                })
                ->rawColumns(['show'])
                ->toJson(true);
    }
}
