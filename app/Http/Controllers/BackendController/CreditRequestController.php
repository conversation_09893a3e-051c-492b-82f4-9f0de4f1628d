<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use App\BackendModel\CreditRequest;
use DB;
use Carbon\Carbon;
use DataTables;
use Auth;

class CreditRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
         return view('backend.pages.credit.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function getInfo()
    {
        $template =DB::table('tbl_users')
        ->select('*')
        ->join('tbl_credit_request', 'tbl_users.pk_int_user_id', '=', 'tbl_credit_request.fk_int_user_id')->orderby('pk_int_credit_request_id','DESC')
        ->whereNull('tbl_credit_request.deleted_at')
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

        if ($template->int_status == 1) {
                        return '
                                
                                      <button credit-id="' . $template->pk_int_credit_request_id . '" class="btn btn-sm btn-info ks-izi-modal-trigger1" data-target="#ks-izi-modal-large"  data-toggle="tooltip" title="View" > <i class="f-16 fa fa-angle-double-right"></i></button>

                                    <button id="delete_plan" credit-id="' . $template->pk_int_credit_request_id . '" class="btn btn-sm btn-danger credit-delete"> <i class="f-16 fa fa-trash-o"></i></button>

                                     <a class="btn btn-sm btn-success btn-activate"   credit-id="' . $template->pk_int_credit_request_id . '" data-toggle="tooltip" title="Approved"> <i class="f-18 fa fa-check-circle active"></i></a>
                                ';
                    } //If Status is not in active state
                    else {
                        return '
                                
                                       <button credit-id="' . $template->pk_int_credit_request_id . '" class="btn btn-sm btn-info  ks-izi-modal-trigger1" data-target="#ks-izi-modal-large"  data-toggle="tooltip" title="View" > <i class="f-16 fa fa-angle-double-right"></i></button>

                                    <button href="" credit-id="' . $template->pk_int_credit_request_id . '" class="btn btn-sm btn-danger credit-delete"> <i class="f-16 fa fa-trash-o"></i></button>

                                    <a class="btn btn-sm btn-warning" credit-id="' . $template->pk_int_credit_request_id . '" data-toggle="tooltip" title="Approval pending"> <i class="f-18 fa fa-times-circle"></i></a>
                                ';
                    }
                })
                ->rawColumns(['show'])
                ->toJson(true);
    }
}
