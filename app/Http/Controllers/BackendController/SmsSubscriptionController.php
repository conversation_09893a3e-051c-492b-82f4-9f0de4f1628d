<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\UserSubscriptionMessage;
use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use App\BackendModel\SenderId;
use DB;
use Carbon\Carbon;
use DataTables;
use Auth;
use App\Core\CustomClass;
use App\BackendModel\ShortCodes;
use App\BackendModel\SmsTemplate;
use App\BackendModel\EmailTemplate;
use Mail;

class SmsSubscriptionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('backend.pages.subscription-message.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        
         $template=UserSubscriptionMessage::where('pk_int_subscription_message_id',$id)->first();
        if($template)
        {
            
             
            return response()->json(['msg'=>'data present', 'status'=>'success','data'=>$template]);
        }
        else
        {
             return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
        }
          
           
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $template = UserSubscriptionMessage::find($id);
            if ($template) {
                
                UserSubscriptionMessage::where('pk_int_subscription_message_id', $id)->delete();
                return response(['msg' => 'Sms subscription message  is deleted.', 'status' => 'success']);

            }
            else
            {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            }
        }
        catch (\Exception $ex) {
                  return response(['msg' =>'Something Went Wrong'  , 'status' => 'failed']);
                
            }
    }

    public function getInfo($id)
    {

         $template = DB::table('tbl_users')
        ->select('*')
        ->join('tbl_user_subscription_message', 'tbl_users.pk_int_user_id', '=', 'tbl_user_subscription_message.fk_int_user_id')->orderby('pk_int_subscription_message_id','DESC')
        ->whereNull('tbl_user_subscription_message.deleted_at')
         ->where('tbl_user_subscription_message.fk_int_user_id',$id)
        
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

        if ($template->int_subscription_message_status == 1) {
                        return '
                                
                                   
                                    <button id="delete_plan" sms-id="' . $template->pk_int_subscription_message_id . '" class="btn btn-sm btn-danger sms-delete"> <i class="f-16 fa fa-trash-o"></i></button>

                                     <a class="btn btn-sm btn-success btn-activate sms-act ks-izi-modal-trigger4"  data-target="#ks-izi-modal-large4" sms-id="' . $template->pk_int_subscription_message_id . '" data-toggle="tooltip" title="Reject"> <i class="f-18 fa fa-check-circle active"></i></a>
                                ';
                    } //If Status is not in active state
                    else {
                        return '
                                
                                    
                                    <button href="" sms-id="' . $template->pk_int_subscription_message_id . '" class="btn btn-sm btn-danger sms-delete"> <i class="f-16 fa fa-trash-o"></i></button>
                                    <a class="btn btn-sm btn-warning sms-act" sms-id="' . $template->pk_int_subscription_message_id . '" data-toggle="tooltip" title="Approve"> <i class="f-18 fa fa-times-circle"></i></a>
                                ';
                    }
                })
                ->rawColumns(['show'])
                ->toJson(true);
    }

    public function getInfoAll()
    {

         $template = DB::table('tbl_users')
        ->select('*')
        ->join('tbl_user_subscription_message', 'tbl_users.pk_int_user_id', '=', 'tbl_user_subscription_message.fk_int_user_id')->orderby('pk_int_subscription_message_id','DESC')
        ->whereNull('tbl_user_subscription_message.deleted_at')
        ->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

        if ($template->int_subscription_message_status == 1) {
                        return '
                                
                                   
                                    <button id="delete_plan" sms-id="' . $template->pk_int_subscription_message_id . '" class="btn btn-sm btn-danger sms-delete"> <i class="f-16 fa fa-trash-o"></i></button>

                                     <a class="btn btn-sm btn-success btn-activate sms-act ks-izi-modal-trigger1"  data-target="#ks-izi-modal-large" sms-id="' . $template->pk_int_subscription_message_id . '" data-toggle="tooltip" title="Reject"> <i class="f-18 fa fa-check-circle active"></i></a>
                                ';
                    } //If Status is not in active state
                    else {
                        return '
                                
                                    
                                    <button href="" sms-id="' . $template->pk_int_subscription_message_id . '" class="btn  btn-sm btn-danger sms-delete"> <i class="f-16 fa fa-trash-o"></i></button>
                                    <a class="btn btn-sm btn-warning sms-act" sms-id="' . $template->pk_int_subscription_message_id . '" data-toggle="tooltip" title="Approve"> <i class="f-18 fa fa-times-circle"></i></a>
                                ';
                    }
                })
                ->rawColumns(['show'])
                ->toJson(true);
    }

    public function deactivate(Request $request)
    {
        $template = UserSubscriptionMessage::where('pk_int_subscription_message_id', $request->id)->first();
        return $request->id;
        if ($template) {

            try
            {

            $user=User::select('email','vchr_user_name','pk_int_user_id')->find($template->fk_int_user_id);
            $input['email']=$user->email;
            $input['username']=$user->vchr_user_name;

            //check email template and short code for use
            $code=ShortCodes::where('vchr_short_code_template','GL_REJECTION_REASON')->first();
                if(!$code)
                {
                     return response()->json(['msg'=>'Please add short code "GL_REJECTION_REASON"', 'status'=>'fail']);
                }

                $emailtemplate=EmailTemplate::where('vchr_email_template_name','USER SUBSCRIPTION MESSAGE REJECTION')->first();
                if(!$emailtemplate)
                {
                     return response()->json(['msg'=>'Add EMAIL Template "USER SUBSCRIPTION MESSAGE REJECTION" with "GL_REJECTION_REASON" shortcode  before you procced', 'status'=>'fail']);
                }
                $template->int_subscription_message_status = UserSubscriptionMessage::DEACTIVATE;
                $flag = $template->save();
                if ($flag) {
                    //email for template
                    $body = str_replace('[GL_REJECTION_REASON]',$request->reason, $emailtemplate->vchr_email_template_code);

                    $data=['template'=>'welcome','email'=>$input['email'],'name'=>$input['username'],'subject'=>' SUBSCRIPTION MESSAGE Rejected','body'=>$body];
                                           // dd($data);
                   // dispatch(new SendEmailJob($data));
                    Mail::send("emails.".$data['template'], $data, function($message) use ($data)
             {
                $message
                    ->from('<EMAIL>',config('app.name'))
                    ->to($data['email'],$data['name'])
                    ->subject($data['subject']);
             });

                    return response()->json(['msg' => "Subscription message rejected.", 'status' => 'success']);
                } else {
                    return response()->json(['msg' => "Subscription message could not be rejected.", 'status' => 'fail']);
                }

            }
            catch(\Exception $e)
            {
                return response()->json(['msg' =>$e->getMessage(), 'status' => 'fail']);
            }  
        } else {
            return response()->json(['msg' => "Subscription message not found.", 'status' => 'fail']);
        }
    }

    public function activate($id)
    {
        $template = UserSubscriptionMessage::where('pk_int_subscription_message_id', $id)->first();
        if ($template) {
            $template->int_subscription_message_status = UserSubscriptionMessage::ACTIVATE;
            $flag = $template->save();
            if ($flag) {
                return response()->json(['msg' => "Subscription message is approved.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Subscription message could not be approved.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Sender id not found.", 'status' => 'fail']);
        }
    }

    
}
