<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Validator;
use DataTables;
use App\Common\Variables;
use DB;
use Illuminate\Support\Facades\Input;
use App\BackendModel\SmsConfiguration;
use Carbon\Carbon;
use App\Core\CustomClass;
use App\User;

class SmsConfigurationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
            $api=SmsConfiguration::first();
           return view('backend.pages.smsconfiguration.index',compact('api'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // get the POST data
         $input = $request->all();

        //DO validation here
        $validator=validator::make($input, SmsConfiguration::$rule, SmsConfiguration::$message);
        //dd($input);

        // if validator passes
        if ($validator->passes()) {
            
            try
            {
                    $api=new SmsConfiguration();
                    $api->fill($input);
                    $api->int_sms_configuration_default=SmsConfiguration::DEFAULT;
                    $api->int_sms_configuration_status=SmsConfiguration::ACTIVATE;
                    $flag1=$api->save();
                    if($flag1)
                    {
                        return response()->json(['msg'=>'SMS API Configuration updated', 'status'=>'success']);
                    }
                    else
                    {
                        return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                    }
                
                
            }
            catch(\Exception $e)
            {
                return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

                //return $e->getMessage();
            }

        } else {
             return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);

           // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
         // get the POST data
         $input = $request->all();

        //DO validation here
        $validator=validator::make($input, SmsConfiguration::$rule, SmsConfiguration::$message);
        //dd($input);

        // if validator passes
        if ($validator->passes()) {
            
            try
            {
                    $api=SmsConfiguration::find($request->id);
                    $api->fill($input);
                    $flag1=$api->save();
                    if($flag1)
                    {
                        return response()->json(['msg'=>'SMS API Configuration updated', 'status'=>'success']);
                    }
                    else
                    {
                        return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
                    }
                
                
            }
            catch(\Exception $e)
            {
                return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

                //return $e->getMessage();
            }

        } else {
             return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);

           // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
