<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\GlPromo;
use App\BackendModel\GlPromoHistory;
use App\User;
use Validator;
use DataTables;
use App\Common\Variables;
use DB;
use Illuminate\Support\Facades\Input;
//use App\Http\Resources\Testimonial\TestimonialResource;

class GlPromoHistoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $user=User::where('int_status',User::ACTIVATE)->where('int_role_id','2')->get();
        return view('backend.pages.promo.history',compact('user'));
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
      
         // get the POST data
        $input = $request->all();
        //print_r($input);die();
        //DO validation here
        $validator=validator::make($input, GlPromo::$rule, GlPromo::$message);
        //dd($input);

        // if validator passes
        if ($validator->passes()) {
            
            try
            {
                $promo = new GlPromo();
                $promo->fk_int_user_id = $input['fk_int_user_id'];
                $promo->int_package_count = $input['int_package_count'];
                $promo->int_status = GlPromo::ACTIVATE;
                $flag=$promo->save();
                if($flag)
                {
                   return response()->json(['msg'=>'Gl Promo added.', 'status'=>'success']);
               }
               else
               {
                   return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
               }
               
           }
           catch(\Exception $e)
           {
            return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

                //return $e->getMessage();
        }

    } else {
       return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);

           // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
   }
}

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $pricing=Smspricing::find($id);
        if(sizeof($pricing)!=0)
        {
          return response()->json(['msg' => "Sms Pricing detail found.", 'status' => 'success', 'data' => $pricing]);
      }
      else {
        return response()->json(['msg' => "Testimonial detail not found.", 'status' => 'fail']);
    }

}

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        
         // get the POST data
        $input = $request->all();
        //print_r($input);
        //DO validation here
        $validator=validator::make($input, Smspricing::$rule, Smspricing::$message);
        //dd($input);

        // if validator passes
        if ($validator->passes()) {
            
            try
            {
                $pricing=Smspricing::find($id);
                $pricing->fk_int_sms_route = $input['vchr_sms_pricing_route'];
                $pricing->vchr_sms_pricing_rangefrom = $input['vchr_sms_pricing_rangefrom'];
                $pricing->vchr_sms_pricing_rangeto= $input['vchr_sms_pricing_rangeto'];
                $pricing->vchr_sms_pricing_rate_sms= $input['vchr_sms_pricing_rate_sms'];
                //$pricing->int_sms_pricing_status = Smspricing::ACTIVATE;
                $flag=$pricing->save();
                if($flag)
                {
                   return response()->json(['msg'=>'Sms pricing updated.', 'status'=>'success']);
               }
               else
               {
                   return response()->json(['msg'=>'Something went wrong, please try again later.', 'status'=>'fail']);
               }
               
           }
           catch(\Exception $e)
           {
            return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);

                //return $e->getMessage();
        }

    } else {
       return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);

           // Flash::error('Whoops! Form validation failed');
            //return redirect()->back()->withInput()->withErrors($validator->messages());
   }
}


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
       try {
        $pricing = SmsPricing::find($id);
        if ($pricing) {
            
            Smspricing::where('pk_int_sms_pricing_id', $id)->delete();
            return response(['msg' => 'Sms Pricing is deleted.', 'status' => 'success']);

        }
        else
        {
            return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
        }
    }
    catch (\Exception $ex) {
      return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
      
  }

}
public function getPromoHistory($id)
{

        //return $id; die();
    if($id!=0)
    {    
        $promo = DB::table('tbl_users')
        ->join('tbl_gl_promo_history', 'tbl_users.pk_int_user_id', '=', 'tbl_gl_promo_history.fk_int_user_id')
        ->where('tbl_gl_promo_history.fk_int_user_id',$id)
        ->orderby('tbl_gl_promo_history.pk_int_promo_history_id','Desc')
        ->get();
    }
    else
    {
      $promo = DB::table('tbl_users')
      ->join('tbl_gl_promo_history', 'tbl_users.pk_int_user_id', '=', 'tbl_gl_promo_history.fk_int_user_id')
      ->orderby('tbl_gl_promo_history.pk_int_promo_history_id','Desc')
      ->get();  
  }    
  
  foreach ($promo as $key => $row) {
    $row->slno=++$key;
}
return Datatables::of($promo)
->editColumn('date', function ($promo) {
    if ($promo->created_at != null) {
        return $promo->created_at;
    } else {
        return "'No Mobile No'";
    }
})
->editColumn('username', function ($promo) {
    if ($promo->vchr_user_name != null) {
        return $promo->vchr_user_name;
    } else {
        return "'No Username'";
    }
})
->editColumn('mobno', function ($promo) {
    if ($promo->vchr_user_mobile != null) {
        return $promo->vchr_user_mobile;
    } else {
        return "'No Mobile No'";
    }
})
->editColumn('package', function ($promo) {
    if ($promo->int_purchased_count_id != null) {
        return $promo->int_purchased_count_id;
    } else {
        return "'No Package'";
    }
})
->editColumn('remarks', function ($promo) {
    if ($promo->vchr_gl_promo_remarks != null) {
        return $promo->vchr_gl_promo_remarks;
    } else {
        return "No Remarks";
    }
})
->toJson(true);
}




}
