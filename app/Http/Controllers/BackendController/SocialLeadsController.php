<?php

namespace App\Http\Controllers\BackendController;

use App\BackendModel\SocialLeads;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Facebook;
use Redirect;

class SocialLeadsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // $formID = "265514980742591";
        // $apiVersion = "v2.11";
        // $accessToken = "EAAEMFebqqJYBAGOp7vq5exXo58TafHshHxGUhub5cst0nRTyClisgRwdvGgyS0BRX1yMZAn1GqxE4Wd8ldEzLxZBAtf2ZAAndRoVyPrqZBekUFt67AYz7LOJPyfQCtKeWYlhiZBdrQ5YXw14aHtKERsRN1dxMStwLEFTm0cSC0xZAp6OJqc1tCv2D57v7ZANIQqDntlNDNSZAZB0iryAqTkwG";
        // //https://graph.facebook.com/oauth/access_token?client_id=your-app-id&client_secret=your-app-secret&redirect_uri=your-redirect-url&grant_type=client_credentials

        // //generate access token
        // $tokenurl = curl_init();

        // curl_setopt_array($tokenurl, array(
        //     CURLOPT_URL => "https://graph.facebook.com/oauth/access_token?client_id=23842973763870043&client_secret=cfc596fada1698c2173fde405837b96e&redirect_uri=http://getleadcrm.local&grant_type=client_credentials",
        //     CURLOPT_RETURNTRANSFER => true,
        //     CURLOPT_ENCODING => "",
        //     CURLOPT_TIMEOUT => 30000,
        //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        //     CURLOPT_CUSTOMREQUEST => "GET",
        //     CURLOPT_HTTPHEADER => array(
        //         // Set Here Your Requesred Headers
        //         'Content-Type: application/json',
        //     ),
        // ));
        // $response = curl_exec($tokenurl);
        // $err = curl_error($tokenurl);
        // curl_close($tokenurl);

        // // $curl = curl_init();

        // // curl_setopt_array($curl, array(
        // //     CURLOPT_URL => "https://graph.facebook.com/$apiVersion/$formID/leads?access_token=$accessToken",
        // //     CURLOPT_RETURNTRANSFER => true,
        // //     CURLOPT_ENCODING => "",
        // //     CURLOPT_TIMEOUT => 30000,
        // //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        // //     CURLOPT_CUSTOMREQUEST => "GET",
        // //     CURLOPT_HTTPHEADER => array(
        // //         // Set Here Your Requesred Headers
        // //         'Content-Type: application/json',
        // //     ),
        // // ));
        // // $response = curl_exec($curl);
        // // $err = curl_error($curl);
        // // curl_close($curl);

        // if ($err) {
        //     echo "cURL Error #:" . $err;
        // } else {
        //     dd(json_decode($response));
        // }
        return view('backend.pages.social-leads.index');
    }

    public function fbLogin()
    {
        return view('backend.pages.social-leads.facebook-login');
        // $fb = new Facebook\Facebook([
        //   'app_id' => '294763184695446', // Replace {app-id} with your app id
        //   'app_secret' => 'cfc596fada1698c2173fde405837b96e',
        //   'default_graph_version' => 'v3.2',
        //   ]);

        // $helper = $fb->getRedirectLoginHelper();

        // $permissions = ['email']; // Optional permissions
        // $loginUrl = $helper->getLoginUrl('http://getleadcrm.local/fb-generate-token', $permissions);

        // // echo '<a href="' . htmlspecialchars($loginUrl) . '">Log in with Facebook!</a>';
        // return Redirect::to($loginUrl);
        // dd($loginUrl);
    }

    public function generateAccessToken()
    {
        
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\BackendModel\SocialLeads  $socialLeads
     * @return \Illuminate\Http\Response
     */
    public function show(SocialLeads $socialLeads)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\BackendModel\SocialLeads  $socialLeads
     * @return \Illuminate\Http\Response
     */
    public function edit(SocialLeads $socialLeads)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\BackendModel\SocialLeads  $socialLeads
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, SocialLeads $socialLeads)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\BackendModel\SocialLeads  $socialLeads
     * @return \Illuminate\Http\Response
     */
    public function destroy(SocialLeads $socialLeads)
    {
        //
    }
}
