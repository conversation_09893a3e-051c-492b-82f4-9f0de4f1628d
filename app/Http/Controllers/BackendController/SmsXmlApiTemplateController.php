<?php

namespace App\Http\Controllers\BackendController;

use App\BackendModel\XMLApiTemplate;
use App\BackendModel\SmsApiTemplate;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use App\BackendModel\Smsroute;
use DataTables;
use Auth;
use Flash;


class SmsXmlApiTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        return view ('backend.pages.sms-xml-api.sms-xml-index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
        $routes=Smsroute::select('pk_int_sms_route_id','vchr_sms_route')->where('int_sms_route_status',Smsroute::ACTIVATE )->get();
        $users=User::select('pk_int_user_id','vchr_user_name')->where('int_role_id',User::USERS)->get();
        return view ('backend.pages.sms-xml-api.sms-xml-create',compact('routes','users'));
        
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        //   return $request;
        $input=$request->all();
        //  return $request;
        if($request->api_type=="sms"){
            $sms_api=new SmsApiTemplate;
            $sms_api->fk_int_user_id=$request->fk_int_user_id;
            $sms_api->fk_int_sms_route_id=$request->fk_int_sms_route_id;
            $sms_api->text_sms_api_template_url=$request->text_sms_api_template_url;
            $sms_api->text_sms_api_template_status_url=$request->text_sms_api_template_status_url;
            $sms_api->text_sms_api_template_balance_url=$request->text_sms_api_template_balance_url;
            $sms_api->int_status=SmsApiTemplate::ACTIVATE;

            SmsApiTemplate::where('int_status',SmsApiTemplate::ACTIVATE)->where('fk_int_user_id', $sms_api->fk_int_user_id)->where('fk_int_sms_route_id', $sms_api->fk_int_sms_route_id)->update(['int_status'=>SmsApiTemplate::DEACTIVATE]);
          
            $flag=$sms_api->save();
        

        }else if($request->api_type=="xml"){
            
            $xml_api=new XMLApiTemplate;
           
            $xml_api->vendor_id=$request->vendor_id;
            $xml_api->api_username=$request->api_username;
            $xml_api->password=$request->password;
            $xml_api->api_password=$request->api_password;
            $xml_api->message_api=$request->message_api;
            $xml_api->status_api=$request->status_api;
            $xml_api->balance_api=$request->balance_api;
            
            $xml_api->status=XMLApiTemplate::ACTIVATE;
            
            $xml_api->created_at=User::getVendorId();
            
            XMLApiTemplate::where('status',XMLApiTemplate::ACTIVATE)->where('vendor_id',$request->vendor_id)->update(['status'=> XMLApiTemplate::DEACTIVATE]);
         
            $flag=$xml_api->save();
           

        }
        if ($flag) {
            Flash::Success('Api Template created successfully!');
                    return redirect('/admin/sms-xml-api-template')->with('Success', 'Api Template created successfully!');
        }
        else {
            Flash::Fail('Something Went Wrong Try Again !!!!');
            return redirect('/admin/sms-xml-api-template/create')->with('Fail', 'Something Went Wrong Try Again !!!!');
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\BackendModel\XMLApiTemplate  $xMLApiTemplate
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
        $routes=Smsroute::select('pk_int_sms_route_id','vchr_sms_route')->where('int_sms_route_status',Smsroute::ACTIVATE )->get();
        $users=User::select('pk_int_user_id','vchr_user_name')->where('int_role_id',User::USERS)->get();
       $sms_api=SmsApiTemplate::find($id);
       return view ('backend.pages.sms-xml-api.sms-show',compact('sms_api','users','routes'));
        

    }
    public function showXml($id)
    {
        //
        
        $users=User::select('pk_int_user_id','vchr_user_name')->where('int_role_id',User::USERS)->get();
       $xml_api=XMLApiTemplate::find($id);
       return view ('backend.pages.sms-xml-api.xml-show',compact('xml_api','users'));
        
        
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\BackendModel\XMLApiTemplate  $xMLApiTemplate
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\BackendModel\XMLApiTemplate  $xMLApiTemplate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,$id)
    {
        //
        $input=$request->all();
        //  return $request;
        if($request->api_type=="sms"){
            $sms_api=SmsApiTemplate::find($id);
            $sms_api->fk_int_user_id=$request->fk_int_user_id;
            $sms_api->fk_int_sms_route_id=$request->fk_int_sms_route_id;
            $sms_api->text_sms_api_template_url=$request->text_sms_api_template_url;
            $sms_api->text_sms_api_template_status_url=$request->text_sms_api_template_status_url;
            $sms_api->text_sms_api_template_balance_url=$request->text_sms_api_template_balance_url;
            $sms_api->int_status=SmsApiTemplate::ACTIVATE;

            SmsApiTemplate::where('int_status',SmsApiTemplate::ACTIVATE)->where('fk_int_user_id', $sms_api->fk_int_user_id)->where('fk_int_sms_route_id', $sms_api->fk_int_sms_route_id)->update(['int_status'=>SmsApiTemplate::DEACTIVATE]);
          
            $flag=$sms_api->save();
            if ($flag) {
                Flash::Success(' SMS Api Template Updated successfully!');
                        return redirect('/admin/sms-xml-api-template')->with('Success', 'Api Template Updated successfully!');
            }

        }else if($request->api_type=="xml"){
            
            $xml_api=XMLApiTemplate::find($id);
           
            $xml_api->vendor_id=$request->vendor_id;
            $xml_api->api_username=$request->api_username;
            $xml_api->password=$request->password;
            $xml_api->api_password=$request->api_password;
            $xml_api->message_api=$request->message_api;
            $xml_api->status_api=$request->status_api;
            $xml_api->balance_api=$request->balance_api;
            
            $xml_api->status=XMLApiTemplate::ACTIVATE;
            
            $xml_api->created_at=User::getVendorId();
            
            XMLApiTemplate::where('status',XMLApiTemplate::ACTIVATE)->where('vendor_id',$request->vendor_id)->update(['status'=> XMLApiTemplate::DEACTIVATE]);
         
            $flag=$xml_api->save();

            if ($flag) {
                Flash::Success(' XML Api Template Updated successfully!');
                        return redirect('/admin/sms-xml-api-template')->with('Success', 'Api Template Updated successfully!');
            }
           

        }
        
        
            Flash::Fail('Something Went Wrong Try Again !!!!');
            return back()->with('Fail', 'Something Went Wrong Try Again !!!!');
        

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\BackendModel\XMLApiTemplate  $xMLApiTemplate
     * @return \Illuminate\Http\Response
     */
    public function destroy( $id)
    {
        
 
            try {
            
                    $flag=SmsApiTemplate::where('pk_int_sms_api_template_id',$id[0])->delete();
                
                
                    if($flag){

                        return response(['msg' => 'SMS  Api  is deleted.', 'status' => 'success']);
                    }
                    return response(['msg' => 'Something Went Wrong !! Try Again', 'status' => 'failed']);    
                
            } catch (\Exception $ex) {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);

            }
      
               
        
    }
    public function deleteXml($id)
    {
       
        try {
              
            $flag=XMLApiTemplate::where('id',$id)->delete();
        
            if($flag){

                return response(['msg' => 'XML  Api  is deleted.', 'status' => 'success']);
            }
            return response(['msg' => 'Something Went Wrong !! Try Again', 'status' => 'failed']);    
            
        } catch (\Exception $ex) {
            return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);

        }
    }
  
  
    // XMLApiTemplate;
// SmsApiTemplate;
    public function getSmsInfo()
    {
            $sms_api=SmsApiTemplate::leftjoin('tbl_users','pk_int_user_id','=','fk_int_user_id')
                                        ->join('tbl_sms_route','pk_int_sms_route_id','=','fk_int_sms_route_id')
                                        ->select('vchr_user_name','vchr_sms_route','text_sms_api_template_url','text_sms_api_template_status_url','text_sms_api_template_balance_url','pk_int_sms_api_template_id as id','fk_int_sms_route_id','fk_int_user_id','tbl_sms_api_template.int_status as status')
                                        ->orderby('pk_int_sms_api_template_id','Desc')->get();

                                         
        foreach($sms_api as $key =>$smsapi)
        {
            
            $smsapi->slno=++$key;
            
        }
        
                return Datatables::of($sms_api)
                ->addColumn('action', function ($sms_api) {
                    if($sms_api->status==1)
                    {    return '
                                        <a href=" ' .url('admin/sms-xml-api-template', $sms_api->id). '
                                         "class="btn btn-xs btn-success edit"><i class="fa fa-edit"></i></a>
                                        <button id="delete_plan" sms-api-id="' . $sms_api->id . '" class="btn btn-sm btn-danger default testimonial-delete" title="Delete"> <i class="fa fa-trash-o"></i></button>
                                        <button class="btn btn-sm btn-success feedback-act btn-activate " sms-api-id="' . $sms_api->id . '"id="make-btn" data-toggle="tooltip" title="Deactivate"> <i class="fa fa-check"></i></button>
                                        ';
                                    
                    }else
                    {
                        return'
                            <a href=" ' .url('admin/sms-xml-api-template', $sms_api->id). '
                        "class="btn btn-xs btn-success edit"><i class="fa fa-edit"></i></a>
                        <button id="delete_plan" sms-api-id="' . $sms_api->id . '" class="btn btn-sm btn-danger testimonial-delete" title="Delete"> <i class="fa fa-trash-o"></i></button>
                        <button class="btn btn-sm btn-warning   feedback-act btn-deactive "sms-api-id="' . $sms_api->id . '" id="make-btn" data-toggle="tooltip" title="Activate"> <i class="fa fa-times"></i></button>';
                        
                    }
                    
                    
                })
                ->rawColumns(['action'])
                ->toJson(true);
    }
    public function getXmlInfo()
    {
        
              $xml_api=XMLApiTemplate::leftjoin('tbl_users','pk_int_user_id','=','vendor_id')
                                        ->select('vchr_user_name','api_username','api_password','message_api','status_api','balance_api','status','id','vendor_id','status','xml_api_templates.password as password')
                                        ->orderby('id','Desc')->get();

                                         
        foreach($xml_api as $key =>$xmlapi)
        {
            
            $xmlapi->slno=++$key;
            
        }
                // return $xml_api;
                return Datatables::of($xml_api)
                ->addColumn('action', function ($xml_api) {
                    if($xml_api->status==1)
                    {    return '
                                        <a href=" ' .url('admin/show-xml-api-template', $xml_api->id). '
                                        "class="btn btn-xs btn-success edit"><i class="fa fa-edit"></i></a>
                                        <button id="delete_plan" xml-api-id="' . $xml_api->id . '" class="btn btn-sm btn-danger default testimonial-delete" title="Delete"> <i class="fa fa-trash-o"></i></button>
                                        <button class="btn btn-sm btn-success feedback-act btn-activate "xml-api-id="' . $xml_api->id . '" id="make-btn"  data-toggle="tooltip" title="Deactivate"> <i class="fa fa-check"></i></button>
                                        ';
                                    
                    }else
                    {
                        return'
                        <a href=" ' .url('admin/show-xml-api-template', $xml_api->id). '
                        "class="btn btn-xs btn-success edit"><i class="fa fa-edit"></i></a>
                        <button id="delete_plan" xml-api-id="' . $xml_api->id . '" class="btn btn-sm btn-danger testimonial-delete" title="Delete"> <i class="fa fa-trash-o"></i></button>
                        <button class="btn btn-sm btn-warning   feedback-act btn-deactive " xml-api-id="' . $xml_api->id . '" id="make-btn" data-toggle="tooltip" title="Activate"> <i class="fa fa-times"></i></button>';
                        
                    }
                    
                    
                })
                ->rawColumns(['action'])
                ->toJson(true);
                
    

    }
    public function activeXml($id)
    {
        $sms_api=XMLApiTemplate::find($id);
        XMLApiTemplate::where('status',XMLApiTemplate::ACTIVATE)->where('vendor_id',$sms_api->vendor_id)->update(['status'=>XMLApiTemplate::DEACTIVATE]);
        $flag=XMLApiTemplate::where('id',$id)->update(['status'=>XMLApiTemplate::ACTIVATE]);

        if($flag){
                    
            return response()->json(['msg'=>"XML Api  Activated Succefully",'status'=>'success']);    
        }

    return response()->json(['msg'=>"Something went wrong !!! Try Agin ",'status'=>'fail']);
    }
    public function activeSms($id)
    {
        // return("activate");
        $sms_api=SmsApiTemplate::find($id);
        SmsApiTemplate::where('int_status',SmsApiTemplate::ACTIVATE)->where('fk_int_user_id', $sms_api->fk_int_user_id)->where('fk_int_sms_route_id', $sms_api->fk_int_sms_route_id)->update(['int_status'=>SmsApiTemplate::DEACTIVATE]);
        $flag=SmsApiTemplate::where('pk_int_sms_api_template_id',$id)->update(['int_status'=>SmsApiTemplate::ACTIVATE]);

        if($flag){
                    
            return response()->json(['msg'=>"SMS Api  Activated Succefully",'status'=>'success']);    
        }

    return response()->json(['msg'=>"Something went wrong !!! Try Agin ",'status'=>'fail']);
    }
    public function deactiveXml($id)
    {
        $flag=XMLApiTemplate::where('id',$id)->update(['status'=>XMLApiTemplate::DEACTIVATE]);
        if($flag){
                    
            return response()->json(['msg'=>"XML Api Deactivated Succefully",'status'=>'success']);    
        }

        return response()->json(['msg'=>"Something went wrong !!! Try Agin ",'status'=>'fail']);
  

    }
    public function deactiveSms($id)
    {
        // return("deactivate");
       $flag=SmsApiTemplate::where('pk_int_sms_api_template_id',$id)->update(['int_status'=>SmsApiTemplate::DEACTIVATE]);
        if($flag){
                    
            return response()->json(['msg'=>"SMS Api Deactivated Succefully",'status'=>'success']);    
        }

        return response()->json(['msg'=>"Something went wrong !!! Try Agin ",'status'=>'fail']);
    }
}
