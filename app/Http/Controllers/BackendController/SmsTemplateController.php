<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Validator;
use App\BackendModel\SmsTemplate;
use App\Common\Variables;
use Flash;

class SmsTemplateController extends Controller
{
    public function index()
    {
        return view('backend.pages.sms.index');
    }

    public function listTemplates()
    {
        $smsTemplates = SmsTemplate::where('int_status', Variables::ACTIVE)->select('vchr_sms_template_name', 'vchr_sms_template_code', 'pk_int_sms_template_id')->get();
        if(count($smsTemplates)){
            return response(['msg' => 'Sms Templates Available', 'status' => 'success', 'data' => $smsTemplates]);
        }
        return response(['msg' => 'No Sms Templates Available', 'status' => 'fail']);
    }

    public function create()
    {
        return view('backend.pages.sms.create');
    }

    public function store(Request $request)
    {
        $input = $request->all();

        $data['vchr_sms_template_name'] = $input['temp_name'];
        $data['vchr_sms_template_code'] = $input['temp_code'];


        $validator = validator( $data, SmsTemplate::$rules, SmsTemplate::$rulesMessage);

        if($validator->passes()){
            $template = new SmsTemplate();
            $template->fill($data);
            $flag = $template->save();
            if($flag){
                Flash::Success('Template added');
                return back();
            }
        }else{
            // Flash::error('');
            // return $validator->messages();
            return redirect()->back()->withInput()->withErrors($validator->messages());
        }
    }

    public function edit($id)
    {
        $template = SmsTemplate::select('vchr_sms_template_name as name', 'vchr_sms_template_code as code')->where('pk_int_sms_template_id', $id)->first();
        if($template)
        return view('backend.pages.sms.edit')->withTemplate($template);
        else {
            return back();
        }
    }

    public function update(Request $request, $id)
    {
        $input = $request->all();

        $data['vchr_sms_template_name'] = $input['temp_name'];
        $data['vchr_sms_template_code'] = $input['temp_code'];

        $template = SmsTemplate::find($id);

        if($template){
            $template->fill($data);
            $flag = $template->save();
            if($flag) {
                // Flash::success('Successfully Edited');
                return back();
            }else{
                // Flash::error('Something went wrong.');
                return back()->withInput();
            }
        }else{
            // Flash::error('Template not found');
            return back()->withInput();
        }
    }

    public function destroy($id)
    {
        $temp = SmsTemplate::find($id);
        if($temp){
            $flag = $temp->delete();
        } else{
            $flag = false;
        }
        if($flag){
            return response(['msg' => "Template deleted successfully.", 'status' => 'success']);
        }else{
            return response(['msg' => "Template not found.", 'status' => 'fail']);
        }
    }
}
