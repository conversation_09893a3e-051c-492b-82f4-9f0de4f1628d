<?php

namespace App\Http\Controllers\BackendController;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\User;
use Validator;
use Illuminate\Support\Facades\Input;
use App\FrontendModel\SmsPurchase;
use DB;
use Carbon\Carbon;
use DataTables;
use Auth;
use App\Core\CustomClass;



class SmsPurchaseController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
           return view('backend.pages.offers.festivaloffer.index');
        
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
     try {
            $template = SmsPurchase::find($id);
            if (sizeof($template)!=0) {
                
                SmsPurchase::where('pk_int_sms_purchase_id', $id)->delete();
                return response(['msg' => 'Record is deleted.', 'status' => 'success']);

            }
            else
            {
                return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
            }
        }
        catch (\Exception $ex) {
                  return response(['msg' =>'Something Went Wrong'  , 'status' => 'failed']);
                
            }
    }

    public function getInfo()
    {
        $template =SmsPurchase::orderby('pk_int_sms_purchase_id','DESC')->get();
        
        foreach ($template as $key => $row) {
                    $row->slno=++$key;
                }
        return Datatables::of($template)
        ->addColumn('show', function ($template) {

                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                   
                                    <button id="delete_plan" purchase-id="' . $template->pk_int_sms_purchase_id . '" class="btn btn-danger purchase-delete px-3"> <i class="f-16 la la-trash-o"></i></button>  
                                </div>';
                    
                })
        ->addColumn('payment', function ($template) {

        if ($template->int_isPaid == 1) {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                   
                                    <button id="" senderid-id="' . $template->pk_int_sms_purchase_id . '" class="btn btn-success  px-3">PAID</button>

                                    
                                </div>';
                    } //If Status is not in active state
                    else {
                        return '
                                <div class="btn-group btn-collection btn-icon-group">
                                    
                                    <button href="" senderid-id="' . $template->pk_int_sms_purchase_id . '" class="btn btn-warning  px-3"> PENDING</i></button>
                                    
                                </div>';
                    }
                })
                ->rawColumns(['show','payment'])
                ->toJson(true);
    }
}
