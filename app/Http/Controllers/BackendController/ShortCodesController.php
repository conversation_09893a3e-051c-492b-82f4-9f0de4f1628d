<?php

namespace App\Http\Controllers\BackendController;

use App\BackendModel\ShortCodes;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Validator;
use Auth;

class ShortCodesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('backend.pages.shortcodes.index');
    }

    public function listTemplates()
    {
        $shortCodeTemplates = ShortCodes::select('vchr_short_code_name', 'vchr_short_code_template', 'vchr_short_code_description', 'pk_int_short_code_id')->get();
        if(count($shortCodeTemplates)){
            return response(['msg' => 'Short Code Templates Available', 'status' => 'success', 'data' => $shortCodeTemplates]);
        }
        return response(['msg' => 'No Short Code Templates Available', 'status' => 'fail']);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $input = $request->all();

        $validator = validator($input, ShortCodes::$rules, ShortCodes::$rulesMessage);

        if ($validator->passes()) {
            $new = new ShortCodes();
            $new->fill($input);
            $new->fk_int_user_id = Auth::id();
            $flag = $new->save();
            if($flag){
                return response()->json(['msg'=>'Short code saved.', 'status'=>'success']);
            }else{
                return response()->json(['msg'=>'Someting went wrong, please try again later.', 'status'=>'fail']);

            }
        } else {
            return response()->json(['msg'=>$validator->messages(), 'status'=>'fail']);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\BackendModel\ShortCodes  $shortCodes
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $shortCodes = ShortCodes::where('pk_int_short_code_id',$id)->first();
        if($shortCodes){
            return response()->json(['msg'=>'Short code detail found.', 'status' => 'success', 'data'=>$shortCodes]);
        }else{
            return response()->json(['msg'=>'Short code detail not found.', 'status' => 'fail']);

        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\BackendModel\ShortCodes  $shortCodes
     * @return \Illuminate\Http\Response
     */
    public function edit(ShortCodes $shortCodes)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\BackendModel\ShortCodes  $shortCodes
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $input = $request->all();

        $shortCode = ShortCodes::where('pk_int_short_code_id', $id)->first();

        if($shortCode) {
            $validator = validator($input, ShortCodes::$rules, ShortCodes::$rulesMessage);
            if($validator->passes()){
                $shortCode->fill($input);
                $flag = $shortCode->save();
                if($flag){
                    return response()->json(['msg' => 'Short code updated', 'status' => 'success']);
                } else {
                    return response()->json(['msg' => 'Something went wrong, please try again later.', 'status' => 'fail']);
                }
            }else{
                return response()->json(['msg' => $validator->messages(), 'status'=>'fail']);
            }
        } else {
            return response()->json(['msg' => 'Short code not found.', 'status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\BackendModel\ShortCodes  $shortCodes
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
		$shortCode = ShortCodes::find($id);
        if($shortCode){
            $flag = $shortCode->delete();
        } else{
            $flag = false;
        }
        if($flag){
            return response(['msg' => "Shortcode deleted successfully.", 'status' => 'success']);
        }else{
            return response(['msg' => "Shortcode not found.", 'status' => 'fail']);
        }
    }
}
