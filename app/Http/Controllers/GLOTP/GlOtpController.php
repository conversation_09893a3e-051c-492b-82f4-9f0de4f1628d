<?php

namespace App\Http\Controllers\GLOTP;

use App\BackendModel\GlApiTokens;
use App\BackendModel\SenderId;
use App\BackendModel\Smsroute;
use App\Common\Variables;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class GlOtpController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $routes = Smsroute::where('int_sms_route_status', Smsroute::ACTIVATE)
            ->where('priority', Variables::OTP_PRIORITY)
            ->first();
        $check_token = GlApiTokens::where('fk_int_user_id', User::getVendorId())->select('vchr_token')->first();
        if (!empty($check_token)) {
            $api_token = $check_token->vchr_token;
        } else {
            $new_token = new GlApiTokens();
            $pool = '0123456789abcdefhiklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ' . time();
            $apitoken = 'gl_' . substr(md5(str_shuffle(str_repeat($pool, 10))), 0, 20);
            $new_token->fk_int_user_id = User::getVendorId();
            $new_token->vchr_token = $apitoken;
            $new_token->int_status = Variables::ACTIVE;
            $new_token->save();
            $api_token = $new_token->vchr_token;
        }
        $senderids = SenderId::select('pk_int_sender_id as id', 'vchr_sender_id as name')
            ->where('int_sender_id_status', Variables::ACTIVE)
            ->where('fk_int_user_id', User::getVendorId())->get();
        return view('gl-otp.otp')
            ->with('api_token', $api_token)
            ->with('senderids', $senderids);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
