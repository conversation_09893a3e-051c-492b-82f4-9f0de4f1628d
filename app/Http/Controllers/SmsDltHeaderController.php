<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Validator;
use Auth;
use App\SmsDltEntity;
use App\SmsDltHeader;
use App\SmsDltTemplate;
use App\User;
use DataTables;
use Session;

class SmsDltHeaderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    protected $vendorId;
    public function __construct()
    {
        if (!Auth::check())
            return redirect('login');
        $this->vendorId = Auth::user()->int_role_id==1 ? Session::get('current_user_id')  : User::getVendorId();
    }
    public function index()
    {
        $entities = SmsDltEntity::where('fk_int_user_id',$this->vendorId)->get();
        return view('backend.user-pages.sms-dlt-header.index',compact('entities'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $input = $request->all();
        $validator=validator::make($input,SmsDltHeader::$rule,SmsDltHeader::$message);

        if($validator->passes())
        {
            try{
                $exist=SmsDltHeader::where('header',$request->header_id)->where('fk_int_user_id',$this->vendorId)->first();
                if( $exist)
                {
                    $msg=['header_id'=>'The header id has already been taken.'];
                    return response()->json(['msg'=>$msg, 'status'=>'error']); 
                }
                $smsDltHeader = new SmsDltHeader();
                $smsDltHeader->entity_id = $request->entity_id;
                $smsDltHeader->header = $request->header_id;
                $smsDltHeader->sender = $request->sender_id;
                $smsDltHeader->fk_int_user_id = $this->vendorId;
                $smsDltHeader->status = SmsDltHeader::APPROVED;
                $smsDltHeader->created_by = auth()->user()->pk_int_user_id;

                if($smsDltHeader->save()){

                    return response()->json(['msg'=>'SMS DLT Header added.', 'status'=>'success']);
                    
                }else{

                    return response()->json(['msg'=>'Something went wrong..Try again.', 'status'=>'error']);
                }
                
            }catch (\Exception $e) {
    
                return response()->json(['status'=>'error','msg' => $e->getMessage()], 500);
            }
        }
        else{
            
            return response()->json(['status' => 'error','msg' =>$validator->messages()]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateDlt(Request $request)
    {
         $input = $request->all();    
         $validator = validator::make($input, SmsDltHeader::$updaterule, SmsDltHeader::$updatemessage);
        
        if ($validator->passes()){

            try{

                $smsDltHeader = SmsDltHeader::find($request->id);
                if($smsDltHeader->fk_int_user_id != $this->vendorId && Auth::user()->int_role_id!=1)
                    return response()->json(['msg'=>'Something went wrong..Try again.', 'status'=>'error']);
                $smsDltHeader->entity_id = $request->entity_id;
                $smsDltHeader->header = $request->header_id;
                $smsDltHeader->sender = $request->sender_id;
                $smsDltHeader->fk_int_user_id = $this->vendorId;
                $smsDltHeader->status = $request->status;
                
                if($smsDltHeader->save()){
                    if($request->status == 1){

                       return response()->json(['msg'=>'SMS DLT Header updated..', 'status'=>'success']);
                    }else{

                       return response()->json(['msg'=>'SMS DLT Header updated.Waiting for admin approval.', 'status'=>'success']);
                    }
                }else{

                    return response()->json(['msg'=>'Something went wrong..Try again.', 'status'=>'error']);
                }
                 
           }catch (\Exception $e) {

                return response()->json(['msg'=>$e->getMessage(), 'status' => 'fail']);
            }
        }else{

            return response()->json(['msg'=>$validator->messages(), 'status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $header = SmsDltHeader::find($id);
        $header_usage = SmsDltTemplate::where('header_id',$header->header)->count();
        if($header_usage>0)
            return response()->json(['msg' => "Not able to delete.Please delete template first", 'status' => 'error']);
        if ($header && ($header->fk_int_user_id == $this->vendorId || Auth::user()->int_role_id==1)) {
            if ($header->delete()) {

                return response()->json(['msg' => "SMS DLT Header deleted.", 'status' => 'success']);
            } else {

                return response()->json(['msg' => "Not able to delete.Please try again", 'status' => 'error']);
            }
        } else {

            return response()->json(['msg' => "SMS DLT Header not found.", 'status' => 'error']);
        }
    }

    /**
     * Function to get all dlt header data
     *
     * @return mixed
     */
    public function getSmsDltData()
    {

        $headers = SmsDltHeader::orderby('created_at','DESC');

            $headers = $headers->where('fk_int_user_id',$this->vendorId);
            $headers = $headers->get();
  
        foreach ($headers as $key => $row) {
            $row->slno=++$key;
        }

        return Datatables::of($headers)

        ->addColumn('show', function ($header) {

            return '<div class="dropdown show">
                        <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>   
                        </a>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                            <a smsheader-id="' . $header->id . '" data-status="'.$header->status.'" data-id="'.$header->id.'"  data-header="'.$header->header.'" data-entity_id="'.$header->entity_id.'"  data-header_id="'.$header->header.'" data-sender_id="'.$header->sender.'" class="dropdown-item ks-izi-modal-trigger-header1" data-target="#ks-izi-modal-large-header1"  data-toggle="modal"> 
                                <i class="fa fa-edit"></i>&nbsp;edit
                            </a>
                            <a id="delete_plan" smsheader-id="' . $header->id . '" class="dropdown-item smddltheader-delete"> 
                                <i class="fa fa-trash-o"></i>&nbsp;delete
                            </a>
                        </div>
                    </div>';
                    // <button class="btn btn-sm btn-warning" smsheader-id="' . $header->id . '" data-toggle="tooltip" title="Admin approval pending"> <i class="fa fa-times"></i></button>';
        //   }
        })
        ->rawColumns(['show'])
        ->toJson(true);
    }
}
