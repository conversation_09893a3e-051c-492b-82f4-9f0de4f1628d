<?php

namespace App\Http\Controllers\Api\Android;

use App\BackendModel\ApiTemplate;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\NotificationType;
use App\BackendModel\SmsApiCredentials;
use App\BackendModel\SmsDomain;
use App\BackendModel\SmsTemplateCode;
use App\Common\Application;
use App\Common\SingleSMS;
use App\Common\Variables;
use App\Core\CustomClass;
use App\Http\Controllers\Controller;
use App\Notifications\AssignLeadToStaff;
use App\SmsPanel;
use App\User;
use Illuminate\Http\Request;
use JWTAuth;
use Validator;
use Carbon\Carbon;
use App\Events\CreateFollowup;

class LeadController extends Controller
{
    protected $userId;
    protected $vendorId;
    protected $user;
    protected $applicationObj;
    protected $vendor;

    public function __construct()
    {
        $this->middleware('jwt.auth');
        $this->applicationObj = new Application();
        $this->user = JWTAuth::parseToken()->toUser();
        $this->userId = $this->user->pk_int_user_id;
        $this->vendorId = $this->applicationObj->getVendorId($this->userId);
        $this->vendor = $this->applicationObj->getVendor($this->vendorId);

        //$this->middleware('auth');
        //
        //
       /* $this->middleware(function ($request, $next) {
            if (Auth::id() ==6) {
          $prvi=Privilege::where('staff_id',Auth::user()->designation_id)->where('Privilage','user/enquiries')->first();
       return response()->json(['status' => 'fail', 'message' => 'Unauthorized Action'], 200);
}
        });*/
    
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $enquiries = Enquiry::leftJoin('tbl_enquiry_types as et', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'et.pk_int_enquiry_type_id')
            ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id', 'created_at')
            ->select(['tbl_enquiries.pk_int_enquiry_id as id', 'tbl_enquiries.fk_int_user_id as vendor_id',
                'vchr_customer_name as customer_name', 'vchr_customer_company_name as company_name',
                'tbl_enquiries.mobile_no', 'vchr_customer_email as customer_email', 'tbl_feedback_status.vchr_status as lead_status',])
          //  ->where('tbl_enquiries.fk_int_user_id', $this->vendorId)
           ->where('tbl_enquiries.created_by', $this->userId)
            ->orderby('pk_int_enquiry_id', 'DESC')
            ->get();
        if (count($enquiries)) {
            return $this->applicationObj->sendResponse('Enquiry retrieved successfully.', $enquiries);
        } else {
            return $this->applicationObj->sendError('No Data Found', '');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_name' => 'required',
            'country_code' => 'required',
            'mobile_no' => 'required|numeric',
            'enq_type_id' => 'required|numeric',
            'enq_status_id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            return $this->applicationObj->sendError('Validation Error', $validator->errors());
        }
        if ($validator->passes()) {

            $enquiry = new Enquiry();
            $enquiry->vchr_customer_name = $request->customer_name;
            $enquiry->country_code = $request->country_code;
            $enquiry->mobile_no = $request->mobile_no;
            $enquiry->vchr_customer_mobile = $request->country_code . $request->mobile_no;
            $enquiry->feedback_status = $request->enq_status_id;
            $enquiry->read_status = Variables::DEACTIVE;
            $enquiry->int_status = Variables::ACTIVE;
            $enquiry->fk_int_enquiry_type_id = $request->enq_type_id;
            $enquiry->created_by = $this->userId;
            $enquiry->fk_int_user_id = $this->vendorId;
            $enquiry->vchr_enquiry_feedback = $request->feedback;
            $enquiry->staff_id = $this->userId;
            $enquiry->save();
            try{
              Enquiry::newLeadFunctions($enquiry->pk_int_enquiry_id);
            }catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
            try {
                event(new CreateFollowup($request->enq_status_id,EnquiryFollowup::TYPE_STATUS,$enquiry->pk_int_enquiry_id,$this->userId));
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }

            if ($enquiry) {
                $enquiryType = EnquiryType::where('pk_int_enquiry_type_id', $request->enq_type_id)->select('vchr_enquiry_type')->first()->vchr_enquiry_type;
                /*--------------------------------------Notifications : User------------------------------------Starts*/
                $userMessageContent = 'You have new lead via ' . $enquiryType . '-' . $request->customer_name . '( ' . $request->mobile_no . ' )';
                /*----------------------------------------Email-------------------------------------------------------*/
                $existEmailNotification = $this->applicationObj->checkNotificationTypeExists($this->vendorId, NotificationType::EMAIL);
                if ($existEmailNotification == true) {
                    $subject = "New Leads Notifications";
                    $from = env('MAIL_FROM_ADDRESS');
                    $to = $this->user->email;
                    $attachment = "";
                    $this->applicationObj->notificationEmail($from, $to, $subject, $userMessageContent, $attachment, $this->vendorId);
                }
                /*---------------------------------------Telegram-----------------------------------------------------*/
                $telegramId = $this->user->telegram_id;
                $this->applicationObj->notificationTelegram($telegramId, $userMessageContent, $this->vendorId);
                /*--------------------------------------Notifications : User--------------------------------------Ends*/
                return $this->applicationObj->sendResponse('Enquiry Stored.', '');

            }
        }
    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $enquiry = Enquiry::leftJoin('tbl_enquiry_types', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'tbl_enquiry_types.pk_int_enquiry_type_id')
            ->leftJoin('tbl_enquiry_purpose', 'tbl_enquiries.fk_int_purpose_id', '=', 'tbl_enquiry_purpose.pk_int_purpose_id')
            ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id', 'created_at')
            ->leftJoin('tbl_users', 'tbl_enquiries.created_by', '=', 'tbl_users.pk_int_user_id')
            ->select('pk_int_enquiry_id as id', 'vchr_customer_name as customer_name',
                'vchr_customer_company_name as company_name', 'vchr_customer_mobile as customer_mobile',
                'vchr_customer_email as customer_email', 'feedback_status as enq_status_id',
                'vchr_status as enq_status', 'vchr_enquiry_feedback as enquiry_feedback ',
                'fk_int_enquiry_type_id as enq_type_id', 'tbl_enquiry_types.vchr_enquiry_type as enq_type',
                'fk_int_purpose_id as enq_purpose_id', 'vchr_purpose as enq_purpose',
                'vchr_color as color', 'tbl_enquiries.int_status as status',
                'tbl_users.vchr_user_name as created_by_user', 'fb_ad_id', 'fb_ad_info',
                'tbl_enquiries.created_at', 'tbl_enquiries.updated_at')
            ->where('tbl_enquiries.pk_int_enquiry_id', $id)
            ->first();
        if ($enquiry) {
            $enquiry->read_status = 1;
            $enquiry->save();
            $enquiry->fb_ad_info = json_decode($enquiry->fb_ad_info);
            $enquiryFollowups = EnquiryFollowup::with(['user', 'added_by', 'assigned'])
                ->where('enquiry_id', $id)->orderBy('created_at', 'desc')->get();
            $sortedarray = array();
            foreach ($enquiryFollowups as $member) {
                $member['group_id'] = $member->created_at->format('F Y');
                $groupid = $member['group_id'];
                if (isset($sortedarray[$groupid])) {
                    $sortedarray[$groupid][] = $member;
                } else {
                    $sortedarray[$groupid] = array($member);
                }
            }
            $enquiry->enquiryFollowups = $sortedarray;
            return $this->applicationObj->sendResponse('Enquiry Details.', $enquiry);
        } else {
            return $this->applicationObj->sendError('No Data Found', '');
        }
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'customer_name' => 'required',
            'country_code' => 'required',
            'mobile_no' => 'required|numeric',
            'enq_type_id' => 'required|numeric',
            'enq_status_id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            return $this->applicationObj->sendError('Validation Error', $validator->errors());
        }
        $enquiry = Enquiry::find($id);
        if ($enquiry) {
            try{
                $vendor = $this->vendor;
                if (($request->staff_id != NULL)) {
                    if ($enquiry->staff_id != NULL) {
                        if ($enquiry->staff_id != $request->staff_id) {
                            $staff = User::find($request->staff_id);
                            $vendor->notify(new AssignLeadToStaff($enquiry,$this->userId));
                            $staff->notify(new AssignLeadToStaff($enquiry,$this->userId));
                        }
                    } else {
                        $enquiry->staff_id = $request->staff_id;
                        $staff = User::find($request->staff_id);
                        $vendor->notify(new AssignLeadToStaff($enquiry,$this->userId));
                        $staff->notify(new AssignLeadToStaff($enquiry,$this->userId));
                    }
                }
                $enquiry->vchr_customer_name = $request->customer_name;
                $enquiry->mobile_no = $request->mobile_no;
                $enquiry->vchr_customer_mobile = $request->country_code . $request->mobile_no;
                $enquiry->updated_by = $this->userId;
                $enquiry->address = $request->address;
                $enquiry->fk_int_purpose_id = $request->enq_purpose_id;
                $enquiry->fk_int_enquiry_type_id = $request->enq_type_id;
                $enquiry->feedback_status = $request->enq_status_id;
                if ($request->more_phone_numbers) {
                    $request->more_phone_numbers = array_filter($request->more_phone_numbers);
                }
                $enquiry->more_phone_numbers = json_encode($request->more_phone_numbers);
                $enquiry->save();
                Enquiry::statusChangeFunction($enquiry);
                Enquiry::handlePurposeChangeAutomation($enquiry);
                return $this->applicationObj->sendResponse('Enquiry Updated.', '');
            }catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
        } else {
            return $this->applicationObj->sendError('No Data Found', '');
        }
    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $enquiry = Enquiry::find($id);
        if ($enquiry) {
            $enquiry->delete();
            EnquiryFollowup::where('enquiry_id', $enquiry->pk_int_enquiry_id)->delete();
            return $this->applicationObj->sendResponse('Enquiry Deleted.', '');
        } else {
            return $this->applicationObj->sendError('No Data Found', '');
        }
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSmsTemplates()
    {
        $smsTemplates = SmsTemplateCode::where('fk_int_user_id', $this->vendorId)
            ->where('int_status', Variables::ACTIVE)
            ->select('pk_int_sms_template_id as id', 'vchr_sms_template_title as title', 'sms_template_code')
            ->get();
        if (count($smsTemplates)) {
            return $this->applicationObj->sendResponse('Sms Templates.', $smsTemplates);
        } else {
            return $this->applicationObj->sendError('No Data Found', '');
        }
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEnquiryTypes()
    {
        $enquiry_types = EnquiryType::where('int_status', Variables::ACTIVE)
            ->where('fk_int_user_id', $this->vendorId)
            ->Orwhere('fk_int_user_id', 0)
            ->select('pk_int_enquiry_type_id as id', 'vchr_enquiry_type as enquiry_type')
            ->get();
        if (count($enquiry_types)) {
            return $this->applicationObj->sendResponse('Enquiry types.', $enquiry_types);
        } else {
            return $this->applicationObj->sendError('No Data Found', '');
        }
    }

    /**
     * @return mixed
     */
    public function getEnquiryStatus()
    {
        $enquiry_status = FeedbackStatus::where('int_status', Variables::ACTIVE)
            ->where('fk_int_user_id', $this->vendorId)
            ->select('pk_int_feedback_status_id as id', 'vchr_status as status')
            ->get();
        if (count($enquiry_status)) {
            return $this->applicationObj->sendResponse('Enquiry types.', $enquiry_status);
        } else {
            return $this->applicationObj->sendError('No Data Found', '');
        }
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTotalLeadsCount()
    {
        return $this->applicationObj->sendResponse('Total Leads', Enquiry::where('fk_int_user_id', $this->vendorId)->count());
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function newLeadsCount()
    {
        $day = Carbon::today()->subDays(7);
        return $this->applicationObj->sendResponse('Total Leads', Enquiry::where('fk_int_user_id', $this->vendorId)->where('created_at', '>', $day)->count());
    }

}
