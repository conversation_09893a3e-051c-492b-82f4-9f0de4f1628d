<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\BackendModel\GlPromo;
use App\BackendModel\GlPromocodes;
use App\BackendModel\GlApiTokens;
use App\BackendModel\Campaign;
use App\BackendModel\SenderId;
use App\Jobs\PromoPackageExpiredJobs;
use App\BackendModel\EnquiryType;
use App\BackendModel\Enquiry;
use App\BackendModel\GlPromoCustomers;
use App\BackendModel\GlPromoMessage;
use App\BackendModel\GlPromoVerify;
use App\User;
use Validator;
use DataTables;
use App\Common\Variables;
use DB;
use Illuminate\Support\Facades\Input;
use Auth;
use Carbon\Carbon;
use App\Common\Common;
use App\Core\CustomClass;
use App\Common\SingleSMS;
use App\Common\Notifications;
use App\SmsPanel;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;


//use App\Http\Resources\Testimonial\TestimonialResource;

class GlPromoController extends Controller
{

    public function index(Request $request)
    {
        $input = $request->all();
        $mobileno = $request->mobileno;
        $rule = [
            'token' => 'required',
            'mobileno' => 'required|numeric|digits_between:8,14'
        ];

        $validator = validator::make($input, $rule);
        if ($validator->passes()) {
            try {
                $check_token = GlApiTokens::where('vchr_token', $request->token)->first();
                if (!empty($check_token)) {
                    $userId = $check_token->fk_int_user_id;
                    $tokenId = $check_token->pk_int_token_id;
                    $vendor_id = User::getVendorIdApi($userId);
                    $block = new Common();
                    $blockSubscribedUsers = $block->checkUserSubscription($userId, Variables::SERVICE_GLP);
                    if ($blockSubscribedUsers) {
                        $glpromocodes = GlPromocodes::where('status', GlPromocodes::ACTIVATE)->where('vendor_id', $userId)->limit(10)->get();
                        $glpromocodesExist = GlPromocodes::where('status', GlPromocodes::ACTIVATE)->where('vendor_id', $userId)->limit(10)->first();
                        if ($glpromocodesExist) {
                            $user = User::select('vchr_user_name')->find($userId);

                            $promo = array();
                            foreach ($glpromocodes as $glpromocode) {
                                $promo[] = $glpromocode->promocodes;
                            }
                            $customersExist = GlPromoCustomers::where('mobileno', $request->mobileno)->where('vendor_id', $userId)->first();
                            if ($customersExist) {
                                $implodepromo = $customersExist->promocodes;
                                $promoId=$customersExist->id;
                                $promocodeMessage = str_replace(',', "\n", $implodepromo);
                            } else {
                                $deactivePromocodes = GlPromocodes::where('status', GlPromocodes::ACTIVATE)->where('vendor_id', $userId)->limit(10)->update(['status' => GlPromocodes::DEACTIVATE]);
                                $implodepromo = implode(',', $promo);
                                $cust = new GlPromoCustomers();
                                $cust->promocodes = $implodepromo;
                                $cust->mobileno = $request->mobileno;
                                $cust->vendor_id = $userId;
                                $cust->status = GlPromoCustomers::ACTIVATE;
                                $cust->save();
                                $promoId=$cust->id;
                                $promocodeMessage = str_replace(',', "\n", $implodepromo);
                                $explodePromo = explode(",",$implodepromo);
                                $promocount=count($explodePromo);
                                for($i=0;$i<$promocount;$i++)
                                {
                                    $singlePromoCode = $explodePromo[$i];
                                    $verify=new GlPromoVerify();
                                    $verify->vendor_id=$userId;
                                    $verify->status=GlPromoVerify::ACTIVATE;
                                    $verify->promocodes=$singlePromoCode;
                                    $verify->promo_id=$promoId;
                                    $verify->save();
                                }
                            }
                            $promocodes = json_encode(explode(",", $implodepromo));
                            //$promocodeMessage = str_replace(',', '<br />', $implodepromo);

                            //$gdeal = $this->savePromocodeDetailsinGdeal($promocodes, $mobileno, $user->vchr_user_mobile, $user->vchr_user_name, '', '', '', '');
                            
                            $promoMessage=GlPromoMessage::where('status',GlPromoMessage::ACTIVATE)->first()->message;
                            $message1= urlencode(str_replace('|GLPROMOCODES|',$promocodeMessage,$promoMessage));
                            $message= str_replace('|GLPROMOCODES|',$promocodeMessage,$promoMessage);
                            if (strlen($message) != strlen(utf8_decode($message)))
                                {
                                    $messageType=1;
                                }
                                else
                                {
                                    $messageType=0;
                                }
                                
                                //return count($explodePromo);



                            //Send Sms

                            $username = $user->vchr_user_name;
                            //$message = 'Hey,Here is your Promo Codes ' . $implodepromo;
                            $defaultSenderId = SingleSMS:: getSenderid($vendor_id, EnquiryType::GLPROMO);
                            $defaultRoute = SingleSMS:: getRoute($vendor_id, EnquiryType::GLPROMO);
                            $apitemplate = CustomClass::userDefaultApiTemplate($vendor_id);
                            if (!empty($apitemplate)) {
                                $input['template_id'] = $apitemplate->pk_int_api_template_id;
                                $input['template'] = $apitemplate->text_api_template_description;
                                $response = CustomClass::urlReplacement($input['template'], $message, $mobileno, $defaultSenderId);
                            } else {
                                $send = new SingleSMS();
                                $smsPanel = $send->getSmsPanel($defaultRoute, $vendor_id);
                                $balanceSms = $send->getSMSBalance($vendor_id, $defaultRoute, $smsPanel);
                                $messageType = 0;
                                if ($balanceSms > 0) {
                                    if ($smsPanel->title == SmsPanel::ALERTBOX) {
                                        $routeCode = $send->getRouteDetails($defaultRoute)->int_sms_route_code;
                                        $smsUrl = $send->getSmsUrl($defaultSenderId, $mobileno, $message1, $defaultRoute, $routeCode, $vendor_id, $messageType);
                                        $smsCount = $send->getInputSMSCount($message,$messageType);
                                        $templateId = $send->getSmsTemplateId($defaultRoute, $vendor_id);
                                        $routeName = $send->getRouteDetails($defaultRoute)->vchr_sms_route;
                                        $insertSms = $send->storeSmsData($vendor_id, $templateId, $mobileno, $defaultSenderId, $messageType, $routeName, $message, EnquiryType::GLPROMO, $routeCode, $defaultRoute, '1', $smsCount);
                                        $response = $send->sendSms($defaultSenderId, $mobileno, $message, $routeCode, $balanceSms, $templateId, $defaultRoute, $messageType,$vendor_id, $smsUrl);

                                        $response = $send->getResponse($insertSms, $response, $templateId, $defaultRoute, $vendor_id, $smsCount);
                                    } else {
                                        $routeCode = $send->getRouteDetails($defaultRoute)->short_code;
                                        $smsUrl = $send->getSmsMerabtUrl($defaultSenderId, $mobileno, $message1, $defaultRoute, $routeCode, $vendor_id, $messageType);
                                        $smsCount = $send->getInputSMSCount($message, $messageType);
                                        $templateId = $send->getSmsTemplateId($defaultRoute, $vendor_id);
                                        $routeName = $send->getRouteDetails($defaultRoute)->vchr_sms_route;
                                        $insertSms = $send->storeSmsData($vendor_id, $templateId, $mobileno, $defaultSenderId, $messageType, $routeName, $message, EnquiryType::GLPROMO, $routeCode, $defaultRoute, '1', $smsCount);
                                        $response = $send->sendSms($defaultSenderId, $mobileno, $message, $routeCode, $balanceSms, $templateId, $defaultRoute, $messageType,$vendor_id, $smsUrl);
                                        $response = $send->getMetabtResponse($insertSms, $response, $templateId, $defaultRoute, $vendor_id, $smsCount);
                                    }
                                }
                            }
                            //
                            $userObject = User::getUserDetails($vendor_id);
                            $userAdminObject = User::getSingleAdminDetails();
                            //Notifications
                            $notifications = new Notifications();
                            $from = env('MAIL_FROM_ADDRESS');
                            $to = $userObject->email;
                            $subject = "GL Promo Notifications";
                            $name = $userObject->vchr_user_name;
                            $logo = $userAdminObject->vchr_logo;
                            $attachment = "";
                            $telegramId = $userObject->telegram_id;
                            $mobileNumber = $userObject->vchr_user_mobile;
                            $defaultSenderIdAdmin = SingleSMS:: getSenderid($userAdminObject->pk_int_user_id, '');
                            $defaultRouteAdmin = SingleSMS:: getRoute($userAdminObject->pk_int_user_id, '');
                            $content1 = "New Leads via GL Promo- " . $mobileno . "";
                            $content2 = "You have new leads via GL Promo- " . $mobileno . "";
                            $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, User::getVendorIdApi($userId), $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin);
                            //-----------------
                            return response()->json(['message' => 'Promo Code', 'promocode' => $implodepromo, 'status' => 'success']);
                        } else {
                            return response()->json(['message' => 'No Promocodes', 'status' => 'fail']);
                        }
                        //

                        Enquiry::getCRMUsers(EnquiryType::GLPROMO, $mobileno, $userId);


                    } else {
                        return response()->json(['message' => 'Plan Expired', 'status' => 'fail']);
                    }
                } else {
                    return response()->json(['message' => 'Please check the parameters', 'status' => 'fail']);
                }
            } catch (\Exception $e) {
                return response()->json(['message' => $e->getMessage(), 'status' => 'fail']);
            }
        } else {
            return response()->json(['message' => $validator->messages(), 'status' => 'fail']);
        }
    }

    /**
     * @param $promocodes
     * @param $mobileno
     * @param $user_user_mobile
     * @param $user_name
     * @param $campaignID
     * @param $promo_campaign
     * @param $campaign_discount
     * @param $promo_campaign_status
     * @return int
     * @throws \GuzzleHttp\Exception\GuzzleException
     * Send Promo code details to Gdeal Server
     * Created on 04 july-2019 at 01.15 PM
     */
    public function savePromocodeDetailsinGdeal($promocodes, $mobileno, $user_user_mobile, $user_name, $campaignID, $promo_campaign, $campaign_discount, $promo_campaign_status)
    {

        $client = new Client();
        $login_url = Variables::GDEAL_DOMAIN . "api/auth/login";
        //$login_url = "http://gdeal.local/api/auth/login";
        $loginRes = $client->request('POST', $login_url, [
            'form_params' => ['mobile' => '8453555000', 'password' => 'admin123']
        ]);
        $loginResponse = json_decode($loginRes->getBody(), true);
        $loginResponse['access_token'];
        //$url = "http://gdeal.local/api/auth/add-promo-code";
        $url = Variables::GDEAL_DOMAIN . "api/auth/add-promo-code";
        $res = $client->request('POST', $url, [
            'headers' => ['Authorization' => 'Bearer' . $loginResponse['access_token'],
                'Accept' => 'application/json'],
            'form_params' => [
                'promocodes' => $promocodes,
                'misscall_number' => $mobileno,
                'business_number' => $user_user_mobile,
                'promo_campaign_id' => $campaignID,
                'promo_campaign' => $promo_campaign,
                'discount' => $campaign_discount,
                'user_name' => $user_name,
                'promo_campaign_status' => $promo_campaign_status
            ]
        ]);
        return $res->getStatusCode();
    }
}
