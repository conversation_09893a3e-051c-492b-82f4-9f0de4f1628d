<?php

namespace App\Http\Controllers\Api\AgentApp;

use App\BackendModel\Country;
use App\BackendModel\Enquiry;
use App\BackendModel\VirtualNumber;
use App\BillingSubscription;
use App\CallLog;
use App\CallMaster;
use App\CallStatus;
use App\CloudTelephonySetting;
use App\Common\AgentApp;
use App\Common\Application;
use App\Common\Variables;
use App\DealTask;
use App\Facades\FileUpload;
use App\Http\Resources\IvrCallListResource;
use App\Ivr\Enums\Direction;
use App\Ivr\Models\Ivr;
use App\Mail\OtpMail;
use App\Modules\Faq\Faq;
use App\Modules\Notification\Notification;
use App\Task;
use App\Traits\BillingTrait;
use App\User;
use App\UserLocationHistory;
use App\UserModule;
use Carbon\Carbon;
use File;
use Hash;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Mail;
use Tymon\JWTAuth\Facades\JWTAuth;

/**
 * @group  Agent App
 *
 * APIs for Agent App Integration
 */
class AgentAppController extends Controller
{
    use BillingTrait;

    protected $agentAppObj;
    protected $applicationObj;

    public function __construct()
    {
        $this->agentAppObj = new AgentApp();
        $this->applicationObj = new Application();
    }


    /**
     * @return \Illuminate\Http\JsonResponse
     */
    /**
     * Profile Details
     *
     * @authenticated
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *      {
     *          "pk_int_user_id": 1,
     *          "vchr_user_name":"abcd",
     *          "email": "<EMAIL>",
     *          "countrycode":  6777,
     *          "mobile"     : 9999988888
     *          "address"    : "abcd",
     *          "visa_id"    : 788,
     *          "profile_pic" : 443.jpg,
     *          "location"    : "abcd",
     *          "id_visa"     :  766
     *      }
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function getProfile(Request $request): JsonResponse
    {
        return $this->response(200, false, null, $request->user()
            ->only("pk_int_user_id",
                "vchr_user_name",
                "email",
                "countrycode",
                "mobile",
                "address",
                "visa_id",
                "location",
                "vchr_user_mobile",
                "profile_pic",
                "address",
                "location",
                "id_visa"
            ));
    }
    /**
     * Forgot Password
     *
     *
     * @bodyParam mobile integer required  Mobile number. Example: 987977887
     * @bodyParam country_code integer required  Counrty Code. Example: 7678
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function forgotPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->response(200, true, $validator->errors());
        }

        $user = User::select('int_role_id', 'parent_user_id', 'pk_int_user_id')->where('email', $request->email)->first();
        if ($user) {
            if ($user->int_role_id == User::STAFF) {
                $getUser = User::getparent($user->parent_user_id);
                if ($getUser) {
                    // $number = $getUser->vchr_user_mobile;
                    $otpmessage = "OTP has been send to admin's email address";
                }
            } else {
                // $number =  $request->country_code .  $request->mobile;
                $otpmessage = 'OTP has been send to your registered email address';
            }

            $otp = rand('1000', '9999');
            User::where('pk_int_user_id', $user->pk_int_user_id)
                ->update([
                    "otp" => $otp,
                    "password_validity" => Carbon::now()->addMinute('5')]);

            try {
                $data['email'] = ($user->int_role_id == User::STAFF) ? $getUser->email : $request->email;
                $data['name'] = $user->vchr_user_name;
                $data['otp'] = $otp;

                Mail::to($data['email'])->send(new OtpMail($data));

            } catch (\Exception $e) {
                \Log::info($e->getMessage());
                return $this->response(200, true, "Otp not sending", $e->getMessage());
            }

            return $this->response(200, false, $otpmessage, null);
        } else {
            return $this->response(200, true, "User Not Found", null);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    /**
     * Reset Password
     *
     *
     * @bodyParam otp integer required   OTP. Example: 7888
     * @bodyParam new_password string  required  New Password.
     * @bodyParam new_confirm_password string required   New Confirm Password.
     * @bodyParam mobile integer required   Mobile. Example: 9739946640.
     * @bodyParam contry_code string required  Country Code. Example: IND
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function resetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'otp' => 'required',
            'new_password' => ['required', 'min:6'],
            'new_confirm_password' => ['required', 'same:new_password'],
            'email' => 'required'
        ]);
        if ($validator->fails()) {
            return $this->response(200, true, $validator->errors());
        }
        $user = User::select('otp', 'password_validity', 'pk_int_user_id', 'email')->where('email', $request->email)->first();
        if (!$user) {
            return $this->response(200, true, "User not found");
        }
        if (($request->otp == $user->otp) && $user->password_validity >= Carbon::now()) {
            User::where('pk_int_user_id', $user->pk_int_user_id)->update(
                [
                    'password' => bcrypt($request->new_password),
                    'otp' => NULL,
                    'password_validity' => NULL
                ]);
            return $this->response(200, false, "Password Changed");
        } else {
            return $this->response(200, true, "Invalid OTP");
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    /**
     * Edit Profile Details
     *
     * @authenticated
     *
     * @bodyParam name string required  Name. Example: abcd
     * @bodyParam mobile integer required   Mobile. Example: 8900887877
     * @bodyParam country_code string required  Country Code. Example: IND
     * @bodyParam email string required  Email. Example: <EMAIL>
     * @bodyParam location string required  Location. Example: abcd
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function updateProfile(Request $request)
    {
        if (Auth::user()->email == $request->email) {
            $rules = [
                'name' => 'required',
                'mobile' => 'required',
                'country_code' => 'required',
                'location' => 'required',
                'email' => 'required',
            ];
        } else {
            $rules = [
                'name' => 'required',
                'mobile' => 'required',
                'country_code' => 'required',
                'email' => ['required', 'unique:tbl_users,email'],
                'location' => 'required',
            ];
        }
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return $this->response(200, true, $validator->errors(), null);
        }
        try {
            $user = User::find($request->user()->pk_int_user_id);
            $user->vchr_user_name = $request->name;
            $user->address = $request->address;
            $user->location = $request->location;
            $user->email = $request->email;
            $user->visa_id = $request->visa_id ?? "";
            $user->mobile = $request->mobile;
            $user->countrycode = $request->country_code;
            $user->vchr_user_mobile = $request->country_code . $request->mobile;
            $user->address = $request->address;
            $user->location = $request->location;
            $user->visa_id = $request->id_visa;

            if ($request->hasFile('logo')) {
                $image = $request->file('logo');
                $path = 'uploads/user-profile/';
                $name = mt_rand() . '.' . $image->getClientOriginalExtension();
                FileUpload::uploadFile($image, $path, $name, 's3');
                $user->vchr_logo = $name;
            }
            $user->save();

            try {
                if ($user->int_role_id == 2)
                    $this->updateUserInformation($user);
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }

            // if ($flag) {
            return $this->response(200, false, "Success", $user);

            // } else {
            //     return $this->response(200, true, "Error");

            // }
        } catch (\Exception $e) {
            \Log::info($e->getMessage());
            return $this->response(200, true, $e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    /**
     * Change Password
     *
     *
     * @bodyParam old_password string  required Old Password.
     * @bodyParam new_password string  required New Password[min:6].
     * @bodyParam new_confirm_password string  required New Confirmation Password.
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'old_password' => 'required',
            'new_password' => ['required', 'min:6'],
            'new_confirm_password' => ['required', 'same:new_password']
        ]);
        if ($validator->fails()) {
            return $this->response(200, true, $validator->errors());
        }
        $user = User::select('password')->find($request->user()->pk_int_user_id);
        if (!Hash::check($request->new_password, $user->password))
            return $this->response(200, true, "Old Password is Incorrect");
        User::where('pk_int_user_id', $request->user()->pk_int_user_id)
            ->update([
                "password" => bcrypt($request->new_password)]);
        return $this->response(200, false, "Success");
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    /**
     * Verify Change Password Otp
     *
     * @bodyParam otp integer  required  otp. Example: 8999
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function verifyChangePasswordOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'otp' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->response(200, true, $validator->errors());
        }
        if (($request->otp == $request->user()->otp) && $request->user()->password_validity >= Carbon::now()) {
            User::where('pk_int_user_id', Auth::user()->pk_int_user_id)->update(
                [
                    'password' => bcrypt(Session::get('new_password')),
                    'otp' => NULL,
                    'password_validity' => NULL
                ]);
            return $this->response(200, false, "Password Changed");
        } else {
            return $this->response(200, true, "Invalid OTP");
        }
    }


    /**
     * @return \Illuminate\Http\JsonResponse
     * Get Faq List
     */

    /**
     * FAQ
     *
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [{
     * "title":"what is ?",
     * "description":"Answer",
     * },{
     * }
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function faq()
    {

        $faqs = Faq::language()
            ->select('title', 'description')
            ->Where('user_role', User::STAFF)
            ->get();
        return $this->response(200, false, null, $faqs);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    /**
     * Get Notification
     *
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [{
     *  "id":1,
     *  "data":"abcd",
     *  "created_at":10/3/2021,
     *  "read_at":12/3/2021,
     *  "notifiable_id":56
     * }
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function getNotifications(): JsonResponse
    {
        $notifications = Notification::query()
            ->select('id',
                'data',
                DB::raw('CONVERT_TZ(created_at, "+00:00", "+05:30") AS created_at'),
                'read_at',
                'notifiable_id',
                'notifiable_type',
                'type')
            ->where('notifiable_id', Auth::user()->pk_int_user_id)
            ->where('notifiable_type', 'App\User')
            ->paginate(15);

        $response['total_count'] = $notifications->total();
        $response['data'] = $notifications;

        return $this->response(200, false, null, $response);
    }

    /**
     * View Notification
     *
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function viewNotification(string $id): JsonResponse
    {
        $notification = Notification::query()
            ->select(['id', 'data'])
            ->whereKey($id)
            ->where('notifiable_id', Auth::user()->pk_int_user_id)
            ->where('notifiable_type', 'App\User')
            ->first();

        if ($notification instanceof Notification) {
            $notification->update([
                'read_at' => Carbon::now()
            ]);
        }

        return $this->response(200, false, null, $notification?->data);

    }

    /**
     * Logout
     *
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function logout(Request $request)
    {
        Auth::guard('api')->logout();
        return $this->response(200, false, 'Successfully logged out', '');

    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * Country List API
     * Date : 14-11-2020 - Saturday
     */
    /**
     * Get Country List
     *
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *      {
     * "id":7,
     * "name":"abcd",
     * "country_code":IND,
     * "code": H89
     *     }
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function getCountryList()
    {
        $countries = Country::select('id', 'name', 'country_code', 'code')->get();
        return $this->response(200, false, null, $countries);
    }

    /**
     * Set Location
     *
     * @authenticated
     *
     * @bodyParam latitude decimal  required  Latitude. Example: 88.79999
     * @bodyParam longitude decimal  required  Longitude. Example: -78.9990
     * @bodyParam speed decimal  required  Speed. Example: 20
     * @bodyParam angle decimal  required  Angle. Example: 50
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function setLocation(Request $request)
    {
        $validate_fields = [
            'latitude' => 'required',
            'longitude' => 'required',
            'speed' => 'required',
            'angle' => 'required',
        ];
        $validate_messages = [

        ];

        $validation = Validator::make($request->all(), $validate_fields, $validate_messages);
        if ($validation->fails()) {
            return $this->response(200, false, $validation->errors());
        } else {
            $user = User::find(Auth::user()->pk_int_user_id);
            $old_lat = $user->latitude;
            $old_long = $user->longitude;
            if ($user) {
                $user->latitude = $request->latitude;
                $user->longitude = $request->longitude;
                $user->speed = $request->speed;
                $user->angle = $request->angle;
                $user->battery = $request->battery;
                $user->device_model = $request->device_model;
                $user->os_version = $request->os_version;
                $user->save();
                if ($old_lat != $request->latitude || $old_long != $request->longitude) {
                    $history = new UserLocationHistory;
                    $history->fk_int_user_id = Auth::user()->pk_int_user_id;
                    $history->latitude = $request->latitude;
                    $history->longitude = $request->longitude;
                    $history->speed = $request->speed;
                    $history->angle = $request->angle;
                    $history->save();
                }
                return $this->response(200, true, 'Success');
            } else {
                return $this->response(200, false, 'Whoops something went wrong');
            }
        }
    }

    public function checkUserSubscription()
    {
        $user = JWTAuth::parseToken()->toUser();
        $userId = $user->pk_int_user_id;
        $roleId = $user->int_role_id;
        $vendorId = $this->applicationObj->getVendorId($userId);
        if ($roleId == User::USERS || $roleId == User::STAFF) {
            if ($user->int_status == 0) {
                $message = 'Your account is inactive. Please contact your administrator.';
                $data['message'] = $message;
                $data['type'] = Variables::INACTIVE;
                $data['subscription_details'] = null;
                try {
                    JWTAuth::refresh(JWTAuth::getToken()); // Invalidate token   
                } catch (\Exception $e) {
                    \Log::info($e->getMessage());
                }

                return response()->json(['status' => 1, 'message' => 'success', 'data' => $data]);
            }

            $userSubscription = BillingSubscription::where('vendor_id', $vendorId)
                ->where('services', 'LIKE', '%' . Variables::SERVICE_CRM . '%')
                ->orderBy('expiry_date', 'desc')
                ->first();

            if ($userSubscription) {
                if (Carbon::parse($userSubscription->expiry_date)->gte(Carbon::today())) {
                    $data['message'] = 'Subscription is Active';
                    $data['type'] = Variables::SUBSCRIBED;
                    $data['subscription_details'] = $userSubscription;

                    if ($vendorId == 2737) {
                        try {
                            JWTAuth::refresh(JWTAuth::getToken()); // Invalidate token   
                        } catch (\Exception $e) {
                            \Log::info('--------invalidate the user token on id 2737-------');
                        }
                    }
                    return response()->json(['status' => 1, 'message' => 'success', 'data' => $data]);
                } else {
                    $data['message'] = 'Subscription expired, Please contact your administrator.';
                    $data['type'] = Variables::EXPIRED;
                    $data['subscription_details'] = $userSubscription;
                    return response()->json(['status' => 1, 'message' => 'success', 'data' => $data]);
                }
            } else {
                $message = 'You are not subscribed to CRM. Please contact your administrator.';
                $data['message'] = $message;
                $data['type'] = Variables::NOT_SUBSCRIBED;
                $data['subscription_details'] = null;
                return response()->json(['status' => 1, 'message' => 'success', 'data' => $data]);
            }
        }
    }

    /**
     * Function to get active modules of users
     *
     * @response {
     *  "status": 1,
     *  "message": "OK",
     *  "data" : [
     *  ],
     * }
     *
     */
    public function modules(Request $request)
    {
        $user = JWTAuth::parseToken()->toUser();
        $userId = $user->pk_int_user_id;
        $vendorId = $this->applicationObj->getVendorId($userId);
        $modules = UserModule::where('fk_int_user_id', $vendorId)->pluck('module_id')->toArray();
        return $this->response(200, false, null, $modules);
    }

    public function setPreferredCallingMethod(Request $request)
    {
        if (!$request->calling_method)
            return $this->response(200, true, 'Calling Method Field is Required');
        if ($request->calling_method == 1 || $request->calling_method == 2) {
            $user = JWTAuth::parseToken()->toUser();
            $user->calling_method = $request->calling_method;
            $user->save();
            return $this->response(200, false, 'Success');
        } else
            return $this->response(200, true, 'Calling Method Field Value Must be 1 or 2');
    }

    public function getPreferredCallingMethod(Request $request)
    {
        $userId = $request->user()->pk_int_user_id;
        $vendorId = $this->applicationObj->getVendorId($userId);
        $vendor = User::select('pk_int_user_id', 'calling_method')->where('pk_int_user_id', $userId)->first();
        $vNumber = VirtualNumber::where('fk_int_user_id', $vendorId)->exists();
        return $this->response(200, false, 'Success', ['calling_method' => $vendor->calling_method, 'ivr_enabled' => $vNumber]);

    }

    public function updateFcmToken(Request $request)
    {
        if ($request->fcm_token) {
            $user = $request->user();
            $user->fcm_token = $request->fcm_token;
            $user->save();
            return $this->response(200, false, 'Success');
        }
        return $this->response(200, true, 'Token missing');
    }

    public function ivrCallList(Request $request)
    {
        Log::info('Request received for IVR call list from mobile app', ['request' => $request->all()]);
        $data = array();
        /**
         * @var User $user
         */
        $user = $request->user();
        $userId = $user->pk_int_user_id;
        $vendorId = $user->getBusinessId();

        $telephony = CloudTelephonySetting::query()
            ->where('default', 1)
            ->where('vendor_id', $vendorId)
            ->first();
        if (!$telephony instanceof CloudTelephonySetting) {
            return response()->json([
                "status" => 1,
                "message" => "Success",
                "data" => [
                    'logs' => [],
                    'total_count' => 0,
                    'per_page' => 10,
                    'current_page' => 0
                ]
            ]);
        }

        if ($telephony->viaBonvoice()) {
            $ivr = CallMaster::query()
                ->where('vendor_id', $vendorId)
                ->whereNotNull('call_status')
                ->with(['virtual_number', 'virtual_number.agent', 'agent', 'enquiry'])
                ->where(function ($q) use ($request) {
                    if ($request->call_status == 'incoming') {
                        $q->where('direction', 'Inbound')
                            ->where('call_status', 'ANSWERED');
                    } elseif ($request->call_status == 'outgoing') {
                        $q->where('direction', 'Outbound');
                    } elseif ($request->call_status == 'missed_call') {
                        $q->where('direction', 'Inbound')
                            ->where('call_status', 'NOANSWER');
                    }
                })
                ->select(
                    'fk_int_enquiry_id',
                    'called_number',
                    'caller_number',
                    'call_date',
                    'recording_url',
                    'call_status',
                    'id',
                    'call_uuid',
                    'agent_number',
                    'direction',
                    'total_call_duration',
                    'conversation_duration'
                );

            $request->merge([
                'operator' => 2,
                'vendorid' => $vendorId
            ]);
        } else {
            $ivr = Ivr::query()
                ->with(['virtual_number', 'virtual_number.agent', 'agent'])
                ->where('vendor_id', $vendorId)
                ->where(function ($q) use ($request) {
                    if ($request->call_status == 'incoming') {
                        $q->where('direction', Direction::Inbound);
                    } elseif ($request->call_status == 'outgoing') {
                        $q->where('direction', Direction::Outbound);
                    } elseif ($request->call_status == 'missed_call') {
                        $q->where('events', 'Missed call');
                    }
                })
                ->select(['fk_int_enquiry_id', 'direction', 'called_number', 'caller_number', 'call_date', 'recording_url', 'call_status', 'id', 'call_uuid', 'agent_number']);
            $request->merge([
                'operator' => 1,
                'vendorid' => $vendorId
            ]);
        }


        if ($user->isStaff()) {
            if ($telephony->viaBonvoice()) {
                if ($request->agent_id) {
                    $ivr->where(function ($where) use ($request) {
                        $where->whereHas('agent', function ($where) use ($request) {
                            $where->where('pk_int_user_id', $request->agent_id);
                        })
                            ->orWhereHas('virtual_number', function ($where) use ($request) {
                                $where->where('agent_id', $request->agent_id);
                            });
                    });
                }

                $ivr->where(function ($where) use ($user) {
                    $lastTenDigits = substr($user->mobile, -10);
                    $where->where('called_number', 'LIKE', '%' . $lastTenDigits);
                });
            } else {
                if ($request->agent_id) {
                    $ivr->where(function ($where) use ($request) {
                        $where->whereHas('agentb', function ($where) use ($request) {
                            $where->where('pk_int_user_id', $request->agent_id);
                        })
                            ->orWhereHas('virtual_number', function ($where) use ($request) {
                                $where->where('agent_id', $request->agent_id);
                            });
                    });
                }

                $ivr->where(function ($query) use ($user) {
                    $query->where('called_number', $user->vchr_user_mobile)
                        ->orWhere('agent_number', $user->vchr_user_mobile);
                });
            }
        }

        $per_page = 10;
        $ivr = $ivr->orderBy('id', 'DESC')
            ->paginate($per_page);

        $request->merge([
            'user_id' => $userId
        ]);
        $collection = IvrCallListResource::collection($ivr);
        $total1 = $collection->total();
        $data['logs'] = $collection;
        $data['total_count'] = $total1;
        $data['per_page'] = $per_page;
        $data['current_page'] = (int)request('page') ?? 1;

        return $this->response(200, false, 'Success', $data);

    }

    public function ivrCallListCount(Request $request)
    {
        Log::info('Request received for IVR call list count from mobile app', ['request' => $request->all()]);

        $counts = array();
        /**
         * @var User $user
         */
        $user = $request->user();

        $vendorId = $user->getBusinessId();

        $telephony = CloudTelephonySetting::query()
            ->where('default', 1)
            ->where('vendor_id', $vendorId)
            ->first();

        if (!$telephony instanceof CloudTelephonySetting) {
            return response()->json([
                'status' => 1,
                'message' => 'Success',
                'total_count' => 0,
                'incoming_count' => 0,
                'outgoing_count' => 0,
                'missed_count' => 0
            ]);
        }

        if ($telephony->viaBonvoice()) {
            $counts = CallMaster::query()
                ->selectRaw('
                    SUM(CASE WHEN direction = "Inbound" OR call_status = "ANSWERED" THEN 1 ELSE 0 END) as incoming_count,
                    SUM(CASE WHEN direction = "Outbound" THEN 1 ELSE 0 END) as outgoing_count,
                    SUM(CASE WHEN direction = "Inbound" AND call_status = "NOANSWER" THEN 1 ELSE 0 END) as missed_count,
                    COUNT(*) as total_count
                ')
                ->where('vendor_id', $vendorId)
                ->whereNotNull('call_status');
        } else {
            $counts = Ivr::query()
                ->selectRaw('
                    SUM(CASE WHEN direction=1 THEN 1 ELSE 0 END) as incoming_count,
                    SUM(CASE WHEN direction=2 THEN 1 ELSE 0 END) as outgoing_count,
                    SUM(CASE WHEN events = "Missed call" THEN 1 ELSE 0 END) as missed_count,
                    COUNT(*) as total_count
                ')
                ->where('vendor_id', $vendorId);
        }

        if ($user->isStaff()) {
            if ($telephony->viaBonvoice()) {
                if ($request->agent_id)
                    $counts->where(function ($where) use ($request) {
                        $where->whereHas('agent', function ($where) use ($request) {
                            $where->where('pk_int_user_id', $request->agent_id);
                        })
                            ->orWhereHas('virtual_number', function ($where) use ($request) {
                                $where->where('agent_id', $request->agent_id);
                            });
                    });

                $counts->where(function ($where) use ($user) {
                    $lastTenDigits = substr($user->mobile, -10);
                    $where->where('called_number', 'LIKE', '%' . $lastTenDigits);
                });
            } else {
                if ($request->agent_id) {
                    $counts->where(function ($where) use ($request) {
                        $where->whereHas('agentb', function ($where) use ($request) {
                            $where->where('pk_int_user_id', $request->agent_id);
                        })
                            ->orWhereHas('virtual_number', function ($where) use ($request) {
                                $where->where('agent_id', $request->agent_id);
                            });
                    });
                }

                $counts->where(function ($query) use ($user) {
                    $query->where('called_number', $user->vchr_user_mobile)
                        ->orWhere('agent_number', $user->vchr_user_mobile);
                });
            }
        }

        $counts = $counts->first();
        return response()->json([
            'status' => 1,
            'message' => 'Success',
            'total_count' => (int)$counts->getAttribute('total_count'),
            'incoming_count' => (int)$counts->getAttribute('incoming_count'),
            'outgoing_count' => (int)$counts->getAttribute('outgoing_count'),
            'missed_count' => (int)$counts->getAttribute('missed_count'),
        ]);
    }

    public function uploadProfilePicture(Request $request)
    {
        $rules = [
            'image' => 'required|image:jpeg,png,jpg|max:10480'
        ];

        $input = $request->only('image');
        $validator = Validator::make($input, $rules);

        if ($validator->fails()) {
            return response()->json(['status' => 0, 'error' => $validator->messages()]);
        }

        $userId = JWTAuth::parseToken()->toUser()->pk_int_user_id;
        $user = User::find($userId);
        $image = $request->file('image');
        $path = 'uploads/user-profile/';

        if ($user->vchr_logo) {
            $imagePath = 'uploads/user-profile/' . $user->vchr_logo;
            if (Storage::disk('s3')->exists($imagePath)) {
                FileUpload::deleteFile($path, 's3');
            }
            FileUpload::uploadFile($image, $path, $user->vchr_logo, 's3');
        } else {
            $name = mt_rand() . '.' . $image->getClientOriginalExtension();
            FileUpload::uploadFile($image, $path, $name, 's3');
            $user->vchr_logo = $name;
            $user->save();
        }

        return response()->json([
            'status' => 1,
            'message' => 'Success',
            'image_path' => FileUpload::viewFile('/uploads/user-profile/' . $user->vchr_logo, 's3')
        ]);
    }

    public function removePicture(Request $request)
    {
        $userId = JWTAuth::parseToken()->toUser()->pk_int_user_id;
        $user = User::find($userId);

        $imagePath = public_path('uploads') . '/' . $user->vchr_logo;
        if (File::exists($imagePath)) {
            unlink($imagePath);
        }

        $user->vchr_logo = null;
        $user->save();

        return response()->json([
            'status' => 1,
            'message' => 'Success',
            'image_path' => null
        ]);
    }

    public function callReport(Request $request)
    {
        /**
         * @var User $user
         */
        $user = $request->user();
        $vendorId = $user->getBusinessId();

        Log::info('Requesting for call report from mobile app', [
            'user_id' => $user->pk_int_user_id,
            'request' => $request->all(),
            'vendor_id' => $vendorId,
        ]);

        $roleId = $user->int_role_id;

        $today = today()->format('Y-m-d');
        $date_from = $today;
        $date_to = $today;
        if ($request->date_from != null && $request->date_to != null) {
            $date_from = $request->date_from;
            $date_to = $request->date_to;
        } else {
            if ($request->date_filter == "month") {
                $date_from = Carbon::now()->startOfMonth()->format('Y-m-d');
                $date_to = Carbon::now()->endOfMonth()->format('Y-m-d');
            }
            if ($request->date_filter == "today") {
                $date_from = $today;
                $date_to = $today;
            }
            if ($request->date_filter == "week") {
                $date_from = Carbon::now()->startOfWeek()->format('Y-m-d');
                $date_to = Carbon::now()->endOfWeek()->format('Y-m-d');
            }
        }


        $call_status_ids = CallStatus::where('vendor_id', $vendorId)->pluck('id')->toArray();
        $all_tasks = Task::where('tasks.vendor_id', $vendorId)
            ->where('task_category_id', 2)
            ->where(function ($q) use ($roleId, $user, $vendorId) {
                if ($roleId == User::STAFF && $user->is_co_admin == 0) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('assigned_to', $user->pk_int_user_id);
                } else if ($roleId == User::USERS || $user->is_co_admin == 1) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('tasks.vendor_id', $vendorId);
                }
            });

        $deal_all_task = DealTask::where('deal_tasks.vendor_id', $vendorId)->where('deal_tasks.task_category_id', 2)
            ->where(function ($q) use ($roleId, $user, $vendorId) {
                if ($roleId == User::STAFF && $user->is_co_admin == 0) {
                    $q->where('assigned_to', $user->pk_int_user_id);
                } else if ($roleId == User::USERS || $user->is_co_admin == 1) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('deal_tasks.vendor_id', $vendorId);
                }
            });

        if ($today <= $date_to) {
            $task_pending = (clone $all_tasks)->NonCampaign()->where('status', 0)
                ->where(function ($q) use ($request, $today, $date_from, $date_to) {
                    if ($date_from != null && $date_to != null) {
                        $q->whereDate('tasks.scheduled_date', '>=', $today)->whereDate('tasks.scheduled_date', '<=', $date_to);
                    }

                })->count();

            $dealtask_pending = (clone $deal_all_task)->where('status', 0)
                ->where(function ($q) use ($request, $today, $date_from, $date_to) {
                    if ($date_from != null && $date_to != null) {
                        $q->whereDate('deal_tasks.scheduled_date', '>=', $today)->whereDate('deal_tasks.scheduled_date', '<=', $date_to);
                    }
                })->count();
            $data['pending'] = $task_pending + $dealtask_pending;
        } else {
            $data['pending'] = 0;
        }

        $task_overdue = (clone $all_tasks)->NonCampaign()->where('status', 0)
            ->where(function ($q) use ($request, $today, $date_from, $date_to) {
                if ($date_from != null && $date_to != null) {
                    if ($today == $date_from) {
                        $to_date = Carbon::yesterday()->format('Y-m-d');
                        $q->whereDate('tasks.scheduled_date', '<=', $to_date);
                    } else if ($today <= $date_to) {
                        $to_date = Carbon::yesterday()->format('Y-m-d');
                        $q->whereDate('tasks.scheduled_date', '>=', $date_from)->whereDate('tasks.scheduled_date', '<=', $to_date);
                    } else {
                        $q->whereDate('tasks.scheduled_date', '>=', $date_from)->whereDate('tasks.scheduled_date', '<=', $date_to);
                    }
                }
            })->count();

        $deal_overdue = (clone $deal_all_task)->where('status', 0)
            ->where(function ($q) use ($request, $today, $date_from, $date_to) {
                if ($date_from != null && $date_to != null) {
                    if ($today == $date_from) {
                        $to_date = Carbon::yesterday()->format('Y-m-d');
                        $q->whereDate('deal_tasks.scheduled_date', '<=', $to_date);
                    } else if ($today <= $date_to) {
                        $to_date = Carbon::yesterday()->format('Y-m-d');
                        $q->whereDate('deal_tasks.scheduled_date', '>=', $date_from)->whereDate('deal_tasks.scheduled_date', '<=', $to_date);
                    } else {
                        $q->whereDate('deal_tasks.scheduled_date', '>=', $date_from)->whereDate('deal_tasks.scheduled_date', '<=', $date_to);
                    }
                }
            })->count();

        $data['overdue'] = $task_overdue + $deal_overdue;


        $tasks = (clone $all_tasks)->where(function ($q) use ($request, $date_from, $date_to) {
            if ($date_from != null && $date_to != null) {
                $q->whereDate('tasks.updated_at', '>=', $date_from)->whereDate('tasks.updated_at', '<=', $date_to);
            }
        });

        $call_report = (clone $tasks)->leftJoin('call_status', 'call_status.id', 'tasks.call_status_id')
            ->whereIn('call_status_id', $call_status_ids)
            ->select('call_status_id', DB::raw('count(*) as count'), 'call_status.name as call_status')
            ->where('tasks.status', 1)
            ->groupBy('call_status')
            ->get();


        $deal_task = (clone $deal_all_task)->where(function ($q) use ($request, $date_from, $date_to) {
            if ($date_from != null && $date_to != null) {
                $q->whereDate('deal_tasks.updated_at', '>=', $date_from)->whereDate('deal_tasks.updated_at', '<=', $date_to);
            }
        });
        $deal_call_report = (clone $deal_task)
            ->leftJoin('call_status', 'call_status.id', 'deal_tasks.call_status_id')
            ->whereIn('deal_tasks.call_status_id', $call_status_ids)
            ->select('call_status_id', DB::raw('count(*) as count'), 'call_status.name as call_status', 'deal_tasks.status')
            ->where('deal_tasks.status', 1)
            ->groupBy('call_status')
            ->get();

        $null_status_count = (clone $tasks)->where('tasks.status', 1)->where('call_status_id', null)->get()->count();
        $null_deal_status_count = (clone $deal_task)->where('deal_tasks.status', 1)->where('call_status_id', null)->get()->count();

        if ($null_status_count > 0) {
            $null_status = (object)array('call_status_id' => 0, 'count' => $null_status_count, 'call_status' => 'action pending');
            $data_call_report = $call_report->push($null_status);
        } else {
            $data_call_report = $call_report;
        }
        if ($null_deal_status_count > 0) {
            $null_deal_status = (object)array('call_status_id' => 0, 'count' => $null_deal_status_count, 'call_status' => 'action pending');
            $data_deal_call_report = $deal_call_report->push($null_deal_status);
        } else {
            $data_deal_call_report = $deal_call_report;
        }

        $all_report = collect($data_call_report)->merge($data_deal_call_report)->groupby('call_status_id');

        $i = $total = 0;
        $da = array();
        foreach ($all_report as $report) {
            $count = 0;
            if (count($report) > 1) {
                foreach ($report as $call) {
                    $count = $count + $call->count;
                }
                $da[$i]['call_status_id'] = $report[0]->call_status_id;
                $da[$i]['call_status'] = $report[0]->call_status;
                $da[$i]['count'] = $count;
                $i++;
                $total = $total + $count;
            } else {
                $da[$i]['call_status_id'] = $report[0]->call_status_id;
                $da[$i]['call_status'] = $report[0]->call_status;
                $da[$i]['count'] = $report[0]->count;
                $i++;
                $total = $total + $report[0]->count;
            }

        }

        $data['call_report'] = $da;
        // $data['completed_calls'] = $data['call_report']->sum('count');
        $data['completed_calls'] = $total;

        $enq_ids = $tasks->with(['enquiry' => function ($q) {
            $q->select('pk_int_enquiry_id');
        }])
            ->where('tasks.status', 1)
            ->get()
            ->map(function ($q) {
                return $q->enquiry ? ($q->enquiry['pk_int_enquiry_id'] != null ? $q->enquiry['pk_int_enquiry_id'] : 0) : 0;
            });

        $data['lead_status'] = Enquiry::whereIn('pk_int_enquiry_id', $enq_ids)
            ->leftJoin('tbl_feedback_status', 'tbl_feedback_status.pk_int_feedback_status_id', 'feedback_status')
            ->where('vchr_status', '!=', null)
            ->select(DB::raw('count(*) as count'), 'feedback_status', 'vchr_status', 'vchr_color')
            ->groupBy('feedback_status')
            ->get();
        return $this->response(200, false, null, $data);
    }


    public function syncCallLog()
    {
        $user = JWTAuth::parseToken()->toUser();
        $userId = $user->pk_int_user_id;
        $vendorId = $this->applicationObj->getVendorId($user->pk_int_user_id);
        if (request('logs')) {
            foreach (json_decode(request('logs'), true) as $logs) {
                $log = new CallLog;
                $log->user_id = $userId;
                $log->vendor_id = $vendorId;
                $log->caller_number = $logs['caller_number'];
                $log->called_number = $logs['called_number'];
                $log->customer_name = $logs['customer_name'] ?? null;
                $log->duration = $logs['duration'];
                $log->date = $logs['date'];
                $log->call_status = $logs['call_status'] ?? null;
                $log->call_direction = $logs['call_direction'] ?? null;
                $log->status = 1;
                $log->save();
            }
        } else {
            return response()->json([
                'status' => 0,
                'message' => 'Failed',
                'data' => 'Empty logs'
            ]);
        }
        return response()->json([
            'status' => 0,
            'message' => 'Success',
            'data' => 'Successfull'
        ]);
    }


}
