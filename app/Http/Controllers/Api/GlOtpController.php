<?php

namespace App\Http\Controllers\Api;

use App\BackendModel\EnquiryType;
use App\BackendModel\GlApiTokens;
use App\BackendModel\SenderId;
use App\BackendModel\SendSmsHistory;
use App\Common\SingleSMS;
use App\Common\SmsApi;
use App\Common\Variables;
use App\SmsPanel;
use App\Subscription\SmsCount;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Validator;
class GlOtpController extends Controller
{
    protected $singleSmsObj;
    protected $smsApiObj;

    public function __construct()
    {
        $this->singleSmsObj = new SingleSMS();
        $this->smsApiObj = new SmsApi();
    }

    public function pushOtp(Request $request)
    {
        $validator = validator::make($request->all(), [
            'username' => 'required',
            'token' => 'required',
            'to' => 'required',
            'sender' => 'required|max:6|regex:/^[a-zA-Z]+$/u|min:6',
            'otp' => 'required',
            'priority' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json(['status' => 'fail', 'message' => $validator->messages()]);
        }
        /*Validation :  User*/
        $check_token = GlApiTokens::where('vchr_token', $request->token)->first();
        if ($check_token) {
            $userDetails = User::where('vchr_user_mobile', $request->username)
                ->Orwhere('pk_int_user_id', $check_token->fk_int_user_id)
                ->where('email', $request->username)
                ->select('pk_int_user_id', 'vchr_user_name', 'email', 'vchr_user_mobile', 'countrycode', 'mobile', 'int_status')
                ->first();
            if ($userDetails) {
                if ($userDetails->int_status == User::DEACTIVATE) {
                    return response()->json(['status' => 'fail', 'message' => 'Invalid User.']);
                } else {
                    $vendorId = User::getUserVendorId($check_token->fk_int_user_id);
                }
            } else {
                return response()->json(['status' => 'fail', 'message' => 'User Not Found']);
            }
        } else {
            return response()->json(['status' => 'fail', 'message' => 'Invalid Token.']);
        }
        /*Validation :  Priority*/
        if ($request->priority != Variables::OTP_PRIORITY && $request->priority != 6) {
            return response()->json(['status' => 'fail', 'message' => 'Invalid Route Selected,Please use priority value 4 for OTP']);
        }
        /*validation : No count*/
        $explodeTo = explode(",", $request->to);
        $numbers = implode(",", $explodeTo);
        $countMobileno = count($explodeTo);
        if ($countMobileno > 1) {
            return response()->json(['status' => 'fail', 'message' => 'Sorry ! You can not send same OTP to multiple numbers']);
        }

        /*Validation :  SenderId*/
        $senderId = SenderId::where('fk_int_user_id', $vendorId)
            ->where('vchr_sender_id', $request->sender)
            ->select('pk_int_sender_id as id', 'vchr_sender_id as name', 'fk_int_user_id', 'int_sender_id_status')
            ->first();
        if ($senderId && ($senderId->int_sender_id_status == Variables::ACTIVE)) {
            $sender = $senderId->name;
        } else if ($request->sender === 'GTLEAD') {
            $sender = 'GTLEAD';
        } else {
            return response()->json(['status' => 'fail', 'message' => 'Invalid Sender Id.']);
        }
         $routeDetails = $this->smsApiObj->getRouteIdByPriority($request->priority);
        if (!$routeDetails) {
            return response()->json(['status' => 'fail', 'message' => 'Invalid Priority']);
        }
        $smsPanel = $this->smsApiObj->getActivePanelByRoute(($request->priority==6 ? 1 : $routeDetails->pk_int_sms_route_id), $vendorId);

        if (!$smsPanel) {
            return response()->json(['status' => 'Fail', 'message' => 'Something went wrong please contact your Admin.Thank you']);
        }
         $smsCount = SmsCount::where('vendor_id', $vendorId)
            ->where('route_id', ($request->priority==6 ? 1 : $routeDetails->pk_int_sms_route_id))
            ->select('id', 'vendor_id', 'route_id', 'total_count', 'used_sms_count', 'credit')
            ->first();
        if ($smsCount) {
            $userBalance = $smsCount->total_count + $smsCount->credit - $smsCount->used_sms_count;
        } else {
            return response()->json(['status' => 'Fail', 'message' => 'Insufficient Balance']);
        }
        if ($userBalance <= ($request->priority==6 ? 1 : 0) ) {
            return response()->json(['status' => 'Fail', 'message' => 'Low Balance']);
        }
        if($request->priority==6)
        {
            $gateway=1;
            $ch = curl_init("https://obd37.sarv.com/api/balance_check_api.php?myuid=r554&mytoken=7kxiNn");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); 
            $resp=curl_exec($ch);
            if(curl_error($ch)) {
                \Log::info('Call Panel Balance Fetch Failed: Switched to Gateway 2');
                // \Log::info(curl_error($ch));
                $gateway=2;
            }
            curl_close($ch);
           //$resp=file_get_contents("https://obd37.sarv.com/api/balance_check_api.php?myuid=r554&mytoken=7kxiNn");
           if(strpos($resp,"VOICE_CREDIT")!== false)
           {
           $start=strpos($resp,"VOICE_CREDIT")+14;
           $end = strpos($resp,"VOICE_CREDIT_VALIDITY")-$start;
           $smsPanelBalance = floatval(substr($resp,$start,$end));
            \Log::info('Call Panel Balance:'.$smsPanelBalance);
           }
           else
           {
            $smsPanelBalance = 500;
            \Log::info('Call Panel Balance Fetch Failed:'.$resp);
            //return response()->json(['status' => 'Fail', 'message' => 'Something went wrong please contact your administrator.Thank you']);
           }
            $now = \Carbon\Carbon::now();
            $start = \Carbon\Carbon::createFromTimeString('07:01');
            $end = \Carbon\Carbon::createFromTimeString('20:58');
            $use_gateway_2=true;
            if (!$now->between($start, $end)) {
                \Log::info('Switched to Gateway 2 due to Time Between 09:00 PM and 07:00 AM');
                $gateway=2;
            }
            if($use_gateway_2)
            {
                $smsPanelBalance = 500;
                \Log::info('Switched to Gateway 2 due to Set Default Gateway to 2');
                $gateway=2;
            }


        }
        else
        $smsPanelBalance = $this->smsApiObj->getPanelBalance($vendorId, $routeDetails->pk_int_sms_route_id, $smsPanel);
        if ($smsPanelBalance <= 0) {
            return response()->json(['status' => 'Fail', 'message' => 'Something went wrong please contact your administrator.Thank you']);
        }
        $messageType = 0;
        $shtime = "";
        $send = new SingleSMS();
        $routeCode = $request->priority;
         $routeName = $this->singleSmsObj->getRouteDetails(($request->priority==6 ? 1 : $routeDetails->pk_int_sms_route_id))->vchr_sms_route;
         if($request->priority==6)
         {
             $routeCode = 1;
             $smsUrl = "https://votp.getlead.co.uk?apiKey=dshfjk66668321&gateway=".$gateway."&otp=".$request->otp."&mobile_number=".$request->to;
             

             $countMobileno = 1;
             $messageType=99;
             $templateId = $send->getSmsTemplateId(1, $vendorId);
             $responses = $send->sendData($smsUrl);
             $messageCount = $send->getInputSMSCount($request->otp, 99);
             $insertMasterSms = $send->storeMasterSmsDataDeveloperApi($vendorId, 0, '', $request->sender, $messageType, $routeName, $request->otp, EnquiryType::API, $routeCode, $routeDetails->pk_int_sms_route_id, $countMobileno, $messageCount,true);
             $insertSms = $send->storeSmsDataDeveloperApi($vendorId, $templateId, $request->to, $request->sender, $messageType, $routeName, $request->otp, EnquiryType::API, $routeCode, $routeDetails->pk_int_sms_route_id, $countMobileno, $messageCount, $insertMasterSms, $responses, $shtime,true);   
             $resp=json_decode($responses,TRUE);
             if(isset($resp['status']) && $resp['status']=='success')
             {    
                 $history =  SendSmsHistory::where('pk_int_send_sms_history_id', $insertSms)->first();
                 $history->api_response = strip_tags($responses);
                 $history->vchr_status = Variables::SMS_STATUS_SENT;
                 $history->save();
                 $send->updateSMSCount($vendorId,  $routeCode, $messageCount);
                 $masterSmsDetails = $send->getMasterSmsDetails($insertMasterSms);
                 return response()->json(['gltrackid' => $insertMasterSms, 'mobilenocount' => $masterSmsDetails->mobileno_count, 'smscount' => $masterSmsDetails->credit_count, 'status' => 'success']);
             }
             else{
                //  \Log::info($resp);
                 $send->updateCreditCountDeveloperApi($insertMasterSms, '0');
                 $history =  SendSmsHistory::where('pk_int_send_sms_history_id', $insertSms)->first();
                 $history->api_response = strip_tags($responses);
                 $history->vchr_status = Variables::SMS_STATUS_FAIL;
                 $history->save();
                 return response()->json(['message' => 'Something Went wrong please try again', 'status' => 'fail']);
                 
             }
         }
         else{
            if($request->app_code)
            {
                $prepend = '< # > ';
                $append = ' '.$request->app_code;
            }else{
                $prepend = '';
                $append = '';
            }
            $comp = ($request->sender === 'GTLEAD' || $request->sender === 'GLDOTP') ? 'Getlead' : ($request->company !="" ? $request->company : 'Getlead');
            if ($request->sender === 'GTLEAD' && $smsPanel->title == SmsPanel::TEXT_LOCAL && $request->purpose == "" && $request->company =="") {
                
                //$message = $request->otp . ' is the SECRET One Time Password (OTP) to verify your phone number .';
                $message = $prepend.'Hi '.$request->otp . ' is the OTP for your request for number verification through '.$comp.'.'.$append;
            } else if($request->purpose != "" && $request->company !="") {
            //$message = 'Hi %%|name^{"inputtype" : "text", "maxlength" : "50"}%%,is the OTP for your request for %%|name2^{"inputtype" : "text", "maxlength" : "100"}%% through %%|name3^{"inputtype" : "text", "maxlength" : "500"}%%';
                $message = $prepend.'Hi '.$request->otp . ' is the OTP for your request for ' . $request->purpose . ' through '.$comp.'.'.$append;// . $request->company . '.'.$append;
            }else{
                $message = $prepend.$request->otp . ' is the SECRET One Time Password (OTP) to verify your phone number .'.$append;
            }
            $messageCount = $this->singleSmsObj->getInputSMSCount($message, $messageType);

            if ($smsPanel->title == SmsPanel::TEXT_LOCAL) {
                $textLocalCredentials = $this->singleSmsObj->getTextLocalCredentials($vendorId, $routeDetails->pk_int_sms_route_id, $smsPanel->id);
                $templateId = $textLocalCredentials->id;
                $apiKey = $textLocalCredentials->api_password;
                $data = $this->singleSmsObj->getTextLocalSmsUrl($sender, $message, $apiKey, $numbers);
                $getTextLocalSmsUrl = $smsPanel->domain . '/send?' . $data;

                $masterSmsId = $this->singleSmsObj->storeMasterSmsDataDeveloperApi($vendorId, $templateId, '', $request->sender, $messageType, $routeName, $message, EnquiryType::API, $routeCode, $routeDetails->pk_int_sms_route_id, $countMobileno, $messageCount);
                // \Log::info($getTextLocalSmsUrl);
                $smsResponse = $this->singleSmsObj->sendData($getTextLocalSmsUrl);
                $textLocalResponse = json_decode($smsResponse, true);

                foreach ($explodeTo as $i => $numb) {
                    $mobno = $numb;
                    $smsHistoryId = $this->singleSmsObj->storeSmsDataDeveloperApi($vendorId, $templateId, $mobno, $sender, $messageType, $routeName, $message, EnquiryType::API, $routeCode, $routeDetails->pk_int_sms_route_id, $countMobileno, $messageCount, $masterSmsId, $smsResponse, $shtime);
                    if ($textLocalResponse['status'] === "failure") {
                        $message = $textLocalResponse['errors'][0]['message'];
                        $this->singleSmsObj->updateTextLocalResponse($smsHistoryId, $smsResponse, Variables::SMS_STATUS_FAIL, $routeDetails->pk_int_sms_route_id);
                        return response()->json(['message' => $message, 'status' => 'fail']);
                    } elseif ($textLocalResponse['status'] === "success") {
                        $this->singleSmsObj->updateTextLocalResponse($smsHistoryId, $smsResponse, Variables::SMS_STATUS_DELIVERED, $routeDetails->pk_int_sms_route_id);
                    }
                }
                $masterSmsDetails = $this->singleSmsObj->getMasterSmsDetails($masterSmsId);
                return response()->json(['gltrackid' => $masterSmsId, 'mobilenocount' => $masterSmsDetails->mobileno_count, 'smscount' => $masterSmsDetails->credit_count, 'status' => 'success']);
            }else if($smsPanel->title == SmsPanel::ALERTBOX){

                $send = new SingleSMS();
                $userId = $userDetails->pk_int_user_id;
                $vendor_id = User::getVendorIdApi($userDetails->pk_int_user_id);

                $routeCode = $request->priority;
                //return User::getVendorIdApi($userId);
                                $smsUrl = $this->smsApiObj->getSmsUrlDeveloperApi($request->sender, $request->to, $message, $routeDetails->pk_int_sms_route_id, $routeCode, $vendor_id, $messageType, $shtime, $request->result_type);
                                $explodeTo = explode(",", $request->to);
                                $countMobileno = count($explodeTo);
                                $smsCount = $send->getInputSMSCount($message, $messageType);
                                $templateId = $send->getSmsTemplateId($routeDetails->pk_int_sms_route_id, $vendor_id);
                                $routeName = $send->getRouteDetails($routeDetails->pk_int_sms_route_id)->vchr_sms_route;
                                $responses = $send->sendData($smsUrl);
                                $messageCount = $send->getInputSMSCount($message, $messageType);
                                $insertMasterSms = $send->storeMasterSmsDataDeveloperApi($vendor_id, $templateId, '', $request->sender, $messageType, $routeName, $message, EnquiryType::API, $routeCode, $routeDetails->pk_int_sms_route_id, $countMobileno, $messageCount);
                                for ($i = 0; $i < $countMobileno; $i++) {
                                    $mobno = $explodeTo[$i];
                                    $insertSms = $send->storeSmsDataDeveloperApi($vendor_id, $templateId, $mobno, $request->sender, $messageType, $routeName, $message, EnquiryType::API, $routeCode, $routeDetails->pk_int_sms_route_id, $countMobileno, $messageCount, $insertMasterSms, $responses, $shtime);
                                    if (strip_tags($responses) == 'Sorry, No valid numbers found!  Trackid=Sorry, No valid numbers found!') {
                                        $send->updateCreditCountDeveloperApi($insertMasterSms, '0');
                                        return response()->json(['message' => 'No Valid Numbers Found', 'status' => 'fail']);
                                    } elseif (strip_tags($responses) == 'Sorry, Invalid Time!  Trackid=Sorry, Invalid Time!') {
                                        $send->updateCreditCountDeveloperApi($insertMasterSms, '0');
                                        return response()->json(['message' => 'Invalid Time', 'status' => 'fail']);
                                    } elseif (strip_tags($responses) == 'Sorry, Invalid Time!') {
                                        $send->updateCreditCountDeveloperApi($insertMasterSms, '0');
                                        return response()->json(['message' => 'Invalid Time', 'status' => 'fail']);
                                    } else {
                                        //get trackId and AlertId
                                        //return 1;
                                        if ($request->priority != 4) {
                                            if ($request->result_type != 2) {
                                                //alert_9967752232,alert_9967752233  Trackid=451531382
                                                $response1 = explode("=", $responses);
                                                $trackId = $response1[1];


                                                /**Get Message Id**/
                                                $response2 = explode(" ", $responses);
                                                $response3 = $response2[0];
                                                $response4 = explode(",", $response3);
                                                $response5 = $response4[$i];
                                                $response6 = explode("_", $response5);
                                                $messageId = $response6[1];
                                            } else {
                                                //return $responses;//alert_9967750930:919544799533,alert_9967750931:919497310242
                                                $response1 = explode(",", $responses);
                                                $response2 = explode(":", $response1[$i]);
                                                $trackId = $response2[1];
                                                $response3 = $response2[0];
                                                $response4 = explode("_", $response3);
                                                $messageId = $response4[1];

                                            }
                                            $response = $send->getResponseDeveloperApi($insertSms, $responses, $templateId, $routeDetails->pk_int_sms_route_id, $vendor_id, $smsCount, $trackId, $messageId);
                                        } else {
                                            //return 1;
                                            $resp1 = explode("=", $responses);
                                            $trackId = $resp1[1];

                                            /**Get Message Id**/
                                            $resp2 = explode(" ", $responses);    
                                            $resp3 = $resp2[0];
                                            $resp4 = explode(",", $resp3);
                                            $resp5 = $resp4[0];
                                            $resp6 = explode("_", $resp5);
                                            $messageId = $resp6[1];
                                            $response = $send->getResponseDeveloperApiOtp($insertSms, $responses, $templateId, $routeDetails->pk_int_sms_route_id, $vendor_id, $smsCount, $trackId, $messageId);
                                        }
                                    }
                                }
                                $masterSmsDetails = $send->getMasterSmsDetails($insertMasterSms);
                                return response()->json(['gltrackid' => $insertMasterSms, 'mobilenocount' => $masterSmsDetails->mobileno_count, 'smscount' => $masterSmsDetails->credit_count, 'status' => 'success']);      
            }else if ($smsPanel->title == SmsPanel::MERABT) {
                $routeCode = $routeDetails->short_code =='OTP' ? 'TL' : $routeDetails->short_code;
                //return User::getVendorIdApi($userId);
                $smsUrl = $send->getSmsMerabtDeveloperApiUrl($request->sender, $request->to, urlencode($message), $routeDetails->pk_int_sms_route_id, $routeCode, $vendorId, $messageType, $shtime);
                $explodeTo = explode(",", $request->to);
                $countMobileno = count($explodeTo);
                $smsCount = $send->getInputSMSCount($message, $messageType);
                $templateId = $send->getSmsTemplateId($routeDetails->pk_int_sms_route_id, $vendorId);
                $routeName = $send->getRouteDetails($routeDetails->pk_int_sms_route_id)->vchr_sms_route;
                $messageCount = $send->getInputSMSCount($message, $messageType);
                $insertMasterSms = $send->storeMasterSmsDataDeveloperApi($vendorId, $templateId, '', $request->sender, $messageType, $routeName, $message, EnquiryType::API, $routeCode, $routeDetails->pk_int_sms_route_id, $countMobileno, $messageCount);
                $responses = $send->sendDataPost($request->sender, $request->to, $message, $routeDetails->pk_int_sms_route_id, $routeCode, $vendorId, $messageType, $shtime);
                // \Log::info($smsUrl);
                
                for ($i = 0; $i < $countMobileno; $i++) {
                    $mobno = $explodeTo[$i];
                    $insertSms = $send->storeSmsDataDeveloperApi($vendorId, $templateId, $mobno, $request->sender, $messageType, $routeName, $message, EnquiryType::API, $routeCode, $routeDetails->pk_int_sms_route_id, $countMobileno, $messageCount, $insertMasterSms, $responses, $shtime);
                    $response = $send->getMetabtDeveloperApiResponse($insertSms, $responses, $templateId, $routeDetails->pk_int_sms_route_id, $vendorId, $smsCount, $i);
                }
                $masterSmsDetails = $send->getMasterSmsDetails($insertMasterSms);
                return response()->json(['gltrackid' => $insertMasterSms, 'mobilenocount' => $masterSmsDetails->mobileno_count, 'smscount' => $masterSmsDetails->credit_count, 'status' => 'success']);
               
            }else if ($smsPanel->title == SmsPanel::AWSSNS) {
                $routeCode = $routeDetails->short_code =='OTP' ? 'TL' : $routeDetails->short_code;
                //return User::getVendorIdApi($userId);
                //$smsUrl = $send->getSmsMerabtDeveloperApiUrl($request->sender, $request->to, urlencode($message), $routeDetails->pk_int_sms_route_id, $routeCode, $vendorId, $messageType, $shtime);
                $explodeTo = explode(",", $request->to);
                $countMobileno = count($explodeTo);
                $smsCount = 3;//$send->getInputSMSCount($message, $messageType);
                $templateId = $send->getSmsTemplateId($routeDetails->pk_int_sms_route_id, $vendorId);
                $routeName = $send->getRouteDetails($routeDetails->pk_int_sms_route_id)->vchr_sms_route;
                $messageCount = 3;//$send->getInputSMSCount($message, $messageType);
                $insertMasterSms = $send->storeMasterSmsDataDeveloperApi($vendorId, $templateId, '', $request->sender, $messageType, $routeName, $message, EnquiryType::API, $routeCode, $routeDetails->pk_int_sms_route_id, $countMobileno, $messageCount);
                //$responses = $send->sendDataPost($request->sender, $request->to, $message, $routeDetails->pk_int_sms_route_id, $routeCode, $vendorId, $messageType, $shtime);
                //\Log::info($smsUrl);
                
                $sns = new \Aws\Sns\SnsClient([
                    'version' => '2010-03-31',
                    'credentials' => new \Aws\Credentials\Credentials(
                        config('services.sns.key'),
                        config('services.sns.secret')
                    ),
                    'region' => config('services.sns.region'),
                ]);
                // $sns->SetSMSAttributes([
                //     'attributes' => [
                //         'DefaultSMSType' => 'Transactional',
                //     ],
                // ]);
                $length= strlen($request->to)-10;
                $result= $sns->publish([
                    'Message' => $message,
                    'PhoneNumber' => "+91".str_replace("+91","",substr($request->to,$length,10)),
                ]);
                $responses='{"route":"amazon","msgid":"'.$result->get('MessageId').'"}';
                for ($i = 0; $i < $countMobileno; $i++) {
                    $mobno = $explodeTo[$i];
                    $insertSms = $send->storeSmsDataDeveloperApi($vendorId, $templateId, $mobno, $request->sender, $messageType, $routeName, $message, EnquiryType::API, $routeCode, $routeDetails->pk_int_sms_route_id, $countMobileno, $messageCount, $insertMasterSms, $responses, $shtime);
                    $response = $send->getAwsSNSDeveloperApiResponse($insertSms, $responses, $templateId, $routeDetails->pk_int_sms_route_id, $vendorId, $smsCount, $i);
                }
                $masterSmsDetails = $send->getMasterSmsDetails($insertMasterSms);
                return response()->json(['gltrackid' => $insertMasterSms, 'mobilenocount' => $masterSmsDetails->mobileno_count, 'smscount' => $masterSmsDetails->credit_count, 'status' => 'success']);
               
            }
        }
    }
}
