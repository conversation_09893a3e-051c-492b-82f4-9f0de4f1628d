<?php

namespace App\Http\Controllers\GlCRM\Admin\Bulksms;

use App\Subscription\SmsCount;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DataTables;


class SettingsController extends Controller
{
    public function getUserSmsCreditsPage()
    {

        $smsCounts = SmsCount::join('tbl_users', 'vendor_id', '=', 'tbl_users.pk_int_user_id')
            ->join('tbl_sms_route', 'route_id', '=', 'tbl_sms_route.pk_int_sms_route_id')
            ->select('sms_counts.id', 'sms_counts.vendor_id', 'sms_counts.route_id', 'sms_counts.total_count', 'sms_counts.used_sms_count', 'tbl_users.vchr_user_name', 'tbl_sms_route.vchr_sms_route', 'tbl_users.customer_id')
            ->get();
        $result = array();
        foreach ($smsCounts as $element) {

            $sms_count['sms_route'] = $element['vchr_sms_route'];
            $sms_count['total_count'] = $element['total_count'];
            $sms_count['used_sms_count'] = $element['used_sms_count'];

            $result[$element['vendor_id']]['name'] = $element['vchr_user_name'];
            $result[$element['vendor_id']]['customer_id'] = $element['customer_id'];
            $result[$element['vendor_id']][] = $sms_count;
        }

        return view('gl-crm.admin-pages.settings.sms.users-sms-balance')->with('sms_counts', $result);
    }

}
