<?php

namespace App\Http\Controllers\GlCRM\User;

use App\Agency;
use App\AgentStaff;
use App\BackendModel\Country;
use App\BackendModel\District;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\Taluk;
use App\CallLog;
use App\CallMaster;
use App\CallStatus;
use App\CloudTelephonySetting;
use App\Common\Scopes\ApplyFilters;
use App\Common\Variables;
use App\DealTask;
use App\DealTaskHistory;
use App\Enquiry\Events\TaskDeleted;
use App\EnquiryFieldCustomise;
use App\Events\CreateFollowup;
use App\Events\DealTaskCompleted;
use App\Events\TaskAssigned;
use App\Events\TaskCompleted;
use App\Facades\AutomationFacade;
use App\FrontendModel\LeadAdditionalDetails;
use App\FrontendModel\LeadAdditionalField;
use App\Http\Controllers\Controller;
use App\Imports\ExcelImport;
use App\Ivr\Models\Ivr;
use App\Reason;
use App\Task;
use App\Task\Http\Controllers\Api\Filters\Tasks\AgentFilter;
use App\TaskCategory;
use App\TaskHistory;
use App\User;
use Carbon\Carbon;
use DB;
use Excel;
use Flash;
use Getlead\Campaign\Jobs\AttachLeadsToCampaign;
use Getlead\Campaign\Models\LeadCampaign;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\DataTables;
use Yajra\DataTables\EloquentDataTable;

class TaskController extends Controller
{
    public function getTaskDashboard()
    {

        if (auth()->user()->int_role_id == User::USERS || auth()->user()->is_co_admin == 1) {

            $tasks = Task::where('vendor_id', Auth::id())
                ->where('task_category_id', '!=', 2)
                ->selectRaw('COUNT(*) as total_count')
                ->selectRaw('SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as completed')
                ->selectRaw('SUM(CASE WHEN status = 0 AND scheduled_date > CURDATE() THEN 1 ELSE 0 END) as overDue')
                ->selectRaw('SUM(CASE WHEN status = 0 AND scheduled_date <= CURDATE() THEN 1 ELSE 0 END) as pending')
                ->first();

            $total_count = $tasks->total_count;
            $completed = $tasks->completed;
            $pending = $tasks->pending;
            $overDue = $tasks->overDue;
        } else {
            $assignedStaffIds = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)
                ->pluck('staff_id')
                ->toArray();

            $tasks = Task::where('task_category_id', '!=', 2)
                ->where(function ($query) use ($assignedStaffIds) {
                    $query->where('assigned_to', Auth::id())
                        ->orWhereIn('assigned_to', $assignedStaffIds);
                })
                ->selectRaw('COUNT(*) as total_count')
                ->selectRaw('SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as completed')
                ->selectRaw('SUM(CASE WHEN status = 0 AND scheduled_date > CURDATE() THEN 1 ELSE 0 END) as overDue')
                ->selectRaw('SUM(CASE WHEN status = 0 AND scheduled_date <= CURDATE() THEN 1 ELSE 0 END) as pending')
                ->first();

            $total_count = $tasks->total_count;
            $completed = $tasks->completed;
            $pending = $tasks->pending;
            $overDue = $tasks->overDue;
        }
        return view('gl-crm.pages.user.tasks.task-dashboard')
            ->with('total_tasks', $total_count)
            ->with('completed', $completed)
            ->with('pending', $pending)
            ->with('overdue', $overDue);
    }

    public function index()
    {
        $vendorId = User::getVendorId();
        if (auth()->user()->int_role_id == User::USERS || auth()->user()->is_co_admin == 1) {
            $tasks = Task::where('vendor_id', $vendorId)
                ->where('task_category_id', '!=', 2)
                ->selectRaw('COUNT(*) as total_count')
                ->selectRaw('SUM(CASE WHEN status = 1 OR scheduled_date is NULL THEN 1 ELSE 0 END) as completed')
                ->selectRaw('SUM(CASE WHEN status = 0 AND scheduled_date <= CURDATE() OR scheduled_date is NULL THEN 1 ELSE 0 END) as overDue')
                ->selectRaw('SUM(CASE WHEN status = 0 AND scheduled_date > CURDATE() THEN 1 ELSE 0 END) as pending')
                ->first();

            $total_count = $tasks->total_count;
            $completed = $tasks->completed;
            $pending = $tasks->pending;
            $overDue = $tasks->overDue;
        } else {

            $assignedStaffIds = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)
                ->pluck('staff_id')
                ->toArray();

            $tasks = Task::where('task_category_id', '!=', 2)
                ->where(function ($query) use ($assignedStaffIds) {
                    $query->where('assigned_to', Auth::id())
                        ->orWhereIn('assigned_to', $assignedStaffIds);
                });

            $total_count = $tasks->count();
            $completed = $tasks->where('status', 1)->count();
            $pending = $tasks->whereNull('status')->orWhere('status', 0)
                ->where(function ($query) use ($assignedStaffIds) {
                    $query->where('assigned_to', Auth::id())
                        ->orWhereIn('assigned_to', $assignedStaffIds);
                })
                ->where('task_category_id', '!=', 2)
                ->whereDate('scheduled_date', '>=', today())
                ->count();

            $overDue = $tasks->whereNull('status')->orWhere('status', 0)
                ->where(function ($query) use ($assignedStaffIds) {
                    $query->where('assigned_to', Auth::id())
                        ->orWhereIn('assigned_to', $assignedStaffIds);
                })
                ->where('task_category_id', '!=', 2)
                ->whereDate('scheduled_date', '<', today())
                ->count();
        }

        $agentsQuery = User::active()->where('parent_user_id', $vendorId);

        if (auth()->user()->int_role_id == User::USERS || auth()->user()->is_co_admin == 1) {
            $agentsQuery->orWhere('pk_int_user_id', $vendorId);
        }

        if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
            $assignedStaffIds = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)
                ->pluck('staff_id')
                ->push(Auth::user()->pk_int_user_id)
                ->toArray();

            $agentsQuery->whereIn('pk_int_user_id', $assignedStaffIds);
        }

        $agents = $agentsQuery->get();
        if ($vendorId == Variables::EZZAT_USER_ID && Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
            $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)
                ->pluck('staff_id')
                ->toArray();
            $staffQuery = User::select('pk_int_user_id as id', 'vchr_user_name as name')->where(function ($where) use ($assignedStafId) {
                $where->where('pk_int_user_id', Auth::user()->pk_int_user_id)->orWhereIn('pk_int_user_id', $assignedStafId);
            });
        } else {
            $staffQuery = User::StaffCheck()
                //   active()
                ->where('parent_user_id', $vendorId)->orWhere('pk_int_user_id', $vendorId)
                ->select('pk_int_user_id as id', 'vchr_user_name as name');
        }


        $staffId = $staffQuery->get();

        $assignedStaffQuery = User::where('pk_int_user_id', Auth::user()->pk_int_user_id)
            ->select('pk_int_user_id as id', 'vchr_user_name as name');

        $assigned_StaffId = $assignedStaffQuery->get();

        $assigned_by = User::active()->where('parent_user_id', $vendorId)
            ->orWhere('pk_int_user_id', $vendorId)
            ->select('pk_int_user_id', 'vchr_user_name')
            ->get();

        $categories = TaskCategory::where('vendor_id', $vendorId)
            ->orwhere('vendor_id', NULL)
            ->where('id', '!=', 2)
            ->select('id', 'name')
            ->get();
        $call_status = CallStatus::where('vendor_id', $vendorId)
            ->orWhere('vendor_id', NULL)
            ->select('id', 'name')
            ->get();
        $assign = User::where('pk_int_user_id', $vendorId)->first();

        $lead_status = DB::table('tbl_feedback_status')->where('fk_int_user_id', $vendorId)->pluck('vchr_status', 'pk_int_feedback_status_id')->toArray();
        $types = EnquiryType::where('int_status', '1')->UsedOnly($vendorId)->orWhere('vendor_id', $vendorId)
            ->get();
        $purposes = EnquiryPurpose::where('fk_int_user_id', $vendorId)->get();
        $enquiry_status = FeedbackStatus::UsedStatuses($vendorId)->get();
        $countrys = Country::All();

        $fields = EnquiryFieldCustomise::where('vendor_id', $vendorId)->first();
        if ($fields) {
            $active_fields = explode(',', $fields->hidden_fields);
            $required_fields = explode(',', $fields->required_fields);
        } else {
            $active_fields = null;
            $required_fields = null;
        }
        $countryCodeIp = "IN";
        $countryNewCheck = Country::where('country_code', $countryCodeIp)->first();
        if ($countryNewCheck) {
            $countryCode = $countryNewCheck->code;
        } else {
            $countryCode = '';
        }

        $district = District::where('vendor_id', $vendorId)
            ->where('status', 1)
            ->get();

        $taluk = Taluk::where('vendor_id', $vendorId)
            ->where('status', 1)
            ->get();
        $agencies = Agency::where('vendor_id', $vendorId)->select('id', 'name')->get();
        $date_by = collect();
        $date_by->push(['id' => 1, 'name' => 'Created']);
        $date_by->push(['id' => 2, 'name' => 'Updated']);
        $additional_fields = LeadAdditionalField::where('vendor_id', $vendorId)->get();
        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
            if ($additional_field->input_type == 3 || $additional_field->input_type == 5) {
                $date_by->push(['id' => $additional_field->id, 'name' => $additional_field->field_name]);
            }
        }
        return view('gl-crm/pages/user/tasks/index')->with('agents', $agents)
            ->with('total_tasks', $total_count)
            ->with('completed', $completed)
            ->with('pending', $pending)
            ->with('overdue', $overDue)
            ->with('call_status', $call_status)
            ->with('assign', $assign)
            ->with('categories', $categories)
            ->with('assigned_by', $assigned_by)
            ->with('types', $types)
            ->with('purposes', $purposes)
            ->with('enquiry_status', $enquiry_status)
            ->with('countrys', $countrys)
            ->with('staffId', $staffId)
            ->with('assigned_StaffId', $assigned_StaffId)
            ->with('active_fields', $active_fields)
            ->with('required_fields', $required_fields)
            ->with('countryCode', $countryCode)
            ->with('district', $district)
            ->with('taluk', $taluk)
            ->with('agencies', $agencies)
            ->with('additional_fields', $additional_fields)
            ->with('lead_status', $lead_status);
    }


    public function taskList(Request $request)
    {
        $vendorId = User::getVendorId();
        $categories = TaskCategory::where('vendor_id', $vendorId)
            ->orwhere('vendor_id', NULL)
            ->select('id', 'name')
            ->get();
        $call_status = CallStatus::where('vendor_id', $vendorId)
            ->orwhere('vendor_id', NULL)
            ->select('id', 'name')
            ->get();
        $selectColumns = [
            'tasks.id', 'tasks.name',
            'tasks.description', 'tasks.scheduled_date',
            'tasks.task_category_id', 'tasks.task_order',
            'tasks.assigned_to', 'tasks.assigned_by',
            'tasks.vendor_id', 'tasks.enquiry_id',
            'tasks.comment', 'tasks.status',
            'task_categories.name as task_category',
            'tbl_enquiries.vchr_customer_name as customer_name',
            'tbl_enquiries.vchr_customer_mobile',
            'tbl_users.vchr_user_name as assigned_user_name',
            't_user.vchr_user_name as assigned_by_user',
            'call_status.name as call_status',
            'is_archived'
        ];

        // dd($selectColumns);
        $tasksQ = Task::where('tasks.vendor_id', $vendorId)
            ->where('task_category_id', '!=', 2)
            ->join('task_categories', 'task_category_id', '=', 'task_categories.id')
            ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
            ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->leftjoin('tbl_users as t_user', 'assigned_by', '=', 't_user.pk_int_user_id')
            ->leftJoin('call_status', 'tasks.call_status_id', '=', 'call_status.id')
            //   ->where('tasks.is_archived', '!=', 1)
            ->orderBy('tasks.id', 'DESC')
            ->select($selectColumns);

        if ($request->has('assigned_by') && $request->filled('assigned_by')) {
            $tasksQ = $tasksQ->where('assigned_by', $request->assigned_by);
        }
        if ($request->has('category_id') && $request->filled('category_id')) {
            $tasksQ = $tasksQ->where('task_category_id', $request->category_id);
        }
        if ($request->has('assigned_to') && $request->filled('assigned_to')) {
            $tasksQ = $tasksQ->where('assigned_to', $request->assigned_to);
        }
        if ($request->has('status') && $request->filled('status')) {
            if ($request->status == 1) {
                $tasksQ = $tasksQ->where('status', 1);
            } elseif ($request->status == 2) {
                $tasksQ = $tasksQ->where(function ($q) {
                    $q->where('status', 0)
                        ->orWhere('status', null);
                })
                    ->where('scheduled_date', '<', Carbon::today());
            } else {
                $tasksQ = $tasksQ->where(function ($q) {
                    $q->where('status', 0)
                        ->orWhere('status', null);
                })
                    ->where('scheduled_date', '>=', Carbon::today());
            }
        }
        if ($request->has('created_at_from') && $request->has('created_at_to') && $request->filled('created_at_from') && $request->filled('created_at_to')) {
            $tasksQ = $tasksQ
                ->whereDate('tasks.created_at', '>', $request->created_at_from)
                ->whereDate('tasks.created_at', '<', $request->created_at_to);
        }
        if ($request->has('filter') && $request->filled('filter')) {
            if ($request->filter == 'completed') {
                $tasksQ = $tasksQ->where('status', 1);
            } else if ($request->filter == 'overdue') {
                $tasksQ = $tasksQ->where(function ($q) {
                    $q->where('status', 0)
                        ->orWhere('status', null);
                })->whereDate('scheduled_date', '<', Carbon::today());
            } else if ($request->filter == 'pending') {
                $tasksQ = $tasksQ->where(function ($q) {
                    $q->where('status', 0)
                        ->orWhere('status', null);
                });//->whereDate('scheduled_date', '>=', Carbon::today());
            }
        }
        if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
            $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->pluck('staff_id')->toArray();
            $tasksQ = $tasksQ->where(function ($where) use ($assignedStafId) {
                $where->where('assigned_to', Auth::user()->pk_int_user_id)->orWhereIn('assigned_to', $assignedStafId);
            });
        }
        if ($request->has('archived') && $request->filled('archived')) {
            $tasksQ = $tasksQ->where('is_archived', 1);
        } else {
            $tasksQ = $tasksQ->whereNull('is_archived');
        }
        $tasks = $tasksQ->paginate(6);
        //$tasks = $tasksQ->get();
        foreach ($tasks as $key => $row) {
            $row->scheduled_date = Carbon::parse($row->scheduled_date)->format('d M Y g:i A');


            if ($row->status == 1) {
                $row->task_status = "Completed";
                $row->flag = 1;
            } elseif ($row->scheduled_date < Carbon::today() && $row->status == 0) {
                $row->task_status = "OverDue";
                $row->flag = 3;
            } elseif ($row->scheduled_date >= Carbon::today() && $row->status == 0) {
                $row->task_status = "Pending";
                $row->flag = 2;
            }

            $row->slno = ++$key;
        }
        if ($request->query() && $request->filled('view') && $request->view == "tile") {
            $sortedarray = [];
            foreach ($tasks as $task) {
                $groupid = $task['flag'];
                if (isset($sortedarray[$groupid])) {
                    $sortedarray[$groupid][] = $task;
                } else {
                    $sortedarray[$groupid] = array($task);
                }
            }
            $tasks = $sortedarray;
            return view('gl-crm.pages.user.tasks.tasks', compact('tasks', 'categories', 'call_status'));
        } else {
            return view('gl-crm.pages.user.tasks.tasks-list', compact('tasks', 'categories', 'call_status'));
        }

    }

    public function getTasks(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();
        $date = explode(' - ', $request->all_date);
        $from_date = $date[0];
        $to_date = $date[1];
        $searchValue = request()->search['value'];
        $selectColumns = [
            'tasks.id', 'tasks.name',
            'tasks.description', 'tasks.scheduled_date',
            'tasks.task_category_id', 'tasks.task_order',
            'tasks.assigned_to', 'tasks.assigned_by',
            'tasks.vendor_id', 'tasks.enquiry_id',
            'tasks.comment', 'tasks.status',
            'task_categories.name as task_category',
            'tbl_enquiries.vchr_customer_name as customer_name',
            'tbl_enquiries.vchr_customer_mobile as mobile',
            'tbl_users.vchr_user_name as assigned_user_name',
            't_user.vchr_user_name as assigned_by_user',
            'call_status.name as call_status'
        ];

        $tasksQ = Task::where('tasks.vendor_id', User::getVendorId())
            ->where('task_category_id', '!=', 2)
            ->where(function ($q) use ($request) {
                $request->lead_status_id ? $q->whereHas('enquiry', function ($q) use ($request) {
                    $q->where('feedback_status', $request->lead_status_id);
                }) : '';
            })
            ->where(function ($q) use ($request) {
                $request->lead_source ? $q->whereHas('enquiry', function ($q) use ($request) {
                    $q->where('fk_int_enquiry_type_id', $request->lead_source);
                }) : '';
            })
            ->join('task_categories', 'task_category_id', '=', 'task_categories.id')
            ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
            ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->leftjoin('tbl_users as t_user', 'assigned_by', '=', 't_user.pk_int_user_id')
            ->leftJoin('call_status', 'tasks.call_status_id', '=', 'call_status.id')
            ->select($selectColumns)
            ->selectRaw("
                CASE
                    WHEN status = 1 THEN 'Completed'
                    WHEN scheduled_date < '" . Carbon::today()->toDateString() . "' AND status = 0 THEN 'OverDue'
                    WHEN scheduled_date >= '" . Carbon::today()->toDateString() . "' AND status = 0 THEN 'Pending'
                    ELSE 'Unknown'
                END AS task_status"
            )
            ->when($request->has('assigned_by') && $request->filled('assigned_by'), function ($builder) use ($request) {
                $builder->where('assigned_by', $request->assigned_by);
            })
            ->when($request->has('category_id') && $request->filled('category_id'), function ($builder) use ($request) {
                $builder->where('task_category_id', $request->category_id);
            })
            ->when($request->has('assigned_to') && $request->filled('assigned_to'), function ($builder) use ($request) {
                $builder->where('assigned_to', $request->assigned_to);
            })
            ->when($request->has('status') && $request->filled('status'), function ($builder) use ($request) {
                if ($request->status == 1) {
                    $builder->where('status', 1);
                } elseif ($request->status == 2) {
                    $builder->where(function ($queryBuilder) {
                        $queryBuilder->where('status', 0)
                            ->orWhereNull('status');
                    })->where('scheduled_date', '<', Carbon::today());
                } else {
                    $builder->where(function ($queryBuilder) {
                        $queryBuilder->where('status', 0)
                            ->orWhereNull('status');
                    })->where('scheduled_date', '>=', Carbon::today());
                }
            })
            ->when($request->has('filter') && $request->filled('filter'), function (Builder $builder) use ($request) {
                if ($request->filter == 'completed') {
                    $builder->where('status', 1);
                } else if ($request->filter == 'overdue') {
                    $builder->where(function ($queryBuilder) {
                        $queryBuilder->where('status', 0)
                            ->orWhereNull('status');
                    })->whereDate('scheduled_date', '<', Carbon::today());
                } else if ($request->filter == 'pending') {
                    $builder->where(function ($queryBuilder) {
                        $queryBuilder->where('status', 0)
                            ->orWhereNull('status');
                    })->whereDate('scheduled_date', '>=', Carbon::today());
                }
            })
            ->tap(new ApplyFilters([
                new AgentFilter($user)
            ]))
            ->orderBy('tasks.id', 'DESC');

        if ($request->date_type == 1) {
            if ($request->has('all_date') && $request->filled('all_date')) {
                $tasksQ = $tasksQ->where(function ($q) use ($from_date, $to_date) {
                    $q->whereDate('tasks.created_at', '>=', $from_date)
                        ->whereDate('tasks.created_at', '<=', $to_date);
                });
            }
        } else if ($request->date_type == 2) {
            if ($request->has('all_date') && $request->filled('all_date')) {
                $tasksQ = $tasksQ->where(function ($q) use ($from_date, $to_date) {
                    $q->whereDate('tasks.scheduled_date', '>=', $from_date)
                        ->whereDate('tasks.scheduled_date', '<=', $to_date);
                });
            }
        } else {
            if ($request->has('all_date') && $request->filled('all_date')) {
                $tasksQ = $tasksQ->where(function ($q) use ($from_date, $to_date) {
                    $q->whereDate('tasks.created_at', '>=', $from_date)
                        ->whereDate('tasks.created_at', '<=', $to_date);
                });
            }
        }

        return (new EloquentDataTable($tasksQ))
            ->addIndexColumn()
            ->filterColumn('DT_RowIndex', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('task_category', function ($query, $keyword) {
                // Prevent task_category from being used in search queries
            })
            ->filterColumn('assigned_by_user', function ($query, $keyword) {
                // Prevent task_category from being used in search queries
            })
            ->filterColumn('assigned_user_name', function ($query, $keyword) {
                // Prevent task_category from being used in search queries
            })
            ->filterColumn('task_status', function ($query, $keyword) {
                // Prevent task_category from being used in search queries
            })
            ->addColumn('assign_agent', function ($row) {
                return ' <div class="form-check form-check-assign" >
                  <input id="checkbox2" type="checkbox" data-enq_id="' . $row->id . '">
                    </div>';
            })
            ->addColumn('lead_info', function ($row) {
                if ($row->customer_name == NULL) {
                    return '<span style="display:block;width:100px;word-wrap:break-word;white-space: normal;"><a class="ks-izi-modal-trigger3" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' . $row->enquiry_id . '">' . ($row->vchr_customer_mobile ?? 'No name') . '</a></span>';
                } else {
                    return '<span style="display:block;width:100px;word-wrap:break-word;white-space: normal;"><a class="ks-izi-modal-trigger3" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' . $row->enquiry_id . '">' . $row->customer_name . '</a></span>';
                }
            })
            ->addColumn('selete_task', function ($row) {
                return '<div class="form-check form-check-assign" >
                       <input id="checkbox2" type="checkbox" name="seleteTask" value="' . $row->id . '" data-task_id="' . $row->id . '">
                   </div>';
            })
            ->editColumn('description', function ($row) {
                return '<p>' . $row->description . '</p>';
            })
            ->editColumn('assigned_by', function ($row) {
                if ($row->assigned_by == Auth::user()->pk_int_user_id) {
                    return "You";
                } else {
                    return $row->assigned_by_user;
                }
            })
            ->editColumn('scheduled_date', function ($row) {
                if ($row->scheduled_date != '0000-00-00 00:00:00')
                    return Carbon::parse($row->scheduled_date)->format('d M Y h:i A');
                else
                    return null;
            })
            ->addColumn('show', function ($row) {
                return
                    '<div class="dropdown show">
                        <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>
                            <i class="fa fa-circle" aria-hidden="true"></i>   
                        </a>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                            <a class="dropdown-item task_edit" href="#" data-task_id="' . $row->id . '">
                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32"><path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4l15-15zm-5-5L24 7.6l-3 3L17.4 7l3-3zM6 22v-3.6l10-10l3.6 3.6l-10 10H6z"/></svg>
                                &nbsp;Edit
                            </a>
                            <a href="#" class="dropdown-item task-delete" id="delete_plan" task-id="' . $row->id . '">
                                <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete
                            </a>
                        </div>
                    </div>';
            })
            ->rawColumns(['selete_task', 'show', 'lead_info', 'description'])
            ->toJson();
    }

    public function show($id)
    {
        $selectColumns = [
            'tasks.id', 'tasks.name',
            'tasks.description', 'tasks.scheduled_date',
            'tasks.task_category_id', 'tasks.task_order',
            'tasks.assigned_to', 'tasks.assigned_by',
            'tasks.vendor_id', 'tasks.enquiry_id',
            'tasks.comment', 'tasks.status', 'tasks.created_at',
            'task_categories.name as task_category',
            'tbl_enquiries.vchr_customer_name as customer_name',
            'tbl_enquiries.vchr_customer_mobile',
            'tbl_users.vchr_user_name as assigned_user_name',
            'call_status_id'];
        $task = Task::query()
            ->where('tasks.vendor_id', User::getVendorId())
            ->where('tasks.id', $id)
            ->with(['creator', 'history', 'history.creator'])
            ->join('task_categories', 'task_category_id', '=', 'task_categories.id')
            ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
            ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->select($selectColumns)
            ->first();
        if ($task instanceof Task) {
            if ($task->scheduled_date != NULL) {
                $task->time_field = $task->scheduled_date->format('g:i');
                $task->scheduled_date_edit = $task->scheduled_date->format('Y-m-d');
            }
            return response()->json(['msg' => "", 'status' => 'success', 'data' => $task]);
        }
        return response()->json(['msg' => "Not Found", 'status' => 'fail']);
    }

    public function update(Request $request, $id)
    {
        $validate_fields = [
            'name' => ['required'],
            'description' => ['required'],
            'task_category_id' => ['required'],
            'assigned_to' => ['numeric']
        ];
        $validate_messages = [
            'name.required' => ' name Required',
            'description.required' => 'Description Required',
            'task_category_id.required' => 'Task category Required',
        ];

        try {
            DB::beginTransaction();
            $task = Task::find($id);
            $vendorId = User::getVendorId();
            if ($task) {
                $input = [
                    'name' => $request->name,
                    'description' => $request->description,
                    'scheduled_date' => Carbon::parse($request->scheduled_date)->format('Y-m-d') . " " . $request->task_time,
                    'task_category_id' => $request->task_category_id ?? 2,
                    'task_order' => $request->task_order,
                    'assigned_to' => $request->assigned_to ?? Auth::id(),
                    'assigned_by' => Auth::id(),
                    'vendor_id' => $vendorId,
                    'comment' => $request->comment,
                ];

                if ($request->status_id != null && $task->status != $request->status_id) {
                    $input['status'] = $request->status_id;
                    TaskHistory::create(['task_id' => $id,
                        'task_status' => $request->status_id == 1 ? 'Feedback Submitted' : 'Reopened',
                        'updated_by' => Auth::id(),
                        'vendor_id' => $vendorId,
                        'comment' => $request->comment,
                        'date' => Carbon::today()
                    ]);
                } else {
                    TaskHistory::create(['task_id' => $id,
                        'task_status' => 'Task Updated',
                        'updated_by' => Auth::id(),
                        'vendor_id' => $vendorId,
                        'comment' => $request->comment ? $request->comment : $request->description,
                        'date' => Carbon::today()
                    ]);
                }

                Task::where('id', $id)->update($input);

            } else {
                return $this->response(200, true, 'fail', null);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::info("Error Occurred While Updating Task through WEB:" . $exception->getMessage());
            return response()->json(['msg' => $exception->getMessage(), 'status' => 'fail']);
        }
        DB::commit();
        return response()->json(['msg' => "Task Updated Successfully", 'status' => 'success']);

    }

    public function importTasks()
    {
        return view('gl-crm/pages/user/tasks/import');
    }

    public function importTasksData(Request $request)
    {
        try {
            $mimes = array('application/vnd.ms-excel', 'text/plain', 'text/csv', 'text/tsv');
            if (!(in_array($_FILES['file']['type'], $mimes))) {
                Flash::error('Validation Error : Unsupported  file');
                return back();
            }
            $path = $request->file('file')->getRealPath();
            // $data1 = Excel::load($path)->get();

            $import = new ExcelImport();
            Excel::import($import, $request->file('file'));
            $data1 = $import->jsonData;
            // dd($data1);
            $vendorId = User::getVendorId();
            if ($import->count) {
                //Validation
                $data = json_decode($data1);
                $data2 = json_decode($data1, true);
                $errors = [];
                foreach ($data as $i => $dat) {
                    $enquiry = null;
                    if ($dat->country_code && $dat->mobile_number)
                        $enquiry = Enquiry::where('vchr_customer_mobile', $dat->country_code . $dat->mobile_number)
                            ->where('fk_int_user_id', $vendorId)->first();

                    $fields = [
                        'mobile_number' => 'required|numeric',
                        'country_code' => 'required|numeric',
                        'task_title' => 'required',
                        'task_category' => 'required'
                    ];
                    if (!$enquiry && $dat->country_code && $dat->mobile_number)
                        $fields['lead_source'] = 'required';

                    if ($data[$i]->email_address != '') {
                        $fields['email_address'] = 'email';
                    }
                    if ($data[$i]->scheduled_date != '')
                        $fields['scheduled_date'] = 'date|date_multi_format:"d-m-Y","Y-m-d"';
                    $validation = Validator::make((array)$data[$i], $fields, []);
                    if ($validation->fails()) {
                        array_push($errors, ['error' => $validation->errors(), 'data' => $data[$i]]);
                    }
                }

                if (count($errors) > 0) {
                    return view('gl-crm.pages.user.tasks.import_list', compact('errors'));
                }
                $additional_fields = LeadAdditionalField::where('vendor_id', $vendorId)->get();
                DB::beginTransaction();
                foreach ($data2 as $key => $value) {
                    if (isset($value['assigned_to'])) {
                        $ass_by = User::where('parent_user_id', $vendorId)->where('vchr_user_name', $value['assigned_to'])->first();
                        if ($ass_by) {
                            $ass_to = $ass_by->pk_int_user_id;
                        } else {
                            $ass_to = Auth::user()->pk_int_user_id;
                        }
                    } else {
                        $ass_to = Auth::user()->pk_int_user_id;
                    }
                    $enquiry_id = NULL;
                    if (isset($value['enquiry_id'])) {
                        $enquiry_id = $value['enquiry_id'];
                    } else {
                        $enquiry = Enquiry::where('vchr_customer_mobile', $value['country_code'] . $value['mobile_number'])
                            ->where('fk_int_user_id', $vendorId)->first();
                        if ($enquiry) {
                            $enquiry_id = $enquiry->pk_int_enquiry_id;
                            $enquiry->updated_at = now();
                            $enquiry->save();
                        } else {
                            $enquiry_type = EnquiryType::where('vchr_enquiry_type', isset($value['lead_source']) ? $value['lead_source'] : $value['enquiry_type'])->where(function ($where) use ($vendorId) {
                                $where->where('fk_int_user_id', $vendorId)->orWhere('fk_int_user_id', 0);
                            })->first();
                            if ($enquiry_type) {
                                $fk_int_enquiry_type_id = $enquiry_type->pk_int_enquiry_type_id;
                            } else {
                                $enquiry_type = new EnquiryType();
                                $enquiry_type->vchr_enquiry_type = isset($value['lead_source']) ? $value['lead_source'] : $value->enquiry_type;
                                $enquiry_type->int_status = Variables::ACTIVE;
                                $enquiry_type->fk_int_user_id = $vendorId;
                                $enquiry_type->vendor_id = $vendorId;
                                $enquiry_type->save();
                                $fk_int_enquiry_type_id = $enquiry_type->pk_int_enquiry_type_id;

                            }
                            $feedback_status = FeedbackStatus::where('vchr_status', 'New')->where(function ($where) use ($vendorId) {
                                $where->where('fk_int_user_id', $vendorId);
                            })->first();
                            if ($feedback_status) {
                                $fk_int_feedback_status_id = $feedback_status->pk_int_feedback_status_id;
                            } else {
                                $feedback_status = new FeedbackStatus();
                                $feedback_status->vchr_status = 'New';
                                $feedback_status->vchr_color = '#000000';
                                $feedback_status->fk_int_user_id = $vendorId;
                                $feedback_status->created_by = auth()->user()->pk_int_user_id;
                                $feedback_status->save();
                                $fk_int_feedback_status_id = $feedback_status->pk_int_feedback_status_id;
                            }
                            $enquiry = [
                                'vchr_customer_name' => $value['name'],
                                'vchr_customer_mobile' => $value['country_code'] . $value['mobile_number'],
                                'mobile_no' => $value['mobile_number'],
                                'country_code' => $value['country_code'],
                                'fk_int_enquiry_type_id' => $fk_int_enquiry_type_id,
                                'fk_int_user_id' => $vendorId,
                                'landline_number' => isset($value['alternate_number1']) ? $value['country_code'] . $value['alternate_number1'] : '',
                                'more_phone_numbers' => isset($value['alternate_number2']) ? $value['alternate_number2'] : '',
                                'vchr_customer_email' => isset($value['email_address']) ? $value['email_address'] : '',
                                'address' => isset($value['address']) ? $value['address'] : '',
                                'new_status' => Enquiry::SHOW,
                                'feedback_status' => $fk_int_feedback_status_id,
                                'staff_id' => $ass_to,
                                'created_at' => isset($value['date']) ? (strlen($value['date']) > 10 ? Carbon::parse(str_replace("/", "-", $value['date']))->format('Y-m-d H:i:s') : Carbon::parse(str_replace("/", "-", $value['date']) . " 00:00:00")->format('Y-m-d H:i:s')) : Carbon::now()->format('Y-m-d H:i:s'),
                                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
                                'vchr_customer_company_name' => isset($value['company_name']) ? $value['company_name'] : '',
                            ];
                            $enquiry = Enquiry::create($enquiry);
                            try {
                                AutomationFacade::newLeadFunctions($enquiry->pk_int_enquiry_id);
                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                            }
                            $enquiry_id = $enquiry->pk_int_enquiry_id;
                            if ($enquiry && $fk_int_feedback_status_id) {
                                $enquiryfollowups = new EnquiryFollowup();
                                $enquiryfollowups->note = $fk_int_feedback_status_id;
                                $enquiryfollowups->log_type = EnquiryFollowup::TYPE_STATUS;
                                $enquiryfollowups->enquiry_id = $enquiry_id;
                                $enquiryfollowups->created_by = Auth::user()->pk_int_user_id;
                                $enquiryfollowups->save();
                            }
                            foreach ($additional_fields as $key => $additional_field) {
                                if (isset($value[strtolower(str_replace(' ', '_', $additional_field->field_name))]) && $additional_field->input_type != 6) {
                                    $additionalDetails = new LeadAdditionalDetails();
                                    $additionalDetails->enquiry_id = $enquiry_id;
                                    $additionalDetails->field_id = $additional_field->id;
                                    $additionalDetails->field_name = $additional_field->field_name;
                                    $additionalDetails->value = $value[strtolower(str_replace(' ', '_', $additional_field->field_name))];
                                    $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                                    $additionalDetails->save();
                                }
                            }
                        }
                    }

                    if (isset($value['task_category'])) {
                        $task_category_id = null;
                        switch (strtolower($value['task_category'])) {
                            case 'call':
                                $task_category_id = 2;
                                break;
                            case 'meeting':
                                $task_category_id = 22;
                                break;
                            case 'sales':
                                $task_category_id = 1;
                                break;

                            default:
                                # code...
                                break;
                        }

                        $cate = TaskCategory::whereRaw('LOWER(`name`) = ? ', [trim(strtolower($value['task_category']))])
                            ->where('vendor_id', $vendorId)
                            ->first();
                        if ($cate)
                            $task_category_id = $cate->id;

                        if (!$task_category_id) {
                            $input = [];
                            $input['created_by'] = Auth::user()->pk_int_user_id;
                            $input['vendor_id'] = $vendorId;
                            $input['name'] = $value['task_category'];
                            $task_category = TaskCategory::create($input);
                            $task_category_id = $task_category->id;
                        }

                    } else {
                        $task_category_id = NULL;
                    }

                    if (isset($value['scheduled_date'])) {
                        $sh_date = Carbon::parse($value['scheduled_date'])->format('Y-m-d');
                    } else {
                        $sh_date = Carbon::today();
                    }


                    $input = [
                        'name' => $value['task_title'],
                        'description' => isset($value['task_description']) ? $value['task_description'] : '',
                        'status' => 0,
                        'scheduled_date' => $sh_date ?? now(),
                        'assigned_by' => Auth::user()->pk_int_user_id,
                        'assigned_to' => $ass_to,
                        'task_category_id' => $task_category_id,
                        'vendor_id' => $vendorId,
                        'enquiry_id' => $enquiry_id,
                        'task_order' => $value['task_order'],
                    ];
                    if ($enquiry_id != NULL) {
                        $existing_task = Task::callTaskClose($enquiry_id, $vendorId, Auth::user()->pk_int_user_id);
                        if ($existing_task == null) {
                            Task::create($input);
                        }
                    }
                }
            }
            Flash::success('Insert Record successfully');
            DB::commit();
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            // Log::info($e->getTraceAsString());
            DB::rollback();
            Flash::error('Failed to Import Tasks');

        }
        return back();

    }

    public function deleteMultipTasks(Request $request): JsonResponse
    {
        /**
         * @var User $user
         */
        $user = auth()->user();
        $vendorId = $user->getBusinessId();

        $taskIds = $request->string('task_ids')->explode(',');

        Task::query()
            ->whereIn('id', $taskIds)
            ->where('vendor_id', $vendorId)
            ->delete();

        foreach ($taskIds as $taskId) {
            Event::dispatch(new TaskDeleted(taskId: $taskId, deletedBy: $user->pk_int_user_id));
        }

        return response()->json(['msg' => "Tasks deleted.", 'status' => 'success']);
    }

    public function completeMultipTasks(Request $request, $id = null)
    {
        try {
            $userId = User::getVendorId();
            if ($id)
                $task_ids = array($id);
            else
                $task_ids = explode(',', $request->ptask_ids);

            $pending_task_ids = Task::whereIn('id', $task_ids)->where('vendor_id', $userId)->select("id", "enquiry_id")->get();

            foreach ($pending_task_ids as $pending_task_id) {
                Task::where('id', $pending_task_id->id)->update(['status' => 1]);
                TaskHistory::create(['task_id' => $pending_task_id->id,
                    'task_status' => 'Completed',
                    'updated_by' => Auth::id(),
                    'vendor_id' => $userId,
                    'comment' => "Task Completed",
                    'date' => Carbon::today()
                ]);
                Enquiry::where('pk_int_enquiry_id', $pending_task_id->enquiry_id)->update(['updated_at' => now()]);
                if ($pending_task_id->enquiry_id) {
                    event(new CreateFollowup('Task Completed', EnquiryFollowup::TASK_COMPLETED, $pending_task_id->enquiry_id, Auth::id(), null, null, null, null, null, $pending_task_id->id));

                }
                $task = Task::find($pending_task_id->id);
                if ($task->assigned_by != Auth::id()) {
                    event(new TaskCompleted($task, Auth::id()));
                }

            }
            return response()->json(['msg' => "Tasks updated.", 'status' => 'success']);

        } catch (\Exception $e) {

            return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
        }
    }

    public function deleteTask(Request $request): JsonResponse
    {
        /**
         * @var User $user
         */
        $user = auth()->user();
        $vendorId = $user->getBusinessId();

        $taskId = (int)$request->route('id');

        $isDeleted = Task::query()
            ->where('id', $taskId)
            ->where('vendor_id', $vendorId)
            ->delete();

        if (!$isDeleted) {
            return response()->json(['msg' => 'Failed to delete', 'status' => 'fail']);
        }

        Event::dispatch(new TaskDeleted(taskId: $taskId, deletedBy: $user->pk_int_user_id));
        return response()->json(['msg' => "Tasks deleted.", 'status' => 'success']);
    }

    public function createCallTaskMultipleLeads(Request $request)
    {
        $vendorId = User::getVendorId();
        if (request('leads_ids_call_task') == null)
            return response()->json(['msg' => 'No lead selected', 'status' => 'fail']);

        try {
            $msg = 'Already have a task';
            foreach (explode(',', $request->leads_ids_call_task) as $id) {

                $enquiry = Enquiry::where('pk_int_enquiry_id', $id)->where('fk_int_user_id', $vendorId)->first();
                if ($enquiry) {
                    //Task Creation
                    $userId = request('agent_id') ?? $enquiry->staff_id;
                    $existing_task = Task::callTaskClose($enquiry->pk_int_enquiry_id, $vendorId, auth::user()->pk_int_user_id);
                    if ($existing_task == null) {
                        $task = [
                            'name' => 'Call Back ' . $enquiry->vchr_customer_name,
                            'description' => 'Created through Leads List',
                            'scheduled_date' => Carbon::now()->addMinutes(1)->format('Y-m-d H:i:s'),
                            'task_category_id' => 2,
                            'assigned_to' => $userId,
                            'assigned_by' => auth()->user()->pk_int_user_id,
                            'vendor_id' => $vendorId,
                            'enquiry_id' => $enquiry->pk_int_enquiry_id,
                            'comment' => '',
                            'status' => 0,
                        ];
                        Task::create($task);
                        $msg = 'Task Created.';
                        $enquiry->updated_at = now();
                        $enquiry->save();
                    }
                    //
                }
            }
            return response()->json(['msg' => $msg, 'status' => 'success']);

        } catch (\Exception $e) {

            return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
        }
    }

    public function callTasksPage()
    {
        $vendorId = User::getVendorId();
        $users = User::StaffCheck()
            // withTrashed()
            ->whereNotIn('pk_int_user_id', ['3642', '4015'])
            ->where(function ($q) use ($vendorId) {
                $q->where('parent_user_id', $vendorId);
                if (auth()->user()->int_role_id == User::USERS || auth()->user()->is_co_admin == 1)
                    $q->orWhere('pk_int_user_id', $vendorId);
            })
            ->select('pk_int_user_id as id', 'vchr_user_name as name')->get();
        $agents = User::StaffCheck()
            // withTrashed()
            ->whereNotIn('pk_int_user_id', ['3642', '4015'])
            ->where(function ($q) use ($vendorId) {
                $q->where('parent_user_id', $vendorId);
                if (auth()->user()->int_role_id == User::USERS || auth()->user()->is_co_admin == 1)
                    $q->orWhere('pk_int_user_id', $vendorId);
            })
            ->select('pk_int_user_id as id', 'vchr_user_name as name');
        if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
            $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->pluck('staff_id')->toArray();
            $agents = $agents->where(function ($where) use ($assignedStafId) {
                $where->where('pk_int_user_id', Auth::user()->pk_int_user_id)->orWhereIn('pk_int_user_id', $assignedStafId);
            });
        }
        $agents = $agents->get();
        $feedback_status = FeedbackStatus::where('fk_int_user_id', $vendorId)->select('pk_int_feedback_status_id', 'vchr_status')->get();
        $call_status = CallStatus::where('vendor_id', $vendorId)->select('id', 'name')->get();
        $reasons = Reason::where('vendor_id', $vendorId)->select('id', 'name')->get();
        $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->pluck('staff_id')->toArray();
        $total_calls = Task::NonCampaign()->where('tasks.vendor_id', $vendorId)->where('task_category_id', 2)->where(function ($where) use ($assignedStafId) {
            if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
                $where->where(function ($where2) use ($assignedStafId) {
                    $where2->where('assigned_to', Auth::user()->pk_int_user_id)->orWhereIn('assigned_to', $assignedStafId);
                });
            }
        })->count();
        $types = EnquiryType::where('int_status', '1')->UsedOnly($vendorId)->orWhere('vendor_id', $vendorId)->get();
        $fields = EnquiryFieldCustomise::where('vendor_id', $vendorId)->first();
        if ($fields) {
            $active_fields = explode(',', $fields->hidden_fields);
            $required_fields = explode(',', $fields->required_fields);
        } else {
            $active_fields = null;
            $required_fields = null;
        }
        $countrys = Country::All();
        $countryCodeIp = "IN";
        $countryNewCheck = Country::where('country_code', $countryCodeIp)->first();
        if ($countryNewCheck) {
            $countryCode = $countryNewCheck->code;
        } else {
            $countryCode = '';
        }
        $district = District::where('vendor_id', $vendorId)
            ->where('status', 1)
            ->get();
        $taluk = Taluk::where('vendor_id', $vendorId)
            ->where('status', 1)
            ->get();
        $agencies = Agency::where('vendor_id', $vendorId)->select('id', 'name')->get();
        $purposes = EnquiryPurpose::where('fk_int_user_id', $vendorId)->get();
        $enquiry_status = FeedbackStatus::UsedStatuses($vendorId)->get();
        $date_by = collect();
        $date_by->push(['id' => 1, 'name' => 'Created']);
        $date_by->push(['id' => 2, 'name' => 'Updated']);
        $additional_fields = LeadAdditionalField::where('vendor_id', $vendorId)->get();
        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
            if ($additional_field->input_type == 3 || $additional_field->input_type == 5) {
                $date_by->push(['id' => $additional_field->id, 'name' => $additional_field->field_name]);
            }
        }
        foreach ($call_status as $c_status) {
            $tasksQ = Task::NonCampaign()->where('tasks.vendor_id', $vendorId)->where('task_category_id', 2)
                ->where('call_status_id', $c_status->id);
            if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
                $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->pluck('staff_id')->toArray();
                $tasksQ = $tasksQ->where(function ($where) use ($assignedStafId) {
                    $where->where('assigned_to', Auth::user()->pk_int_user_id)->orWhereIn('assigned_to', $assignedStafId);
                });
            }
            $c_status->task_count = $tasksQ->count();
        }
        return view('gl-crm/pages/user/tasks/call-tasks', compact('agents', 'users', 'call_status', 'feedback_status', 'reasons', 'total_calls', 'types', 'active_fields', 'required_fields', 'countrys', 'countryCode', 'district', 'taluk', 'agencies', 'purposes', 'enquiry_status', 'additional_fields'));
    }

    public function getCallTasks(Request $request)
    {
      
        /** @var User $user */
        $user = Auth::user();

        $assignedToIds = $request->filled('assigned_to')
            ? [$request->integer('assigned_to')]
            : [];

        $selectColumns = [
            'tasks.id', 'tasks.name',
            'tasks.description', 'tasks.scheduled_date',
            'tasks.task_category_id', 'tasks.task_order',
            'tasks.assigned_to', 'tasks.assigned_by',
            'call_status.name as call_status',
            'tasks.vendor_id', 'tasks.enquiry_id',
            'tasks.comment',
            'tasks.status',
            'task_categories.name as task_category',
            'tbl_enquiries.vchr_customer_name as customer_name',
            'tbl_enquiries.vchr_customer_mobile as mobile',
            'tbl_enquiries.feedback_status',
            'tbl_users.vchr_user_name as assigned_user_name',
            't_user.vchr_user_name as assigned_by_user',
            'call_status_id',
            'call_status.name as call_status_name'
        ];

        $tasksQ = Task::NonCampaign()
            ->where('tasks.vendor_id', User::getVendorId())
            ->where('task_category_id', '=', 2)
            ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
            ->join('task_categories', 'task_category_id', '=', 'task_categories.id')
            ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->leftjoin('tbl_users as t_user', 'assigned_by', '=', 't_user.pk_int_user_id')
            ->leftjoin('call_status', 'call_status_id', '=', 'call_status.id')
            ->when($request->has('assigned_by') && $request->filled('assigned_by'), function (Builder $builder) use ($request) {
                $builder->where('assigned_by', $request->assigned_by);
            })
            ->when($request->has('status') && $request->filled('status'), function (Builder $builder) use ($request) {
                if ($request->status == 1) {
                    $builder->where('status', 1);
                } elseif ($request->status == 2) {
                    $builder->where('status', 0)
                        ->where('scheduled_date', '<', Carbon::today());
                } else {
                    $builder->where('status', 0)
                        ->where('scheduled_date', '>=', Carbon::today()->startOfDay());
                }
            })
            ->when($request->has('call_status') && $request->filled('call_status'), function (Builder $builder) use ($request) {
                $builder->whereIn('call_status.id', $request->call_status);
            })
            ->when($request->has('feedback_status') && $request->filled('feedback_status'), function (Builder $builder) use ($request) {
                $builder->whereIn('tbl_enquiries.feedback_status', $request->feedback_status);
            })
            ->when($request->has('source') && $request->filled('source'), function (Builder $builder) use ($request) {
                $builder->whereIn('tbl_enquiries.fk_int_enquiry_type_id', [$request->source]);
            })
            ->when($request->has('all_date') && $request->filled('all_date'), function (Builder $builder) use ($request): void {
                $date = explode(' - ', $request->all_date);
                $from = Carbon::parse($date[0])->startOfDay();
                $to = Carbon::parse($date[1])->endOfDay();

                if ((int)$request->sortby === 1) {
                    $builder->whereBetween('tasks.created_at', [$from, $to]);
                } else if ((int)$request->sortby === 2) {
                    $builder->whereBetween('tasks.updated_at', [$from, $to]);
                } else {
                    $builder->whereBetween('tasks.scheduled_date', [$from, $to]);
                }
            })
            ->when($request->has('filter') && $request->filled('filter'), function (Builder $builder) use ($request) {
                if ($request->filter == 'completed') {
                    $builder->where('tasks.status', 1);
                } elseif ($request->filter == 'overdue') {
                    $builder->where('tasks.status', 0)
                        ->whereDate('scheduled_date', '<', Carbon::today());
                } elseif ($request->filter == 'pending') {
                    $builder->where('tasks.status', 0);
                }
            })
            ->tap(new ApplyFilters([
                new AgentFilter(user: $user, agentIds: $assignedToIds)
            ]))
            ->when($request->has('sortby') && $request->filled('sortby'), function (Builder $builder) use ($request) {
                if ($request->sortby == 1) {
                    $builder->orderBy('tasks.created_at', 'DESC');
                } elseif ($request->sortby == 2) {
                    $builder->orderBy('tasks.updated_at', 'DESC');
                } elseif ($request->sortby == 3) {
                    $builder->orderBy('tasks.scheduled_date', 'DESC');
                } else {
                    $builder->orderBy('tasks.id', 'ASC');
                }
            })
            ->select($selectColumns)
            ->selectRaw("
                CASE
                    WHEN tasks.status = 1 THEN 'Completed'
                    WHEN tasks.scheduled_date < '" . Carbon::today()->toDateString() . "' AND status = 0 THEN 'OverDue'
                    WHEN tasks.scheduled_date >= '" . Carbon::today()->toDateString() . "' AND status = 0 THEN 'Pending'
                    ELSE 'Unknown'
                END AS task_status"
            )
            ->selectRaw("
                CASE 
                    WHEN tasks.created_at IS NOT NULL 
                    THEN DATE_FORMAT(tasks.created_at, '%d %b %Y %h:%i %p')
                    ELSE ''
                END AS created_date
            ")
            ->selectRaw("
                CASE 
                    WHEN tasks.updated_at IS NOT NULL 
                    THEN DATE_FORMAT(tasks.updated_at, '%d %b %Y %h:%i %p')
                    ELSE ''
                END AS updated_date
            ")
            ->selectRaw("
                CASE 
                    WHEN tasks.scheduled_date IS NOT NULL 
                    THEN DATE_FORMAT(tasks.scheduled_date, '%d %b %Y %h:%i %p')
                    ELSE ''
                END AS scheduled_fdate
            ");

        return (new EloquentDataTable($tasksQ))
            ->addIndexColumn()
            ->filterColumn('DT_RowIndex', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('mobile', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('assigned_by_user', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('assigned_user_name', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('task_status', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('call_status_name', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('created_date', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('updated_date', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('scheduled_fdate', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->addColumn('assign_agent', function ($tasks) {
                return ' <div class="form-check form-check-assign" ><input id="checkbox2" type="checkbox" data-enq_id="' . $tasks->id . '"></div>';
            })
            ->addColumn('lead_info', function ($task) {
                if ($task->customer_name == NULL) {
                    return '<span style="display:block;width:100px;word-wrap:break-word;white-space: normal;"><a class="ks-izi-modal-trigger2" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' . $task->enquiry_id . '">No Name</a></span>';
                } else {
                    return '<span style="display:block;width:100px;word-wrap:break-word;white-space: normal;"><a class="ks-izi-modal-trigger2" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' . $task->enquiry_id . '">' . '<strong>' . $task->customer_name ?? 'No Name' . '</strong><br></a></span>';
                }
            })
            ->addColumn('selete_task', function ($task) {
                return '<div class="form-check form-check-assign" ><input id="checkbox2" type="checkbox" name="seleteTask" value="' . $task->id . '" data-task_id="' . $task->id . '"></div>';
            })
            ->editColumn('description', function ($task) {
                return '<p>' . $task->description . '</p>';
            })
            ->editColumn('assigned_by', function ($task) {
                if ($task->assigned_by == Auth::user()->pk_int_user_id) {
                    return "You";
                } else {
                    return $task->assigned_by_user;
                }
            })
            ->addColumn('show', function ($task) {
                return '<button id="delete_plan" task-id="' . $task->id . '" class="btn btn-sm btn-danger mg-b-10 task-delete"><i class="fa fa-trash-o" aria-hidden="true"></i></button>';
            })
            ->rawColumns(['selete_task', 'show', 'name', 'lead_info', 'description'])
            ->toJson();
    }

    public function assignAgentMultipleTasks(Request $request)
    {
        // return $request->all();
        if ($request->task_ids == "") {
            return response()->json(['msg' => "Please Choose Task", 'status' => false]);
        }
        $task_ids = (explode(',', $request->task_ids));
        foreach ($task_ids as $task_id) {
            $task = Task::where('id', $task_id)->first();
            if ($task) {
                $task->assigned_to = $request->staff_id;
                $task->save();
            }
        }
        return response()->json(['msg' => "Agent Assigned", 'status' => true]);
    }

    public function callTaskDetails(Request $request)
    {
        $date = explode(' - ', request('date'));
        $from_date = $date[0];
        $to_date = $date[1];
        $id = $request->flag;
        $selectColumns = [
            'tasks.id', 'tasks.name',
            'tasks.description', 'tasks.scheduled_date',
            'tasks.task_category_id', 'tasks.task_order',
            'tasks.assigned_to', 'tasks.assigned_by',
            'tasks.vendor_id', 'tasks.enquiry_id',
            'tasks.comment', 'tasks.status', 'tasks.created_at', 'tasks.call_status_id',
            'task_categories.name as task_category',
            'tbl_enquiries.vchr_customer_name as customer_name',
            'tbl_enquiries.vchr_customer_mobile', 'tbl_enquiries.feedback_status as lead_status',
            'tbl_users.vchr_user_name as assigned_user_name'];
        $tasks = Task::with(['creator', 'history', 'history.creator', 'agent'])
            ->where('tasks.vendor_id', User::getVendorId())
            ->where('tasks.task_category_id', '=', 2)
            ->where('tasks.status', '=', 0)
            ->join('task_categories', 'task_category_id', '=', 'task_categories.id')
            ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
            ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->select($selectColumns)->whereDate('tasks.scheduled_date', '>=', $from_date)
            ->whereDate('tasks.scheduled_date', '<=', $to_date);
        $count = $tasks->count();
        // Log::info(count($tasks->get()));
        if ($id == 2) {
            $task_details = $tasks->orderBy('scheduled_date', 'ASC')->first();
        } else if ($id == 1) {
            $task_details = $tasks->orderBy('scheduled_date', 'ASC')->where('tasks.id', '>', $request->task_id)->first();
        } else if ($id == 3) {
            $task_details = $tasks->orderBy('scheduled_date', 'DESC')->where('tasks.id', '<', $request->task_id)->first();
        } else if ($id == 4) {
            $task_details = $tasks->where('tasks.id', $request->task_id)->first();
        }
        // Log::info($count);
        $data = [
            'task_details' => $task_details,
            'count' => $count,
        ];
        if ($task_details) {
            return response()->json(['msg' => "", 'status' => 'success', 'data' => $data]);
        } else {
            return response()->json(['msg' => "Not Found", 'status' => 'fail']);
        }
    }

    public function callTaskStatusUpdate(Request $request)
    {

        // $validate_fields = [

        //     'comment' => ['required']
        // ];
        // $validate_messages = [

        //     'comment.required' => 'Feedback is required',
        // ];
        // $input = $request->all();

        // $rules = [
        //     // 'call_status' => 'required',
        //     'lead_status' => 'required',
        // ];
        // $rulesMessage = [
        //     // 'call_status.required' => 'Call status required.',
        //     'lead_status.required' => 'Lead status required.'
        // ];
        // $validator = validator($input, $rules, $rulesMessage);
        // if ($validator->passes()) {
        DB::beginTransaction();
        try {
            $vendorId = User::getVendorId();
            if ($request->Isdeal == 1) {
                DealTask::where('id', $request->s_task_id)->where('vendor_id', $vendorId)->update(['status' => 1]);

                DealTaskHistory::create(['deal_task_id' => $request->s_task_id,
                    'task_status' => 'Feedback Submitted',
                    'updated_by' => Auth::id(),
                    'vendor_id' => $vendorId,
                    'comment' => $request->remark,
                    'date' => Carbon::today()
                ]);
                $task = DealTask::where('id', $request->s_task_id)->where('vendor_id', $vendorId)->first();
                if ($task->assigned_by != Auth::id()) {
                    event(new DealTaskCompleted($task, Auth::id()));
                }
            } else {
                $existing_task1 = Task::find($request->s_task_id);
                if ($existing_task1) {
                    Task::where('id', $request->s_task_id)->update([
                        'status' => 1,
                        'comment' => $request->remark,
                        'call_status_id' => $request->call_status
                    ]);
                    event(new CreateFollowup('Feedback Submitted', EnquiryFollowup::TASK_COMPLETED, $existing_task1->enquiry_id, Auth::id(), null, null, null, null, null, $request->s_task_id));
                    if ($existing_task1->assigned_by != Auth::id()) {
                        event(new TaskCompleted($existing_task1, Auth::id()));
                    }
                }
                TaskHistory::create(['task_id' => $request->s_task_id,
                    'task_status' => 'Feedback Submitted',
                    'updated_by' => Auth::id(),
                    'vendor_id' => $vendorId,
                    'comment' => $request->remark,
                    'date' => Carbon::today()
                ]);
                $old = Enquiry::where('pk_int_enquiry_id', $existing_task1->enquiry_id)->first();
                $old_status_id = $old ? $old->feedback_status : null;
                Enquiry::where('pk_int_enquiry_id', $existing_task1->enquiry_id)->update(['feedback_status' => $request->lead_status, 'updated_at' => now()]);
                // $enquiryfollowup = new EnquiryFollowup();
                // $enquiryfollowup->note = $request->lead_status;
                // $enquiryfollowup->log_type = EnquiryFollowup::TYPE_STATUS;
                // $enquiryfollowup->enquiry_id = $existing_task1->enquiry_id;
                // $enquiryfollowup->created_by = Auth::id();
                // $enquiryfollowup->save();
                try {
                    if ($request->lead_status) {
                        event(new CreateFollowup($request->lead_status, EnquiryFollowup::TYPE_STATUS, $existing_task1->enquiry_id, Auth::id(), $old_status_id));
                    }
                } catch (\Exception $e) {
                    \Log::info($e->getMessage());
                }

                if ($request->remark || $request->reason_id) {
                    $enquiryfollowup = new EnquiryFollowup();

                    if ($request->reason_id) {
                        $reason = Reason::where('id', $request->reason_id)->first();
                        if ($reason) {
                            $reason_name = $reason->name;
                        } else {
                            $reason_name = '';
                        }
                        if ($reason_name == $request->remark) {
                            $enquiryfollowup->note = ($reason_name);
                        } else {
                            $enquiryfollowup->note = ($reason_name) . "," . ($request->remark);
                        }

                    }
                    $enquiryfollowup->log_type = EnquiryFollowup::TYPE_NOTE;
                    $enquiryfollowup->enquiry_id = $existing_task1->enquiry_id;
                    $enquiryfollowup->created_by = Auth::id();
                    $enquiryfollowup->save();
                }
                if ($request->has('need_to_add')) {
                    if (($request->enquiry_status_id == 3107 || ($request->has('follow_up') && $request->follow_up >= 0))) {
                        if ($request->follow_up > 0)
                            $request->merge([
                                'schedule_date_time' => Carbon::now()->addHours($request->follow_up)->format('Y-m-d H:i:s')
                            ]);
                        $input = [
                            'name' => 'Call Back ' . $existing_task1->description,
                            'description' => $request->remark,
                            'scheduled_date' => Carbon::parse($request->schedule_date_time)->format('Y-m-d H:i:s') ?? now(),
                            'task_category_id' => 2,
                            'assigned_to' => Auth::id(),
                            'assigned_by' => Auth::id(),
                            'vendor_id' => $vendorId,
                            'enquiry_id' => $existing_task1->enquiry_id,
                            'comment' => $request->remark,
                            'status' => 0,
                        ];
                        $existing_task = Task::callTaskClose($existing_task1->enquiry_id, $vendorId, Auth::user()->pk_int_user_id);
                        if ($existing_task == null) {
                            Task::create($input);

                        }

                    }
                }    // else {
                //     return response()->json(['msg' => "Error", 'status' => false]);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::info($exception->getMessage());
            return response()->json(['msg' => "Error", 'status' => false, 'message' => $exception->getMessage()]);

        }
        DB::commit();
        return response()->json(['msg' => "Success", 'status' => true, 'data' => $request->all()]);

        // } else {
        //     // Log::info($validator->messages());
        //     return response()->json(['msg' => "Validation Error", 'status' => false, 'errors' => $validator->messages()]);
        // }
    }

    public function markAsArchive(Request $request)
    {
        try {
            $userId = User::getVendorId();
            Task::where('id', $request->id)->where('vendor_id', $userId)->update(['is_archived' => 1]);
            return response()->json(['msg' => "Task Archived.", 'status' => 'success']);
        } catch (\Exception $e) {

            return response()->json(['msg' => $e->getMessage(), 'status' => 'fail']);
        }
    }

    public function statusCount(Request $request)
    {
        $vendorId = User::getVendorId();
        /** @var User $user */
        $user = Auth::user();

        $statusCountQuery = Task::NonCampaign()
            ->where('tasks.vendor_id', '=', $vendorId)
            ->where('task_category_id', '=', 2)
            ->when($request->has('assigned_by') && $request->filled('assigned_by'), function (Builder $builder) use ($request) {
                $builder->where('assigned_by', $request->assigned_by);
            })
            ->tap(new ApplyFilters([
                new AgentFilter($user, $request->has('assigned_to') ? [$request->get('assigned_to')] : []),
            ]))
            ->when($request->has('status') && $request->filled('status'), function (Builder $builder) use ($request) {
                if ($request->status == 1) {
                    $builder->where('status', 1);
                } elseif ($request->status == 2) {
                    $builder->where('status', 0)
                        ->where('scheduled_date', '<', Carbon::today());
                } else {
                    $builder->where('status', 0)
                        ->where('scheduled_date', '>=', Carbon::today());
                }
            })
            ->when($request->has('call_status') && $request->filled('call_status'), function (Builder $builder) use ($request) {
                $builder->whereIn('call_status_id', $request->call_status);
            })
            ->when($request->has('source') && $request->filled('source'), function (Builder $builder) use ($request) {
                $builder->whereIn('enquiry_id', function (QueryBuilder $builder) use ($request) {
                    $builder->select('pk_int_enquiry_id')
                        ->from('tbl_enquiries')
                        ->where('fk_int_enquiry_type_id', $request->source);
                });
            })
            ->when($request->has('fdate') && $request->filled('fdate'), function (Builder $builder) use ($request): void {
                $from = Carbon::parse($request->fdate)->startOfDay();
                $to = Carbon::parse($request->tdate)->endOfDay();

                $builder->whereBetween('tasks.scheduled_date', [$from, $to]);

                if ((int)$request->sortby == 2) {
                    $builder->orWhereBetween('tasks.updated_at', [$from, $to]);
                } else if ((int)$request->sortby == 1) {
                    $builder->orWhereBetween('tasks.created_at', [$from, $to]);
                }
            });

        $pendingCallsQuery = (clone $statusCountQuery)
            ->where('status', 0)
            ->whereRaw('scheduled_date >= NOW()')
            ->selectRaw('COUNT(id) as call_count')
            ->selectRaw("'pending_calls' as call_type");

        $overdueCallsQuery = (clone $statusCountQuery)
            ->where('status', 0)
            ->whereRaw('scheduled_date < NOW()')
            ->selectRaw('COUNT(id) as call_count')
            ->selectRaw("'overdue_calls' as call_type");

        $completedCallsQuery = (clone $statusCountQuery)
            ->where('status', 1)
            ->selectRaw('COUNT(id) as call_count')
            ->selectRaw("'completed_calls' as call_type");

        $totalCallsQuery = (clone $statusCountQuery)
            ->selectRaw('COUNT(id) as call_count')
            ->selectRaw("'total_calls' as call_type");

        $combinedQuery = $pendingCallsQuery
            ->unionAll($overdueCallsQuery)
            ->unionAll($completedCallsQuery)
            ->unionAll($totalCallsQuery);

        $results = $combinedQuery->get()->keyBy('call_type')->toArray();

        $pending_calls = $results['pending_calls']['call_count'] ?? 0;
        $overdue_calls = $results['overdue_calls']['call_count'] ?? 0;
        $completed_calls = $results['completed_calls']['call_count'] ?? 0;
        $total_calls = $results['total_calls']['call_count'] ?? 0;

        $knownCallStatuses = CallStatus::query()
            ->where('vendor_id', $vendorId)
            ->select('id', 'name')
            ->selectRaw('0 as task_count')
            ->get()
            ->keyBy('id');

        if ($total_calls > 0) {
            $callStatusCounts = (clone $statusCountQuery)
                ->leftjoin('call_status', 'tasks.call_status_id', '=', 'call_status.id')
                ->selectRaw('`tasks`.`call_status_id` as id')
                ->selectRaw('COALESCE(`call_status`.`name`, "UNKNOWN") as name')
                ->selectRaw('COUNT(tasks.id) as task_count')
                ->groupBy(['tasks.call_status_id'])
                ->get();

            [$knownCallStatusesCount, $unknownCallStatuses] = $callStatusCounts->partition(function ($callStatus) {
                return $callStatus->name !== 'UNKNOWN';
            });
        } else {
            $knownCallStatusesCount = CallStatus::query()
                ->where('vendor_id', $vendorId)
                ->select('id', 'name')
                ->selectRaw('0 as task_count')
                ->get()
                ->keyBy('id');

            $unknownCallStatuses = (clone $statusCountQuery)
                ->whereNull('call_status_id')
                ->selectRaw('COUNT(tasks.id) as task_count')
                ->get();
        }

        $call_status = $knownCallStatuses->merge($knownCallStatusesCount)->values()->toArray();
        $unknown = $unknownCallStatuses->toArray()[0]['task_count'] ?? 0;

        return response()->json(['msg' => "Success", 'status' => true, 'data' => compact('total_calls', 'call_status', 'pending_calls', 'completed_calls', 'overdue_calls', 'unknown')]);
    }

    public function agentStatusReport(Request $request)
    {
        $vendorid = User::getVendorId();
        $task = new Task;
        $data = $task->agentReport($vendorid, $request);
        if ($data) {
            return response()->json(['msg' => "Success", 'status' => true, 'data' => $data]);
        } else {
            return response()->json(['msg' => "Not Found", 'status' => 'fail']);
        }
    }

    public function callTasksReport()
    {
        return view('gl-crm.pages.user.tasks.call-tasks-report');
    }

    public function store()
    {
        $validate_fields = [
            'name' => ['required'],
            'description' => ['required'],
            'task_category_id' => ['required'],
            'scheduled_date_add' => ['required'],
            'assigned_to' => ['numeric'],
            'time_task' => ['required'],
        ];
        $validate_messages = [
            'name.required' => ' name Required',
            'description.required' => 'Description Required',
            'task_category_id.required' => 'Task category Required',
            'scheduled_date_add.required' => 'Scheduled Date Required',
            // 'assigned_to.required' => 'Assigned To Required',
        ];
        try {
            $validation = Validator::make(request()->all(), $validate_fields, $validate_messages);
            if ($validation->fails()) {
                return response()->json(['msg' => $validation->errors(), 'status' => 'fail']);
            }
            if (request('scheduled_date_add') != " " && request('time_task') != "") {
                $date_time = Carbon::parse(request('scheduled_date_add'))->format('Y-m-d') . ' ' . Carbon::createFromFormat('H:i A', request('time_task'))->toTimeString();
            } else {
                $date_time = request('scheduled_date_add');
            }

            $task = new Task;
            $input = [
                'name' => request()->name,
                'description' => request()->description,
                'scheduled_date' => $date_time ?? now(),
                'task_category_id' => request()->task_category_id,
                'task_order' => request()->task_order,
                'assigned_to' => request()->assigned_to ?? Auth::id(),
                'assigned_by' => Auth::id(),
                'vendor_id' => User::getVendorId(),
                'comment' => request()->comment,
                'status' => 0,
            ];
            $task = Task::create($input);
            event(new TaskAssigned($task));

        } catch (\Exception $exception) {
            \Log::info("Error Occurred While Adding Task through WEB:" . $exception->getMessage());
            return response()->json(['msg' => $exception->getMessage(), 'status' => 'fail']);
        }
        return response()->json(['msg' => "Task Added Successfully", 'status' => 'success']);
    }

    public function callReport()
    {
        return view('gl-crm.pages.user.leads.reports.call-reports');
    }

    public function ivrCallReport()
    {
        $agents = User::StaffCheck()->where('parent_user_id', User::getVendorId())->orWhere('pk_int_user_id', User::getVendorId())->select('pk_int_user_id', 'vchr_user_name')->get();
        $today = Carbon::today();
        // dd($callDuration);
        return view('gl-crm.pages.user.tasks.call-report', compact('agents', 'today'));
    }

    public function callFeedbackReport(Request $request)
    {
        $agents = User::StaffCheck()
            ->whereNotIn('pk_int_user_id', ['3642', '4015'])
            ->where(function ($where) {
                if (auth()->user()->int_role_id == 2 || auth()->user()->is_co_admin == 1) {
                    $where->where(function ($wh) {
                        $wh->where('int_role_id', User::STAFF)
                            ->where('parent_user_id', User::getVendorId());
                        $wh->orWhere('pk_int_user_id', User::getVendorId());
                    })->orWhere('pk_int_user_id', Auth::id());
                    //  ->orwhere('pk_int_user_id',User::getVendorId());
                } else {
                    $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->pluck('staff_id')->toArray();
                    $where->where(function ($wh) use ($assignedStafId) {
                        $wh->where('pk_int_user_id', Auth::id())
                            ->orWhereIn('pk_int_user_id', $assignedStafId);
                    });
                }
            })
            ->get();
        $today = Carbon::today();
        $call_status = DB::table('call_status')->where('vendor_id', User::getVendorId())->select('id', 'name')->get();

        return view('gl-crm.pages.user.tasks.new-call-report', compact('agents', 'today', 'call_status'));

    }

    public function callLeadStatusCount(Request $request)
    {
        $user = Auth::user();
        $userId = $user->pk_int_user_id;
        $roleId = $user->int_role_id;
        $vendorId = User::getVendorId();

        $date = explode(' - ', $request->all_date);
        $date_from = $date[0];
        $date_to = $date[1];


        $tasks = Task::where('tasks.vendor_id', $vendorId)
            ->where('task_category_id', 2)
            ->where(function ($q) use ($roleId, $user, $vendorId) {
                if ($roleId == User::STAFF && $user->is_co_admin == 0) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('assigned_to', $user->pk_int_user_id);
                } else if ($roleId == User::USERS || $user->is_co_admin == 1) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('tasks.vendor_id', $vendorId);
                }
            })
            ->where(function ($q) use ($request, $date_from, $date_to) {
                if ($date_from != null && $date_to != null) {
                    $q->whereDate('tasks.updated_at', '>=', $date_from)->whereDate('tasks.updated_at', '<=', $date_to);
                }

            });

        $enq_ids = $tasks->with(['enquiry' => function ($q) {
            $q->select('pk_int_enquiry_id');
        }])
            ->where('tasks.status', 1)
            ->get()
            ->map(function ($q) {
                return $q->enquiry ? ($q->enquiry['pk_int_enquiry_id'] != null ? $q->enquiry['pk_int_enquiry_id'] : 0) : 0;
            });

        $data['status'] = Enquiry::whereIn('pk_int_enquiry_id', $enq_ids)
            ->leftJoin('tbl_feedback_status', 'tbl_feedback_status.pk_int_feedback_status_id', 'feedback_status')
            ->where('vchr_status', '!=', null)
            ->select(DB::raw('count(*) as count'), 'feedback_status', 'vchr_status', 'vchr_color')
            ->groupBy('feedback_status')
            ->get();
        $chart = array();
        foreach ($data['status'] as $key => $da) {
            $chart['labels'][$key] = $da->vchr_status;
            $chart['series'][$key] = $da->count;
        }
        $data['chart'] = $chart;
        return response()->json(['result' => $data]);
    }

    public function pendingOverdueTaskCount(Request $request)
    {
        $user = Auth::user();
        $userId = $user->pk_int_user_id;
        $roleId = $user->int_role_id;
        $vendorId = User::getVendorId();

        $data = [];
        $date = explode(' - ', $request->all_date);
        $from_date = $date[0];
        $to_date = $date[1];
        $today = today()->format('Y-m-d');

        $all_tasks = Task::NonCampaign()->where('tasks.vendor_id', $vendorId)
            ->where('task_category_id', 2)
            ->where(function ($q) use ($roleId, $user, $vendorId) {
                if ($roleId == User::STAFF && $user->is_co_admin == 0) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('assigned_to', $user->pk_int_user_id);
                } else if ($roleId == User::USERS || $user->is_co_admin == 1) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('tasks.vendor_id', $vendorId);
                }
            });

        $deal_all_task = DealTask::where('deal_tasks.vendor_id', $vendorId)
            ->where('deal_tasks.task_category_id', 2)
            ->where(function ($q) use ($roleId, $user, $vendorId) {
                if ($roleId == User::STAFF && $user->is_co_admin == 0) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('assigned_to', $user->pk_int_user_id);
                } else if ($roleId == User::USERS || $user->is_co_admin == 1) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('deal_tasks.vendor_id', $vendorId);
                }
            });


        if ($today <= $to_date) {

            $task_pending = (clone $all_tasks)->where('status', 0)
                ->where(function ($q) use ($request, $from_date, $to_date, $today) {
                    if ($from_date != null && $to_date != null) {
                        $q->whereDate('tasks.scheduled_date', '>=', $today)->whereDate('tasks.scheduled_date', '<=', $to_date);
                    }
                })->count();

            $dealtask_pending = (clone $deal_all_task)->where('status', 0)
                ->where(function ($q) use ($request, $from_date, $to_date, $today) {
                    if ($from_date != null && $to_date != null) {
                        $q->whereDate('deal_tasks.scheduled_date', '>=', $today)->whereDate('deal_tasks.scheduled_date', '<=', $to_date);
                    }
                })->count();
            $data['pending'] = $task_pending + $dealtask_pending;
        } else {
            $data['pending'] = 0;
        }


        $task_overdue = (clone $all_tasks)->where('status', 0)
            ->where(function ($q) use ($request, $from_date, $to_date, $today) {
                if ($from_date != null && $to_date != null) {
                    if ($today == $from_date || $from_date == Carbon::yesterday()->format('Y-m-d')) {
                        $to_date = Carbon::yesterday()->format('Y-m-d');
                        $q->whereDate('tasks.scheduled_date', '<=', $to_date);
                    } else if ($today <= $to_date) {
                        $to_date = Carbon::yesterday()->format('Y-m-d');
                        $q->whereDate('tasks.scheduled_date', '>=', $from_date)->whereDate('tasks.scheduled_date', '<=', $to_date);
                    } else {
                        $q->whereDate('tasks.scheduled_date', '>=', $from_date)->whereDate('tasks.scheduled_date', '<=', $to_date);
                    }
                }
            })->count();

        $deal_overdue = (clone $deal_all_task)->where('status', 0)
            ->where(function ($q) use ($request, $from_date, $to_date, $today) {
                if ($from_date != null && $to_date != null) {
                    if ($today == $from_date || $from_date == Carbon::yesterday()->format('Y-m-d')) {
                        $to_date = Carbon::yesterday()->format('Y-m-d');
                        $q->whereDate('deal_tasks.scheduled_date', '<=', $to_date);
                    } else if ($today <= $to_date) {
                        $to_date = Carbon::yesterday()->format('Y-m-d');
                        $q->whereDate('deal_tasks.scheduled_date', '>=', $from_date)->whereDate('deal_tasks.scheduled_date', '<=', $to_date);
                    } else {
                        $q->whereDate('deal_tasks.scheduled_date', '>=', $from_date)->whereDate('deal_tasks.scheduled_date', '<=', $to_date);
                    }
                }
            })->count();

        $data['overdue'] = $task_overdue + $deal_overdue;
        return response()->json(['result' => $data]);


    }

    public function callReportStatusData(Request $request)
    {
        $user = Auth::user();
        $userId = $user->pk_int_user_id;
        $roleId = $user->int_role_id;
        $vendorId = User::getVendorId();

        $data = [];
        $da = [];
        $date = explode(' - ', $request->all_date);
        $from_date = $date[0];
        $to_date = $date[1];

        $call_status_ids = DB::table('call_status')->where('vendor_id', $vendorId)->pluck('id')->toArray();

        $all_tasks = Task::where('tasks.vendor_id', $vendorId)
            ->where('task_category_id', 2)
            ->where(function ($q) use ($roleId, $user, $vendorId) {
                if ($roleId == User::STAFF && $user->is_co_admin == 0) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('assigned_to', $user->pk_int_user_id);
                } else if ($roleId == User::USERS || $user->is_co_admin == 1) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('tasks.vendor_id', $vendorId);
                }
            });

        $deal_all_task = DealTask::where('deal_tasks.vendor_id', $vendorId)->where('deal_tasks.task_category_id', 2)
            ->where(function ($q) use ($roleId, $user, $vendorId) {
                if ($roleId == User::STAFF && $user->is_co_admin == 0) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('assigned_to', $user->pk_int_user_id);
                } else if ($roleId == User::USERS || $user->is_co_admin == 1) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('deal_tasks.vendor_id', $vendorId);
                }
            });

        $deal_task = (clone $deal_all_task)->where(function ($q) use ($request, $from_date, $to_date) {
            if ($from_date != null && $to_date != null) {
                $q->whereDate('deal_tasks.updated_at', '>=', $from_date)->whereDate('deal_tasks.updated_at', '<=', $to_date);
            }
        });

        $deal_call_report = (clone $deal_task)
            ->leftJoin('call_status', 'call_status.id', 'deal_tasks.call_status_id')
            ->select('call_status_id', DB::raw('count(*) as count'), 'call_status.name as call_status', 'deal_tasks.status')
            ->where('deal_tasks.status', 1)
            ->groupBy('call_status.name')
            ->where(function ($q) use ($request, $call_status_ids) {
                if ($request->status) {
                    $q->where('deal_tasks.call_status_id', $request->status);
                } else {
                    $q->whereIn('deal_tasks.call_status_id', $call_status_ids);
                }
            })
            ->get();

        $tasks = (clone $all_tasks)->where(function ($q) use ($request, $from_date, $to_date) {
            if ($from_date != null && $to_date != null) {
                $q->whereDate('tasks.updated_at', '>=', $from_date)->whereDate('tasks.updated_at', '<=', $to_date);
            }
        });

        $call_report = (clone $tasks)->leftJoin('call_status', 'call_status.id', 'tasks.call_status_id')
            ->where(function ($q) use ($request, $call_status_ids) {
                if ($request->status) {
                    $q->where('call_status_id', $request->status);
                } else {
                    $q->whereIn('call_status_id', $call_status_ids);
                }
            })
            //  ->whereIn('call_status_id',$call_status_ids)
            ->select('call_status_id', DB::raw('count(*) as count'), 'call_status.name as call_status', 'tasks.status')
            ->where('tasks.status', 1)
            ->groupBy('call_status')
            ->get();


        $null_status_count = (clone $tasks)->where('tasks.status', 1)->where('call_status_id', null)->get()->count();
        $null_deal_status_count = (clone $deal_task)->where('deal_tasks.status', 1)->where('call_status_id', null)->get()->count();

        if (($null_status_count > 0 && $request->status == '') || $request->status == -1) {
            $null_status = (object)array('call_status_id' => 0, 'count' => $null_status_count, 'call_status' => 'action pending');
            $data_call_report = $call_report->push($null_status);
        } else {
            $data_call_report = $call_report;
        }
        if (($null_deal_status_count > 0 && $request->status == '') || $request->status == -1) {
            $null_deal_status = (object)array('call_status_id' => 0, 'count' => $null_deal_status_count, 'call_status' => 'action pending');
            $data_deal_call_report = $deal_call_report->push($null_deal_status);
        } else {
            $data_deal_call_report = $deal_call_report;
        }

        $all_report = collect($data_call_report)->merge($data_deal_call_report)->groupby('call_status');

        foreach ($all_report as $key => $report) {
            $count = 0;
            if (count($report) > 1) {
                foreach ($report as $call) {
                    $count = $count + $call->count;
                }
                $da[$key]['call_status_id'] = $report[0]->call_status_id;
                $da[$key]['call_status'] = $report[0]->call_status;
                $da[$key]['count'] = $count;
            } else {
                $da[$key]['call_status_id'] = $report[0]->call_status_id;
                $da[$key]['call_status'] = $report[0]->call_status;
                $da[$key]['count'] = $report[0]->count;
            }

        }

        return response()->json(['result' => $da]);

    }

    public function getAgentCallReport(Request $request)
    {
        $user = Auth::user();
        $userId = $user->pk_int_user_id;
        $roleId = $user->int_role_id;
        $vendorId = User::getVendorId();

        $data = [];
        $date = explode(' - ', $request->all_date);
        $from_date = $date[0];
        $to_date = $date[1];

        $call_status_ids = DB::table('call_status')->where('vendor_id', $vendorId)->pluck('id')->toArray();

        $all_tasks = Task::where('tasks.vendor_id', $vendorId)
            ->where('task_category_id', 2)
            ->where(function ($q) use ($roleId, $user, $vendorId) {
                if ($roleId == User::STAFF && $user->is_co_admin == 0) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('assigned_to', $user->pk_int_user_id);
                } else if ($roleId == User::USERS || $user->is_co_admin == 1) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('tasks.vendor_id', $vendorId);
                }
            });

        $deal_all_task = DealTask::where('deal_tasks.vendor_id', $vendorId)->where('deal_tasks.task_category_id', 2)
            ->where(function ($q) use ($roleId, $user, $vendorId) {
                if ($roleId == User::STAFF && $user->is_co_admin == 0) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('assigned_to', $user->pk_int_user_id);
                } else if ($roleId == User::USERS || $user->is_co_admin == 1) {
                    request('agent_id') ? $q->where('assigned_to', request('agent_id')) : $q->where('deal_tasks.vendor_id', $vendorId);
                }
            });


        $tasks = (clone $all_tasks)->where(function ($q) use ($request, $from_date, $to_date) {
            if ($from_date != null && $to_date != null) {
                $q->whereDate('tasks.updated_at', '>=', $from_date)->whereDate('tasks.updated_at', '<=', $to_date);
            }
        });
        $deal_task = (clone $deal_all_task)->where(function ($q) use ($request, $from_date, $to_date) {
            if ($from_date != null && $to_date != null) {
                $q->whereDate('deal_tasks.updated_at', '>=', $from_date)->whereDate('deal_tasks.updated_at', '<=', $to_date);
            }
        });

        $call_report = (clone $tasks)->leftJoin('call_status', 'call_status.id', 'tasks.call_status_id')
            ->leftJoin('tbl_enquiries', 'tbl_enquiries.pk_int_enquiry_id', '=', 'tasks.enquiry_id')
            ->leftJoin('tbl_enquiry_purpose', 'tbl_enquiry_purpose.pk_int_purpose_id', '=', 'tbl_enquiries.fk_int_purpose_id')
            ->leftJoin('tbl_users', 'tbl_users.pk_int_user_id', 'tasks.assigned_to')
            //  ->whereIn('call_status_id',$call_status_ids)
            ->select('call_status_id', 'call_status.name as call_status', 'tbl_enquiries.vchr_customer_name', 'tbl_enquiries.vchr_customer_mobile', 'tasks.comment', 'tbl_users.vchr_user_name as agent', 'tasks.updated_at')
            ->where('tasks.status', 1)
            ->where(function ($q) use ($request, $call_status_ids) {
                if ($request->status && $request->status != -1) {
                    $q->where('call_status_id', $request->status);
                } elseif ($request->status && $request->status == -1) {
                    $q->where('call_status_id', null);
                } else {
                    $q->whereIn('call_status_id', $call_status_ids)->orwhere('call_status_id', null);
                }
            })
            ->orderBy('tasks.updated_at', 'DESC')->get();


        $deal_call_report = (clone $deal_task)->leftJoin('call_status', 'call_status.id', 'deal_tasks.call_status_id')
            ->leftJoin('deals', 'deals.pk_int_deal_id', '=', 'deal_tasks.deal_id')
            ->leftJoin('tbl_enquiries', 'tbl_enquiries.pk_int_enquiry_id', '=', 'deals.lead_id')
            //  ->leftJoin('tbl_enquiry_purpose','tbl_enquiry_purpose.pk_int_purpose_id','=','tbl_enquiries.fk_int_purpose_id')
            ->leftJoin('tbl_users', 'tbl_users.pk_int_user_id', 'deal_tasks.assigned_to')
            //  ->whereIn('call_status_id',$call_status_ids)
            ->select('call_status_id', 'call_status.name as call_status', 'tbl_enquiries.vchr_customer_name', 'tbl_enquiries.vchr_customer_mobile', 'deal_tasks.comment', 'tbl_users.vchr_user_name as agent', 'deal_tasks.updated_at')
            ->where('deal_tasks.status', 1)
            ->where(function ($q) use ($request, $call_status_ids) {
                if ($request->status && $request->status != -1) {
                    $q->where('deal_tasks.call_status_id', $request->status);
                } elseif ($request->status && $request->status == -1) {
                    $q->where('deal_tasks.call_status_id', null);
                } else {
                    $q->whereIn('deal_tasks.call_status_id', $call_status_ids)->orwhere('deal_tasks.call_status_id', null);
                }
            })
            //  ->when($request->status, function ($q) {
            //     if(request('status') != -1)
            //        return $q->where('call_status_id', request('status'));
            //     else
            //       return $q->where('call_status_id',null);
            // })
            // ->groupBy('call_status')
            ->orderBy('deal_tasks.updated_at', 'DESC')->get();

        $all_call_report = collect($call_report)->merge($deal_call_report);


        foreach ($all_call_report as $call) {
            $call->comment = $call->comment ?? '';
            $call->call_status = $call->call_status ?? 'Action pending';
            $call->last_updated = Carbon::parse($call->updated_at)->format('d M Y, h:i A');
        }


        $data['call_report'] = $all_call_report->sortByDesc('updated_at');


        $data['completed_calls'] = $data['call_report']->sum('count');

        return response()->json(['result' => $data]);

    }

    public function callReportData(Request $request)
    {
        $data = [];
        $date = explode(' - ', $request->all_date);
        $from_date = $date[0];
        $to_date = $date[1];
        $vendorId = User::getVendorId();
        $user = Auth::user();
        if ($user->int_role_id == User::STAFF && $user->is_co_admin == 0) {
            $agent_id = $user->pk_int_user_id;
        } else {
            $agent_id = $request->agent;
        }
        $settings = CloudTelephonySetting::where('vendor_id', $vendorId)
            ->where('status', 1)
            ->where('default', 1)
            ->first();

        if ($settings) {
            if ($settings->operator == "2") {
                try {
                    $bonVoice = CallMaster::where('vendor_id', $vendorId)
                        ->whereNotNull('call_status')
                        ->whereDate('created_at', '>=', $from_date)
                        ->whereDate('created_at', '<=', $to_date)
                        ->where(function ($query) use ($agent_id) {
                            if ($agent_id != 0) {
                                $usr = User::select('mobile')->find($agent_id);
                                if ($usr)
                                    $query->where('called_number', 'like', "%" . $usr->mobile);
                            }
                        });


                    $data['totalCalls'] = (clone $bonVoice)->count();
                    $data['incomingCall'] = (clone $bonVoice)->where('direction', 'Inbound')->count();
                    $data['attended'] = (clone $bonVoice)->where('recording_url', '!=', null)->count();
                    $data['missed'] = (clone $bonVoice)->where('recording_url', null)->count();
                    $data['outBound'] = (clone $bonVoice)->where('direction', 'Outbound')->count();
                    $data['uniqNum'] = (clone $bonVoice)->distinct('caller_number')->count('caller_number');
                    $data['duration'] = (int)(clone $bonVoice)->sum('conversation_duration');
                } catch (\Exception $e) {
                    \Log::info($e->getMessage());
                }

            } else {
                $voxbay = Ivr::where('vendor_id', $vendorId)
                    ->whereDate('created_at', '>=', $from_date)
                    ->whereDate('created_at', '<=', $to_date)
                    ->where(function ($query) use ($agent_id) {
                        if ($agent_id != 0) {
                            $usr = User::select('vchr_user_mobile')->find($agent_id);
                            if ($usr)
                                return $query->where('agent_number', $usr->vchr_user_mobile);
                        }
                    });

                $data['totalCalls'] = (clone $voxbay)->count();
                $data['incomingCall'] = (clone $voxbay)->where('call_uuid', 'not like', 'Out%')->count();
                $data['attended'] = (clone $voxbay)->where('recording_url', '!=', null)->count();
                $data['missed'] = (clone $voxbay)->where('recording_url', null)->count();
                $data['outBound'] = (clone $voxbay)->where('call_uuid', 'like', 'Out%')->count();
                $data['uniqNum'] = (clone $voxbay)->distinct('caller_number')->count('caller_number');
                $data['duration'] = (int)(clone $voxbay)->sum('conversation_duration');
            }
        } else {
            $voxbay = Ivr::where('vendor_id', $vendorId)
                ->whereDate('created_at', '>=', $from_date)
                ->whereDate('created_at', '<=', $to_date)
                ->where(function ($query) use ($agent_id) {
                    if ($agent_id != 0) {
                        $usr = User::select('vchr_user_mobile')->find($agent_id);
                        if ($usr)
                            return $query->where('agent_number', $usr->vchr_user_mobile);
                    }
                });

            $data['totalCalls'] = (clone $voxbay)->count();
            $data['incomingCall'] = (clone $voxbay)->where('call_uuid', 'not like', 'Out%')->count();
            $data['attended'] = (clone $voxbay)->where('recording_url', '!=', null)->count();
            $data['missed'] = (clone $voxbay)->where('recording_url', null)->count();
            $data['outBound'] = (clone $voxbay)->where('call_uuid', 'like', 'Out%')->count();
            $data['uniqNum'] = (clone $voxbay)->distinct()->count('called_number');
            $data['duration'] = (int)(clone $voxbay)->sum('conversation_duration');
        }

        $hour = floor($data['duration'] / 3600);
        $min = floor(($data['duration'] % 3600) / 60);
        $sec = $data['duration'] % 60;

        $setTime = $hour . 'hr ' . $min . 'min ' . $sec . 'sec';

        return response()->json(['result' => $data, 'time' => $setTime]);
    }

    public function getCallReport(Request $request)
    {
        $date = explode(' - ', $request->date);
        $from_date = $date[0];
        $to_date = $date[1];
        $draw = $request->draw;
        $start = $request->start; //Start is the offset
        $length = $request->length; //How many records to show
        $column = $request->order[0]['column']; //Column to orderBy
        $dir = $request->order[0]['dir']; //Direction of orderBy
        $searchValue = $request->search['value']; //Search value
        $user = Auth::user();
        if ($user->int_role_id == User::STAFF && $user->is_co_admin == 0) {
            $agent_id = $user->pk_int_user_id;
        } else {
            $agent_id = $request->agent;
        }
        //Sets the current page
        Paginator::currentPageResolver(function () use ($start, $length) {
            return ($start / $length + 1);
        });

        $vendorId = User::getVendorId();

        $settings = CloudTelephonySetting::where('vendor_id', $vendorId)
            ->where('status', 1)
            ->where('default', 1)
            ->first();

        if ($settings) {
            $dataQuery = $settings->operator == "2"
                ? CallMaster::select('vendor_id', 'caller_number', 'events', 'recording_url', 'call_status', 'direction', 'called_number', 'call_date', 'call_masters.created_at', 'conversation_duration')
                    ->where('vendor_id', $vendorId)
                    ->whereNotNull('call_status')
                    ->whereIn('events', ['Missed call', 'Cancel', 'Not Connected', 'NOANSWER', 'Incoming call'])
                    ->whereDate('call_masters.created_at', '>=', $from_date)
                    ->whereDate('call_masters.created_at', '<=', $to_date)
                    ->when($agent_id != 0, function ($query) use ($agent_id) {
                        $usr = User::select('mobile')->find($agent_id);
                        if ($usr) {
                            $query->where(function ($q) use ($usr) {
                                $q->where('called_number', 'like', "%" . $usr->mobile)
                                    ->orWhere('caller_number', 'like', "%" . $usr->mobile);
                            });
                        }
                    })
                    ->paginate(10)
                : Ivr::select('ivr.*', 'tbl_users.vchr_user_name')
                    ->where('vendor_id', $vendorId)
                    ->whereDate('ivr.created_at', '>=', $from_date)
                    ->whereDate('ivr.created_at', '<=', $to_date)
                    // ->when($agent_id != 0, function ($query) use ($agent_id) {
                    //     return $query->where('tbl_users.pk_int_user_id', $agent_id);
                    // })
                    ->where(function ($query) use ($agent_id) {
                        if ($agent_id != 0) {
                            $usr = User::select('vchr_user_mobile')->find($agent_id);
                            if ($usr)
                                return $query->where('agent_number', $usr->vchr_user_mobile);
                        }
                    })
                    ->whereIn('events', ['Missed call', 'Incoming call', 'Outgoing Call', 'Call Connected'])
                    ->leftJoin('tbl_users', 'tbl_users.mobile', '=', ltrim('ivr.agent_number', 0))
                    ->paginate(10);
        } else {
            $dataQuery = Ivr::select('ivr.*', 'tbl_users.vchr_user_name')
                ->where('vendor_id', $vendorId)
                ->where(function ($q) use ($from_date, $to_date) {
                    $q->whereDate('ivr.call_date', '>=', $from_date)
                        ->whereDate('ivr.call_date', '<=', $to_date);
                })
                ->where(function ($query) use ($agent_id) {
                    if ($agent_id != 0) {
                        $usr = User::select('vchr_user_mobile')->find($agent_id);
                        if ($usr)
                            return $query->where('agent_number', $usr->vchr_user_mobile);
                    }
                })
                ->whereIn('events', ['Missed call', 'Incoming call', 'Outgoing Call', 'Call Connected'])
                ->leftJoin('tbl_users', 'tbl_users.mobile', '=', ltrim('ivr.agent_number', 0))
                ->paginate(10);
        }

        $data = $dataQuery;

        foreach ($data as $key => $row) {
            $enq = $row->enquiryWeb;
            if ($enq) {
                $number = $enq->vchr_customer_name ?? $enq->vchr_customer_mobile;
            } else {
                $number = (in_array(auth()->user()->pk_int_user_id, Variables::IVR_NUMBER_RESTRICT)) ? '+' . '********' . substr($row->caller_number, -2) : '+' . $row->caller_number;
            }
            //  $date = Carbon::parse($row->created_at)->format('M d, h:m A');
            $date = Carbon::parse($row->created_at)->format('M d') . ', ' . Carbon::parse($row->created_at)->format('g:i A ');
            $minute = gmdate("H:i:s", (int)$row->conversation_duration);
            if ($settings) {
                if ($settings->operator == "2") {
                    if ($row->recording_url == null) {
                        $event = '<img src="/backend/call-task-media/assets/missed-call.svg">' . 'Missed';
                    } elseif ($row->call_status == 'ANSWERED') {
                        $event = '<img src="/backend/call-task-media/assets/call-r-3.svg">' . 'Answered';
                    } elseif ($row->call_status == 'BUSY') {
                        $event = '<img src="/backend/call-task-media/assets/call-r-5.svg">' . 'Busy';
                    } elseif ($row->call_status == 'CANCEL') {
                        $event = '<img src="/backend/call-task-media/assets/missed-call.svg">' . 'Cancel';
                    }

                    if ($row->direction == 'Inbound') {
                        $user = User::active()->where('mobile', substr($row->called_number, -10))->first();
                        $row->agent_name = $user ? $user->vchr_user_name : '';
                    } elseif ($row->direction == 'Outbound') {
                        $user = User::active()->where('mobile', substr($row->called_number, -10))->first();
                        $row->agent_name = $user ? $user->vchr_user_name : '';
                    } else {
                        $row->agent_name = 'No Name';
                    }

                    $row->caller_number = $number ? $number : 'No Number';
                    $row->events = $event;
                    $row->vchr_user_name = $row->agent_name;
                    $row->call_date = $date ?? 'No Date';
                    $row->conversation_duration = $minute;
                } else {
                    if ($row->events == 'Missed call') {
                        $event = '<img src="/backend/call-task-media/assets/missed-call.svg">' . 'Missed';
                    } elseif ($row->events == 'Incoming call') {
                        $event = '<img src="/backend/call-task-media/assets/call-r-3.svg">' . 'Answered';
                    } elseif ($row->events == 'Outgoing Call') {
                        $event = '<img src="/backend/call-task-media/assets/call-r-5.svg">' . 'OutBound';
                    } elseif ($row->events == 'CANCEL') {
                        $event = '<img src="/backend/call-task-media/assets/missed-call.svg">' . 'Cancelled';
                    } elseif ($row->events == 'Call Connected') {
                        $event = '<img src="/backend/call-task-media/assets/call-r-3.svg">' . 'Connected';
                    } else {
                        $event = "";
                    }
                    $row->caller_number = $number ? $number : 'No Number';
                    $row->events = $event;
                    $row->vchr_user_name = $row->agent_name;
                    $row->call_date = $date ?? 'No Date';
                    $row->conversation_duration = $minute;
                }
            } else {
                if ($row->events == 'Missed call') {
                    $event = '<img src="/backend/call-task-media/assets/missed-call.svg">' . 'Missed';
                } elseif ($row->events == 'Incoming call') {
                    $event = '<img src="/backend/call-task-media/assets/call-r-3.svg">' . 'Answered';
                } elseif ($row->events == 'Outgoing Call') {
                    $event = '<img src="/backend/call-task-media/assets/call-r-5.svg">' . 'OutBound';
                } elseif ($row->events == 'CANCEL') {
                    $event = '<img src="/backend/call-task-media/assets/missed-call.svg">' . 'Cancelled';
                } elseif ($row->events == 'Call Connected') {
                    $event = '<img src="/backend/call-task-media/assets/call-r-3.svg">' . 'Connected';
                } else {
                    $event = '';
                }
                $row->caller_number = $number ? $number : 'No Number';
                $row->events = $event;
                $row->vchr_user_name = $row->agent_name;
                $row->call_date = $date ?? 'No Date';
                $row->conversation_duration = $minute;
            }
        }

        return [
            'draw' => $draw,
            'recordsTotal' => $data->total(),
            'recordsFiltered' => $data->total(),
            'data' => $data
        ];
    }

    public function getAgentCallCount(Request $request)
    {
        $date = explode(' - ', $request->date);
        $from_date = $date[0];
        $to_date = $date[1];
        $vendorId = User::getVendorId();
        $user = Auth::user();
        if ($user->int_role_id == User::STAFF && $user->is_co_admin == 0) {
            $agent_id = $user->pk_int_user_id;
        } else {
            $agent_id = $request->agent;
        }
        $settings = CloudTelephonySetting::where('vendor_id', $vendorId)
            ->where('status', 1)
            ->where('default', 1)
            ->first();

        // if($settings){
        // if($settings->operator == "2"){
        // $ivr = CallMaster::with(['virtual_number','virtual_number.agent','agent'])
        //                 ->where('vendor_id',$vendorId)
        //                 ->whereDate('created_at','>=',$from_date)
        //                 ->whereDate('created_at','<=',$to_date)
        //                 ->where(function ($query) use($request) {
        //                     if($request->agent != 0){
        //                         $usr=User::select('mobile')->find($request->agent);
        //                         if($usr)
        //                             $query->where('called_number','like',"%".$usr->mobile)
        //                                     ->orWhere('caller_number',"%".$usr->mobile);
        //                     }
        //                 });
        //                 // ->whereNotNull('call_status');
        //                 // ->whereDate('created_at',today())
        // $ivrObj = $ivr;
        // $ivr =      $ivr->get()
        //                 ->unique(['called_number'])
        //                 ->values();
        // }else{
        //     $ivr = IVR::where('vendor_id',$vendorId)
        //                 ->whereDate('created_at','>=',$from_date)
        //                 ->whereDate('created_at','<=',$to_date)
        //                 ->when($request->agent !=0, function ($query, $agent) {
        //                     return $query->whereHas('agent', function ($query) use ($agent) {
        //                         $query->where('pk_int_user_id', $agent);
        //                     });
        //                  });
        //         // ->whereDate('created_at',today())
        //     $ivrObj = $ivr;
        //     $ivr=$ivr->get()
        //         ->unique('agent_number')
        //         ->values();
        // }
        // }else{
        //     $ivr = IVR::where('vendor_id',$vendorId)
        //             ->whereDate('created_at','>=',$from_date)
        //             ->whereDate('created_at','<=',$to_date)
        //             ->when($request->agent !=0, function ($query, $agent) {
        //                 return $query->whereHas('agent', function ($query) use ($agent) {
        //                     $query->where('pk_int_user_id', $agent);
        //                 });
        //              });;
        //         // ->whereDate('created_at',today())
        //     $ivrObj = $ivr;     
        //     $ivr  =  $ivr->get()
        //         ->unique('agent_number')
        //         ->values();
        // }

        if ($settings) {
            if ($settings->operator == "2") {
                $ivr = CallMaster::where('vendor_id', $vendorId)
                    ->whereDate('created_at', '>=', $from_date)
                    ->whereDate('created_at', '<=', $to_date)
                    ->where(function ($query) use ($agent_id) {
                        if ($agent_id != 0) {
                            $usr = User::select('mobile')->find($agent_id);
                            if ($usr)
                                $query->where('called_number', 'like', "%" . $usr->mobile)
                                    ->orWhere('caller_number', "%" . $usr->mobile);
                        }
                    });
                $ivrObj = $ivr;
            } else {
                $ivr = Ivr::where('vendor_id', $vendorId)
                    ->whereDate('created_at', '>=', $from_date)
                    ->whereDate('created_at', '<=', $to_date)
                    ->where(function ($query) use ($agent_id) {
                        if ($agent_id != 0) {
                            $usr = User::select('vchr_user_mobile')->find($agent_id);
                            if ($usr)
                                return $query->where('agent_number', $usr->vchr_user_mobile);
                        }
                    });
                $ivrObj = $ivr;
            }
        } else {
            $ivr = Ivr::where('vendor_id', $vendorId)
                ->whereDate('created_at', '>=', $from_date)
                ->whereDate('created_at', '<=', $to_date)
                ->where(function ($query) use ($agent_id) {
                    if ($agent_id != 0) {
                        $usr = User::select('vchr_user_mobile')->find($agent_id);
                        if ($usr)
                            return $query->where('agent_number', $usr->vchr_user_mobile);
                    }
                });
            $ivrObj = $ivr;
        }

        $defaultUser = json_decode(collect(['pk_int_user_id' => 0, 'vchr_user_name' => 'No Name', 'parent_user_id' => User::getVendorId(), 'mobile' => null, 'vchr_user_mobile' => null]));
        $users = User::select('pk_int_user_id', 'vchr_user_name', 'parent_user_id', 'mobile', 'vchr_user_mobile')
            ->where(function ($q) use ($user, $vendorId) {
                if ($user->int_role_id == User::STAFF && $user->is_co_admin == 0) {
                    $q->where('pk_int_user_id', $user->pk_int_user_id);
                } else if ($user->int_role_id == User::USERS || $user->is_co_admin == 1) {
                    $q->where('parent_user_id', User::getVendorId());
                }
            })
            // ->where('parent_user_id',User::getVendorId())
            ->get()->prepend($defaultUser);

        $result = [];
        foreach ($users as $key => $user) {
            $data = [];

            if ($settings) {
                if ($settings->operator == "2") {
                    $agent = $user;
                    $data['user_name'] = $agent ? $agent->vchr_user_name : 'No Name';
                    $data['count'] = 0;
                    $query = (clone $ivrObj)->where('vendor_id', $vendorId)
                        ->where(function ($q) use ($from_date, $to_date) {
                            $q->whereDate('created_at', '>=', $from_date)
                                ->whereDate('created_at', '<=', $to_date);
                        })
                        ->where(function ($where) use ($agent) {
                            if ($agent->pk_int_user_id == 0) {
                                $where->where('called_number', null)
                                    ->orWhere('called_number', 'None');
                            } else {
                                $where->where('called_number', 'LIKE', '%' . $agent->mobile);
                            }
                        });

                    if ($agent->pk_int_user_id == 0)
                        $count = $query->get(['caller_number', 'called_number', 'created_at', 'vendor_id', 'id'])->unique('caller_number')->count();
                    else
                        $count = $query->get(['caller_number', 'called_number', 'created_at', 'vendor_id', 'id'])->count();

                    $data['count'] = $count;
                } else {
                    $agent = ($user->mobile) ? User::active()->where('mobile', $user->mobile)->first() : null;
                    $data['user_name'] = $agent ? $agent->vchr_user_name : 'No Name';

                    // $agent = User::where('mobile',$user->agent_number)->first();
                    $count = (clone $ivrObj)->where(function ($where) use ($agent, $user) {
                        $where->where('agent_number', $user->vchr_user_mobile)
                            ->orWhereHas('virtual_number', function ($where) use ($agent) {
                                if ($agent)
                                    $where->where('agent_id', $agent->pk_int_user_id);
                            });
                    })->count();
                    $data['count'] = $count;
                }
            } else {
                $agent = $user;
                $count = (clone $ivrObj)->where(function ($where) use ($agent, $user) {
                    $where->where('agent_number', $user->vchr_user_mobile)
                        ->orWhereHas('virtual_number', function ($where) use ($agent) {
                            if ($agent)
                                $where->where('agent_id', $agent->pk_int_user_id);
                        });
                })->count();
                $data['count'] = $count;
            }

            if ($count != 0)
                array_push($result, $data);
        }

        return Datatables::of($result)
            ->addColumn('user_name', function ($result) {
                return $result['user_name'] ?? 'No name';
            })
            ->addColumn('count', function ($result) {
                return $result['count'] ?? 'No Count';
            })
            ->rawColumns(['user_name', 'count'])
            ->toJson(true);
    }

    public function campaignTask()
    {
        $vendorId = User::getVendorId();

        /** @var User $user */
        $user = Auth::user();

        if (auth()->user()->int_role_id == User::USERS || auth()->user()->is_co_admin == 1) {

            $taskQuery = Task::query()
                ->where('vendor_id', $vendorId)
                ->where('task_category_id', '!=', 1)
                ->where('campaign_id', '!=', 0);

            $counts = $taskQuery->selectRaw("
                COUNT(*) AS total_count,
                COUNT(CASE WHEN status = 1 THEN 1 END) AS completed,
                COUNT(CASE WHEN status = 0 AND scheduled_date >= ? THEN 1 END) AS pending,
                COUNT(CASE WHEN status = 0 AND scheduled_date < ? THEN 1 END) AS overdue
            ", [
                Carbon::today()->format('Y-m-d'),
                Carbon::today()->format('Y-m-d')
            ])
                ->first();

            $total_count = $counts->total_count;
            $completed = $counts->completed;
            $pending = $counts->pending;
            $overDue = $counts->overdue;
        } else {

            $taskQuery = Task::query()
                ->where('vendor_id', $vendorId)
                ->where('task_category_id', '!=', 1)
                ->where('campaign_id', '!=', 0)
                ->tap(new ApplyFilters([
                    new AgentFilter($user)
                ]));

            $counts = $taskQuery->selectRaw("
                COUNT(*) AS total_count,
                COUNT(CASE WHEN status = 1 THEN 1 END) AS completed,
                COUNT(CASE WHEN status = 0 AND scheduled_date >= ? THEN 1 END) AS pending,
                COUNT(CASE WHEN status = 0 AND scheduled_date < ? THEN 1 END) AS overdue
            ", [
                Carbon::today()->format('Y-m-d'),
                Carbon::today()->format('Y-m-d')
            ])
                ->first();

            $total_count = $counts->total_count;
            $completed = $counts->completed;
            $pending = $counts->pending;
            $overDue = $counts->overdue;
        }
        if ($vendorId == Variables::EZZAT_USER_ID && Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
            $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)
                ->pluck('staff_id')
                ->toArray();
            $agents = User::where(function ($where) use ($assignedStafId) {
                $where->where('pk_int_user_id', Auth::user()->pk_int_user_id)->orWhereIn('pk_int_user_id', $assignedStafId);
            })->whereNotIn('pk_int_user_id', ['3642', '4015'])->get();
        } else {
            $agents = User::where('parent_user_id', $vendorId)->orWhere('pk_int_user_id', $vendorId)
                ->select('pk_int_user_id as id', 'vchr_user_name as name');
            if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
                $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)
                    ->pluck('staff_id')
                    ->toArray();
                $agents = $agents->where(function ($where) use ($assignedStafId) {
                    $where->where('pk_int_user_id', Auth::user()->pk_int_user_id)
                        ->orWhereIn('pk_int_user_id', $assignedStafId);
                });
            }
            if (Auth::user()->pk_int_user_id == 3635) {
                $agents = $agents->get();
            } else {
                $agents = $agents->StaffCheck()
                    ->get();
            }

        }

        $assign = User::where('pk_int_user_id', $vendorId)
            ->first();
        $campaigns = LeadCampaign::where('vendor_id', $vendorId)
            ->select('id', 'name')
            ->get();
        $call_status = CallStatus::where('vendor_id', $vendorId)
            ->select('id', 'name')
            ->get();
        $categories = TaskCategory::where('vendor_id', $vendorId)
            ->orwhere('vendor_id', NULL)
            ->where('id', '!=', 2)
            ->select('id', 'name')
            ->get();
        $types = EnquiryType::where('int_status', '1')
            ->UsedOnly($vendorId)
            ->orWhere('vendor_id', $vendorId)
            ->get();

        $fields = EnquiryFieldCustomise::where('vendor_id', $vendorId)->first();
        if ($fields) {
            $active_fields = explode(',', $fields->hidden_fields);
            $required_fields = explode(',', $fields->required_fields);
        } else {
            $active_fields = null;
            $required_fields = null;
        }
        $countrys = Country::All();
        $countryCodeIp = "IN";
        $countryNewCheck = Country::where('country_code', $countryCodeIp)->first();
        if ($countryNewCheck) {
            $countryCode = $countryNewCheck->code;
        } else {
            $countryCode = '';
        }
        $district = District::where('vendor_id', $vendorId)
            ->where('status', 1)
            ->get();
        $taluk = Taluk::where('vendor_id', $vendorId)
            ->where('status', 1)
            ->get();
        $agencies = Agency::where('vendor_id', $vendorId)->select('id', 'name')->get();
        $purposes = EnquiryPurpose::where('fk_int_user_id', $vendorId)->get();
        $enquiry_status = FeedbackStatus::UsedStatuses($vendorId)->get();
        $date_by = collect();
        $date_by->push(['id' => 1, 'name' => 'Created']);
        $date_by->push(['id' => 2, 'name' => 'Updated']);
        $additional_fields = LeadAdditionalField::where('vendor_id', $vendorId)->get();
        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
            if ($additional_field->input_type == 3 || $additional_field->input_type == 5) {
                $date_by->push(['id' => $additional_field->id, 'name' => $additional_field->field_name]);
            }
        }

        return view('gl-crm.pages.user.tasks.campaign-task')
            ->with('agents', $agents)
            ->with('total_tasks', $total_count)
            ->with('completed', $completed)
            ->with('pending', $pending)
            ->with('overdue', $overDue)
            ->with('call_status', $call_status)
            ->with('assign', $assign)
            ->with('campaigns', $campaigns)
            ->with('categories', $categories)
            ->with('enquiry_status', $enquiry_status)
            ->with('enquiryType', $types)
            ->with('types', $types)
            ->with('active_fields', $active_fields)
            ->with('required_fields', $required_fields)
            ->with('countrys', $countrys)
            ->with('countryCode', $countryCode)
            ->with('district', $district)
            ->with('taluk', $taluk)
            ->with('agencies', $agencies)
            ->with('purposes', $purposes)
            ->with('additional_fields', $additional_fields)
            ->with('call_status', $call_status);
    }

    public function getCampaignTask(Request $request)
    {

        $date = explode(' - ', $request->all_date);
        $from_date = $date[0] . ' 00:00:00';
        $to_date = $date[1] . ' 23:59:59';

        $selectColumns = [
            'tasks.id', 'tasks.name',
            'tasks.description', 'tasks.scheduled_date',
            'tasks.task_category_id', 'tasks.task_order',
            'tasks.assigned_to', 'tasks.assigned_by',
            'tasks.vendor_id', 'tasks.enquiry_id',
            'tasks.comment', 'tasks.status',
            'task_categories.name as task_category',
            'tasks.campaign_id',
            'tbl_enquiries.vchr_customer_name as customer_name',
            'tbl_enquiries.vchr_customer_mobile as mobile',
            'tbl_users.vchr_user_name as assigned_user_name',
            't_user.vchr_user_name as assigned_by_user',
            'call_status.name as call_status', 'call_status_id',
            'call_status.name as call_status_name',
            'tbl_feedback_status.vchr_status as lead_status',
            'tbl_feedback_status.pk_int_feedback_status_id',
            'lead_campaigns.name as campaign_name',
            'tbl_enquiry_types.vchr_enquiry_type'
        ];

        /** @var User $user */
        $user = Auth::user();

        $tasksQ = Task::where('tasks.vendor_id', User::getVendorId())
            ->where('task_category_id', '!=', 1)
            ->where('tasks.campaign_id', '!=', 0)
            ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
            ->leftjoin('tbl_feedback_status', 'tbl_feedback_status.pk_int_feedback_status_id', '=', 'tbl_enquiries.feedback_status')
            ->leftjoin('tbl_enquiry_types', 'tbl_enquiry_types.pk_int_enquiry_type_id', '=', 'tbl_enquiries.fk_int_enquiry_type_id')
            ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->leftjoin('tbl_users as t_user', 'assigned_by', '=', 't_user.pk_int_user_id')
            ->leftJoin('task_categories', 'task_category_id', '=', 'task_categories.id')
            ->leftJoin('call_status', 'tasks.call_status_id', '=', 'call_status.id')
            ->leftJoin('lead_campaigns', 'lead_campaigns.id', '=', 'tasks.campaign_id')
            ->select($selectColumns)
            ->selectRaw("
                CASE
                    WHEN tasks.status = 1 THEN 'Completed'
                    WHEN tasks.scheduled_date < '" . Carbon::today()->toDateString() . "' AND status = 0 THEN 'OverDue'
                    WHEN tasks.scheduled_date >= '" . Carbon::today()->toDateString() . "' AND status = 0 THEN 'Pending'
                    ELSE 'Unknown'
                END AS task_status"
            )
            ->orderBy('tasks.id', 'DESC')
            ->when($request->has('call_status') && $request->filled('call_status'), function ($query) use ($request) {
                if (in_array("-1", $request->call_status)) {
                    $query->whereIn('call_status.id', $request->call_status)
                        ->orWhereNull('call_status.id');
                } else {
                    $query->whereIn('call_status.id', $request->call_status);
                }
            })
            ->when($request->has('lead_status') && $request->filled('lead_status'), function ($query) use ($request) {
                $query->where('tbl_feedback_status.pk_int_feedback_status_id', $request->lead_status);
            })
            ->when($request->has('assigned_by') && $request->filled('assigned_by'), function ($query) use ($request) {
                $query->where('assigned_by', $request->assigned_by);
            })
            ->when($request->has('campaign_id') && $request->filled('campaign_id'), function ($query) use ($request) {
                $query->where('campaign_id', $request->campaign_id);
            })
            ->when($request->has('assigned_to') && $request->filled('assigned_to'), function ($query) use ($request) {
                $query->where('assigned_to', $request->assigned_to);
            })
            ->when($request->has('lead_source') && $request->filled('lead_source'), function ($query) use ($request) {
                $query->where('tbl_enquiry_types.pk_int_enquiry_type_id', $request->lead_source);
            })
            ->when($request->has('status') && $request->filled('status'), function ($query) use ($request) {
                if ($request->status == 1) {
                    $query->where('status', 1);
                } elseif ($request->status == 2) {
                    $query->where('status', 0)
                        ->where('scheduled_date', '<', Carbon::today());
                } else {
                    $query->where('status', 0)
                        ->where('scheduled_date', '>=', Carbon::today());
                }
            })
            ->when($request->has('all_date') && $request->filled('all_date'), function ($query) use ($from_date, $to_date) {
                $query->whereBetween('tasks.created_at', [$from_date, $to_date]);
            })
            ->when($request->has('filter') && $request->filled('filter'), function ($query) use ($request) {
                if ($request->filter == 'completed') {
                    $query->where('status', 1);
                } elseif ($request->filter == 'overdue') {
                    $query->where('status', 0)->whereDate('scheduled_date', '<', Carbon::today());
                } elseif ($request->filter == 'pending') {
                    $query->where('status', 0)->whereDate('scheduled_date', '>=', Carbon::today());
                }
            })->tap(new ApplyFilters([
                new AgentFilter($user),
            ]));

        return (new EloquentDataTable($tasksQ))
            ->addIndexColumn()
            ->filterColumn('slno', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('task_category', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('mobile', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('assigned_by_user', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('assigned_user_name', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('lead_status', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('vchr_enquiry_type', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('task_status', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->filterColumn('call_status_name', function ($query, $keyword) {
                // Prevent DT_RowIndex from being used in search queries
            })
            ->addColumn('assign_agent', function ($tasks) {
                return ' <div class="form-check form-check-assign" >
                            <input id="checkbox2" type="checkbox" data-enq_id="' . $tasks->id . '">
                        </div>';
            })
            ->addColumn('lead_info', function ($task) {
                if ($task->customer_name == NULL) {
                    return '<span style="display:block;width:100px;word-wrap:break-word;white-space: normal;"><a class="ks-izi-modal-trigger3" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' . $task->enquiry_id . '">' . $task->vchr_customer_mobile . '</a></span>';
                } else {
                    return '<span style="display:block;width:100px;word-wrap:break-word;white-space: normal;"><a class="ks-izi-modal-trigger3" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' . $task->enquiry_id . '">' . $task->customer_name . '</a></span>';
                }
            })
            ->addColumn('selete_task', function ($task) {
                return '<div class="form-check form-check-assign" >
                       <input id="checkbox2" type="checkbox" name="seleteTask" value="' . $task->id . '" data-task_id="' . $task->id . '">
                   </div>';
            })
            ->editColumn('assigned_by', function ($task) {
                if ($task->assigned_by == Auth::user()->pk_int_user_id) {
                    return "You";
                } else {
                    return $task->assigned_by_user;
                }
            })
            ->editColumn('scheduled_date', function ($task) {
                return $task->scheduled_date = Carbon::parse($task->scheduled_date)->format('d M Y h:i A');

            })
            ->editColumn('description', function ($task) {
                return '<p>' . $task->description . '</p>';
            })
            ->addColumn('show', function ($task) {
                return
                    '
                        <div class="dropdown show">
                            <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa fa-circle" aria-hidden="true"></i>
                                <i class="fa fa-circle" aria-hidden="true"></i>
                                <i class="fa fa-circle" aria-hidden="true"></i>   
                            </a>
                            <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                <a class="dropdown-item task-delete" href="#" id="delete_plan" task-id="' . $task->id . '">
                                    <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete
                                </a>
                            </div>
                        </div>';
            })
            ->editColumn('call_status_name', function ($task) {
                return $task->call_status_name = $task->call_status_name ?? 'Action Pending';
            })
            ->addColumn('campaign_name', function ($task) {
                return $task->campaign_name ?? "No Campaign Name";
            })
            ->rawColumns(['selete_task', 'show', 'lead_info', 'description'])
            ->toJson();
    }

    public function dealTaskIndex()
    {
        $vendorId = User::getVendorId();
        if (auth()->user()->int_role_id == User::USERS || auth()->user()->is_co_admin == 1) {
            $tasks = DealTask::where('vendor_id', $vendorId);
            $total_count = $tasks->count();

            $completed = (clone $tasks)->where('status', 1)->count();
            $pending = (clone $tasks)->where('status', 0)
                ->where('vendor_id', $vendorId)
                ->whereDate('scheduled_date', '>=', Carbon::today())
                ->count();
            $overDue = (clone $tasks)->where('status', 0)
                ->whereDate('scheduled_date', '<', Carbon::today())
                ->where('vendor_id', $vendorId)->count();
        } else {
            $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->pluck('staff_id')->toArray();

            $tasks = DealTask::where(function ($where) use ($assignedStafId) {
                $where->where('assigned_to', Auth::id())->orWhereIn('assigned_to', $assignedStafId);
            });
            $total_count = $tasks->count();
            $completed = (clone $tasks)->where('status', 1)->count();
            $pending = (clone $tasks)->where('status', 0)
                ->where(function ($where) use ($assignedStafId) {
                    $where->where('assigned_to', Auth::id())->orWhereIn('assigned_to', $assignedStafId);
                })
                ->whereDate('scheduled_date', '>=', Carbon::today())
                ->count();
            $overDue = (clone $tasks)->where('status', 0)
                ->where(function ($where) use ($assignedStafId) {
                    $where->where('assigned_to', Auth::id())->orWhereIn('assigned_to', $assignedStafId);
                })
                ->whereDate('scheduled_date', '<', Carbon::today())
                ->count();
        }

        $agents = User::active()->where('parent_user_id', $vendorId)->select('pk_int_user_id as id', 'vchr_user_name as name');
        if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
            $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->pluck('staff_id')->toArray();
            $agents = $agents->where(function ($where) use ($assignedStafId) {
                $where->where('pk_int_user_id', Auth::user()->pk_int_user_id)->orWhereIn('pk_int_user_id', $assignedStafId);
            });
        }
        $agents = $agents->get();
        $categories = TaskCategory::where('vendor_id', $vendorId)
            ->orwhere('vendor_id', NULL)
            ->select('id', 'name')
            ->get();
        $call_status = CallStatus::where('vendor_id', $vendorId)
            ->orwhere('vendor_id', NULL)
            ->select('id', 'name')
            ->get();
        $assign = User::where('pk_int_user_id', $vendorId)->first();
        return view('gl-crm.pages.user.tasks.deal_tasks')->with('agents', $agents)
            ->with('total_tasks', $total_count)
            ->with('completed', $completed)
            ->with('pending', $pending)
            ->with('overdue', $overDue)
            ->with('call_status', $call_status)
            ->with('assign', $assign)
            ->with('categories', $categories);

    }

    public function getDealTasks(Request $request)
    {

        $date = explode(' - ', $request->all_date);
        $from_date = $date[0];
        $to_date = $date[1];

        $selectColumns = [
            'tasks.id', 'tasks.name',
            'tasks.description', 'tasks.scheduled_date',
            'tasks.task_category_id', 'tasks.task_order',
            'tasks.assigned_to', 'tasks.assigned_by',
            'tasks.vendor_id', 'tasks.enquiry_id',
            'tasks.comment', 'tasks.status',
            'task_categories.name as task_category',
            'tbl_enquiries.vchr_customer_name as customer_name',
            'tbl_enquiries.vchr_customer_mobile',
            'tbl_users.vchr_user_name as assigned_user_name',
            't_user.vchr_user_name as assigned_by_user',
            'call_status.name as call_status'
        ];

        $tasksQ = DealTask::where('tasks.vendor_id', User::getVendorId())
            ->lefJoin('task_categories', 'task_category_id', '=', 'task_categories.id')
            ->leftjoin('tbl_enquiries', 'enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
            ->leftjoin('tbl_users', 'assigned_to', '=', 'tbl_users.pk_int_user_id')
            ->leftjoin('tbl_users as t_user', 'assigned_by', '=', 't_user.pk_int_user_id')
            ->leftJoin('call_status', 'tasks.call_status_id', '=', 'call_status.id')
            ->select($selectColumns)
            ->orderBy('tasks.id', 'DESC');

        if ($request->has('assigned_by') && $request->filled('assigned_by')) {
            $tasksQ = (clone $tasksQ)->where('assigned_by', $request->assigned_by);
        }
        if ($request->has('category_id') && $request->filled('category_id')) {
            $tasksQ = (clone $tasksQ)->where('task_category_id', request('category_id'));
        }
        if ($request->has('assigned_to') && $request->filled('assigned_to')) {
            $tasksQ = (clone $tasksQ)->where('assigned_to', $request->assigned_to);
        }
        if ($request->has('status') && $request->filled('status')) {
            if ($request->status == 1) {
                $tasksQ = (clone $tasksQ)->where('status', 1);
            } elseif ($request->status == 2) {
                $tasksQ = (clone $tasksQ)->where('status', 0)
                    ->where('scheduled_date', '<', Carbon::today());
            } else {
                $tasksQ = (clone $tasksQ)->where('status', 0)
                    ->where(function ($q) {
                        $q->where('scheduled_date', '>=', Carbon::today())
                            ->orWhere('scheduled_date', '=', '0000-00-00 00:00:00');
                    });
            }
        }
        if ($request->has('all_date') && $request->filled('all_date')) {
            $tasksQ = $tasksQ
                ->whereDate('tasks.created_at', '>=', $from_date)
                ->whereDate('tasks.created_at', '<=', $to_date);
        }
        if ($request->has('filter') && $request->filled('filter')) {
            if ($request->filter == 'completed') {
                (clone $tasksQ)->where('status', 1);
            } else if ($request->filter == 'overdue') {
                $tasksQ = (clone $tasksQ)->where('status', 0)->whereDate('scheduled_date', '<', Carbon::today());
            } else if ($request->filter == 'pending') {
                $tasksQ = (clone $tasksQ)->where('status', 0)->whereDate('scheduled_date', '>=', Carbon::today());
            }
        }
        if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
            $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->pluck('staff_id')->toArray();
            $tasksQ = (clone $tasksQ)->where(function ($where) use ($assignedStafId) {
                $where->where('assigned_to', Auth::user()->pk_int_user_id)->orWhereIn('assigned_to', $assignedStafId);
            });
        }

        $tasks = $tasksQ->get();
        foreach ($tasks as $key => $row) {

            if ($row->status == 1) {
                $row->task_status = "Completed";
            } elseif ($row->scheduled_date < Carbon::today() && $row->status == 0) {
                $row->task_status = "OverDue";
            } elseif (($row->scheduled_date >= Carbon::today() || $row->scheduled_date = '0000-00-00 00:00:00') && $row->status == 0) {
                $row->task_status = "Pending";
            }

            $row->slno = ++$key;
        }
        return Datatables::of($tasks)
            ->addColumn('assign_agent', function ($tasks) {
                return ' <div class="form-check form-check-assign" >
      <input id="checkbox2" type="checkbox" data-enq_id="' . $tasks->id . '">
     
        </div>';
            })
            ->addColumn('lead_info', function ($task) {
                if ($task->customer_name == NULL) {
                    return '<span style="display:block;width:100px;word-wrap:break-word;white-space: normal;"><a class="ks-izi-modal-trigger3" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' . $task->enquiry_id . '">' . $task->vchr_customer_mobile . '</a></span>';
                } else {
                    return '<span style="display:block;width:100px;word-wrap:break-word;white-space: normal;"><a class="ks-izi-modal-trigger3" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' . $task->enquiry_id . '">' . $task->customer_name . '</a></span>';
                }
            })
            ->addColumn('selete_task', function ($task) {
                return '<div class="form-check form-check-assign" >
                       <input id="checkbox2" type="checkbox" name="seleteTask" value="' . $task->id . '" data-task_id="' . $task->id . '">
                   </div>';
            })
            ->editColumn('assigned_by', function ($task) {
                if ($task->assigned_by == Auth::user()->pk_int_user_id) {
                    return "You";
                } else {
                    return $task->assigned_by_user;
                }
            })
            ->editColumn('scheduled_date', function ($task) {
                if ($task->scheduled_date != '0000-00-00 00:00:00')
                    return Carbon::parse($task->scheduled_date)->format('d M Y h:i A');
                else
                    return null;

            })
            ->addColumn('show', function ($task) {
                return
                    '<button id="delete_plan" task-id="' . $task->id . '" class="btn btn-sm btn-danger mg-b-10 task-delete"> <i class="fa fa-trash-o mg-r-5"></i></button>
                    <button  data-task_id="' . $task->id . '" class="btn btn-sm btn-primary mg-b-10 task_edit"> <i class="fa fa-edit mg-r-5"></i></button>';
            })
            ->rawColumns(['selete_task', 'show', 'lead_info'])
            ->toJson(true);
    }

    public function reschdeuleTask(Request $request, $id)
    {
        try {
            $task = Task::find($id);
            if (request('re_scheduled_date') != " " && request('time_task') != "") {
                $date_time = Carbon::parse(request('re_scheduled_date'))->format('Y-m-d') . ' ' . Carbon::createFromFormat('H:i A', request('time_task'))->toTimeString();
            } else {
                $date_time = request('re_scheduled_date');
            }

            if ($task) {
                $old_date = $task->scheduled_date;
                $task->scheduled_date = $date_time;
                $task->save();
                TaskHistory::create([
                    'task_id' => $id,
                    'task_status' => 'Rescheduled',
                    'updated_by' => Auth::user()->pk_int_user_id,
                    'vendor_id' => User::getVendorId(),
                    'old_date' => ($old_date) ? $old_date : null,
                    'date' => $date_time,
                    'comment' => $request->reason,
                ]);
            } else {
                return response()->json(['status' => false, 'message' => 'Task not valid'], 200);
            }
        } catch (\Exception $exception) {
            \Log::info("Error Occurred While Updating (done) Task through API:" . $exception->getMessage());
            return response()->json(['status' => false, 'message' => $exception->getMessage()], 200);
        }
        return response()->json(['status' => true, 'message' => 'Task Successfully rescheduled'], 200);
    }

    public function taskCanbanPage()
    {
        $assign = User::where('pk_int_user_id', User::getVendorId())->first();
        $categories = TaskCategory::where('vendor_id', User::getVendorId())
            ->orwhere('vendor_id', NULL)
            ->where('id', '!=', 2)
            ->select('id', 'name')
            ->get();
        $call_status = CallStatus::where('vendor_id', User::getVendorId())
            ->orWhere('vendor_id', NULL)
            ->select('id', 'name')
            ->get();
        return view('gl-crm.pages.user.leads.reports.task-canban')
            ->with('assign', $assign)
            ->with('categories', $categories)
            ->with('call_status', $call_status);
    }

    public function assignCampaignToCampaign(Request $request)
    {
        $task_ids = explode(',', $request->get('camp_task_ids'));

        $leadIds = Task::query()->where('id', $task_ids)->pluck('enquiry_id');

        $campaign = LeadCampaign::query()->where('id', $request->int('campaign_id'))->firstOrFail();

        AttachLeadsToCampaign::dispatch($campaign, $leadIds, auth()->user()->pk_int_user_id);

        return response()->json(['msg' => " Lead add to Campaign", 'status' => true]);

    }

    public function callDurationReport(Request $request)
    {
        $today = Carbon::today();

        return view('gl-crm.pages.user.tasks.call-duration-report', compact('today'));
    }

    public function callDurationData(Request $request)
    {
        $user = Auth::user();
        $userId = $user->pk_int_user_id;
        $roleId = $user->int_role_id;
        $vendorId = User::getVendorId();

        if ($roleId == User::STAFF && $user->is_co_admin == 0) {
            $agents = [$userId];
        } elseif ($roleId == User::USERS || $user->is_co_admin == 1) {
            $agents = User::where('parent_user_id', $vendorId)->orWhere('pk_int_user_id', $vendorId)->pluck('pk_int_user_id')->toArray();
        } elseif ($roleId == User::STAFF && $user->is_co_admin == 1) {
            $agents = User::where('parent_user_id', $vendorId)->orWhere('pk_int_user_id', $vendorId)->pluck('pk_int_user_id')->toArray();
        } else {
            $agents = [];
        }
        $date = explode(' - ', $request->all_date);
        $from_date = $date[0];
        $to_date = $date[1];

        $user_id = count($agents) > 0 ? $agents : null;
        $callData = CallLog::whereIn('user_id', $user_id)
            ->where(function ($q) use ($from_date, $to_date) {
                $q->whereDate('date', '>=', $from_date)
                    ->whereDate('date', '<=', $to_date);
            })
            ->get();

        return response()->json(['result' => $callData]);

    }
}
