<?php

namespace App\Http\Controllers\GlCRM\User;

use App\BackendModel\Settings;
use App\Common\Variables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use App\Currency;
use App\StaffWorkMode;
use App\UserDealLocale;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;

class CrmSettingsController extends Controller
{
    public function crmSettingsPage(Request $request)
    {
        return view('gl-crm.pages.user.crm-settings.index');
    }

    public function accountSettingsPage(Request $request)
    {
        return view('gl-crm.pages.user.crm-settings.account-settings');
    }

    public function generalSettings()
    {
        $filePath = public_path('timezone.json');
        $currencies = Currency::where('status', 1)->get();
        $jsonContent = File::get($filePath);
        $timezones = json_decode($jsonContent);
        return view('gl-crm.pages.user.general-settings.index', compact('currencies', 'timezones'));
    }

    public function updateGeneralSettings()
    {
        try {
            $settingsType = request('tag');
            $status = request('status');
            $vendorId = User::getVendorId();
            $reload = false;

            $global_label = '';
            $global_action = '';
            $updatedValue = null;


            if ($settingsType === 'currency') {

                if ($status != '')
                    $global_label = 'Currency enabled.';
                else
                    $global_label = 'Currency disabled.';

                $settings = Settings::where('vchr_settings_type', $settingsType)->where('fk_int_user_id', $vendorId)->first();
                if (!$settings) {
                    $settings = new Settings();
                    $settings->vchr_settings_type = $settingsType;
                    $settings->vchr_settings_value = $status;
                    $settings->fk_int_user_id = $vendorId;
                    $settings->save();
                } else {
                    $settings->vchr_settings_type = $settingsType;
                    $settings->vchr_settings_value = $status;
                    $settings->fk_int_user_id = $vendorId;
                    $settings->save();
                }
            } else if ($settingsType === 'change-deal-name') {
                // Update or create the deal locale for the vendor
                $dealLocale = UserDealLocale::updateOrCreate(
                    ['vendor_id' => $vendorId],
                    ['locale_name' => $status]
                );

                // Save the value to cache with vendor-specific key
                Cache::put('vendor_' . $vendorId . '_deal_locale', $status, now()->addYear());
            } else {

                if ($settingsType == 'timezone') {
                    $user = User::find(auth()->user()->pk_int_user_id)->update(['time_zone' => request('value')]);
                    $global_action = 'changed.';
                }

                $settings = Settings::where('vchr_settings_type', $settingsType);
                $userSettings = (clone $settings)->whereJsonContains('vchr_settings_value', $vendorId)->first();

                if ($status === 'true' && $settingsType != 'timezone') {
                    $updatedValue = (clone $settings)->first() ? array_unique(array_merge(json_decode((clone $settings)->first()->vchr_settings_value, true) ?? [], [$vendorId])) : json_encode([$vendorId]);
                    $global_action = 'enabled.';
                } else {
                    $updatedValue = ($userSettings) ? array_values(array_diff(json_decode($userSettings->vchr_settings_value, true) ?? [], [$vendorId])) : null;
                    $global_action = 'disabled.';
                }

                $settings = Settings::where('vchr_settings_type', $settingsType);

                $userSettings = (clone $settings)->whereJsonContains('vchr_settings_value', $vendorId)->first();

                if ($status === 'true') {
                    $updatedValue = (clone $settings)->first() ? array_unique(array_merge(json_decode((clone $settings)->first()->vchr_settings_value, true) ?? [], [$vendorId])) : json_encode([$vendorId]);
                    $global_action = 'enabled.';
                } else {
                    $updatedValue = ($userSettings) ? array_values(array_diff(json_decode($userSettings->vchr_settings_value, true) ?? [], [$vendorId])) : null;
                    $global_action = 'disabled.';
                }
                if ($settingsType === 'scratch-bypass') {
                    $global_label = 'Scratch bypass ';
                } else if ($settingsType === 'global-search') {
                    $global_label = 'Global search ';
                    $reload = true;
                } else if ($settingsType === 'branch-filter') {
                    $global_label = 'Branch vise filter ';
                } else if ($settingsType === 'number-masking') {
                    $global_label = 'Number Masking ';
                } else if ($settingsType === 'web-notification') {
                    $global_label = 'Web Notification ';
                } else if ($settingsType === 'web-sound') {
                    $global_label = 'Web Sound ';
                } else if ($settingsType === 'web-ivr') {
                    $global_label = 'Web IVR Calling ';
                } else if ($settingsType === 'attendance-status') {
                    $global_label = 'Attendance Status ';
                } else if ($settingsType === 'ivr-app-notification') {
                    $global_label = 'IVR App Notification ';
                } else if ($settingsType === 'timezone') {
                    $global_label = 'Timezone settings ';
                } else if ($settingsType === 'deleted-agent-in-filter') {
                    $global_label = 'Deleted Agent ';
                }

                if ($settings->first()) {
                    $settings = $settings->first();
                    if (empty($updatedValue)) {
                        $settings->delete();
                    } else {
                        $settings->vchr_settings_value = json_encode($updatedValue);
                        $settings->save();
                    }
                } else {
                    $newSettings = new Settings();
                    $newSettings->vchr_settings_type = $settingsType;
                    $newSettings->vchr_settings_value = json_encode([$vendorId]);
                    $newSettings->save();
                }
            }

            // invalidate cache
            cache()->forget('purpose_status_list' . $vendorId);

            return response(['label' => $global_label, 'action' => $global_action, 'status' => 'success', 'reload' => $reload]);

        } catch (\Exception $e) {

            \Log::info($e->getMessage());
            return response(['msg' => 'Something Went Wrong', 'status' => 'failed']);
        }
    }
}
