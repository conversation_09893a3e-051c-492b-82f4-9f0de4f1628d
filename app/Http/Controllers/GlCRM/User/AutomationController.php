<?php

namespace App\Http\Controllers\GlCRM\User;

use App\AgentDepartment;
use App\AutomationRule;
use App\BackendModel\EnquiryType;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\WhatsappTemplate;
use App\Common\Variables;
use App\Department;
use App\FrontendModel\LeadAdditionalField;
use App\SmsDltTemplate;
use App\TaskCategory;
use App\User;
use App\WebHook;
use App\BackendModel\Enquiry;
use Getlead\Sales\Models\Product;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use DataTables;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Getlead\Campaign\Models\LeadCampaign;
use App\BackendModel\EmailTemplateHtml;
use DB;
use App\AutomationTrigger;

class AutomationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $additional_fields = LeadAdditionalField::where('vendor_id', User::getVendorId())->whereIn('input_type', [2, 8])->get();
        foreach ($additional_fields as $key => $additional_field) {
            $additional_field->values = json_decode($additional_field->values, true);
        }
        $triggers = AutomationTrigger::get();
        return view('gl-crm.pages.user.automation.index', compact('additional_fields', 'triggers'));
    }

    public function listActions(Request $request)
    {
        $actions = DB::table('automation_actions')
            ->select('automation_actions.*')
            ->leftJoin('automation_action_triggers', 'automation_action_triggers.action_id', '=', 'automation_actions.id')
            ->where('automation_action_triggers.trigger_id', $request->trigger)->get();
        return response()->json(['msg' => "action list.", 'status' => true, 'data' => $actions]);
    }

    public function getRules()
    {

        $rules = AutomationRule::query()
            ->where('automation_rules.vendor_id', User::getVendorId())
            ->join('tbl_users', 'created_by', '=', 'tbl_users.pk_int_user_id')
            ->leftjoin('web_hooks', 'webhook_id', '=', 'web_hooks.id')
            ->leftjoin('tbl_feedback_status', 'feedback_status_id', '=', 'tbl_feedback_status.pk_int_feedback_status_id')
            ->leftjoin('sms_dlt_templates', 'sms_template_id', '=', 'sms_dlt_templates.id')
            ->leftjoin('tbl_whatsapp_template', 'whatsapp_template_id', '=', 'tbl_whatsapp_template.pk_int_whatsapp_template_id')
            ->select('automation_rules.*', 'tbl_users.vchr_user_name', 'web_hooks.url', 'web_hooks.name as we_name', 'vchr_whatsapp_template_title')
            ->orderby('automation_rules.id', 'DESC')
            ->get();
        foreach ($rules as $key => $row) {
            $row->slno = ++$key;
        }
        return Datatables::of($rules)
            ->editColumn('added_by', function ($rule) {
                if ($rule->created_by == Auth::user()->pk_int_user_id) {
                    return "You";
                } else {
                    return $rule->vchr_user_name;
                }
            })
            ->addColumn('trigger_type', function ($rule) {
                if ($rule->trigger == "new_lead") {
                    return "New Enquiry";
                } elseif ($rule->trigger == "service_remider") {
                    return "Service Remider";
                } elseif ($rule->trigger == "new_scratch") {
                    return "New Scratch";
                } elseif ($rule->trigger == "new_luckydraw") {
                    return "New Lucky Draw";
                } elseif ($rule->trigger == "source_change") {
                    return "Source Change";
                } elseif ($rule->trigger == "value_change") {
                    return "Value Change";
                } elseif ($rule->trigger == "timer") {
                    return "Timer";
                } elseif ($rule->trigger == "notification") {
                    return "Notification";
                } elseif ($rule->trigger == "reassign") {
                    return "Reassign";
                } elseif ($rule->trigger == "purpose_change") {
                    return "Purpose change";
                } else {
                    return "Status Change";
                }
            })
            ->addColumn('rule_action', function ($rule) {
                return strtoupper($rule->action);
            })
            ->editColumn('url', function ($rule) {
                if ($rule->action == "whatsapp") {
                    return $rule->vchr_whatsapp_template_title;
                } elseif ($rule->action == "api") {
                    return $rule->api;
                }
            })
            ->addColumn('show', function ($rule) {

                return '<div class="dropdown show">
                            <a class="btn dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa fa-circle" aria-hidden="true"></i>
                                <i class="fa fa-circle" aria-hidden="true"></i>
                                <i class="fa fa-circle" aria-hidden="true"></i>
                            </a>
                            <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                <a href="#" data-rule-id="' . $rule->id . '" class="dropdown-item rule_edit" data-target="#edit_modal_rule"  data-toggle="modal">
                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32"><path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4l15-15zm-5-5L24 7.6l-3 3L17.4 7l3-3zM6 22v-3.6l10-10l3.6 3.6l-10 10H6z"/></svg>
                                &nbsp;Edit
                                </a>
                                <a href="#" rule_id="' . $rule->id . '" class="dropdown-item rule-delete px-3">
                                <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;Delete
                                </a>
                            </div>
                        </div>';
            })
            ->rawColumns(['show'])
            ->toJson(true);


    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getWebhookList()
    {
        $vendorId = User::getVendorId();
        $webhooks = WebHook::where('vendor_id', $vendorId)
            ->select('id', 'name')->get();
        $status = FeedbackStatus::where('fk_int_user_id', $vendorId)
            ->get();
        $enquiry_source = EnquiryType::where('int_status', '1')
            ->where('fk_int_user_id', $vendorId)
            ->select('pk_int_enquiry_type_id as id', 'vchr_enquiry_type')->get();

        $data['webhooks'] = $webhooks;
        $data['status'] = $status;
        $data['source'] = $enquiry_source;
        return response()->json(['msg' => "Webhook found.", 'status' => true, 'data' => $data]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $rules = [
            'name' => 'required',
            'trigger' => 'required',
            'action' => 'required',
            'webhook_id' => "required_if:action,==,webhook",
            'task_category_id' => "required_if:action,==,task",
            'task_title' => "required_if:action,==,task",
            'task_description' => "required_if:action,==,task",
            'task_assigned_to' => "required_if:action,==,task",
            'enquiry_purpose_id' => "required_if:trigger,==,purpose_change"
        ];
        $rulesMessage = [
            'name.required' => 'Required Field.',
            'trigger.required' => 'Required Field.',
            'action.required' => 'Required Field.',
        ];
        if (is_array($request->feedback_status_id)) {
            $status = implode(",", $request->feedback_status_id);
        } else {
            $status = $request->feedback_status_id;
        }

        $validator = validator($request->all(), $rules, $rulesMessage);
        if ($validator->passes()) {
            if ($request->trigger == 'service_remider') {
                $day = $request->reminder_type . $request->days;
            } else {
                $day = $request->days;
            }

            AutomationRule::create([
                'rule_name' => $request->name,
                'vendor_id' => User::getVendorId(),
                'created_by' => auth()->user()->pk_int_user_id,
                'trigger' => $request->trigger,
                'action' => $request->action,
                'webhook_id' => $request->webhook_id,
                'feedback_status_id' => $status ?? NULL,
                'service_id' => $request->service ? implode(",", $request->service) : null,
                'sms_template_id' => $request->sms_template_id,
                'whatsapp_template_id' => $request->whatsapp_template_id,
                'enquiry_purpose_id' => $request->enquiry_purpose_id,
                'log_day' => $day,
                'api' => $request->api,
                'task_category_id' => $request->task_category_id,
                'task_title' => $request->task_title,
                'task_description' => $request->task_description,
                'task_assigned_to' => $request->task_assigned_to,
                'enquiry_source_id' => $request->enquiry_source_id,
                'assign_mode' => $request->assign_mode,
                'campaign_id' => $request->campaign_id,
                'assign_id' => $request->assign_id,
                'duration' => $request->duration ?? $request->timer_input,
                'additional_field' => request()->has('field') ? request('field') : NULL,
                'additional_field_value' => request()->has('field_value') ? request('field_value') : NULL,
                'email_template' => $request->emplate_id ?? Null,
                'notification_type' => $request->notification_type ?? Null
            ]);
            return response()->json(['msg' => "Rule Added.", 'status' => 'success']);
        } else {
            return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);

        }


    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $rule = AutomationRule::where('id', $id)->first();
        if ($rule) {
            return response()->json(['msg' => "Rule found.", 'status' => 'success', 'data' => $rule]);
        } else {
            return response()->json(['msg' => "Rule not  found.", 'status' => 'fail']);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        $rule = AutomationRule::find($id);
        if ($rule) {
            $validator = validator($request->all(), ['name' => 'required'], ['name' => 'Required']);
            if ($validator->passes()) {
                if (is_array($request->feedback_status_id)) {
                    $status = implode(",", $request->feedback_status_id);
                } else {
                    $status = $request->feedback_status_id;
                }

                AutomationRule::where('id', $id)
                    ->update([
                        'rule_name' => $request->name,
                        'updated_by' => auth()->user()->pk_int_user_id,
                        'trigger' => $request->trigger,
                        'action' => $request->action,
                        'webhook_id' => $request->webhook_id,
                        'feedback_status_id' => $status,
                        'sms_template_id' => $request->sms_template_id,
                        'whatsapp_template_id' => $request->whatsapp_template_id,
                        'log_day' => $request->days,
                        'api' => $request->api,
                        'task_category_id' => $request->task_category_id,
                        'task_title' => $request->task_title,
                        'task_description' => $request->task_description,
                        'task_assigned_to' => $request->task_assigned_to,
                        'enquiry_source_id' => $request->enquiry_source_id,
                        'assign_mode' => $request->rule_assign_mode,
                        'assign_id' => $request->rule_assign_id,
                        'enquiry_purpose_id' => $request->enquiry_purpose_id,
                        'duration' => $request->duration ?? $request->timer_input_edit,
                        'campaign_id' => $request->campaign_id,
                        'additional_field' => request()->has('field_edit') ? request('field_edit') : NULL,
                        'additional_field_value' => request()->has('field_value_edit') ? request('field_value_edit') : NULL,
                        'email_template' => $request->edit_emplate_id ?? Null,
                        'notification_type' => $request->notification_type_edit ?? Null
                    ]);
                return response()->json(['msg' => "Rule  Updated.", 'status' => 'success']);

            } else {
                return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $rule = AutomationRule::find($id);
        if ($rule) {
            $rule->delete();
            return response()->json(['msg' => "Rule  deleted.", 'status' => 'success']);
        } else {
            return response()->json(['msg' => "Rule not found.", 'status' => 'fail']);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function getAutoData(Request $request)
    {
        $vendorId = User::getVendorId();
        $status = FeedbackStatus::where('fk_int_user_id', $vendorId)
            ->get();

        $enquiry_source = EnquiryType::query()
            ->where('int_status', '1')
            ->UsedOnly($vendorId)
            ->orWhere('vendor_id', $vendorId)
            ->select('pk_int_enquiry_type_id as id', 'vchr_enquiry_type')->get();

        $enquiry_purpose = EnquiryPurpose::where('fk_int_user_id', $vendorId)
            ->select('pk_int_purpose_id as id', 'vchr_purpose')->get();

        $agents = User::where('parent_user_id', $vendorId)->orWhere('pk_int_user_id', $vendorId)->where('int_status', 1)->select('pk_int_user_id',
            'vchr_user_name')->get();
        $sevice = Product::where('type', 2)->where('fk_int_user_id', $vendorId)->get();
        if ($request->action == "whatsapp") {

            $action = WhatsappTemplate::where('int_status', Variables::ACTIVE)
                ->where('fk_int_user_id', $vendorId)
                ->select('pk_int_whatsapp_template_id as id', 'vchr_whatsapp_template_title')
                ->get();
            // $enquiry_source = EnquiryType::where('fk_int_user_id', User::getVendorId())->select('pk_int_enquiry_type_id as id', 'vchr_enquiry_type')->get();
            $data['status'] = $status;
            $data['action'] = $action;
            $data['source'] = $enquiry_source;
            $data['purpose'] = $enquiry_purpose;
            $data['service'] = $sevice;
            return response()->json(['msg' => "Webhook found.", 'status' => true, 'data' => $data]);
        } else if ($request->action == "sms") {
            $action = SmsDltTemplate::where('fk_int_user_id', $vendorId)->select('id', 'title')->get();
            $data['status'] = $status;
            $data['action'] = $action;
            $data['source'] = $enquiry_source;
            $data['purpose'] = $enquiry_purpose;
            $data['service'] = $sevice;
            return response()->json(['msg' => "Webhook found.", 'status' => true, 'data' => $data]);
        } else if ($request->action == "task") {
            $action = TaskCategory::where('vendor_id', $vendorId)
                ->orwhere('vendor_id', NULL)->get();

            $data['status'] = $status;
            $data['action'] = $action;
            $data['source'] = $enquiry_source;
            $data['purpose'] = $enquiry_purpose;
            $data['agents'] = $agents;
            $data['service'] = $sevice;
            return response()->json(['msg' => "found.", 'status' => true, 'data' => $data]);
        } else if ($request->action == "api") {
            // $enquiry_source = EnquiryType::where('fk_int_user_id', User::getVendorId())->select('pk_int_enquiry_type_id as id', 'vchr_enquiry_type')->get();
            $emails_templates = EmailTemplateHtml::where('user_id', User::getVendorId())->orderby('id', 'Desc')->get();

            $data['status'] = $status;
            $data['service'] = $sevice;
            $data['source'] = $enquiry_source;
            $data['purpose'] = $enquiry_purpose;
            $data['email_template'] = $emails_templates;
            return response()->json(['msg' => "found.", 'status' => true, 'data' => $data]);
        } else if ($request->action == "campaign") {
            $campaign = LeadCampaign::where('vendor_id', $vendorId)
                ->where('type', 1)
                ->select('id', 'name')
                ->get();

            $data['status'] = $status;
            $data['service'] = $sevice;
            $data['source'] = $enquiry_source;
            $data['purpose'] = $enquiry_purpose;
            $data['campaign'] = $campaign;

            return response()->json(['msg' => "found.", 'status' => true, 'data' => $data]);
        } else if ($request->action == "data_pool") {
            $campaign = LeadCampaign::where('vendor_id', $vendorId)
                ->where('type', 3)
                ->select('id', 'name')
                ->get();

            $data['status'] = $status;
            $data['service'] = $sevice;
            $data['source'] = $enquiry_source;
            $data['purpose'] = $enquiry_purpose;
            $data['campaign'] = $campaign;

            return response()->json(['msg' => "found.", 'status' => true, 'data' => $data]);
        } else {
            return response()->json(['msg' => "Not found.", 'status' => false]);
        }


    }

    public function getAssignData($mode)
    {
        $vendorId = User::getVendorId();
        if ($mode == 1) {
            $data = User::select('pk_int_user_id as id', 'vchr_user_name as name')->where('int_role_id', User::STAFF)->where('parent_user_id', $vendorId)->where('int_status', 1)->orWhere('pk_int_user_id', $vendorId)->get();
        } else {
            $data = Department::select('id', 'name')->where('vendor_id', $vendorId)->get();
        }
        return response()->json(['msg' => "found.", 'status' => true, 'data' => $data]);

    }

    public function checkAssignRuleExist()
    {
        $auto_assign = AutomationRule::where('trigger', 'new_lead')->where('vendor_id', User::getVendorId())->where('action', 'assign')->orderby('id', 'DESC')->first();
        if (!$auto_assign)
            return false;
        else
            return true;
    }

    public function getEmailTemplate()
    {
        $emails_templates = EmailTemplateHtml::where('user_id', User::getVendorId())->orderby('id', 'Desc')->get();
        return $emails_templates;
    }
}
