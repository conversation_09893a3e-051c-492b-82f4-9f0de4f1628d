<?php

declare(strict_types=1);

namespace App;

use App\BackendModel\Designation;
use App\BackendModel\EmailTemplateHtml;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\LeadType;
use App\BackendModel\MailConfiguration;
use App\Common\Unguarded;
use App\Events\LeadAssigned;
use App\FrontendModel\LeadAdditionalDetails;
use App\Mail\StatusMailSend;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/**
 * @property int $id
 * @property string|null $rule_name
 * @property int|null $vendor_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $trigger
 * @property string|null $action
 * @property string|null $service_id
 * @property int|null $enquiry_source_id
 * @property int|null $enquiry_purpose_id
 * @property int|null $task_category_id
 * @property string|null $task_title
 * @property string|null $task_description
 * @property int|null $task_assigned_to
 * @property string|null $api
 * @property int|null $webhook_id
 * @property string|null $feedback_status_id
 * @property int|null $sms_template_id
 * @property int|null $whatsapp_template_id
 * @property string|null $additional_field
 * @property string|null $additional_field_value
 * @property int|null $campaign_id
 * @property int|null $log_day
 * @property int|null $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int|null $assign_mode
 * @property int|null $assign_id
 * @property float|null $duration
 * @property string|null $email_template
 * @property int|null $email_template_id
 * @property int|null $notification_type
 * @method static \Database\Factories\AutomationRuleFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule query()
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereAdditionalField($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereAdditionalFieldValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereApi($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereAssignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereAssignMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereEmailTemplate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereEmailTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereEnquiryPurposeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereEnquirySourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereFeedbackStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereLogDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereNotificationType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereRuleName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereServiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereSmsTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereTaskAssignedTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereTaskCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereTaskDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereTaskTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereTrigger($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereVendorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereWebhookId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule whereWhatsappTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AutomationRule withoutTrashed()
 * @mixin \Eloquent
 */
class AutomationRule extends Model
{
    use SoftDeletes;
    use Unguarded;
    use HasFactory;

    public const AGENT_ASSIGN_MODE = 1;

    public function hasAgentAssigned(): bool
    {
        return $this->assign_mode === self::AGENT_ASSIGN_MODE;
    }

    public static function autoassign($assignData, $lead, $is_notify = null)
    {
        if ($assignData->assign_mode == 1) {
            $staff = AgentDepartment::where('agent_id', $assignData->assign_id)->first();
            $enquiry = Enquiry::findorFail($lead);

            if ($staff) {
                $staff->count = $staff->count + 1;
                $staff->save();
            }

            if ($assignData->assign_id) {
                $enquiry->staff_id = $assignData->assign_id;
                $enquiry->assigned_date = Carbon::now();
                $enquiry->save();

                event(new LeadAssigned($enquiry->pk_int_enquiry_id, $enquiry->created_by));
            }
        } else {
            $count = AgentDepartment::where('department_id', $assignData->assign_id)->min('count');
            $staff = AgentDepartment::where('department_id', $assignData->assign_id)->where(
                'count',
                $count
            )->first();

            $enquiry = Enquiry::findorFail($lead);
            if ($staff) {
                $staff->count = $staff->count + 1;
                $staff->save();

                $enquiry->staff_id = $staff->agent_id;
                $enquiry->assigned_date = Carbon::now();
                $enquiry->save();
                event(new LeadAssigned($enquiry->pk_int_enquiry_id, $enquiry->created_by));

            }
        }
    }

    public static function sendMail($enquiry_id, $ruleDate)
    {
        Log::info('automailsend');
        $enquiry = Enquiry::find($enquiry_id);
        if ($enquiry->staff_id) {
            $staff = User::select('email', 'vchr_user_name', 'email_password')->find($enquiry->staff_id);
        } else {
            $staff = Auth::user();
        }
        $status = FeedbackStatus::where('pk_int_feedback_status_id', $enquiry->feedback_status)->select('vchr_status')
            ->first();
        if ($status) {
            $lead_status = $status->vchr_status;
        } else {
            $lead_status = 'New Status';
        }
        $settings = MailConfiguration::where('vendor_id', User::getVendorId())->first();
        if (! $settings) {
            return;
        }

        $users = User::select('vchr_logo')->find(2);
        $data['email'] = $enquiry->vchr_customer_email;
        $data['subject'] = 'Mail notification';
        $data['content'] = 'status updated';
        $data['name'] = $enquiry->vchr_customer_name;
        $data['from'] = $settings->from_address;
        $data['from_name'] = $staff->vchr_user_name;
        $data['logo'] = url('') . $users->vchr_logo;
        $data['mobile'] = $enquiry->vchr_customer_mobile;
        $data['path'] = '';
        $data['cc'] = '';
        $data['bcc'] = '';

        $company = $enquiry->vchr_customer_company_name;
        $sourceData = EnquiryType::where('pk_int_enquiry_type_id', $enquiry->fk_int_enquiry_type_id)->first();
        $typeData = LeadType::where('id', $enquiry->lead_type_id)->first();
        $source = $sourceData ? $sourceData->vchr_enquiry_type : '';
        $type = $typeData ? $typeData->vchr_enquiry_type : '';
        $designationData = Designation::where('pk_int_designation_id', $staff->designation_id)->first();
        $designation = $designationData ? $designationData->vchr_designation : '';

        if ($ruleDate && $ruleDate->email_template) {
            $emailTemplate_da = EmailTemplateHtml::find($ruleDate->email_template);
            if ($emailTemplate_da) {
                $emailTemplate = $emailTemplate_da->email_template_code;
                $emailTemplate = str_replace('[gl_name]', $data['name'], $emailTemplate);
                $emailTemplate = str_replace('[gl_agent]', $staff->vchr_user_name, $emailTemplate);
                $emailTemplate = str_replace('[gl_company]', $company, $emailTemplate);
                $emailTemplate = str_replace('[gl_mobile]', $data['mobile'], $emailTemplate);
                $emailTemplate = str_replace('[gl_email]', $data['email'], $emailTemplate);
                $emailTemplate = str_replace('[gl_source]', $source, $emailTemplate);
                $emailTemplate = str_replace('[gl_lead_type]', $type, $emailTemplate);
                $emailTemplate = str_replace('[gl_lead_status]', $lead_status, $emailTemplate);
                $emailTemplate = str_replace('[gl_content]', $data['content'], $emailTemplate);
                $emailTemplate = str_replace('[gl_designation]', $designation, $emailTemplate);

                $data['template'] = $emailTemplate;
            } else {
                $data['template'] = '';
            }
        } else {
            $data['template'] = '';
        }

        try {
            // code for the email sending
            $flag = Mail::to($enquiry->vchr_customer_email)->send(new StatusMailSend($data));

        } catch (Excpetion $e) {
            Log::info('Authomation error:' . $e->getMessage());
            return false;
        }

        return $flag;
    }

    // Department vise staff assign
    public static function departmentViseAutoAssign($request, $enquiry, $vendor_id): void
    {
        $department = Department::where('vendor_id', $vendor_id)
            ->whereRaw('UPPER( departments.name ) LIKE ?', ['%' . strtoupper($request->department) . '%'])
            ->first();
        if (! $department) {
            return;
        }

        $count = AgentDepartment::where('department_id', $department->id)->min('count');
        $staff = AgentDepartment::where('department_id', $department->id)->where('count', $count)->first();
        if (! $staff) {
            return;
        }

        $staff->count = $staff->count + 1;
        $staff->save();

        $enquiry->staff_id = $staff->agent_id;
        $enquiry->assigned_date = Carbon::now();
        $enquiry->save();
        event(new LeadAssigned($enquiry->pk_int_enquiry_id, $enquiry->created_by));
    }

    // Dearptment wise additional field
    public static function storeDepartmentToField($request, $enquiry, $vendor_id)
    {
        $departmentText = str_replace('MARKETING', '', $request->department);
        $department = str_replace('DISTRICTMANAGER', '', $departmentText);

        try {
            if ($department) {
                LeadAdditionalDetails::updateOrCreate(
                    [
                        'enquiry_id' => $enquiry->pk_int_enquiry_id,
                        'field_id' => 750,
                    ],
                    [
                        'field_name' => 'DISTRICT',
                        'value' => $department,
                        'created_by' => $vendor_id,
                    ]
                );
            }
        } catch (Exception $e) {
            Log::info('deprtment creation from api');
        }

        return true;

    }

    // Fetch automation rule with pool timer
    public static function fetchTimerHours()
    {
        return self::where('trigger', 'timer')->where('action', 'unassign')->select(
            'duration',
            'vendor_id',
            'feedback_status_id'
        )->get();
    }

    public static function taskCreate($taskAdditionalData, $vendorId, $enquiry): void
    {
        if (! $taskAdditionalData) {
            return;
        }

        $scheduled_date = $taskAdditionalData->duration
            ? Carbon::now()->addMinutes($taskAdditionalData->duration)
            : Carbon::now();

        $input = [
            'name' => $taskAdditionalData->task_title,
            'description' => $taskAdditionalData->task_description,
            'scheduled_date' => $scheduled_date,
            'task_category_id' => $taskAdditionalData->task_category_id ?? 2,
            'assigned_to' => $taskAdditionalData->task_assigned_to,
            'assigned_by' => $vendorId,
            'vendor_id' => $vendorId,
            'enquiry_id' => $enquiry->pk_int_enquiry_id,
            'status' => 0,
        ];

        Task::create($input);
    }
}
