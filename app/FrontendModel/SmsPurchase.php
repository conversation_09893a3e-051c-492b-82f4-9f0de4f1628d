<?php

namespace App\FrontendModel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class SmsPurchase extends Model
{
	use SoftDeletes;

	const SUCCESS=1;
	const FAIL=0;

    protected $dates = ['deleted_at'];
    protected $primaryKey = 'pk_int_sms_purchase_id';
    protected $table = 'tbl_sms_purchase';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

     protected $fillable = [
        'vchr_username','vchr_email','vchr_mobile', 'vchr_plan','int_amount','int_sms_required','int_isPaid'
    ];
}








