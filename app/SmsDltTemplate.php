<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SmsDltTemplate extends Model
{
    const APPROVED = 1;
    const PENDING = 0;

    use SoftDeletes;
    
    public static $rule = [
        'title' => 'required',
        'template'=> 'required',
        // 'entity_id'=> 'required',
        'header_id'=> 'required',
        // 'template_id'=> ['required','unique:sms_dlt_templates,template_id'],
        'template_id'=> 'required',
        // 'sender_id'=> 'required',
      ];
  
      public static $message = [
          'title.required'=>'SMS Template Title is required',
          'template.required'=>'SMS Template is required', 
          // 'entity_id.required'=>'Entity ID is required',
          'header_id.required'=>'Header ID is required', 
          'template_id.required'=>'Template ID is required',
          // 'sender_id.required'=>'Sender ID is required', 
      ];

      public static $updaterule = [
        'title' => 'required',
        'template'=> 'required',
        // 'entity_id'=> 'required',
        'header_id'=> 'required',
        'template_id'=> 'required',
        // 'sender_id'=> 'required',
      ];
  
      public static $updatemessage = [
          'title.required'=>'SMS Template Title is required',
          'template.required'=>'SMS Template is required', 
          // 'entity_id.required'=>'Entity ID is required',
          'header_id.required'=>'Header ID is required', 
          'template_id.required'=>'Template ID is required',
          // 'sender_id.required'=>'Sender ID is required', 
      ];
    public function header(){
      return $this->belongsTo(SmsDltHeader::class);
    }
}
