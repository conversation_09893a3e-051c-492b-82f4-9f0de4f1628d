<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\BackendModel\SendSmsHistory;
use App\BackendModel\Smsroute;
use App\Common\Variables;

class MasterSMSHistory extends Model
{
    use SoftDeletes;
    protected $table = "master_sms_history";
    protected $appends = ['message_encoded','message_date'];

    public function sendsms(){
        return $this->hasOne(SendSmsHistory::class,'gl_track_id','id');
    }
    public function routedata(){
        return $this->hasOne(Smsroute::class,'pk_int_sms_route_id', 'sms_route_id');
    }
    public function getMessageDateAttribute(){
        if ($this->created_at != NULL) {
            $date = Variables::dateFormat($this->created_at) . ' , ' . Variables::changeTimeFormat($this->created_at);
            return $date;
            //  return $history->created_at;
        } else {
            return "No Date";
        }
    }
    public function getMessageEncodedAttribute(){
        if ($this->message != null) {
            $message = preg_replace('/[^A-Za-z0-9\-]/', ' ', $this->message);
        } else {
            $message = "No Message";
        }
        return $message;
    }
}
