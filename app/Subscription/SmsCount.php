<?php

namespace App\Subscription;

use App\User;
use Illuminate\Database\Eloquent\Model;

class SmsCount extends Model
{
    protected $table = 'sms_counts';

    public static function smsCreditBalance()
    {
        $balance = SmsCount::where('vendor_id', User::getVendorId())
            ->join('tbl_sms_route', 'route_id', '=', 'tbl_sms_route.pk_int_sms_route_id')
            ->select('sms_counts.*', 'tbl_sms_route.vchr_sms_route')
            ->get();


        return $balance;
    }

    public static function userSmsBalance($userId)
    {
        /*$smsCounts = SmsCount::where('vendor_id', $userId)
            ->join('tbl_sms_route', 'route_id', '=', 'tbl_sms_route.pk_int_sms_route_id')
            ->select('sms_counts.*', 'tbl_sms_route.vchr_sms_route')
            ->get();*/
        $smsCounts = SmsCount::join('tbl_users', 'vendor_id', '=', 'tbl_users.pk_int_user_id')
            ->join('tbl_sms_route', 'route_id', '=', 'tbl_sms_route.pk_int_sms_route_id')
            ->where('vendor_id', $userId)
            ->select('sms_counts.id', 'sms_counts.vendor_id', 'sms_counts.route_id', 'sms_counts.credit', 'sms_counts.total_count', 'sms_counts.used_sms_count', 'tbl_users.vchr_user_name', 'tbl_sms_route.vchr_sms_route', 'tbl_users.customer_id')
            ->get();
        return $smsCounts;
    }
}
