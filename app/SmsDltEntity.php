<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SmsDltEntity extends Model
{
    const APPROVED = 1;
    const PENDING = 0;

    use SoftDeletes;
    
    // public static $rule = [
    //     'entity_id'=> ['required','unique:sms_dlt_entities,entity'],
    //   ];
    public static $rule = [
        'entity_id'=> ['required'],
      ];
  
      public static $message = [
          'entity_id.required'=>'Entity ID is required',
      ];

      
}
