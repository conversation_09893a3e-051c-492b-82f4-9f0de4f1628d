<?php

declare(strict_types=1);

namespace App\ActivityLogger\Observers;

use App\ActivityLogger\Services\ActivityLoggerService;
use App\BackendModel\Enquiry;

/**
 * Observer for Enquiry model to log activities.
 */
final class EnquiryObserver
{
    public function __construct(
        private readonly ActivityLoggerService $activityLogger
    ) {
    }

    /**
     * Handle the Enquiry "created" event.
     */
    public function created(Enquiry $enquiry): void
    {
        $this->activityLogger->logCreated(
            model: $enquiry,
            metadata: [
                'vendor_id' => $enquiry->fk_int_user_id,
                'enquiry_type_id' => $enquiry->fk_int_enquiry_type_id,
                'customer_mobile' => $enquiry->vchr_customer_mobile,
            ]
        );
    }

    /**
     * Handle the Enquiry "updated" event.
     */
    public function updated(Enquiry $enquiry): void
    {
        $changes = $enquiry->getDirty();

        // Log all other changes
        $this->activityLogger->logUpdated(
            model: $enquiry,
            metadata: [
                'vendor_id' => $enquiry->fk_int_user_id,
                'customer_mobile' => $enquiry->vchr_customer_mobile,
                'changes_count' => count($changes),
            ]
        );
    }

    /**
     * Handle the Enquiry "deleted" event.
     */
    public function deleted(Enquiry $enquiry): void
    {
        $this->activityLogger->logDeleted(
            model: $enquiry,
            metadata: [
                'vendor_id' => $enquiry->fk_int_user_id,
                'customer_mobile' => $enquiry->vchr_customer_mobile,
                'feedback_status' => $enquiry->feedback_status,
            ]
        );
    }

    /**
     * Handle the Enquiry "restored" event.
     */
    public function restored(Enquiry $enquiry): void
    {
        $this->activityLogger->logAttributeChange(
            model: $enquiry,
            attributeName: 'model',
            oldValue: 'deleted',
            newValue: 'restored',
            action: 'restored',
            metadata: [
                'vendor_id' => $enquiry->fk_int_user_id,
                'customer_mobile' => $enquiry->vchr_customer_mobile,
            ]
        );
    }
}
