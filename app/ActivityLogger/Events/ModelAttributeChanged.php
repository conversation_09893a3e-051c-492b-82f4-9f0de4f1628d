<?php

declare(strict_types=1);

namespace App\ActivityLogger\Events;

use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Event dispatched when a model attribute changes.
 */
final class ModelAttributeChanged implements ShouldDispatchAfterCommit
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public readonly Model $model,
        public readonly string $attributeName,
        public readonly mixed $oldValue,
        public readonly mixed $newValue,
        public readonly string $action = 'updated',
        public readonly ?int $userId = null,
        public readonly string $userType = 'App\\User',
        public readonly ?array $metadata = null
    ) {
    }

    public function getModelType(): string
    {
        return get_class($this->model);
    }

    public function getModelId(): int
    {
        return $this->model->getKey();
    }

    public function getOldValueAsString(): ?string
    {
        return $this->convertValueToString($this->oldValue);
    }

    public function getNewValueAsString(): ?string
    {
        return $this->convertValueToString($this->newValue);
    }

    private function convertValueToString(mixed $value): ?string
    {
        if ($value === null) {
            return null;
        }

        if (is_string($value) || is_numeric($value)) {
            return (string) $value;
        }

        if (is_bool($value)) {
            return $value ? '1' : '0';
        }

        if (is_array($value) || is_object($value)) {
            return json_encode($value);
        }

        return (string) $value;
    }
}
