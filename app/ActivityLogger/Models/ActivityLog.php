<?php

declare(strict_types=1);

namespace App\ActivityLogger\Models;

use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Activity Log Model
 * 
 * @property int $id
 * @property string $loggable_type
 * @property int $loggable_id
 * @property string $attribute_name
 * @property string|null $old_value
 * @property string|null $new_value
 * @property string $action
 * @property int|null $user_id
 * @property string $user_type
 * @property array|null $metadata
 * @property \Carbon\Carbon $created_at
 * @property-read \Illuminate\Database\Eloquent\Model $loggable
 * @property-read \App\User|null $user
 */
class ActivityLog extends Model
{
    public const UPDATED_AT = null; // We only need created_at

    protected $fillable = [
        'loggable_type',
        'loggable_id',
        'attribute_name',
        'old_value',
        'new_value',
        'action',
        'user_id',
        'user_type',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
    ];

    /**
     * Get the model that this activity log belongs to.
     */
    public function loggable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who performed this activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo($this->user_type, 'user_id');
    }

    /**
     * Get the user who performed this activity (default to User model).
     */
    public function performer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get formatted old value for display.
     */
    public function getFormattedOldValueAttribute(): string
    {
        return $this->formatValue($this->old_value);
    }

    /**
     * Get formatted new value for display.
     */
    public function getFormattedNewValueAttribute(): string
    {
        return $this->formatValue($this->new_value);
    }

    /**
     * Format value for display.
     */
    private function formatValue(?string $value): string
    {
        if ($value === null) {
            return 'null';
        }

        // Try to decode JSON
        $decoded = json_decode($value, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return json_encode($decoded, JSON_PRETTY_PRINT);
        }

        return $value;
    }
}
