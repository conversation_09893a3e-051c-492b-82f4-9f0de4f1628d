<?php

declare(strict_types=1);

namespace App\ActivityLogger\Listeners;

use App\ActivityLogger\Events\ModelAttributeChanged;
use App\ActivityLogger\Models\ActivityLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * Listener to log model activity changes.
 */
final class LogModelActivity implements ShouldQueue
{
    use InteractsWithQueue;

    public string $queue = 'activity:logger';

    /**
     * Handle the event.
     */
    public function handle(ModelAttributeChanged $event): void
    {
        try {
            ActivityLog::create([
                'loggable_type' => $event->getModelType(),
                'loggable_id' => $event->getModelId(),
                'attribute_name' => $event->attributeName,
                'old_value' => $event->getOldValueAsString(),
                'new_value' => $event->getNewValueAsString(),
                'action' => $event->action,
                'user_id' => $event->userId,
                'user_type' => $event->userType,
                'metadata' => $event->metadata,
            ]);

            Log::info('Activity logged', [
                'model_type' => $event->getModelType(),
                'model_id' => $event->getModelId(),
                'attribute' => $event->attributeName,
                'action' => $event->action,
                'user_id' => $event->userId,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log activity', [
                'error' => $e->getMessage(),
                'model_type' => $event->getModelType(),
                'model_id' => $event->getModelId(),
                'attribute' => $event->attributeName,
            ]);
        }
    }
}
