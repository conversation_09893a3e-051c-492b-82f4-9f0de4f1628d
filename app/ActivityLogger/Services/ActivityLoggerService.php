<?php

declare(strict_types=1);

namespace App\ActivityLogger\Services;

use App\ActivityLogger\Events\ModelAttributeChanged;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

/**
 * Service for logging model activities.
 */
final class ActivityLoggerService
{
    /**
     * Attributes to ignore when logging changes.
     */
    private const IGNORED_ATTRIBUTES = [
        'updated_at',
        'created_at',
        'deleted_at',
        'remember_token',
    ];

    /**
     * Log model creation.
     */
    public function logCreated(Model $model, ?int $userId = null, ?array $metadata = null): void
    {
        $userId = $userId ?? $this->getCurrentUserId();

        event(new ModelAttributeChanged(
            model: $model,
            attributeName: 'model',
            oldValue: null,
            newValue: 'created',
            action: 'created',
            userId: $userId,
            metadata: $metadata
        ));
    }

    /**
     * Log model updates by comparing dirty attributes.
     */
    public function logUpdated(Model $model, ?int $userId = null, ?array $metadata = null): void
    {
        $userId = $userId ?? $this->getCurrentUserId();
        $changes = $model->getDirty();
        $original = $model->getOriginal();

        foreach ($changes as $attribute => $newValue) {
            if ($this->shouldIgnoreAttribute($attribute)) {
                continue;
            }

            $oldValue = $original[$attribute] ?? null;

            if ($oldValue === $newValue) {
                continue;
            }

            event(new ModelAttributeChanged(
                model: $model,
                attributeName: $attribute,
                oldValue: $oldValue,
                newValue: $newValue,
                action: 'updated',
                userId: $userId,
                metadata: $metadata
            ));
        }
    }

    /**
     * Log model deletion.
     */
    public function logDeleted(Model $model, ?int $userId = null, ?array $metadata = null): void
    {
        $userId = $userId ?? $this->getCurrentUserId();

        event(new ModelAttributeChanged(
            model: $model,
            attributeName: 'model',
            oldValue: 'exists',
            newValue: null,
            action: 'deleted',
            userId: $userId,
            metadata: $metadata
        ));
    }

    /**
     * Log a specific attribute change.
     */
    public function logAttributeChange(
        Model $model,
        string $attributeName,
        mixed $oldValue,
        mixed $newValue,
        string $action = 'updated',
        ?int $userId = null,
        ?array $metadata = null
    ): void {
        $userId = $userId ?? $this->getCurrentUserId();

        event(new ModelAttributeChanged(
            model: $model,
            attributeName: $attributeName,
            oldValue: $oldValue,
            newValue: $newValue,
            action: $action,
            userId: $userId,
            metadata: $metadata
        ));
    }

    /**
     * Check if an attribute should be ignored.
     */
    private function shouldIgnoreAttribute(string $attribute): bool
    {
        return in_array($attribute, self::IGNORED_ATTRIBUTES, true);
    }

    /**
     * Get the current authenticated user ID.
     */
    private function getCurrentUserId(): ?int
    {
        $user = Auth::user();
        
        if (!$user) {
            return null;
        }

        return $user->pk_int_user_id ?? null;
    }
}
