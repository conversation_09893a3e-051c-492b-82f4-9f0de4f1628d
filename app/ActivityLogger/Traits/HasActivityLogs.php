<?php

declare(strict_types=1);

namespace App\ActivityLogger\Traits;

use App\ActivityLogger\Models\ActivityLog;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * Trait to add activity logging functionality to models.
 */
trait HasActivityLogs
{
    /**
     * Get all activity logs for this model.
     */
    public function activityLogs(): MorphMany
    {
        return $this->morphMany(ActivityLog::class, 'loggable')
            ->orderBy('created_at', 'desc');
    }

    /**
     * Get activity logs for a specific attribute.
     */
    public function activityLogsForAttribute(string $attributeName): MorphMany
    {
        return $this->activityLogs()
            ->where('attribute_name', $attributeName);
    }

    /**
     * Get activity logs for a specific action.
     */
    public function activityLogsForAction(string $action): MorphMany
    {
        return $this->activityLogs()
            ->where('action', $action);
    }

    /**
     * Get the latest activity log.
     */
    public function latestActivityLog(): ?ActivityLog
    {
        return $this->activityLogs()->first();
    }

    /**
     * Get activity logs by a specific user.
     */
    public function activityLogsByUser(int $userId, string $userType = 'App\\User'): MorphMany
    {
        return $this->activityLogs()
            ->where('user_id', $userId)
            ->where('user_type', $userType);
    }

    /**
     * Check if this model has any activity logs.
     */
    public function hasActivityLogs(): bool
    {
        return $this->activityLogs()->exists();
    }

    /**
     * Get activity logs count.
     */
    public function activityLogsCount(): int
    {
        return $this->activityLogs()->count();
    }
}
