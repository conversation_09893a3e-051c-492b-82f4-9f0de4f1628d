<?php

declare(strict_types=1);

namespace App\Ivr\IVRWebhook\Voxbay;

use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Shared\Exceptions\PhoneNumberIsInvalid;
use Shared\ValueObjects\PhoneNumber;

class VoxbayEventPayload
{
    public readonly ?PhoneNumber $callerNumber;

    public readonly ?PhoneNumber $calledNumber;

    public readonly string|PhoneNumber|null $agentNumber;

    public function __construct(
        ?string $callerNumber = null,
        ?string $calledNumber = null,
        public readonly ?string $callDate = null,
        public readonly ?string $callStatus = null,
        public readonly ?string $recordingUrl = null,
        public readonly ?string $callStartTime = null,
        public readonly ?string $callEndTime = null,
        public readonly ?int $conversationDuration = null,
        public readonly ?string $dtmf = null,
        ?string $agentNumber = null,
        public readonly ?int $totalCallDuration = null,
    ) {
        $this->calledNumber = $calledNumber !== null
            ? $this->parseCalledNumber($calledNumber)
            : null;

        $this->callerNumber = $callerNumber !== null
            ? $this->parseCallerNumber($callerNumber)
            : null;

        $this->agentNumber = $agentNumber !== null
            ? $this->parseAgentNumber($agentNumber)
            : null;
    }

    public function parseAgentNumber(string $agentNumber): string|PhoneNumber
    {
        return rescue(
            static fn () => new PhoneNumber('+91' . preg_replace('/^91/', '', $agentNumber)),
            $agentNumber,
            false
        );
    }

    public function dateTime(): CarbonImmutable|null
    {
        return $this->callDate
            ? CarbonImmutable::parse($this->callDate)
            : null;
    }

    public function startTime(): ?string
    {
        return $this->callStartTime
            ? CarbonImmutable::parse($this->callStartTime)->toTimeString()
            : null;
    }

    public function endTime(): ?string
    {
        return $this->callEndTime
            ? Carbon::createFromTimestamp($this->callEndTime)->toTimeString()
            : null;
    }

    private function parseCallerNumber(string $callerNumber): PhoneNumber
    {
        $callerNumber = ltrim($callerNumber, '0');

        try {
            return new PhoneNumber('+' . $callerNumber);
        } catch (PhoneNumberIsInvalid) {
            return new PhoneNumber('+91' . $callerNumber);
        }
    }

    private function parseCalledNumber(string $calledNumber): PhoneNumber
    {
        $normalized = '+' . ltrim($calledNumber, '+');

        return new PhoneNumber($normalized);
    }
}
