<?php

namespace App\Common;


use App\AutomationRule;
use App\BackendModel\SmsDomain;
use App\Subscription\Invoice;
use App\Subscription\Plan;
use App\Subscription\PlanService;
use App\Subscription\SmsCount;
use App\Subscription\UserServiceSubscription;
use App\User;
use App\BackendModel\EmailTemplateHtml;
use App\BackendModel\Enquiry;
use App\BackendModel\NotificationType;
use App\BackendModel\NotificationTypeUsers;
use App\BackendModel\Designation;
use App\BillingSubscription;
use App\Jobs\IvrCallAutoCallJob;
use App\Jobs\SentServiceJob;
use App\Jobs\WebhookPostJob;
use App\WebHook;
use Barryvdh\DomPDF\Facade as PDF;
use Carbon\Carbon;
use Getlead\Campaign\Jobs\AttachLeadsToCampaign;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Mail;
use Getlead\Campaign\Models\LeadCampaign;
use Illuminate\Support\Facades\Log;

class Common
{
    /**
     * @param $vendorId
     * @return User|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function getUserSubscription($vendorId)
    {
        return $userSubscriptions = User::with([
            'subscriptions' => function ($query) {
                $query->where('date_to', '>=', Carbon::today());
            },
            'subscriptions.plans.services' => function ($query) {
                $query->where('service', Variables::SERVICE_SMS);
            }])->where('pk_int_user_id', $vendorId)->first();
    }

    /**
     * @param $userSubscriptions
     * @param $vendorId
     * @return mixed
     */
    public function checkUserSubscriptionExists($userSubscriptions, $vendorId)
    {
        return $exists = $userSubscriptions->subscriptions->contains($vendorId);
    }

    /**
     * @param $subscriptionArray
     * @return mixed
     */
    public function getSMSSubscription($subscriptionArray)
    {
        foreach ($subscriptionArray as $index => $sub) {
            if ($sub->plans->services->contains('plan_id', $sub->plans->id)) {
                $sub['total_sms_count'] = $sub->plans->services[0]['count'];
                $sub['sms_count'] = $sub->plans->services[0]['sms_count'];
            } else {
                unset($subscriptionArray[$index]);
            }
        }
        return $subscriptionArray;
    }

    /**
     * @param $notification
     * @return string
     * Get Notifications
     */
    public function getNotifications($notification)
    {
        switch ($notification->type) {
            case "App\Notifications\NewLead":
                $createdBy = User::select('vchr_user_name')->find($notification->data['created_by']);
                if ($createdBy) {
                    $message2 = $createdBy->vchr_user_name;
                } else if ($notification->data['created_by']) {
                    $message2 = $notification->data['created_by'];
                } else {
                    $message2 = "";
                }

                return $message2;
                break;
            case "App\Notifications\AssignLeadToStaff":
                // $assignedTo = User::select('vchr_user_name')->find($notification->data['assigned_to']);
                $created_by = User::select('vchr_user_name')->find($notification->data['created_by']);
                try {
                    if ($notification->data['assigned_to'] != "") {
                        $message2 = $notification->data['assigned_to'] . (($created_by) ? (' by ' . $created_by->vchr_user_name) : '');
                    } else {
                        $message2 = "";
                    }
                } catch (\Exception $e) {
                    \Log::info($e->getMessage());
                    \Log::info('--Notification--');
                    $message2 = "";
                }


                return $message2;
                break;
            default:
                return "";
                break;
        }
    }

    public function addSMSCount($planId, $vendorId)
    {
        $plan = Plan::with('services')->with('country')->find($planId);
        $smsServiceArray = [];
        foreach ($plan->services as $val) {
            if ($val->service == Variables::SERVICE_SMS) {
                $smsServiceArray['sms_count'] = $val->sms_count;
            }
        }
        if (isset($smsServiceArray['sms_count'])) {
            $sms_count = json_decode($smsServiceArray['sms_count'], true);
            foreach ($sms_count as $item) {
                $sms_counts = SmsCount::where('vendor_id', $vendorId)->where('route_id', $item['id'])->first();
                if (!$sms_counts) {
                    $sms_counts = new SmsCount();
                    $sms_counts->vendor_id = $vendorId;
                    $sms_counts->route_id = $item['id'];
                    $sms_counts->total_count = $sms_counts->total_count + $item['count'];
                    $sms_counts->save();
                } else {
                    $sms_counts->total_count = $sms_counts->total_count + $item['count'];
                    $sms_counts->save();

                }
            }
        }
    }

    /**
     * @param $plan
     * @param $vendorId
     * Add Service subscription
     */
    public function addServiceSubscription($planId, $vendorId, $planDuration, $billing = null)
    {

        $plan_services = PlanService::where('plan_id', $planId)->get();
        foreach ($plan_services as $service) {
            $user_service_subscription = UserServiceSubscription::where('service', $service->service)
                ->where('vendor_id', $vendorId)
                ->first();
            if ($user_service_subscription) {
                if (Carbon::today() <= $user_service_subscription->expiry_date)
                    $expiry_date = Carbon::parse($user_service_subscription->expiry_date)->addDays($planDuration);
                else
                    $expiry_date = Carbon::today()->addDays($planDuration);
                $user_service_subscription->expiry_date = $expiry_date;
                $user_service_subscription->count = $user_service_subscription->count + (($billing) ? 0 : $service->count);

                if ($service->service == Variables::SERVICE_CRM && $user_service_subscription->user_count != NULL) {
                    $user_service_subscription->user_count = $user_service_subscription->user_count + $service->user_count;
                }
                $user_service_subscription->save();

            } else {
                $user_service_subscription = new UserServiceSubscription();
                $user_service_subscription->vendor_id = $vendorId;
                $user_service_subscription->service = $service->service;
                $user_service_subscription->count = $service->count;
                $user_service_subscription->expiry_date = Carbon::today()->addDays($planDuration);
                $user_service_subscription->user_count = $service->user_count;
                $user_service_subscription->save();
            }


        }
        $this->showCrmUsersSubscription($vendorId);

    }

    public function showCrmUsersSubscription($vendorId)
    {
        $subscription = UserServiceSubscription::where('vendor_id', $vendorId)->where('service', Variables::SERVICE_CRM)->where('expiry_date', '>', Carbon::today())->first();
        if ($subscription) {
            $userCount = $subscription->user_count;
        } else {
            $userCount = 0;
        }
        $enquiryCount = Enquiry::where('fk_int_user_id', $vendorId)->count();

    }


    //----------------------------------------

    public function checkUserSubscription(int $vendorId, string $serviceName)
    {
        return BillingSubscription::query()
            ->where('vendor_id', $vendorId)
            ->where('services', 'LIKE', '%' . $serviceName . '%')
            ->orderByDesc('expiry_date')
            ->whereDate('expiry_date', '>=', now())
            ->first();
    }

    public function checkUserAllSubscriptions($vendorId)
    {
        $userSubscription = UserServiceSubscription::where('vendor_id', $vendorId)
            ->where('expiry_date', '>', Carbon::today())->first();
        return $userSubscription;
    }

    //-----------------------Default Email Template----------------------
    public function emailTemplate($vendorId, $username, $content, $template_id = null, $enq = null)
    {
        $template = EmailTemplateHtml::where('user_id', $vendorId)
            ->where(function ($q) use ($template_id) {
                if ($template_id)
                    $q->where('id', $template_id);
            })
            // ->where('default', EmailTemplateHtml::DEFAULT)
            ->where('status', EmailTemplateHtml::ACTIVATE)
            ->first();

        if ($template) {
            if ($enq) {
                $agent = ($enq?->assigned_user) ? $enq?->assigned_user->vchr_user_name : 'Un Assigned agent';
                $email = $enq->vchr_customer_email;
                $mobile = $enq->vchr_customer_mobile;
                $company = $enq->vchr_customer_company_name;
                $source = ($enq?->leadSource) ? $enq?->leadSource->vchr_enquiry_type : '';
                $status = ($enq?->feedbackStatus) ? $enq?->feedbackStatus->vchr_status : 'None';
                $designationData = ($enq?->assigned_user) ? Designation::where('pk_int_designation_id', $enq?->assigned_user->designation_id)->first() : null;
                $designation = $designationData ? $designationData->vchr_designation : '';
            }
            $emailTemplate = $template->email_template_code;
            $emailTemplate = str_replace("[gl_name]", $username, $emailTemplate);
            $emailTemplate = str_replace("[gl_content]", $content, $emailTemplate);
            $emailTemplate = str_replace("[gl_agent]", $agent, $emailTemplate);
            $emailTemplate = str_replace("[gl_email]", $email, $emailTemplate);
            $emailTemplate = str_replace("[gl_mobile]", $mobile, $emailTemplate);
            $emailTemplate = str_replace("[gl_source]", $source, $emailTemplate);
            $emailTemplate = str_replace("[gl_lead_status]", $status, $emailTemplate);
            $emailTemplate = str_replace("[gl_company]", $company, $emailTemplate);
            $emailTemplate = str_replace("[gl_designation]", $designation, $emailTemplate);
            return $emailTemplate;
        } else {
            return "";
        }
    }

    public function emailTemplateContent($vendorId, $username, $agentname, $company, $mobile, $email, $source, $type, $status, $content, $template_id, $designation)
    {
        $template = EmailTemplateHtml::where('user_id', $vendorId)
            // ->where('default', EmailTemplateHtml::DEFAULT)
            ->where('id', $template_id)
            ->where('status', EmailTemplateHtml::ACTIVATE)->first();
        if ($template) {
            $emailTemplate = $template->email_template_code;
            $emailTemplate = str_replace("[gl_name]", $username, $emailTemplate);
            $emailTemplate = str_replace("[gl_agent]", $agentname, $emailTemplate);
            $emailTemplate = str_replace("[gl_company]", $company, $emailTemplate);
            $emailTemplate = str_replace("[gl_mobile]", $mobile, $emailTemplate);
            $emailTemplate = str_replace("[gl_email]", $email, $emailTemplate);
            $emailTemplate = str_replace("[gl_source]", $source, $emailTemplate);
            $emailTemplate = str_replace("[gl_lead_type]", $type, $emailTemplate);
            $emailTemplate = str_replace("[gl_lead_status]", $status, $emailTemplate);
            $emailTemplate = str_replace("[gl_content]", $content, $emailTemplate);
            $emailTemplate = str_replace("[gl_designation]", $designation, $emailTemplate);
            return $emailTemplate;
        } else {
            return "";
        }
    }

    /**
     * @param $invoiceViewPagePath
     * @param $invoice_data
     * @return mixed
     */
    public
    function generateInvoice($invoiceViewPagePath, $invoice_data)
    {
        $pdf = PDF::loadView($invoiceViewPagePath, ['data' => $invoice_data])->setPaper('a4');;
        PDF::setOptions(['dpi' => 150, 'defaultFont' => 'dejavusans']);

        return $pdf;
    }

    /**
     * @param $invoicePdf
     * @param $vendorId
     * @param $subscriptionId
     * @param $grant_total
     * Save Invoice
     */
    public
    function createEntryToInvoiceTable($vendorId, $subscriptionId, $grant_total)
    {
        $invoice = new Invoice();
        $invoice->gl_invoice_number = 'GL' . $vendorId . $subscriptionId;
        $invoice->date = Carbon::today();
        $invoice->vendor_id = $vendorId;
        $invoice->subscription_id = $subscriptionId;
        $invoice->amount = $grant_total;
        $invoice->save();

        return $invoice;
    }

    /**
     * @param $invoiceId
     * @param $invoicePdf
     * save invoice file
     */
    public
    function saveInvoice($invoiceId, $invoicePdf)
    {
        $invoice = Invoice::find($invoiceId);
        $invoiceFile = 'invoice' . $invoiceId . '.pdf';
        $invoice->invoice_file = $invoiceFile;
        $output = $invoicePdf->output();

        $path = public_path('invoice');
        file_put_contents($path . '/' . $invoiceFile, $output);
        $invoice->save();
        return $invoice;
    }

    /**
     * @param $subscription_details
     * @param $email_settings
     * @param $pdf
     */
    public
    function sendInvoiceToCustomer($subscription_details, $email_settings, $pdf)
    {
        Mail::send('emails.subscription-email-notification', ['data' => $subscription_details], function ($message) use ($email_settings, $pdf) {
            $message->from($email_settings['from'], 'Getlead Analytics');
            $message->to($email_settings['to'])->subject('Plan Subscription');
            $message->attachData($pdf->output(), "invoice.pdf");
        });
    }

    public static function getIp()
    {
        foreach (array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR') as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip); // just to be safe
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
    }

    public
    static function getClientIp()
    {
        $ipaddress = '';
        if (getenv('HTTP_CLIENT_IP'))
            $ipaddress = getenv('HTTP_CLIENT_IP');
        else if (getenv('HTTP_X_FORWARDED_FOR'))
            $ipaddress = getenv('HTTP_X_FORWARDED_FOR');
        else if (getenv('HTTP_X_FORWARDED'))
            $ipaddress = getenv('HTTP_X_FORWARDED');
        else if (getenv('HTTP_FORWARDED_FOR'))
            $ipaddress = getenv('HTTP_FORWARDED_FOR');
        else if (getenv('HTTP_FORWARDED'))
            $ipaddress = getenv('HTTP_FORWARDED');
        else if (getenv('REMOTE_ADDR'))
            $ipaddress = getenv('REMOTE_ADDR');
        else
            $ipaddress = '0';
        return $ipaddress;
    }

    public
    static function getAlertBoxBalance($domainId, $priority)
    {
        $smsDomain = SmsDomain::find($domainId);
        $url = $smsDomain->domain . '/balancecheck.php?username=' . $smsDomain->getlead_username . '&api_password=' . $smsDomain->getlead_api_password . '&priority=' . $priority;
        $client = new Client();
        $res = $client->get($url);
        $balance = $res->getBody()->getContents();
        return $balance;

    }


    static function getRoadDistance($points)
    {
        try {
            $client = new Client();
            $res = $client->get("http://127.0.0.1:8989/route" . $points . "&type=json&locale=en-GB&key=&elevation=false&profile=car");
            $response = $res->getBody()->getContents();

            $data = json_decode($response, TRUE);
            $distance = 0;
            //foreach($data['paths'] as $paths)
            $distance = $distance + $data['paths'][0]['distance'];
            return $distance / 1000;
        } catch (\Exception $exp) {
            \Log::info($exp->getMessage());
            return 0;
        }
    }

    static function getTimeSlots($from, $to)
    {
        $start = \Carbon\Carbon::instance(new \DateTime(\Carbon\Carbon::today()->format('Y-m-d') . $from));
        $end = \Carbon\Carbon::instance(new \DateTime(\Carbon\Carbon::today()->format('Y-m-d') . $to));
        $minSlotHours = 0;
        $minSlotMinutes = 30;
        $minInterval = \Carbon\CarbonInterval::hour($minSlotHours)->minutes($minSlotMinutes);

        $reqSlotHours = 0;
        $reqSlotMinutes = 30;
        $reqInterval = \Carbon\CarbonInterval::hour($reqSlotHours)->minutes($reqSlotMinutes);
        $allTimes = [];
        foreach (new \DatePeriod($start, $minInterval, $end) as $slot) {
            $to = $slot->copy()->add($reqInterval);

            array_push($allTimes, $slot->format('h:i A') . ' - ' . $to->format('h:i A'));
        }
        return $allTimes;
    }

    static function getMatchingDLTTemplate($vendor_id, $senderId, $message)
    {
        $templates = \App\SmsDltTemplate::where('sms_dlt_templates.fk_int_user_id', $vendor_id)
            ->join('sms_dlt_headers', 'sms_dlt_headers.id', '=', 'sms_dlt_templates.header_id')
            ->join('sms_dlt_entities', 'sms_dlt_entities.id', '=', 'sms_dlt_headers.entity_id')
            ->where('sms_dlt_headers.sender', $senderId)
            ->select('sms_dlt_entities.entity', 'sms_dlt_templates.template_id', 'sms_dlt_templates.template')
            ->get();
        foreach ($templates as $key => $template) {
            try {
                $temp = str_replace(".", "", $template->template);
                $message = str_replace(".", "", $message);

                $temp = str_replace("/", "", $temp);
                $message = str_replace("/", "", $message);

                $temp = str_replace(" ", "", $temp);
                $message = str_replace(" ", "", $message);

                $temp = str_replace("-", "", $temp);
                $message = str_replace("-", "", $message);

                $temp = urldecode(str_replace("%0D", "", urlencode($temp)));
                $message = urldecode(str_replace("%0D", "", urlencode($message)));

                $temp = str_replace("
", "", $temp);
                $message = str_replace("
", "", $message);
                $temp = str_replace("\n", "", $temp);
                $message = str_replace("\n", "", $message);

                $temp = str_replace("\r", "", $temp);
                $message = str_replace("\r", "", $message);

                $temp = str_replace("(", "", $temp);
                $message = str_replace("(", "", $message);

                $temp = str_replace(")", "", $temp);
                $message = str_replace(")", "", $message);

                $temp = '/' . str_replace("{#var#}", ".+?", $temp) . '/';

                if (preg_match($temp, $message)) {
                    return $template;
                }
            } catch (\Exception $exp) {
                \Log::info($exp->getMessage());
            }
        }
        return null;
    }

    function getRule($vendorId, $type, $action, $source = null)
    {
        if ($action == 'api') {
            return AutomationRule::query()
                ->where('vendor_id', $vendorId)
                ->where('trigger', $type)
                ->where('action', $action)
                ->where(function ($q) use ($source) {
                    $q->where('enquiry_source_id', $source);
                    $q->orWhere('enquiry_source_id', NULL);
                })
                ->orderBy('id', 'DESC')
                ->first();
        }
        if ($action == 'webhook') {
            return AutomationRule::query()
                ->where('vendor_id', $vendorId)
                ->where('trigger', $type)
                ->where('action', $action)
                ->where(function ($q) use ($source) {
                    $q->where('enquiry_source_id', $source);
                    $q->orWhere('enquiry_source_id', NULL);
                })
                ->orderBy('id', 'DESC')
                ->get();
        }
        return AutomationRule::query()
            ->where('vendor_id', $vendorId)
            ->where('trigger', $type)
            ->where('action', $action)
            ->when($source, function ($q) use ($source) {
                $q->where('enquiry_source_id', $source);
            })
            ->orderBy('id', 'DESC')
            ->first();
    }

    function getRuleStatusChange($vendorId, $type, $action, $statusId)
    {
        $rule = AutomationRule::where('vendor_id', $vendorId)
            ->where('trigger', $type)
            ->where('action', $action)
            ->where('feedback_status_id', $statusId)
            ->orderBy('id', 'DESC')
            ->first();
        return $rule;
    }

    function getWebHookById($id)
    {
        return WebHook::query()
            ->select('url')
            ->whereKey($id)
            ->first();
    }

    function postToWebHook($url, $data)
    {
        try {
            if ($url) {
                WebhookPostJob::dispatch($url, $data);
            }
        } catch (\Exception $e) {
            \Log::info($e->getMessage());
        }
    }

    function postToServiceCall($url, $data)
    {
        try {
            if ($url) {
                SentServiceJob::dispatch($url, $data);
            }
        } catch (\Exception $e) {
            Log::info($e->getMessage());
        }
    }

    function getRuleByStatus($vendorId, $type, $action, $status)
    {
        return AutomationRule::where('vendor_id', $vendorId)
            ->where('trigger', $type)
            ->where('action', $action)
            ->where('feedback_status_id', $status)
            ->orderBy('id', 'DESC')
            ->first();
    }

    public static function makeClickableLinks($text)
    {
        $pattern = '/\b((?:https?|http):\/\/[^\s<]+[^<.,:;"\')\]\s])/i';
        $replacement = '<a href="$1" target="_blank">View File</a>';
        $text = preg_replace($pattern, $replacement, $text);
        return $text;
    }

    // Add to campaign
    public static function addToCampaign($automation, $lead, $userId, $vendorId, $type)
    {
        $campaign = LeadCampaign::query()->where('id', $automation->campaign_id)->firstOrFail();
        Log::info('add to campaign  - ', ['LeadCampaign' => $campaign, 'lead' => $lead]);
        AttachLeadsToCampaign::dispatch($campaign, [$lead->pk_int_enquiry_id], $userId);
        return true;
    }

    public static function hasEmailNotificationEnabled($vendor_id): bool
    {
        return User::query()
                ->where('verify_email', 1)
                ->whereKey($vendor_id)
                ->exists() || NotificationTypeUsers::query()
                ->where('vendor_id', $vendor_id)
                ->where('status', NotificationTypeUsers::ACTIVATE)
                ->where('type', NotificationType::EMAIL)
                ->exists();
    }

    function postToIvrAutoCall($url, $extension, $data)
    {
        try {
            IvrCallAutoCallJob::dispatch($data['phone'], $extension, $url);
            return true;
        } catch (\Exception $e) {
            \Log::info('ivr call auto call : ' . $e->getMessage());
        }
    }

    /**
     * Click to call function for bonvoice
     */
    public static function clickToAutoCall($enquiry, $destination, $ivr_number)
    {
        if ($enquiry) {
            if ($ivr_number) {
                $base_url = 'https://backend.pbx.bonvoice.com/autoDialManagement/autoCallBridging/';
                $legBChannelID = 1;
                $legAChannelID = 1;
                $autocallType = 2;
                $legADialAttempts = 1;
                $legBDialAttempts = 1;
                $eventID = $enquiry->pk_int_enquiry_id;
                $token = '6bb3678cee23cde036737ea6b5b6ecc5b56ed580';
                #Echo Result Of bonvoice Server
                $curl = curl_init();
                $mobile = $enquiry->mobile_no;
                curl_setopt_array($curl, array(
                    CURLOPT_URL => $base_url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => '{
                        "autocallType": "' . $autocallType . '",
                        "destination": "' . $destination . '",
                        "ringStrategy":"ringall",
                        "legACallerID": "8037887123",
                        "legAChannelID": "' . $legAChannelID . '",
                        "legADialAttempts": "' . $legADialAttempts . '",
                        "legBDestination": "' . $mobile . '",
                        "legBChannelID": "' . $legBChannelID . '",
                        "legBCallerID": "8037887123",
                        "legBDialAttempts": "' . $legBDialAttempts . '",
                        "eventID": "' . $eventID . '"
                  }',
                    CURLOPT_HTTPHEADER => array(
                        'Authorization: Token ' . $token,
                        'Content-Type: application/json'
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $obj = json_decode($response, true);
                if (isset($obj['responseType'])) {
                    if ($obj['responseType'] == 'Success') {
                        return true;
                    } elseif ($obj['responseCode'] == 910) {
                        return false;
                    } elseif ($obj['has_credit'] == 'no') {
                        return false;
                    } elseif ($obj['responseType'] == 'Error') {
                        return false;
                    }
                } elseif (isset($obj['has_credit']) && $obj['has_credit'] == 'no') {
                    return false;
                } elseif (isset($obj['error'])) {
                    return false;
                } elseif (isset($obj['detail'])) {
                    return false;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }


}