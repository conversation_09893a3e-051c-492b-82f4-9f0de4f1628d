<?php

declare(strict_types=1);

namespace App\Common;

use App\BackendModel\Designation;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\LeadType;
use App\BackendModel\Permission;
use App\BackendModel\Settings;
use App\BackendModel\Smsroute;
use App\Country;
use App\User;
use Carbon\Carbon;
use GuzzleHttp\Client;

class Variables
{
    public const ROLE_ADMIN = 1;

    public const USER = 2;

    public const STAFF = 3;

    public const ACTIVE = 1;

    public const DEACTIVE = 0;

    public const SMS_STATUS_QUEUE = 'Submitted';

    public const SMS_STATUS_SENT = 'Sent';

    public const SMS_STATUS_FAIL = 'Fail';

    public const SMS_STATUS_DELIVERED = 'Delivered';

    public const SMS_STATUS_ERROR = 'Error';

    public const TELEGRAM_TYPE_PRIVATE = 'private';

    public const TELEGRAM_TYPE_GROUP = 'group';

    public const NOT_SUBSCRIBED = 'NOT SUBSCRIBED';

    public const SUBSCRIBED = 'SUBSCRIBED';

    public const EXPIRED = 'EXPIRED';

    public const INACTIVE = 'INACTIVE';

    public const BOT_TOKEN = '737148201:AAH8K-HgqekcYyfOpNZHsfWQvjl633Cq434';

    public const SERVICE_CRM = 'CRM';

    public const SERVICE_GLP = 'GL Promo';

    public const SERVICE_GLS = 'GL Scratch';

    public const SERVICE_GLV = 'GL Verify';

    public const SERVICE_MISSEDCALL = 'Missed Call';

    public const SERVICE_SMS = 'SMS';

    public const SERVICE_IVR = 'IVR';

    public const SERVICE_EVENTS = 'GL Events';

    public const SERVICE_SALES = 'Sales';

    public const SERVICE_CAMPAIGNS = 'Campaigns';

    public const BULK_SMS = 'Bulk SMS';

    public const DYNAMIC_MESSAGING = 'Dynamic SMS';

    public const MESSAGE_VIA_API = 'API';

    public const DEF_COUNTRY_INDIA = 29;

    public const DYNAMIC_MESSAGE_FIELD_START = 2;

    public const DYNAMIC_MESSAGE_FIELD_END = 10;

    /*------------- Bulk SMS Panel--------------------------*/
    public const ALERT_BOX = 'Alertbox';

    public const ALERTBOX = 'AlertBox';

    public const MERABT = 'Merabt';

    public const AWSSNS = 'AmazonSNS';

    public const TEXT_LOCAL = 'TextLocal';
    /*------------- Bulk SMS Panel--------------------------*/

    public const GDEAL_DOMAIN = 'http://gdeal.getlead.co.uk/';

    public const MESSAGE_API = 1;

    public const BALANCE_API = 2;

    public const STATUS_API = 3;

    public const OTP_PRIORITY = 4;

    public const INTL_PRIORITY = 5;

    public const ALERT_BOX_ER_01 = 'Wrong Username or password.';

    public const ALERT_BOX_ER_02 = 'Sorry! Insufficient balance!';

    public const ALERT_BOX_ER_03 = 'Sorry! Insufficient balance! Trackid=Sorry! Insufficient balance!';

    public const ALERT_BOX_ER_04 = 'Sorry, No valid numbers found!';

    public const ALERT_BOX_ER_05 = 'Sorry, No valid numbers found! Trackid=Sorry, No valid numbers found!';

    public const ALERT_BOX_ER_06 = 'Sorry, senderid not valid';

    public const ALERT_BOX_ER_07 = 'this msg_id is not valid';

    public const ALERT_BOX_ER_08 = 'No user found!';

    public const ALERT_BOX_SENT_MESSAGE = 'Your message has been sent';

    public const GETLEAD_SERVICES = [
        self::SERVICE_CRM,
        self::SERVICE_GLP,
        self::SERVICE_GLS,
        self::SERVICE_GLV,
        self::SERVICE_MISSEDCALL,
        self::SERVICE_SMS,
        self::SERVICE_IVR,
        self::SERVICE_EVENTS,
        self::SERVICE_SALES,
        self::SERVICE_CAMPAIGNS,
    ];

    public const APOLO_USER_ID = 765;

    //const APOLO_USER_ID = 2;
    public const BAZANI_USER_ID = 1119;

    public const FORTUNE_USER_ID = 1213;

    public const NIKSHAN_USER_ID = 1346;

    public const EZZAT_USER_ID = 3636;

    public const BONVOICE_SERVER_2 = [2544, 3290, 3289, 3288, 3287, 3268, 3204, 2417, 2554];

    public const IVR_NUMBER_RESTRICT = [3119];

    public const SCARTCH_BYPASS = [3286, 3316, 1870];

    public function getPermissions(): mixed
    {
        return $permissions = Permission::paginate(15);
    }

    public static function sendData($url): mixed
    {

        $client = new Client();
        $res = $client->get($url);
        return json_decode($res->getBody(), true);
    }

    public static function getCountryList(): mixed
    {
        return Country::select('id', 'name')->get();
    }

    /**
     * @return string
     * Chnage Date format
     */
    public static function changeDateFormat($date): string
    {
        return Carbon::parse($date)->format('M d');
    }

    /**
     * @return string
     * Change Time format
     */
    public static function changeTimeFormat($date): string
    {
        return Carbon::parse($date)->format('g:i A ');
    }

    /**
     * @return string
     * Get with Year
     */
    public static function dateFormatWithYear($date): string
    {
        return Carbon::parse($date)->format('Y M d');
    }

    public static function dateFormatWithYears($date)
    {
        return Carbon::parse($date)->format('M d,Y');
    }

    /**
     * @return string
     * Get Date String
     */
    public static function dateFormat($date): string
    {
        return Carbon::parse($date)->toDateString();

    }

    public static function getLogType($logType): string
    {
        switch ($logType) {
            case EnquiryFollowup::TYPE_NOTE:
                return 'Note';
                break;
            case EnquiryFollowup::TYPE_LOG_CALL:
                return 'Call';
                break;
            case EnquiryFollowup::TYPE_LOG_EMAIL:
                return 'Email';
                break;
            case EnquiryFollowup::TYPE_LOG_MEETING:
                return 'Meeting';
                break;
            case EnquiryFollowup::TYPE_TASK:
                return 'Task';
                break;
            case EnquiryFollowup::TYPE_SCHEDULE:
                return 'Schedule';
                break;
            default:
                return 'No information available for that day . ';
                break;
        }
    }

    /**
     * @param $id
     * @param $array
     * @param $key
     * @return null
     */
    function searchForService($id, $array, $key)
    {
        foreach ($array as $index => $val) {
            if ($val[$key] === $id) {
                return $val[$key];
            }
        }
        return null;
    }

    /**
     * @param $array
     * @param $service
     * @return null
     */
    public function getGetleadService($array, $service)
    {
        foreach ($array as $index => $sub) {
            if ($sub->plans->services->contains('plan_id', $sub->plans->id)) {
                $sub->user_service = $this->searchForService($service, $sub->plans->services, 'service');
            } else {
                unset($array[$index]);
            }
        }
        return $service_name = $this->searchForService($service, $array, 'user_service');
    }

    public static function checkSubscription($service = null)
    {
        $commonObj = new Common();
        $userSubscription = $commonObj->checkUserSubscription(User::getVendorId(), $service);

        if ($userSubscription) {
            return true;
        }
    }

    public static function getSmsRoutes($otp)
    {
        $query = Smsroute::where('int_sms_route_status', Smsroute::ACTIVATE)
            ->where('vchr_sms_route', '!=', Smsroute::EMAIL);
        if ($otp == 0) {
            $query = $query->where('priority', '!=', self::OTP_PRIORITY);
        }
        return $query->get();
    }

    public static function getDesignations()
    {
        return Designation::where('vendor_id', User::getVendorId())->select(
            'pk_int_designation_id',
            'vchr_designation'
        )->get();
    }

    public static function getLeadTypes()
    {
        return LeadType::where('vendor_id', User::getVendorId())
            ->select('id', 'name')
            ->get();
    }

    public static function checkEnableSettings(string $label, int $vendorId = null): bool
    {
        return Settings::query()
            ->selectRaw('1 as exists')
            ->where('vchr_settings_type', $label)
            ->whereJsonContains('vchr_settings_value', $vendorId ?? User::getVendorId())
            ->select('pk_int_settings_id', 'vchr_settings_value')
            ->exists();
    }

    public static function checkCurrencyEnableSetting($label)
    {
        $settings = Settings::where('vchr_settings_type', $label)->where(
            'fk_int_user_id',
            User::getVendorId()
        )->select('pk_int_settings_id', 'vchr_settings_value')->first();
        if ($settings) {
            return $settings->vchr_settings_value;
        }

        return null;
    }
}
