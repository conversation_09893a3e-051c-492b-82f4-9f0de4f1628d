<?php


namespace App\Common;


use App\BackendModel\SmsApiCredentials;
use App\BackendModel\SmsDomain;
use App\BackendModel\Smsroute;
use App\SmsPanel;
use App\Subscription\SmsCount;
use GuzzleHttp\Client;

class SmsApi
{
    /**
     * @param $priority
     * @return mixed
     */
    public function getRouteIdByPriority($priority)
    {
        $routeDetails = Smsroute::where('priority', $priority)->first();
        if ($routeDetails) {
            return $routeDetails;
        }
    }

    public function getActivePanelByRoute($routeId, $vendorId)
    {
        $template = SmsApiCredentials::where('route_id', $routeId)->where('status', Variables::ACTIVE)->where('vendor_id', $vendorId)->first();
        if ($template) {
            $domain = SmsDomain::where('id', $template->sms_domain_id)->first();
            return $domain;
        } else {
            $templates = SmsApiCredentials::where('route_id', $routeId)->where('status', Variables::ACTIVE)->where('vendor_id', 0)->first();
            if ($templates) {
                $domain = SmsDomain::where('id', $templates->sms_domain_id)->first();
                return $domain;
            }
        }

    }

    public function getGeleadBalance($vendorId, $routeId, $smsPanel)
    {
        $smsCount = SmsCount::where('vendor_id', $vendorId)
            ->where('route_id', $routeId)->first();
        if ($smsCount) {
            $balance = $smsCount->total_count  +  $smsCount->credit- $smsCount->used_sms_count;
        } else {
            return $balance = 0;
        }
        return $balance;
    }


    public function getPanelBalance($vendorId, $routeId, $smsPanel)
    {
         $template = SmsApiCredentials::where('route_id', $routeId)->where('status', Variables::ACTIVE)
            ->where('vendor_id', $vendorId)->first();
        if (!$template) {
            $template = SmsApiCredentials::where('route_id', $routeId)
                ->where('vendor_id', 0)
                ->where('status', Variables::ACTIVE)->first();
        }
        if ($template) {
            $domainId = $template->sms_domain_id;
            $domainDetails = SmsDomain::where('id', $domainId)->first();
            $username = $template->username;
            $api_password = $template->api_password;
             $routeCode = $this->getRouteDetails($routeId)->short_code;
            if ($smsPanel->title == SmsPanel::ALERTBOX) {
                $smsBalanceUrl = $domainDetails->domain . '/balancecheck.php?' . 'username=' . $username . '&api_password=' . $api_password . '&priority=' . $this->getRouteDetails($routeId)->priority;
                $bal = $this->sendData($smsBalanceUrl);
                $bal1 = explode(" ", $bal);
                return $apiBalance = $bal1;
            } else if ($smsPanel->title == SmsPanel::MERABT) {
                $routeCode= $routeCode=='OTP' ? 'TL' :  $routeCode ;
                $smsBalanceUrl = $domainDetails->domain . '/api/sms/format/json/key/' . $api_password . '/method/GET/route/' . $routeCode;
                $bal = json_decode($this->sendData($smsBalanceUrl), true);
                $bal1 = isset($bal['data']['api']) ? $bal['data']['api'] : 100;
                if ($bal1 == null) {
                    return $apiBalance = 0;
                }
                return $apiBalance = $bal1;
            } else if ($smsPanel->title == SmsPanel::TEXT_LOCAL) {
                $apiKey = urlencode($template->api_password);
                $data = 'apikey=' . $apiKey;
                $balance_url = 'https://api.textlocal.in/balance/?' . $data;
                return $bal = json_decode($this->sendData($balance_url), true);
                if ($bal['status'] == "success") {
                    return $bal['balance']['sms'];
                } else {
                    return 0;
                }
            }
            else if ($smsPanel->title == SmsPanel::AWSSNS) {
                return 500;
            }
        }
    }


    public function getRouteDetails($routeId)
    {
        $routeDetails = Smsroute::where('pk_int_sms_route_id', $routeId)->first();
        return $routeDetails;
    }

    public function sendData($url)
    {
        try{
            $client = new Client();
            $response = $client->get($url);
            return $response->getBody();
        }catch(\Exception $e){
            \Log::info($e->getMessage());
            return '';
        }
   
    }

    public function getSmsUrlDeveloperApi($senderId, $mobileNumber, $message, $routeId, $routeCode, $vendorId, $messageType, $shtime, $result_type)
    {
        $template = SmsApiCredentials::where('route_id', $routeId)->where('status', '1')->where('vendor_id', $vendorId)->first();
        if ($shtime == 0) {
            $shetime = "";
        } else {
            $shetime = "&shtime=" . $shtime;
        }
        if ($result_type == 2) {
            $result_type = "&result_type=2";
        } else {
            $result_type = "";
        }
        if ($template) {
            $domainId = $template->sms_domain_id;
            $domainDetails = SmsDomain::where('id', $domainId)->first();
            $username = $template->username;
            $api_password = $template->api_password;
            if ($messageType == 0) {
                return $smsUrl = $domainDetails->domain . '/pushsms.php?' . 'username=' . $username . '&api_password=' . $api_password . '&sender=' . $senderId . '&to=' . $mobileNumber . '&message=' . $message . '&priority=' . $routeCode . $shetime . $result_type;
            } else if ($messageType == 1) {
                return $smsUrl = $domainDetails->domain . '/pushsms.php?' . 'username=' . $username . '&api_password=' . $api_password . '&sender=' . $senderId . '&to=' . $mobileNumber . '&message=' . $message . '&priority=' . $routeCode . '&unicode=' . '1' . $shetime . $result_type;
            } else if ($messageType == 2) {
                return $smsUrl = $domainDetails->domain . '/pushsms.php?' . 'username=' . $username . '&api_password=' . $api_password . '&sender=' . $senderId . '&to=' . $mobileNumber . '&message=' . $message . '&priority=' . $routeCode . '&flash=' . '1' . $shetime . $result_type;
            } else if ($messageType == 3) {
                return $smsUrl = $domainDetails->domain . '/pushsms.php?' . 'username=' . $username . '&api_password=' . $api_password . '&sender=' . $senderId . '&to=' . $mobileNumber . '&message=' . $message . '&priority=' . $routeCode . '&picture=' . '1' . $shetime . $result_type;
            }
        } else {
            $templates = SmsApiCredentials::where('route_id', $routeId)->where('status', '1')->where('vendor_id', 0)->first();
            $domainId = $templates->sms_domain_id;
            $domainDetails = SmsDomain::where('id', $domainId)->first();
            $username = $templates->username;
            $api_password = $templates->api_password;
            if ($messageType == 0) {
                return $smsUrl = $domainDetails->domain . '/pushsms.php?' . 'username=' . $username . '&api_password=' . $api_password . '&sender=' . $senderId . '&to=' . $mobileNumber . '&message=' . $message . '&priority=' . $routeCode . $shetime . $result_type;
            } else if ($messageType == 1) {
                return $smsUrl = $domainDetails->domain . '/pushsms.php?' . 'username=' . $username . '&api_password=' . $api_password . '&sender=' . $senderId . '&to=' . $mobileNumber . '&message=' . $message . '&priority=' . $routeCode . '&unicode=' . '1' . $shetime . $result_type;
            } else if ($messageType == 2) {
                return $smsUrl = $domainDetails->domain . '/pushsms.php?' . 'username=' . $username . '&api_password=' . $api_password . '&sender=' . $senderId . '&to=' . $mobileNumber . '&message=' . $message . '&priority=' . $routeCode . '&flash=' . '1' . $shetime . $result_type;
            } else if ($messageType == 3) {
                return $smsUrl = $domainDetails->domain . '/pushsms.php?' . 'username=' . $username . '&api_password=' . $api_password . '&sender=' . $senderId . '&to=' . $mobileNumber . '&message=' . $message . '&priority=' . $routeCode . '&picture=' . '1' . $shetime . $result_type;
            }

        }
    }

}