<?php

declare(strict_types=1);

namespace App\GlApi\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class CreateNoteRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'note' => ['required', 'string', 'max:2000'],
            'mobile_no' => ['nullable', 'numeric', 'digits_between:8,14'],
            'countrycode' => ['nullable', 'string', 'max:5'],
            'email' => ['nullable', 'email', 'max:255'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'note.required' => 'Note content is required',
            'note.max' => 'Note cannot exceed 2000 characters',
            'mobile_no.numeric' => 'Mobile number must be numeric',
            'mobile_no.digits_between' => 'Mobile number must be between 8 and 14 digits',
            'email.email' => 'Please provide a valid email address',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default country code if not provided
        if (!$this->has('countrycode') && $this->filled('mobile_no')) {
            $this->merge([
                'countrycode' => '91' // Default to India
            ]);
        }

        // Clean mobile number - remove any non-numeric characters
        if ($this->filled('mobile_no')) {
            $this->merge([
                'mobile_no' => preg_replace('/[^0-9]/', '', $this->get('mobile_no'))
            ]);
        }
    }
}