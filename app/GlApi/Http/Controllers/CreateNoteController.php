<?php

declare(strict_types=1);

namespace App\GlApi\Http\Controllers;

use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\Events\CreateFollowup;
use App\GlApi\Http\Requests\CreateNoteRequest;
use App\GlApi\Http\Resources\ApiResponse;
use App\GlApi\Http\Traits\HasApiAuthentication;
use App\Http\Controllers\Controller;
use App\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class CreateNoteController extends Controller
{
    use HasApiAuthentication;
    /**
     * Create a note for an existing enquiry
     */
    public function __invoke(CreateNoteRequest $request): JsonResponse
    {
        Log::info('Note creation api request received', [
            'request' => $request->validated()
        ]);

        try {
            // Get vendor ID from middleware
            $vendorId = $this->getVendorId($request);
            $userId = $vendorId; // Use vendor ID as user ID for note creation

            $enquiry = $this->findEnquiry($request, $vendorId);

            if (!$enquiry) {
                return ApiResponse::error('Enquiry not found', 404);
            }

            $this->createNote($request->get('note'), $enquiry, $userId);

            Log::info('Note created successfully', [
                'enquiry_id' => $enquiry->pk_int_enquiry_id,
                'vendor_id' => $vendorId
            ]);

            return ApiResponse::success('Note added successfully');

        } catch (\Exception $e) {
            Log::error('Note creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return ApiResponse::error($e->getMessage(), 500);
        }
    }


    /**
     * Find enquiry by mobile or email
     */
    private function findEnquiry(CreateNoteRequest $request, int $vendorId): ?Enquiry
    {
        $exist = Enquiry::query()
            ->where('fk_int_user_id', $vendorId)
            ->where(function ($q) use ($request) {
                $q->where('vchr_customer_mobile', $request->countrycode . $request->mobile_no)
                    ->orWhere('mobile_no', $request->mobile_no);
            })
            ->first();
        if (!$exist) {
            return Enquiry::where('fk_int_user_id', $vendorId)
                ->where('vchr_customer_email', 'LIKE', $request->email)
                ->first();
        }
        return $exist;
    }

    /**
     * Create note using event
     */
    private function createNote(string $note, Enquiry $enquiry, int $userId): void
    {
        event(new CreateFollowup(
            note: $note,
            log_type: EnquiryFollowup::TYPE_NOTE,
            enquiry_id: $enquiry->pk_int_enquiry_id,
            created_by: $userId
        ));
    }
}