<?php

declare(strict_types=1);

namespace App\GlApi\Http\Middleware;

use App\BackendModel\GlApiTokens;
use App\GlApi\Http\Resources\ApiResponse;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ValidateApiToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $vendorid = $request->input('vendorid');

        if (empty($vendorid)) {
            Log::warning('API request without vendorid', [
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
            ]);

            return ApiResponse::error('Vendor ID is required', 401);
        }

        try {
            // Validate that the vendor ID is numeric and exists
            if (!is_numeric($vendorid)) {
                Log::warning('Invalid vendor ID format', [
                    'vendorid' => $vendorid,
                    'ip' => $request->ip(),
                ]);

                return ApiResponse::error('Invalid vendor ID format', 401);
            }

            $vendorId = (int) $vendorid;

            // Attach the vendor ID to the request for use in controllers
            $request->merge([
                'vendorid' => $vendorId,
            ]);

            // Also set as request attribute for better practice
            $request->attributes->set('vendorid', $vendorId);

            Log::info('API vendor ID validated successfully', [
                'vendor_id' => $vendorId,
            ]);

            return $next($request);

        } catch (\Exception $e) {
            Log::error('Error validating vendor ID', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ApiResponse::error('Authentication error', 500);
        }
    }
}