<?php

declare(strict_types=1);

namespace App\GlApi\Http\Traits;

use Illuminate\Http\Request;

trait HasApiAuthentication
{
    /**
     * Get the authenticated vendor ID from the request
     */
    protected function getVendorId(Request $request): int
    {
        return (int) $request->attributes->get('vendorid');
    }

    /**
     * Get the API token object from the request
     */
    protected function getApiToken(Request $request): ?\App\BackendModel\GlApiTokens
    {
        return $request->attributes->get('api_token');
    }
}