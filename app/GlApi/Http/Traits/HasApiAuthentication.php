<?php

declare(strict_types=1);

namespace App\GlApi\Http\Traits;

use Illuminate\Http\Request;

trait HasApiAuthentication
{
    /**
     * Get the authenticated API user ID from the request
     */
    protected function getApiUserId(Request $request): int
    {
        return (int) $request->attributes->get('api_user_id');
    }

    /**
     * Get the API token object from the request
     */
    protected function getApiToken(Request $request): ?\App\BackendModel\GlApiTokens
    {
        return $request->attributes->get('api_token');
    }
}