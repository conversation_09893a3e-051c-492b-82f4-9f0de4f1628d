<?php

declare(strict_types=1);

use App\Auth\Http\Controllers\Api\AuthenticateUserController;
use App\Common\Middleware\TransformValidationResponseForApi;
use App\Contact\Http\Controllers\Api\GetContactController;
use App\Contact\Http\Controllers\Api\GetContactsController;
use App\Contact\Http\Controllers\Api\SearchEnquiriesController;
use App\Contact\Http\Middlewares\ForceJsonParsing;
use App\Enquiry\Http\Controllers\UploadContactsController;
use App\Ivr\IVRWebhook\Voxbay\IvrEventsWebhookController;
use App\Modules\Facebook\Http\Controllers\RecordEnquiryController;
use App\Task\Http\Controllers\Api\GetTasksController;
use App\Task\Http\Controllers\Api\SubmitCallFeedbackController;
use Illuminate\Support\Facades\Route;

//integration
Route::post('/gl-note-creation-api', \App\GlApi\Http\Controllers\CreateNoteController::class)
    ->middleware(\App\GlApi\Http\Middleware\ValidateApiToken::class);


Route::post('callcenterbridging', IvrEventsWebhookController::class)->name('voxbay-webhook');

Route::post('webhook/facebook/get-call-back', RecordEnquiryController::class)
    ->middleware(['api'])
    ->name('meta.record-enquiry-webhook');

Route::group([
    'prefix' => 'agent-app',
], static function (): void {
    Route::middleware(TransformValidationResponseForApi::class)
        ->post('login', AuthenticateUserController::class);

    Route::group([
        'middleware' => 'jwt.auth',
    ], static function (): void {
        Route::middleware(ForceJsonParsing::class)
            ->post('v1/enquiry-call-feedback', SubmitCallFeedbackController::class)
            ->name('api.enquiry-call-feedback');

        Route::middleware(ForceJsonParsing::class)
            ->post('v1/tasks', GetTasksController::class)
            ->name('api.tasks');

        Route::group([
            'prefix' => 'enquiries',
        ], static function (): void {
            Route::middleware(ForceJsonParsing::class)
                ->post('/', GetContactsController::class)->name('api.enquiries.list');
            Route::post('details-v2', GetContactController::class)->name('api.enquiries.details');

            Route::group([
                'namespace' => 'App\Http\Controllers\Api\AgentApp',
            ], static function (): void {
                Route::post('store', 'EnquiryController@store');
                Route::post('show', 'EnquiryController@showLead');
                Route::post('details', 'EnquiryController@show');
                Route::post('delete', 'EnquiryController@destroy');
                Route::post('update', 'EnquiryController@update');
                Route::post('update-status', 'EnquiryController@updateStatus');
            });
        });

        Route::post('enquiries-search', SearchEnquiriesController::class);
    });
});

Route::post('/api/contacts/upload', UploadContactsController::class)
    ->name('api.contacts.upload');
